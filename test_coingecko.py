#!/usr/bin/env python3
"""
Test CoinGecko MCP Integration
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append('.')

async def test_coingecko_integration():
    """Test CoinGecko MCP integration"""
    print("🧪 Testing CoinGecko MCP Integration...")
    
    try:
        # Test client import
        from coingecko_mcp_client import coingecko_client
        print("✅ CoinGecko client imported successfully")
        
        # Test usage stats
        usage_stats = coingecko_client.get_usage_stats()
        print(f"📊 Usage Stats: {usage_stats['monthly_calls']}/{usage_stats['monthly_limit']} calls")
        print(f"📈 Usage Percentage: {usage_stats['usage_percentage']:.1f}%")
        
        # Test integration service
        from coingecko_integration import coingecko_integration
        print("✅ CoinGecko integration imported successfully")
        
        # Test usage summary
        usage_summary = coingecko_integration.get_usage_summary()
        print(f"📈 Integration Status: {usage_summary['status']}")
        print(f"💰 Monthly Cost: ${usage_summary['estimated_monthly_cost']:.2f}")
        print(f"🔄 Plan: {usage_summary['plan']}")
        
        # Test API call if we have quota
        if usage_stats['monthly_remaining'] > 5:
            print("🔄 Testing API call...")
            try:
                # Test simple price fetch
                prices = await coingecko_client.get_simple_price(['bitcoin', 'ethereum'])
                if prices:
                    print(f"✅ Price data fetched: {len(prices)} coins")
                    for coin, data in prices.items():
                        usd_price = data.get('usd', 0)
                        print(f"  {coin}: ${usd_price:,.2f}")
                else:
                    print("⚠️ No price data returned")
                    
                # Test trending coins
                trending = await coingecko_client.get_trending_coins()
                if trending and 'coins' in trending:
                    print(f"✅ Trending data fetched: {len(trending['coins'])} coins")
                    for coin_data in trending['coins'][:3]:
                        coin = coin_data.get('item', {})
                        name = coin.get('name', 'Unknown')
                        symbol = coin.get('symbol', 'N/A')
                        print(f"  Trending: {name} ({symbol})")
                        
            except Exception as e:
                print(f"⚠️ API call failed: {e}")
        else:
            print("⚠️ Skipping API test - low quota remaining")
        
        # Test enhanced token data (cached)
        print("🔄 Testing enhanced token data...")
        enhanced_tokens = await coingecko_integration.get_enhanced_token_data(limit=5)
        if enhanced_tokens:
            print(f"✅ Enhanced tokens: {len(enhanced_tokens)} tokens")
            for token in enhanced_tokens[:3]:
                symbol = token.get('symbol', 'N/A')
                price = token.get('price', 0)
                alpha_score = token.get('alpha_score', 0)
                signal = token.get('signal', 'N/A')
                print(f"  {symbol}: ${price:.4f} (Score: {alpha_score:.2f}, Signal: {signal})")
        else:
            print("⚠️ No enhanced token data")
        
        # Close client
        await coingecko_client.close()
        
        print("✅ CoinGecko integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ CoinGecko test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test basic imports"""
    print("🔍 Testing imports...")
    
    try:
        from coingecko_mcp_client import coingecko_client
        print("✅ coingecko_mcp_client imported")
        
        from coingecko_integration import coingecko_integration
        print("✅ coingecko_integration imported")
        
        from coingecko_usage_monitor import coingecko_usage_monitor
        print("✅ coingecko_usage_monitor imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting CoinGecko MCP Integration Tests")
    print("=" * 50)
    
    # Test imports first
    if not test_imports():
        print("❌ Import tests failed - exiting")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Test integration
    success = asyncio.run(test_coingecko_integration())
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All CoinGecko tests passed!")
    else:
        print("❌ Some tests failed")
        sys.exit(1)
