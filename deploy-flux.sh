#!/bin/bash

# Alpha Predator Bot - Flux Deployment Script
# This script builds and pushes Docker images for Flux deployment

set -e

echo "🚀 Alpha Predator Bot - Flux Deployment Script"
echo "=============================================="

# Configuration
DOCKER_REGISTRY="kryptomerch"
FRONTEND_IMAGE="alpha-frontend"
BACKEND_IMAGE="alpha-backend"
TAG="latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if logged into Docker Hub
if ! docker info | grep -q "Username"; then
    print_warning "Not logged into Docker Hub. Attempting to log in..."
    docker login
fi

print_status "Building Docker images for AMD64 architecture..."

# Build Frontend Image
print_status "Building frontend image: ${DOCKER_REGISTRY}/${FRONTEND_IMAGE}:${TAG}"
cd frontend
docker build --platform linux/amd64 -f Dockerfile.prod -t ${DOCKER_REGISTRY}/${FRONTEND_IMAGE}:${TAG} .
if [ $? -eq 0 ]; then
    print_success "Frontend image built successfully"
else
    print_error "Frontend image build failed"
    exit 1
fi
cd ..

# Build Backend Image
print_status "Building backend image: ${DOCKER_REGISTRY}/${BACKEND_IMAGE}:${TAG}"
cd backend
docker build --platform linux/amd64 -f Dockerfile.prod -t ${DOCKER_REGISTRY}/${BACKEND_IMAGE}:${TAG} .
if [ $? -eq 0 ]; then
    print_success "Backend image built successfully"
else
    print_error "Backend image build failed"
    exit 1
fi
cd ..

# Push Frontend Image
print_status "Pushing frontend image to Docker Hub..."
docker push ${DOCKER_REGISTRY}/${FRONTEND_IMAGE}:${TAG}
if [ $? -eq 0 ]; then
    print_success "Frontend image pushed successfully"
else
    print_error "Frontend image push failed"
    exit 1
fi

# Push Backend Image
print_status "Pushing backend image to Docker Hub..."
docker push ${DOCKER_REGISTRY}/${BACKEND_IMAGE}:${TAG}
if [ $? -eq 0 ]; then
    print_success "Backend image pushed successfully"
else
    print_error "Backend image push failed"
    exit 1
fi

print_success "All images built and pushed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "======================"
echo "Frontend Image: ${DOCKER_REGISTRY}/${FRONTEND_IMAGE}:${TAG}"
echo "Backend Image:  ${DOCKER_REGISTRY}/${BACKEND_IMAGE}:${TAG}"
echo ""
echo "🔧 Next Steps:"
echo "1. Update your Flux deployment configurations with these image tags"
echo "2. Deploy to Flux using the Flux CLI or web interface"
echo "3. Monitor the deployment logs for any issues"
echo ""
echo "🌐 Expected URLs after deployment:"
echo "Frontend: https://www.alphapredatorbot.xyz"
echo "Backend:  https://api.alphapredatorbot.xyz"
echo ""
print_success "Deployment script completed successfully!"
