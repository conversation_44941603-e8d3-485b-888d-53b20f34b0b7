#!/usr/bin/env python3
"""
Fix KuCoin Import Issue
The kucoin-python package has changed its import structure
"""

import os
import sys
from pathlib import Path

def fix_kucoin_imports():
    """Fix KuCoin import issues in all relevant files"""
    print("🔧 Fixing KuCoin import issues...")
    
    # Files that need KuCoin import fixes
    files_to_fix = [
        'backend/kucoin_transaction_tracker.py',
        'backend/kucoin_portfolio_tracker.py',
        'backend/kucoin_sdk_migration.py'
    ]
    
    for file_path in files_to_fix:
        if Path(file_path).exists():
            print(f"📝 Fixing {file_path}...")
            
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Replace the old import with the new one
            old_import = "from kucoin.client import Client"
            new_import = """try:
    from kucoin.client import Client
except ImportError:
    try:
        from kucoin import Client
    except ImportError:
        print("KuCoin client not available - install with: pip install kucoin-python")
        Client = None"""
            
            if old_import in content:
                content = content.replace(old_import, new_import)
                
                # Also add error handling for client initialization
                if "self.client = Client(" in content:
                    content = content.replace(
                        "self.client = Client(",
                        """if Client is None:
            print("KuCoin client not available")
            self.client = None
            return
        
        try:
            self.client = Client("""
                    )
                
                with open(file_path, 'w') as f:
                    f.write(content)
                
                print(f"✅ Fixed {file_path}")
            else:
                print(f"ℹ️ {file_path} already has correct imports")

def create_fallback_kucoin_client():
    """Create a fallback KuCoin client that works without real API"""
    print("🔧 Creating fallback KuCoin client...")
    
    fallback_content = '''#!/usr/bin/env python3
"""
Fallback KuCoin Client
Provides mock data when real KuCoin API is not available
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class FallbackKuCoinClient:
    """Fallback client that provides mock data"""
    
    def __init__(self):
        self.client = None
        logger.info("Using fallback KuCoin client (mock data)")
    
    def get_live_trades_data(self, limit: int = 50) -> Dict[str, Any]:
        """Return mock trades data"""
        mock_trades = [
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': 'BTC-USDT',
                'action': 'BUY',
                'quantity': 0.001,
                'price': 45000.0,
                'total': 45.0,
                'status': 'COMPLETED',
                'fees': 0.045,
                'trade_id': 'mock_001',
                'source': 'fallback_mock'
            },
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': 'ETH-USDT',
                'action': 'SELL',
                'quantity': 0.01,
                'price': 3000.0,
                'total': 30.0,
                'status': 'COMPLETED',
                'fees': 0.03,
                'trade_id': 'mock_002',
                'source': 'fallback_mock'
            }
        ]
        
        return {
            'trades': mock_trades[:limit],
            'last_updated': datetime.now().isoformat(),
            'total_trades': len(mock_trades),
            'source': 'fallback_kucoin_client',
            'metadata': {
                'note': 'This is mock data. Configure KuCoin API for real trades.'
            }
        }

# Global fallback instance
fallback_client = FallbackKuCoinClient()

def get_kucoin_live_trades(limit: int = 50) -> Dict[str, Any]:
    """Get live trades with fallback to mock data"""
    return fallback_client.get_live_trades_data(limit)

def get_kucoin_portfolio():
    """Get portfolio with fallback to mock data"""
    mock_portfolio = {
        'BTC': {'qty': 0.001, 'avg_price': 45000.0},
        'ETH': {'qty': 0.01, 'avg_price': 3000.0}
    }
    mock_balance = 1000.0
    
    return mock_portfolio, mock_balance
'''
    
    with open('backend/fallback_kucoin_client.py', 'w') as f:
        f.write(fallback_content)
    
    print("✅ Created fallback KuCoin client")

def update_trade_logger_imports():
    """Update trade_logger.py to use fallback client"""
    print("🔧 Updating trade_logger.py imports...")
    
    trade_logger_path = Path('backend/trade_logger.py')
    if trade_logger_path.exists():
        with open(trade_logger_path, 'r') as f:
            content = f.read()
        
        # Update the import to use fallback
        old_import = "from kucoin_transaction_tracker import get_kucoin_live_trades"
        new_import = """try:
        from kucoin_transaction_tracker import get_kucoin_live_trades
    except ImportError:
        from fallback_kucoin_client import get_kucoin_live_trades
        logger.info("Using fallback KuCoin client")"""
        
        if old_import in content:
            content = content.replace(old_import, new_import)
            
            with open(trade_logger_path, 'w') as f:
                f.write(content)
            
            print("✅ Updated trade_logger.py imports")

def main():
    """Main fix function"""
    print("🚀 Fixing KuCoin Import Issues")
    print("=" * 40)
    
    fix_kucoin_imports()
    create_fallback_kucoin_client()
    update_trade_logger_imports()
    
    print("\\n✅ KuCoin import fixes completed!")
    print("\\n📋 Next Steps:")
    print("1. Restart the backend server")
    print("2. The system will now use fallback data if KuCoin API is unavailable")
    print("3. Configure KuCoin API keys in .env for real trading data")

if __name__ == "__main__":
    main()
