#!/usr/bin/env python3
"""
Comprehensive Cost Estimator for Alpha Predator Trading Bot
Analyzes costs for all APIs and services used in the project
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple

def main():
    """Run comprehensive cost analysis"""
    print("🏦 Comprehensive Cost Analysis for Alpha Predator Trading Bot")
    print("=" * 80)
    
    # AI Provider Costs (per 1M tokens)
    ai_costs = {
        "OpenAI GPT-4o-mini": {"input": 0.15, "output": 0.60},
        "Claude 3.5 Haiku": {"input": 0.25, "output": 1.25},
        "Gemini 1.5 Flash": {"input": 0.075, "output": 0.30},
        "DeepSeek V3": {"input": 0.14, "output": 0.28}
    }
    
    # Daily usage estimates
    daily_ai_usage = {
        "news_sentiment": {"calls": 100, "input_tokens": 150, "output_tokens": 50},
        "trade_decisions": {"calls": 50, "input_tokens": 300, "output_tokens": 100},
        "arbitrage_analysis": {"calls": 20, "input_tokens": 400, "output_tokens": 150},
        "breaking_news": {"calls": 10, "input_tokens": 200, "output_tokens": 75}
    }
    
    print("\n🤖 AI PROVIDER COST ANALYSIS")
    print("-" * 60)
    
    # Calculate daily AI costs for each provider
    for provider, pricing in ai_costs.items():
        daily_cost = 0.0
        total_input_tokens = 0
        total_output_tokens = 0
        
        for task, usage in daily_ai_usage.items():
            calls = usage["calls"]
            input_tokens = usage["input_tokens"] * calls
            output_tokens = usage["output_tokens"] * calls
            
            total_input_tokens += input_tokens
            total_output_tokens += output_tokens
            
            input_cost = (input_tokens / 1_000_000) * pricing["input"]
            output_cost = (output_tokens / 1_000_000) * pricing["output"]
            daily_cost += input_cost + output_cost
        
        monthly_cost = daily_cost * 30
        annual_cost = daily_cost * 365
        
        print(f"\n{provider}:")
        print(f"  Daily tokens: {total_input_tokens:,} input + {total_output_tokens:,} output")
        print(f"  Daily cost: ${daily_cost:.4f}")
        print(f"  Monthly cost: ${monthly_cost:.2f}")
        print(f"  Annual cost: ${annual_cost:.2f}")
    
    # Data Provider Costs
    print("\n\n📡 DATA PROVIDER COST ANALYSIS")
    print("-" * 60)
    
    data_providers = {
        "TokenMetrics": {
            "free": {"calls": 1000, "cost": 0},
            "basic": {"calls": 10000, "cost": 49},
            "pro": {"calls": 50000, "cost": 199}
        },
        "CoinGecko": {
            "free": {"calls": 10000, "cost": 0},
            "analyst": {"calls": 100000, "cost": 129},
            "pro": {"calls": 500000, "cost": 499}
        },
        "CryptoPanic": {
            "free": {"calls": 3000, "cost": 0},
            "basic": {"calls": 20000, "cost": 19},
            "pro": {"calls": 100000, "cost": 49}
        }
    }
    
    # Estimated daily calls
    daily_data_calls = {
        "TokenMetrics": 90,  # AI reports + technical indicators + token info
        "CoinGecko": 700,    # Market data + price updates
        "CryptoPanic": 148   # News fetch + sentiment analysis
    }
    
    for provider, tiers in data_providers.items():
        daily_calls = daily_data_calls[provider]
        monthly_calls = daily_calls * 30
        
        print(f"\n{provider}:")
        print(f"  Estimated daily calls: {daily_calls}")
        print(f"  Estimated monthly calls: {monthly_calls:,}")
        
        for tier, limits in tiers.items():
            if monthly_calls <= limits["calls"]:
                status = "✅ WITHIN LIMITS"
            else:
                status = "❌ EXCEEDS LIMITS"
            
            print(f"  {tier.title()} tier: {limits['calls']:,} calls/month, ${limits['cost']}/month {status}")
    
    # Exchange Trading Fees
    print("\n\n💱 EXCHANGE TRADING FEES")
    print("-" * 60)
    
    exchanges = {
        "KuCoin": {"maker": 0.1, "taker": 0.1},  # 0.1%
        "Binance": {"maker": 0.1, "taker": 0.1}  # 0.1%
    }
    
    # Trading volume scenarios
    volume_scenarios = {
        "Conservative": 1000,   # $1,000/day
        "Moderate": 5000,       # $5,000/day
        "Aggressive": 20000     # $20,000/day
    }
    
    for scenario, daily_volume in volume_scenarios.items():
        print(f"\n{scenario} Trading ({daily_volume:,} USD/day):")
        for exchange, fees in exchanges.items():
            avg_fee = (fees["maker"] + fees["taker"]) / 2 / 100  # Convert to decimal
            daily_fee = daily_volume * avg_fee
            monthly_fee = daily_fee * 30
            annual_fee = daily_fee * 365
            
            print(f"  {exchange}: ${daily_fee:.2f}/day, ${monthly_fee:.2f}/month, ${annual_fee:.2f}/year")
    
    # Infrastructure Costs
    print("\n\n🖥️ INFRASTRUCTURE COSTS (Monthly)")
    print("-" * 60)
    
    infrastructure = {
        "VPS Hosting (Medium)": 20,
        "Database (Medium)": 10,
        "Monitoring (Basic)": 0,
        "Telegram Bot": 0,
        "Domain & SSL": 2
    }
    
    total_infra = 0
    for service, cost in infrastructure.items():
        print(f"  {service}: ${cost}/month")
        total_infra += cost
    
    print(f"\n  Total Infrastructure: ${total_infra}/month")
    
    # Summary
    print("\n\n💰 COST SUMMARY")
    print("-" * 60)
    
    # Find cheapest AI provider
    cheapest_ai_cost = float('inf')
    cheapest_ai_provider = ""
    
    for provider, pricing in ai_costs.items():
        daily_cost = 0.0
        for task, usage in daily_ai_usage.items():
            calls = usage["calls"]
            input_tokens = usage["input_tokens"] * calls
            output_tokens = usage["output_tokens"] * calls
            input_cost = (input_tokens / 1_000_000) * pricing["input"]
            output_cost = (output_tokens / 1_000_000) * pricing["output"]
            daily_cost += input_cost + output_cost
        
        if daily_cost < cheapest_ai_cost:
            cheapest_ai_cost = daily_cost
            cheapest_ai_provider = provider
    
    print(f"\n🏆 RECOMMENDED CONFIGURATION:")
    print(f"  AI Provider: {cheapest_ai_provider} (${cheapest_ai_cost:.4f}/day)")
    print(f"  TokenMetrics: Basic tier ($49/month)")
    print(f"  CoinGecko: Free tier (sufficient for moderate usage)")
    print(f"  CryptoPanic: Basic tier ($19/month)")
    print(f"  Infrastructure: ${total_infra}/month")
    
    monthly_ai = cheapest_ai_cost * 30
    monthly_data = 49 + 0 + 19  # TokenMetrics Basic + CoinGecko Free + CryptoPanic Basic
    monthly_total = monthly_ai + monthly_data + total_infra
    
    print(f"\n📊 MONTHLY TOTALS:")
    print(f"  AI Costs: ${monthly_ai:.2f}")
    print(f"  Data Providers: ${monthly_data}")
    print(f"  Infrastructure: ${total_infra}")
    print(f"  Total (excluding trading fees): ${monthly_total:.2f}/month")
    print(f"  Annual cost: ${monthly_total * 12:.2f}/year")
    
    print(f"\n💡 COST OPTIMIZATION TIPS:")
    print(f"  • Use DeepSeek V3 for 90% of AI calls (cheapest)")
    print(f"  • Cache API responses to reduce call frequency")
    print(f"  • Monitor usage to stay within free tiers where possible")
    print(f"  • Consider upgrading data providers only when limits are exceeded")
    print(f"  • Trading fees will be your largest variable cost")

if __name__ == "__main__":
    main()
