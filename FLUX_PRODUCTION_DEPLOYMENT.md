# Alpha Predator Bot - Flux Production Deployment Guide

## 🚨 Critical Issues Fixed

The following critical issues have been identified and resolved:

### 1. **Frontend nginx Configuration**
- **Issue**: nginx was trying to proxy to `backend:3005` which doesn't exist in Flux
- **Fix**: Updated to proxy directly to `https://api.alphapredatorbot.xyz`

### 2. **Docker AMD64 Compatibility**
- **Issue**: Dockerfiles weren't specifying AMD64 platform
- **Fix**: Added `--platform=linux/amd64` to all Docker builds

### 3. **Backend Configuration Validation**
- **Issue**: Backend was failing validation for missing environment variables
- **Fix**: Relaxed validation in production mode for Flux environment variables

### 4. **CORS Configuration**
- **Issue**: Backend CORS was misconfigured for production
- **Fix**: Properly configured allowed origins for Flux domains

## 📋 Deployment Steps

### Step 1: Build and Push Docker Images

Run the deployment script:

```bash
./deploy-flux.sh
```

This script will:
- Build AMD64-compatible Docker images
- Push to Docker Hub with correct tags
- Verify the build process

### Step 2: Verify Image Tags

Ensure your Flux deployment uses these exact image tags:
- **Frontend**: `kryptomerch/alpha-frontend:latest`
- **Backend**: `kryptomerch/alpha-backend:latest`

### Step 3: Environment Variables

#### Frontend Environment Variables (Set in Flux)
```json
{
  "name": "NODE_ENV",
  "value": "production"
},
{
  "name": "VITE_BACKEND_URL",
  "value": "https://api.alphapredatorbot.xyz"
},
{
  "name": "VITE_PUBLIC_TELEGRAM",
  "value": "https://t.me/alphapredatortrading"
},
{
  "name": "VITE_PUBLIC_GITHUB",
  "value": "https://github.com/kryptomerch/alpha-predator-safe"
},
{
  "name": "VITE_BRAND_NAME",
  "value": "Alpha Predator Bot"
}
```

#### Backend Environment Variables
The backend will use the environment variables from your `.env.production` file, which should be properly configured for production.

### Step 4: Flux Deployment Configuration

#### Frontend Configuration
```json
{
  "name": "alphAfrontend",
  "description": "React frontend dashboard with nginx serving static files",
  "dockerRepository": "kryptomerch/alpha-frontend:latest",
  "ports": [37466],
  "containerPorts": [80],
  "domains": ["www.alphapredatorbot.xyz"],
  "cpu": 0.5,
  "ram": 1000,
  "ssd": 10,
  "environmentParameters": [
    {"name": "NODE_ENV", "value": "production"},
    {"name": "VITE_BACKEND_URL", "value": "https://api.alphapredatorbot.xyz"},
    {"name": "VITE_PUBLIC_TELEGRAM", "value": "https://t.me/alphapredatortrading"},
    {"name": "VITE_PUBLIC_GITHUB", "value": "https://github.com/kryptomerch/alpha-predator-safe"},
    {"name": "VITE_BRAND_NAME", "value": "Alpha Predator Bot"}
  ]
}
```

#### Backend Configuration
```json
{
  "name": "alphabackend",
  "description": "backend component for alpha predator bot",
  "dockerRepository": "kryptomerch/alpha-backend:latest",
  "ports": [33480],
  "containerPorts": [3005],
  "domains": ["api.alphapredatorbot.xyz"],
  "cpu": 2,
  "ram": 4000,
  "ssd": 50
}
```

## 🔧 Key Fixes Applied

### 1. Frontend nginx.prod.conf
- Fixed proxy configuration to point to external backend URL
- Added proper CORS headers
- Added gzip compression
- Added security headers
- Added health check endpoint

### 2. Frontend Dockerfile.prod
- Added AMD64 platform specification
- Added proper health check
- Optimized build process
- Added all required environment variables

### 3. Backend Dockerfile.prod
- Added AMD64 platform specification
- Fixed NLTK data download
- Added proper health check
- Optimized for production

### 4. Backend Configuration
- Relaxed environment validation for production
- Fixed CORS configuration
- Added proper logging

## 🚀 Deployment Verification

After deployment, verify the following:

### 1. Frontend Health Check
```bash
curl https://www.alphapredatorbot.xyz/health
```
Expected response: `healthy`

### 2. Backend Health Check
```bash
curl https://api.alphapredatorbot.xyz/health
```
Expected response: `{"status": "healthy"}`

### 3. API Connectivity
```bash
curl https://api.alphapredatorbot.xyz/api/health
```
Expected response: `{"status": "ok", "version": "1.1.0", "env": "production"}`

## 🐛 Troubleshooting

### Frontend Issues
1. **White screen**: Check browser console for errors
2. **API errors**: Verify backend is running and accessible
3. **CORS errors**: Check nginx configuration

### Backend Issues
1. **Container won't start**: Check environment variables
2. **Health check fails**: Verify port 3005 is accessible
3. **Database errors**: Check connection strings

### Common Issues
1. **Images not found**: Verify Docker Hub push was successful
2. **Environment variables**: Ensure all required vars are set
3. **Domain routing**: Verify DNS and Flux domain configuration

## 📊 Monitoring

Monitor your deployment using:
1. Flux dashboard logs
2. Application health endpoints
3. Browser developer tools for frontend issues

## 🔄 Updates

To update the deployment:
1. Make code changes
2. Run `./deploy-flux.sh` to build and push new images
3. Update Flux deployment to pull latest images
4. Monitor deployment logs

## 📞 Support

If issues persist:
1. Check Flux deployment logs
2. Verify all environment variables are set correctly
3. Ensure Docker images are built for AMD64 architecture
4. Verify domain DNS configuration

---

**Note**: This deployment guide addresses the specific issues that were preventing your Flux deployment from starting. The main problems were related to nginx proxy configuration, Docker platform compatibility, and environment variable validation.
