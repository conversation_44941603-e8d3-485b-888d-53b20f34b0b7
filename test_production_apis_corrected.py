#!/usr/bin/env python3
"""
Corrected Production API Testing Script
Tests actual available endpoints with proper authentication
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any

# API Configuration
BASE_URL = "http://localhost:33903"
HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "AlphaPredator-Production-Test/1.0"
}

class CorrectedAPITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
        self.test_results = []
        self.auth_token = None
        
    def log_test(self, test_name: str, success: bool, details: str, response_time: float = 0):
        """Log test results"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details} ({response_time:.2f}s)")

    def authenticate(self):
        """Attempt to authenticate with the API"""
        print("\n🔐 Testing Authentication...")
        
        # Try with a test email/password (this will likely fail but shows the auth flow)
        try:
            start_time = time.time()
            
            # Test login endpoint
            login_data = {
                "email": "<EMAIL>",
                "password": "testpassword"
            }
            
            response = requests.post(
                f"{self.base_url}/api/login",
                json=login_data,
                headers=self.headers,
                timeout=10
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                self.headers["Authorization"] = f"Bearer {self.auth_token}"
                self.log_test("Authentication", True, "Successfully authenticated", response_time)
                return True
            else:
                self.log_test("Authentication", False, f"HTTP {response.status_code}: {response.text}", response_time)
                return False
                
        except Exception as e:
            self.log_test("Authentication", False, f"Exception: {str(e)}", 0)
            return False

    def test_health_endpoints(self):
        """Test health endpoints (no auth required)"""
        print("\n🏥 Testing Health Endpoints...")
        
        endpoints = [
            {"path": "/", "name": "Root"},
            {"path": "/health", "name": "Basic Health"},
            {"path": "/api/health", "name": "API Health"}
        ]
        
        success_count = 0
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint['path']}", headers=self.headers, timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    details = f"Status: {data.get('status', 'ok')}"
                    if 'version' in data:
                        details += f", Version: {data['version']}"
                    self.log_test(f"Health - {endpoint['name']}", True, details, response_time)
                    success_count += 1
                else:
                    self.log_test(f"Health - {endpoint['name']}", False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_test(f"Health - {endpoint['name']}", False, f"Exception: {str(e)}", 0)
        
        return success_count == len(endpoints)

    def test_tokenmetrics_endpoints(self):
        """Test TokenMetrics endpoints (requires auth)"""
        print("\n📊 Testing TokenMetrics Endpoints...")
        
        if not self.auth_token:
            print("⚠️  Skipping TokenMetrics tests - no authentication token")
            return False
        
        # Test popular tokens
        test_tokens = ["BTC", "ETH", "ADA", "SOL", "MATIC"]
        
        success_count = 0
        for token in test_tokens:
            try:
                start_time = time.time()
                
                response = requests.get(
                    f"{self.base_url}/api/tokenmetrics/{token}",
                    headers=self.headers,
                    timeout=30
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    details = f"Data received for {token}"
                    if 'symbol' in data:
                        details += f", Symbol: {data['symbol']}"
                    if 'available' in data:
                        details += f", Available: {data['available']}"
                    self.log_test(f"TokenMetrics - {token}", True, details, response_time)
                    success_count += 1
                elif response.status_code == 404:
                    self.log_test(f"TokenMetrics - {token}", False, f"Token {token} not found", response_time)
                else:
                    self.log_test(f"TokenMetrics - {token}", False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_test(f"TokenMetrics - {token}", False, f"Exception: {str(e)}", 0)
        
        return success_count > 0

    def log_(self, message: str):
        """Simple logging method"""
        print(message)

    def run_all_tests(self):
        """Run all production API tests"""
        print("🚀 Starting Production API Tests")
        print("=" * 50)
        
        # Test health endpoints first (no auth required)
        health_ok = self.test_health_endpoints()
        
        # Try authentication
        auth_ok = self.authenticate()
        
        # Test TokenMetrics if authenticated
        tokenmetrics_ok = False
        if auth_ok:
            tokenmetrics_ok = self.test_tokenmetrics_endpoints()
        
        # Summary
        print("\n📋 Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Save results to file
        with open("production_api_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: production_api_test_results.json")
        
        return passed_tests == total_tests

def main():
    """Main test function"""
    tester = CorrectedAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
