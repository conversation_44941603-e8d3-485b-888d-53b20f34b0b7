#!/usr/bin/env python3
"""
Advanced Trading Bot Startup Script
===================================
Starts the comprehensive automated trading system with:
- Continuous AI-driven buying
- Multi-level profit taking
- Advanced portfolio management
- Real-time monitoring
"""

import asyncio
import logging
import signal
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append('./backend')

from backend.advanced_trading_orchestrator import advanced_orchestrator
from backend.config import TRADING_MODE, KUCOIN_VALID

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_trading.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class AdvancedTradingBot:
    """Main trading bot controller"""
    
    def __init__(self):
        self.orchestrator = advanced_orchestrator
        self.running = False
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Setup graceful shutdown handlers"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}. Shutting down gracefully...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def startup_checks(self) -> bool:
        """Perform startup validation checks"""
        logger.info("🔍 Performing startup checks...")
        
        # Check trading mode
        if TRADING_MODE.upper() != "LIVE":
            logger.error(f"❌ Trading mode is {TRADING_MODE}, but LIVE mode required for real trading")
            return False
        
        # Check KuCoin connection
        if not KUCOIN_VALID:
            logger.error("❌ KuCoin API credentials not valid")
            return False
        
        # Test KuCoin connection
        try:
            from backend.kucoin_api import KuCoinAPI
            kucoin = KuCoinAPI()
            if not kucoin.authenticated:
                logger.error("❌ KuCoin authentication failed")
                return False
            
            balance = kucoin.get_account_balance("USDT")
            logger.info(f"💰 USDT Balance: ${balance:.2f}")
            
            if balance < 1.0:
                logger.warning("⚠️ Low USDT balance - may limit trading opportunities")
            
        except Exception as e:
            logger.error(f"❌ KuCoin connection test failed: {e}")
            return False
        
        # Check AI systems
        try:
            from backend.ai_clients.ai_request_manager import get_ai_decision_with_consensus
            test_prompt = "Test prompt for BTC-USDT analysis"
            result = get_ai_decision_with_consensus(test_prompt, "BTC-USDT")
            if not result:
                logger.error("❌ AI decision system not responding")
                return False
            logger.info("✅ AI decision system operational")
        except Exception as e:
            logger.error(f"❌ AI system test failed: {e}")
            return False
        
        logger.info("✅ All startup checks passed")
        return True
    
    async def start(self):
        """Start the advanced trading bot"""
        logger.info("🚀 Starting Advanced Trading Bot")
        logger.info("=" * 60)
        
        # Perform startup checks
        if not await self.startup_checks():
            logger.error("❌ Startup checks failed. Exiting.")
            return
        
        # Display configuration
        self.display_configuration()
        
        # Start the orchestrator
        self.running = True
        
        try:
            # Start monitoring task
            monitor_task = asyncio.create_task(self.monitor_system())
            
            # Start the main orchestrator
            orchestrator_task = asyncio.create_task(self.orchestrator.start_orchestrator())
            
            # Wait for either task to complete
            await asyncio.gather(monitor_task, orchestrator_task, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"❌ Critical error in trading bot: {e}")
        finally:
            await self.shutdown()
    
    def display_configuration(self):
        """Display current trading configuration"""
        logger.info("📊 TRADING CONFIGURATION")
        logger.info("-" * 40)
        logger.info(f"Trading Mode: {TRADING_MODE}")
        logger.info(f"Max Positions: {self.orchestrator.max_positions}")
        logger.info(f"Min Confidence: {self.orchestrator.min_confidence:.2f}")
        logger.info(f"Base Order Size: ${self.orchestrator.default_profit_levels[0]}")
        logger.info("Profit Levels:")
        for i, level in enumerate(self.orchestrator.default_profit_levels, 1):
            logger.info(f"  Level {i}: {level.percentage*100:.1f}% profit → Sell {level.quantity_ratio*100:.0f}% ({level.order_type})")
        logger.info("-" * 40)
    
    async def monitor_system(self):
        """Monitor system health and performance"""
        while self.running:
            try:
                # Get portfolio summary
                summary = self.orchestrator.get_portfolio_summary()
                
                # Log status every 5 minutes
                logger.info("📊 PORTFOLIO STATUS")
                logger.info(f"   Positions: {summary['total_positions']}")
                logger.info(f"   Total Value: ${summary['total_value']:.2f}")
                logger.info(f"   Total P&L: ${summary['total_pnl']:.2f} ({summary['total_pnl_percentage']:.2f}%)")
                
                # Log individual positions if any
                if summary['positions']:
                    logger.info("   Active Positions:")
                    for pos in summary['positions'][:5]:  # Show top 5
                        logger.info(f"     {pos['symbol']}: ${pos['current_value']:.2f} ({pos['pnl_percentage']:.2f}%)")
                
                await asyncio.sleep(300)  # Monitor every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in system monitor: {e}")
                await asyncio.sleep(60)
    
    async def shutdown(self):
        """Graceful shutdown"""
        if not self.running:
            return
            
        logger.info("🛑 Initiating graceful shutdown...")
        self.running = False
        
        try:
            # Stop the orchestrator
            await self.orchestrator.stop_orchestrator()
            
            # Final portfolio summary
            summary = self.orchestrator.get_portfolio_summary()
            logger.info("📊 FINAL PORTFOLIO SUMMARY")
            logger.info(f"   Total Positions: {summary['total_positions']}")
            logger.info(f"   Total Value: ${summary['total_value']:.2f}")
            logger.info(f"   Total P&L: ${summary['total_pnl']:.2f}")
            
            logger.info("✅ Shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

async def main():
    """Main entry point"""
    bot = AdvancedTradingBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        logger.info("Advanced Trading Bot stopped")

if __name__ == "__main__":
    print("🤖 Advanced Trading Bot")
    print("=" * 50)
    print("Features:")
    print("✅ Continuous AI-driven buying")
    print("✅ Multi-level profit taking (3%, 6%, 10%, 15%)")
    print("✅ Trailing stop losses")
    print("✅ Dynamic position sizing")
    print("✅ Real-time portfolio monitoring")
    print("=" * 50)
    print()
    
    # Confirm before starting
    if TRADING_MODE.upper() == "LIVE":
        response = input("⚠️  LIVE TRADING MODE - This will use real money! Continue? (yes/no): ")
        if response.lower() != 'yes':
            print("❌ Aborted by user")
            sys.exit(0)
    
    # Run the bot
    asyncio.run(main())
