"""
Enhanced Arbitrage System with TokenMetrics AI Integration
Maximizes returns on available positions through intelligent arbitrage detection
and AI-powered trading signals.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json

from tokenmetrics_api import TokenMetricsAPI
from exchange_data import get_prices_from_all_exchanges
from token_selector import generate_top_token_list
from optimized_ai_core import optimized_ai_core
from trade_executor import execute_trade
from price_fetcher import get_price
from error_codes import error_response
from cache import get_cached_data, set_cached_data
from api_optimization_manager import api_optimizer

logger = logging.getLogger(__name__)

class EnhancedArbitrageEngine:
    """
    Enhanced arbitrage engine that combines:
    1. TokenMetrics AI signals
    2. Internal AI analysis
    3. Sentiment analysis
    4. Multi-exchange arbitrage opportunities
    5. Position optimization
    """
    
    def __init__(self):
        self.tokenmetrics_api = TokenMetricsAPI()
        self.min_arbitrage_threshold = 0.005  # 0.5%
        self.min_volume_usd = 10000
        self.max_position_size = 1000  # USD
        self.min_confidence_score = 0.7
        
    async def find_enhanced_arbitrage_opportunities(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Find arbitrage opportunities enhanced with AI signals and sentiment analysis
        """
        logger.info(f"Starting enhanced arbitrage analysis for {limit} tokens")
        
        try:
            # Get top tokens for analysis
            top_tokens = generate_top_token_list(limit=limit)
            if not top_tokens:
                logger.warning("No tokens found for arbitrage analysis")
                return []
            
            # Filter tokens using API optimizer to avoid unsupported tokens
            token_symbols = []
            for token in top_tokens:
                symbol = token.get("symbol")
                if symbol and isinstance(symbol, str):
                    token_symbols.append(symbol)
            
            supported_symbols = api_optimizer.filter_supported_tokens(token_symbols)
            
            # Filter original token list to only include supported tokens
            supported_tokens = [
                token for token in top_tokens 
                if token.get("symbol") is not None and token.get("symbol") in supported_symbols
            ]
            
            logger.info(f"Filtered {len(top_tokens)} tokens down to {len(supported_tokens)} supported tokens for arbitrage analysis")
            
            if not supported_tokens:
                logger.warning("No supported tokens found for arbitrage analysis")
                return []
            
            opportunities = []
            
            # Process tokens in smaller batches for efficiency
            batch_size = api_optimizer.get_batch_size('coingecko')
            for i in range(0, len(supported_tokens), batch_size):
                batch = supported_tokens[i:i + batch_size]
                batch_opportunities = await self._process_token_batch(batch)
                opportunities.extend(batch_opportunities)
                
                # Use optimal delay from API optimizer
                optimal_delay = api_optimizer.get_optimal_delay('coingecko')
                await asyncio.sleep(optimal_delay)
            
            # Sort by potential profit and confidence
            opportunities.sort(key=lambda x: x.get('total_score', 0), reverse=True)
            
            logger.info(f"Found {len(opportunities)} enhanced arbitrage opportunities")
            return opportunities[:10]  # Return top 10 opportunities
            
        except Exception as e:
            logger.error(f"Error in enhanced arbitrage analysis: {e}")
            return []
    
    async def _process_token_batch(self, tokens: List[Dict]) -> List[Dict[str, Any]]:
        """Process a batch of tokens for arbitrage opportunities"""
        batch_opportunities = []
        
        for token_info in tokens:
            symbol = token_info.get("symbol")
            if not symbol:
                continue
                
            try:
                opportunity = await self._analyze_token_opportunity(symbol, token_info)
                if opportunity:
                    batch_opportunities.append(opportunity)
            except Exception as e:
                logger.warning(f"Error analyzing {symbol}: {e}")
                continue
        
        return batch_opportunities
    
    async def _analyze_token_opportunity(self, symbol: str, token_info: Dict) -> Optional[Dict[str, Any]]:
        """Comprehensive analysis of a single token for arbitrage opportunity"""
        
        # 1. Get basic arbitrage data
        arbitrage_data = await self._get_basic_arbitrage_data(symbol)
        if not arbitrage_data:
            return None
        
        # 2. Get TokenMetrics AI signals (pass arbitrage prices if available)
        current_prices = arbitrage_data.get('all_prices') if arbitrage_data else None
        tokenmetrics_signals = await self._get_tokenmetrics_signals(symbol, current_prices)
        
        # 3. Get internal AI analysis
        ai_analysis = await self._get_internal_ai_analysis(symbol)
        
        # 4. Get sentiment analysis
        sentiment_data = await self._get_sentiment_analysis(symbol)
        
        # 5. Calculate composite score
        composite_score = self._calculate_composite_score(
            arbitrage_data, tokenmetrics_signals, ai_analysis, sentiment_data
        )
        
        # 6. Only return if meets minimum criteria
        if composite_score['total_score'] < self.min_confidence_score:
            return None
        
        # 7. Calculate optimal position size
        optimal_position = self._calculate_optimal_position(arbitrage_data, composite_score)
        
        return {
            "symbol": symbol,
            "arbitrage_data": arbitrage_data,
            "tokenmetrics_signals": tokenmetrics_signals,
            "ai_analysis": ai_analysis,
            "sentiment_data": sentiment_data,
            "composite_score": composite_score,
            "optimal_position": optimal_position,
            "total_score": composite_score['total_score'],
            "recommendation": self._generate_recommendation(composite_score),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def _get_basic_arbitrage_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get basic arbitrage price differences across exchanges"""
        try:
            prices = await get_prices_from_all_exchanges(symbol)
            if not prices or len(prices) < 2:
                return None
            
            # Filter valid prices
            valid_prices = {
                exch: price for exch, price in prices.items() 
                if isinstance(price, (int, float)) and price > 0
            }
            
            if len(valid_prices) < 2:
                return None
            
            min_price_exchange = min(valid_prices, key=lambda k: valid_prices[k])
            max_price_exchange = max(valid_prices, key=lambda k: valid_prices[k])
            
            min_price = valid_prices[min_price_exchange]
            max_price = valid_prices[max_price_exchange]
            
            price_diff = max_price - min_price
            percentage_diff = (price_diff / min_price) if min_price > 0 else 0
            
            if percentage_diff < self.min_arbitrage_threshold:
                return None
            
            return {
                "buy_exchange": min_price_exchange,
                "buy_price": min_price,
                "sell_exchange": max_price_exchange,
                "sell_price": max_price,
                "price_difference": price_diff,
                "percentage_difference": percentage_diff,
                "all_prices": valid_prices
            }
            
        except Exception as e:
            logger.warning(f"Error getting arbitrage data for {symbol}: {e}")
            return None
    
    async def _get_tokenmetrics_signals(self, symbol: str, current_prices: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """Get comprehensive TokenMetrics analysis for trading signals"""
        try:
            logger.info(f"Getting TokenMetrics analysis for {symbol}")
            
            # Get regular comprehensive analysis from TokenMetrics
            analysis = self.tokenmetrics_api.get_comprehensive_analysis(symbol.replace("-USDT", ""))
            
            if not analysis.get("available"):
                logger.warning(f"TokenMetrics data not available for {symbol}")
                return {"available": False, "signal": "NEUTRAL", "confidence": 0.0}
            
            # Extract signal data from available analysis
            combined_signal = analysis.get("combined_signal", "NEUTRAL")
            combined_confidence = analysis.get("confidence", 0.0)
            price_analysis = analysis.get("price_analysis", {})
            
            # Create arbitrage-aware analysis using price data if available
            arbitrage_context = {}
            if current_prices and len(current_prices) >= 2:
                prices = list(current_prices.values())
                min_price = min(prices)
                max_price = max(prices)
                spread_percentage = ((max_price - min_price) / min_price) * 100 if min_price > 0 else 0
                
                arbitrage_context = {
                    "spread_percentage": spread_percentage,
                    "profitable": spread_percentage > 1.0,  # Basic profitability check
                    "min_exchange": list(current_prices.keys())[prices.index(min_price)],
                    "max_exchange": list(current_prices.keys())[prices.index(max_price)],
                }
            
            signal_data = {
                "available": True,
                "signal": combined_signal,
                "confidence": combined_confidence,
                "price_target": price_analysis.get("current_price"),
                "risk_level": "MEDIUM",  # Default risk level
                "arbitrage_context": arbitrage_context,
                "price_analysis": price_analysis,
                "token_id": analysis.get("token_id"),
                "raw_data": analysis
            }
            
            return signal_data
            
        except Exception as e:
            logger.warning(f"Error getting TokenMetrics analysis for {symbol}: {e}")
            return {"available": False, "signal": "NEUTRAL", "confidence": 0.0}
    
    async def _get_internal_ai_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get internal AI analysis using optimized AI core"""
        try:
            analysis = await optimized_ai_core.analyze_token_optimized(symbol)
            
            if not analysis:
                return {"available": False, "decision": "HOLD", "confidence": 0.0}
            
            return {
                "available": True,
                "decision": analysis.decision,
                "confidence": analysis.confidence,
                "reason": analysis.reason,
                "sentiment_score": analysis.sentiment_score,
                "processing_time": analysis.processing_time
            }
            
        except Exception as e:
            logger.warning(f"Error getting internal AI analysis for {symbol}: {e}")
            return {"available": False, "decision": "HOLD", "confidence": 0.0}
    
    async def _get_sentiment_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get simplified sentiment analysis"""
        try:
            # Simplified sentiment analysis - can be enhanced later
            # For now, return neutral sentiment to avoid blocking the system
            return {
                "available": True,
                "sentiment_score": 0.0,  # Neutral sentiment
                "confidence": 0.5,
                "source_count": 0,
                "weighted_score": 0.0,
                "sources": []
            }
            
        except Exception as e:
            logger.warning(f"Error getting sentiment analysis for {symbol}: {e}")
            return {"available": False, "sentiment_score": 0.0, "confidence": 0.0}
    
    def _calculate_composite_score(self, arbitrage_data: Dict, tokenmetrics: Dict, 
                                 ai_analysis: Dict, sentiment: Dict) -> Dict[str, Any]:
        """Calculate composite score from all analysis components"""
        
        # Base arbitrage score (0-1)
        arbitrage_score = min(arbitrage_data.get('percentage_difference', 0) * 20, 1.0)
        
        # TokenMetrics score (0-1)
        tm_signal = tokenmetrics.get('signal', 'NEUTRAL')
        tm_confidence = tokenmetrics.get('confidence', 0.0)
        
        if tm_signal == 'BUY':
            tm_score = tm_confidence
        elif tm_signal == 'SELL':
            tm_score = -tm_confidence
        else:
            tm_score = 0.0
        
        # Internal AI score (0-1)
        ai_decision = ai_analysis.get('decision', 'HOLD')
        ai_confidence = ai_analysis.get('confidence', 0.0)
        
        if ai_decision == 'BUY':
            ai_score = ai_confidence
        elif ai_decision == 'SELL':
            ai_score = -ai_confidence
        else:
            ai_score = 0.0
        
        # Sentiment score (-1 to 1)
        sentiment_score = sentiment.get('sentiment_score', 0.0)
        sentiment_confidence = sentiment.get('confidence', 0.0)
        weighted_sentiment = sentiment_score * sentiment_confidence
        
        # Composite calculation with weights
        weights = {
            'arbitrage': 0.3,
            'tokenmetrics': 0.25,
            'ai_analysis': 0.25,
            'sentiment': 0.2
        }
        
        total_score = (
            arbitrage_score * weights['arbitrage'] +
            max(0, tm_score) * weights['tokenmetrics'] +
            max(0, ai_score) * weights['ai_analysis'] +
            max(0, weighted_sentiment) * weights['sentiment']
        )
        
        return {
            'arbitrage_score': arbitrage_score,
            'tokenmetrics_score': tm_score,
            'ai_score': ai_score,
            'sentiment_score': weighted_sentiment,
            'total_score': total_score,
            'weights': weights
        }
    
    def _calculate_optimal_position(self, arbitrage_data: Dict, composite_score: Dict) -> Dict[str, Any]:
        """Calculate optimal position size based on opportunity and risk"""
        
        percentage_diff = arbitrage_data.get('percentage_difference', 0)
        total_score = composite_score.get('total_score', 0)
        
        # Base position size
        base_position = min(self.max_position_size, 500)
        
        # Adjust based on arbitrage percentage
        arbitrage_multiplier = min(percentage_diff * 100, 2.0)  # Cap at 2x
        
        # Adjust based on confidence
        confidence_multiplier = total_score
        
        optimal_size = base_position * arbitrage_multiplier * confidence_multiplier
        optimal_size = min(optimal_size, self.max_position_size)
        optimal_size = max(optimal_size, 50)  # Minimum position
        
        return {
            'position_size_usd': round(optimal_size, 2),
            'base_position': base_position,
            'arbitrage_multiplier': arbitrage_multiplier,
            'confidence_multiplier': confidence_multiplier,
            'max_risk_pct': min(percentage_diff * 0.5, 0.02)  # Max 2% risk
        }
    
    def _generate_recommendation(self, composite_score: Dict) -> Dict[str, Any]:
        """Generate trading recommendation based on composite score"""
        
        total_score = composite_score.get('total_score', 0)
        
        if total_score >= 0.8:
            action = "STRONG_BUY"
            urgency = "HIGH"
        elif total_score >= 0.6:
            action = "BUY"
            urgency = "MEDIUM"
        elif total_score >= 0.4:
            action = "WEAK_BUY"
            urgency = "LOW"
        else:
            action = "HOLD"
            urgency = "NONE"
        
        return {
            'action': action,
            'urgency': urgency,
            'confidence': total_score,
            'reasoning': self._generate_reasoning(composite_score)
        }
    
    def _generate_reasoning(self, composite_score: Dict) -> str:
        """Generate human-readable reasoning for the recommendation"""
        
        reasons = []
        
        if composite_score.get('arbitrage_score', 0) > 0.5:
            reasons.append("Strong arbitrage opportunity detected")
        
        if composite_score.get('tokenmetrics_score', 0) > 0.5:
            reasons.append("TokenMetrics AI signals bullish")
        
        if composite_score.get('ai_score', 0) > 0.5:
            reasons.append("Internal AI analysis positive")
        
        if composite_score.get('sentiment_score', 0) > 0.3:
            reasons.append("Positive market sentiment")
        
        if not reasons:
            reasons.append("Mixed signals, proceed with caution")
        
        return "; ".join(reasons)

    async def execute_arbitrage_trade(self, opportunity: Dict[str, Any]) -> Dict[str, Any]:
        """Execute an arbitrage trade based on the opportunity analysis"""
        
        try:
            symbol = opportunity['symbol']
            arbitrage_data = opportunity['arbitrage_data']
            optimal_position = opportunity['optimal_position']
            recommendation = opportunity['recommendation']
            
            if recommendation['action'] not in ['BUY', 'STRONG_BUY']:
                return {
                    'success': False,
                    'message': f"Recommendation is {recommendation['action']}, not executing trade"
                }
            
            # Execute buy order on the cheaper exchange
            buy_exchange = arbitrage_data['buy_exchange']
            buy_price = arbitrage_data['buy_price']
            position_size = optimal_position['position_size_usd']
            
            trade_result = execute_trade(
                token_symbol=symbol,
                side="BUY",
                amount_usd=position_size,
                price=buy_price,
                strategy="Enhanced_Arbitrage",
                reason=f"AI-Enhanced Arbitrage: {recommendation['reasoning']}"
            )
            
            if trade_result.get('success'):
                logger.info(f"Successfully executed enhanced arbitrage trade for {symbol}")
                return {
                    'success': True,
                    'trade_result': trade_result,
                    'opportunity': opportunity
                }
            else:
                logger.warning(f"Failed to execute arbitrage trade for {symbol}: {trade_result.get('message')}")
                return {
                    'success': False,
                    'message': trade_result.get('message', 'Trade execution failed')
                }
                
        except Exception as e:
            logger.error(f"Error executing arbitrage trade: {e}")
            return {
                'success': False,
                'message': f"Trade execution error: {str(e)}"
            }

# Global instance
enhanced_arbitrage_engine = EnhancedArbitrageEngine()

# API functions
async def get_enhanced_arbitrage_opportunities(limit: int = 20) -> List[Dict[str, Any]]:
    """Get enhanced arbitrage opportunities with AI analysis"""
    return await enhanced_arbitrage_engine.find_enhanced_arbitrage_opportunities(limit)

async def execute_enhanced_arbitrage_trade(opportunity: Dict[str, Any]) -> Dict[str, Any]:
    """Execute an enhanced arbitrage trade"""
    return await enhanced_arbitrage_engine.execute_arbitrage_trade(opportunity)

def get_arbitrage_performance_stats() -> Dict[str, Any]:
    """Get performance statistics for the arbitrage engine"""
    return {
        "engine_status": "active",
        "min_arbitrage_threshold": enhanced_arbitrage_engine.min_arbitrage_threshold,
        "max_position_size": enhanced_arbitrage_engine.max_position_size,
        "min_confidence_score": enhanced_arbitrage_engine.min_confidence_score,
        "last_analysis": datetime.utcnow().isoformat()
    }
