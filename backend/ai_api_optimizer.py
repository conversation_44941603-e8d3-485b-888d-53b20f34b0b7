#!/usr/bin/env python3
"""
🤖 AI-POWERED API USAGE OPTIMIZER
Intelligent system that analyzes API usage patterns and optimizes call frequency
"""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)

class AIAPIOptimizer:
    """AI-powered API usage optimizer that learns and adapts."""
    
    def __init__(self):
        self.usage_history = defaultdict(deque)  # Service -> deque of timestamps
        self.response_times = defaultdict(deque)  # Service -> deque of response times
        self.error_rates = defaultdict(deque)  # Service -> deque of error indicators
        self.data_freshness = defaultdict(float)  # Service -> last data change timestamp
        
        # Current optimized intervals (starts with defaults)
        self.optimized_intervals = {
            "cost-monitoring": 45,  # Increased from 30s
            "news/live": 60,        # Increased from 30s  
            "tokens": 90,           # Increased from 60s
            "discover": 120,        # Increased from 45s
            "analytics": 300,       # Keep at 5 minutes
            "pnl": 30,             # Keep frequent for trading
            "trades/live": 15,     # Keep frequent for trading
            "portfolio": 20,       # Keep frequent for trading
        }
        
        # AI analysis thresholds
        self.analysis_window = 3600  # 1 hour analysis window
        self.min_calls_for_analysis = 10  # Minimum calls needed for AI analysis
        
        # Optimization rules
        self.optimization_rules = {
            "high_frequency_threshold": 0.5,  # Calls per minute threshold
            "low_change_threshold": 0.1,      # Data change rate threshold
            "error_rate_threshold": 0.2,      # Error rate threshold
            "response_time_threshold": 5.0,   # Response time threshold (seconds)
        }
    
    def record_api_call(self, endpoint: str, response_time: float = 0.0, 
                       had_error: bool = False, data_changed: bool = True):
        """Record API call for analysis."""
        current_time = time.time()
        
        # Record call timestamp
        self.usage_history[endpoint].append(current_time)
        
        # Record response time
        if response_time > 0:
            self.response_times[endpoint].append(response_time)
        
        # Record error
        self.error_rates[endpoint].append(1 if had_error else 0)
        
        # Record data freshness
        if data_changed:
            self.data_freshness[endpoint] = current_time
        
        # Keep only recent data (last hour)
        cutoff_time = current_time - self.analysis_window
        
        # Clean old data
        while (self.usage_history[endpoint] and 
               self.usage_history[endpoint][0] < cutoff_time):
            self.usage_history[endpoint].popleft()
        
        while (self.response_times[endpoint] and 
               len(self.response_times[endpoint]) > 100):  # Keep last 100 response times
            self.response_times[endpoint].popleft()
        
        while (self.error_rates[endpoint] and 
               len(self.error_rates[endpoint]) > 100):  # Keep last 100 error rates
            self.error_rates[endpoint].popleft()
    
    def analyze_usage_pattern(self, endpoint: str) -> Dict[str, Any]:
        """Analyze usage pattern for an endpoint using AI logic."""
        if len(self.usage_history[endpoint]) < self.min_calls_for_analysis:
            return {"status": "insufficient_data", "recommendation": "keep_current"}
        
        current_time = time.time()
        calls = list(self.usage_history[endpoint])
        
        # Calculate metrics
        call_frequency = len(calls) / (self.analysis_window / 60)  # Calls per minute
        
        # Calculate data change frequency
        last_data_change = self.data_freshness.get(endpoint, current_time - 3600)
        time_since_change = (current_time - last_data_change) / 60  # Minutes
        data_change_rate = 1 / max(time_since_change, 1)  # Changes per minute
        
        # Calculate average response time
        avg_response_time = (
            statistics.mean(self.response_times[endpoint]) 
            if self.response_times[endpoint] else 1.0
        )
        
        # Calculate error rate
        error_rate = (
            statistics.mean(self.error_rates[endpoint]) 
            if self.error_rates[endpoint] else 0.0
        )
        
        # AI Decision Logic
        analysis = {
            "call_frequency": call_frequency,
            "data_change_rate": data_change_rate,
            "avg_response_time": avg_response_time,
            "error_rate": error_rate,
            "current_interval": self.optimized_intervals.get(endpoint, 60)
        }
        
        # AI Optimization Decision
        recommendation = self._ai_optimization_decision(analysis)
        
        return {
            "status": "analyzed",
            "metrics": analysis,
            "recommendation": recommendation["action"],
            "new_interval": recommendation["interval"],
            "reason": recommendation["reason"]
        }
    
    def _ai_optimization_decision(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """AI logic to decide optimization strategy."""
        current_interval = metrics["current_interval"]
        call_frequency = metrics["call_frequency"]
        data_change_rate = metrics["data_change_rate"]
        error_rate = metrics["error_rate"]
        response_time = metrics["avg_response_time"]
        
        # AI Decision Tree
        
        # 1. High error rate -> Increase interval significantly
        if error_rate > self.optimization_rules["error_rate_threshold"]:
            new_interval = min(current_interval * 2, 300)  # Max 5 minutes
            return {
                "action": "increase_significantly",
                "interval": new_interval,
                "reason": f"High error rate ({error_rate:.1%}) detected, reducing call frequency"
            }
        
        # 2. Slow response times -> Increase interval moderately
        if response_time > self.optimization_rules["response_time_threshold"]:
            new_interval = min(current_interval * 1.5, 180)  # Max 3 minutes
            return {
                "action": "increase_moderately",
                "interval": new_interval,
                "reason": f"Slow response times ({response_time:.1f}s) detected"
            }
        
        # 3. High frequency but low data changes -> Increase interval
        if (call_frequency > self.optimization_rules["high_frequency_threshold"] and 
            data_change_rate < self.optimization_rules["low_change_threshold"]):
            new_interval = min(current_interval * 1.3, 120)  # Max 2 minutes
            return {
                "action": "increase_moderate",
                "interval": new_interval,
                "reason": f"High call frequency ({call_frequency:.1f}/min) but low data changes ({data_change_rate:.3f}/min)"
            }
        
        # 4. Very low call frequency and high data changes -> Decrease interval
        if (call_frequency < 0.2 and data_change_rate > 0.5):
            new_interval = max(current_interval * 0.8, 15)  # Min 15 seconds
            return {
                "action": "decrease_moderate",
                "interval": new_interval,
                "reason": f"Low call frequency ({call_frequency:.1f}/min) but high data changes ({data_change_rate:.1f}/min)"
            }
        
        # 5. Optimal performance -> Keep current or slight adjustment
        if error_rate < 0.05 and response_time < 2.0:
            # Fine-tune based on data freshness
            if data_change_rate < 0.05:  # Very low change rate
                new_interval = min(current_interval * 1.1, 90)
                return {
                    "action": "fine_tune_increase",
                    "interval": new_interval,
                    "reason": "Optimal performance, slight increase due to low data changes"
                }
            else:
                return {
                    "action": "keep_current",
                    "interval": current_interval,
                    "reason": "Optimal performance metrics"
                }
        
        # Default: Keep current
        return {
            "action": "keep_current",
            "interval": current_interval,
            "reason": "No clear optimization signal"
        }
    
    def get_optimized_interval(self, endpoint: str) -> int:
        """Get the current optimized interval for an endpoint."""
        return self.optimized_intervals.get(endpoint, 60)
    
    def update_interval(self, endpoint: str, new_interval: int, reason: str = ""):
        """Update the optimized interval for an endpoint."""
        old_interval = self.optimized_intervals.get(endpoint, 60)
        self.optimized_intervals[endpoint] = new_interval
        
        logger.info(f"🤖 AI Optimizer: {endpoint} interval {old_interval}s → {new_interval}s ({reason})")
    
    def run_optimization_cycle(self) -> Dict[str, Any]:
        """Run a full optimization cycle on all tracked endpoints."""
        results = {}
        total_optimizations = 0
        
        for endpoint in self.usage_history.keys():
            analysis = self.analyze_usage_pattern(endpoint)
            results[endpoint] = analysis
            
            if analysis["recommendation"] != "keep_current":
                self.update_interval(
                    endpoint, 
                    analysis["new_interval"], 
                    analysis["reason"]
                )
                total_optimizations += 1
        
        return {
            "timestamp": datetime.now().isoformat(),
            "endpoints_analyzed": len(results),
            "optimizations_made": total_optimizations,
            "results": results,
            "current_intervals": dict(self.optimized_intervals)
        }
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of current optimization status."""
        summary = {
            "total_endpoints": len(self.optimized_intervals),
            "current_intervals": dict(self.optimized_intervals),
            "recent_activity": {},
            "recommendations": []
        }
        
        # Add recent activity for each endpoint
        current_time = time.time()
        for endpoint, calls in self.usage_history.items():
            if calls:
                recent_calls = len([c for c in calls if c > current_time - 300])  # Last 5 minutes
                summary["recent_activity"][endpoint] = {
                    "calls_last_5min": recent_calls,
                    "total_calls_tracked": len(calls),
                    "current_interval": self.optimized_intervals.get(endpoint, 60)
                }
        
        return summary


# Global optimizer instance
api_optimizer = AIAPIOptimizer()

def record_endpoint_call(endpoint: str, response_time: float = 0.0, 
                        had_error: bool = False, data_changed: bool = True):
    """Record an API endpoint call for optimization analysis."""
    api_optimizer.record_api_call(endpoint, response_time, had_error, data_changed)

def get_optimal_interval(endpoint: str) -> int:
    """Get the AI-optimized interval for an endpoint."""
    return api_optimizer.get_optimized_interval(endpoint)

def run_ai_optimization() -> Dict[str, Any]:
    """Run AI optimization cycle."""
    return api_optimizer.run_optimization_cycle()

def get_optimization_status() -> Dict[str, Any]:
    """Get current optimization status."""
    return api_optimizer.get_optimization_summary()

if __name__ == "__main__":
    # Test the AI optimizer
    print("🤖 TESTING AI API OPTIMIZER")
    print("=" * 50)
    
    # Simulate some API calls
    for i in range(20):
        record_endpoint_call("cost-monitoring", response_time=1.5, data_changed=(i % 5 == 0))
        record_endpoint_call("news/live", response_time=2.0, had_error=(i % 10 == 0))
        time.sleep(0.1)  # Small delay for simulation
    
    # Run optimization
    results = run_ai_optimization()
    
    print(f"Endpoints analyzed: {results['endpoints_analyzed']}")
    print(f"Optimizations made: {results['optimizations_made']}")
    
    for endpoint, analysis in results['results'].items():
        if analysis['recommendation'] != 'keep_current':
            print(f"✅ {endpoint}: {analysis['recommendation']} → {analysis['new_interval']}s")
            print(f"   Reason: {analysis['reason']}")
    
    print("\n🤖 AI API optimizer working correctly!")
