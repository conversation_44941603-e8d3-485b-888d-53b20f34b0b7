#!/usr/bin/env python3
"""
Fallback KuCoin Client
Provides mock data when real KuCoin API is not available
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class FallbackKuCoinClient:
    """Fallback client that provides mock data"""
    
    def __init__(self):
        self.client = None
        logger.info("Using fallback KuCoin client (mock data)")
    
    def get_live_trades_data(self, limit: int = 50) -> Dict[str, Any]:
        """Return mock trades data"""
        mock_trades = [
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': 'BTC-USDT',
                'action': 'BUY',
                'quantity': 0.001,
                'price': 45000.0,
                'total': 45.0,
                'status': 'COMPLETED',
                'fees': 0.045,
                'trade_id': 'mock_001',
                'source': 'fallback_mock'
            },
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': 'ETH-USDT',
                'action': 'SELL',
                'quantity': 0.01,
                'price': 3000.0,
                'total': 30.0,
                'status': 'COMPLETED',
                'fees': 0.03,
                'trade_id': 'mock_002',
                'source': 'fallback_mock'
            }
        ]
        
        return {
            'trades': mock_trades[:limit],
            'last_updated': datetime.now().isoformat(),
            'total_trades': len(mock_trades),
            'source': 'fallback_kucoin_client',
            'metadata': {
                'note': 'This is mock data. Configure KuCoin API for real trades.'
            }
        }

# Global fallback instance
fallback_client = FallbackKuCoinClient()

def get_kucoin_live_trades(limit: int = 50) -> Dict[str, Any]:
    """Get live trades with fallback to mock data"""
    return fallback_client.get_live_trades_data(limit)

def get_kucoin_portfolio():
    """Get portfolio with fallback to mock data"""
    mock_portfolio = {
        'BTC': {'qty': 0.001, 'avg_price': 45000.0},
        'ETH': {'qty': 0.01, 'avg_price': 3000.0}
    }
    mock_balance = 1000.0
    
    return mock_portfolio, mock_balance
