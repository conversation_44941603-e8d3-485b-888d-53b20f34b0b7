import csv
import json
import os
import logging
import time
import asyncio
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback

from config import (
    DEFAULT_TRADE_USD,
    MAX_TRADE_RISK_PCT,
    STOP_LOSS_PCT,
    VOLATILITY_MULTIPLIER,
    TAKE_PROFIT_USD,
    BASE_ORDER_SIZE_USDT,
)
from real_time_metrics import (
    real_time_metrics,
    update_trade_counts,
    update_profit_loss,
    update_portfolio_value,
)

# Enhanced configuration for high-frequency trading
BATCH_SIZE = 10  # Process trades in batches
MAX_CONCURRENT_TRADES = 5  # Maximum concurrent trade executions
TRADE_TIMEOUT = 30  # Timeout for individual trades
POSITION_CACHE_TTL = 60  # Cache position data for 1 minute
RISK_CHECK_INTERVAL = 10  # Check risk every 10 trades

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

TRADE_LOG_PATH = "backend/data/trade_logs.csv"
PNL_REPORT_CSV = "backend/data/pnl_report.csv"
PNL_REPORT_JSON = "backend/data/pnl_report.json"
PORTFOLIO_PATH = "backend/data/portfolio_state.json"

# Enhanced caching and state management
_portfolio_cache = {"data": None, "timestamp": 0}
_position_cache = {}
_trade_queue = []
_active_trades = set()
_trade_counter = 0


class EnhancedPositionManager:
    """Enhanced position manager for high-frequency trading."""

    def __init__(self):
        self.positions = {}
        self.balance = 1000.0
        self.last_update = 0
        self.risk_metrics = {
            "total_exposure": 0.0,
            "daily_trades": 0,
            "daily_pnl": 0.0,
            "max_drawdown": 0.0,
        }

    def get_position(self, symbol: str) -> Dict[str, Any]:
        """Get position data with caching."""
        cache_key = f"pos_{symbol}"
        current_time = time.time()

        if (
            cache_key in _position_cache
            and current_time - _position_cache[cache_key]["timestamp"]
            < POSITION_CACHE_TTL
        ):
            return _position_cache[cache_key]["data"]

        # Load from portfolio
        portfolio, balance = load_portfolio()
        position_data = {
            "quantity": portfolio.get(symbol, 0),
            "symbol": symbol,
            "last_update": current_time,
        }

        _position_cache[cache_key] = {"data": position_data, "timestamp": current_time}

        return position_data

    def update_position(
        self, symbol: str, quantity_change: float, price: float, side: str
    ):
        """Update position with optimized caching."""
        current_position = self.get_position(symbol)
        new_quantity = current_position["quantity"]

        if side.upper() == "BUY":
            new_quantity += quantity_change
            self.balance -= quantity_change * price
        else:  # SELL
            new_quantity -= quantity_change
            self.balance += quantity_change * price

        # Update cache
        cache_key = f"pos_{symbol}"
        _position_cache[cache_key] = {
            "data": {
                "quantity": new_quantity,
                "symbol": symbol,
                "last_update": time.time(),
            },
            "timestamp": time.time(),
        }

        # Update risk metrics
        self.risk_metrics["total_exposure"] = sum(
            pos["data"]["quantity"] * price
            for pos in _position_cache.values()
            if pos["data"]["quantity"] > 0
        )

    def check_risk_limits(
        self, symbol: str, amount_usd: float, side: str
    ) -> Tuple[bool, str]:
        """Enhanced risk checking with multiple criteria."""
        try:
            # Basic balance check
            if side.upper() == "BUY" and self.balance < amount_usd:
                return (
                    False,
                    f"Insufficient balance: {self.balance:.2f} < {amount_usd:.2f}",
                )

            # Position size limit
            max_position_value = self.balance * MAX_TRADE_RISK_PCT
            if amount_usd > max_position_value:
                return (
                    False,
                    f"Position too large: {amount_usd:.2f} > {max_position_value:.2f}",
                )

            # Daily trade limit
            if self.risk_metrics["daily_trades"] >= 100:  # Increased for high-frequency
                return False, "Daily trade limit reached"

            # Concentration risk
            current_exposure = self.risk_metrics["total_exposure"]
            if (
                side.upper() == "BUY"
                and (current_exposure + amount_usd) > self.balance * 0.8
            ):
                return False, "Portfolio concentration too high"

            return True, "Risk check passed"

        except Exception as e:
            logger.error(f"Risk check failed: {e}")
            return False, f"Risk check error: {str(e)}"


# Global position manager instance
position_manager = EnhancedPositionManager()


# ===============================
# PnL DASHBOARD PRINT & SAVE
# ===============================
def print_pnl_dashboard() -> None:
    """
    Reads trade logs and prints the Profit and Loss dashboard.
    Also saves the PnL report to CSV and JSON files.
    """
    if not os.path.exists(TRADE_LOG_PATH):
        logger.info("No trade log found.")
        return

    token_stats = defaultdict(
        lambda: {"invested": 0.0, "realized": 0.0, "buy_qty": 0.0, "sell_qty": 0.0}
    )

    try:
        with open(TRADE_LOG_PATH, mode="r") as file:
            reader = csv.DictReader(file)
            for row in reader:
                token = row["token"]
                side = row["side"]
                value = float(row["value"])
                qty = float(row["amount"])

                if side == "BUY":
                    token_stats[token]["invested"] += value
                    token_stats[token]["buy_qty"] += qty
                elif side == "SELL":
                    token_stats[token]["realized"] += value
                    token_stats[token]["sell_qty"] += qty

        logger.info("\nP&L DASHBOARD")
        logger.info("------------------------------------------------------------")
        logger.info(f"{'Token':<15}{'Invested':<12}{'Realized':<12}{'P/L %'}")
        logger.info("------------------------------------------------------------")

        csv_data = []
        json_data = []

        for token, stats in token_stats.items():
            invested = stats["invested"]
            realized = stats["realized"]
            pnl_pct = ((realized - invested) / invested) * 100 if invested > 0 else 0.0

            logger.info(f"{token:<15}{invested:<12.2f}{realized:<12.2f}{pnl_pct:.2f}%")

            csv_data.append([token, invested, realized, round(pnl_pct, 2)])
            json_data.append(
                {
                    "token": token,
                    "invested": invested,
                    "realized": realized,
                    "pnl_pct": round(pnl_pct, 2),
                }
            )

        with open(PNL_REPORT_CSV, "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["Token", "Invested", "Realized", "P/L %"])
            writer.writerows(csv_data)

        with open(PNL_REPORT_JSON, "w") as f:
            json.dump(json_data, f, indent=4)

        logger.info("------------------------------------------------------------\n")
        logger.info("PnL reports saved to:")
        logger.info(f" - {PNL_REPORT_CSV}")
        logger.info(f" - {PNL_REPORT_JSON}")

    except Exception as e:
        logger.error(f"Failed to print/save PnL dashboard: {e}")
        logger.error(traceback.format_exc())


# ===============================
# ENHANCED PORTFOLIO LOAD & SAVE WITH CACHING
# ===============================
def load_portfolio() -> tuple[dict, float]:
    """
    Enhanced portfolio loading with intelligent caching for high-frequency access.
    Returns a tuple (portfolio_dict, balance).
    """
    global _portfolio_cache
    current_time = time.time()

    # Check cache first
    if (
        _portfolio_cache["data"] is not None
        and current_time - _portfolio_cache["timestamp"] < POSITION_CACHE_TTL
    ):
        data = _portfolio_cache["data"]
        return data.get("portfolio", {}), data.get("balance", 1000.0)

    try:
        if not os.path.exists(PORTFOLIO_PATH):
            # Starting with empty portfolio and $1000 balance
            default_data = {"portfolio": {}, "balance": 1000.0}
            _portfolio_cache = {"data": default_data, "timestamp": current_time}
            return {}, 1000.0

        with open(PORTFOLIO_PATH, "r") as f:
            data = json.load(f)

        # Update cache
        _portfolio_cache = {"data": data, "timestamp": current_time}

        # Update position manager
        position_manager.balance = data.get("balance", 1000.0)

        return data.get("portfolio", {}), data.get("balance", 1000.0)

    except Exception as e:
        logger.error(f"Failed to load portfolio: {e}")
        logger.error(traceback.format_exc())
        # Return cached data if available, otherwise defaults
        if _portfolio_cache["data"]:
            data = _portfolio_cache["data"]
            return data.get("portfolio", {}), data.get("balance", 1000.0)
        return {}, 1000.0


def save_portfolio(portfolio: dict, balance: float) -> None:
    """
    Enhanced portfolio saving with cache invalidation and atomic writes.
    """
    global _portfolio_cache, _trade_counter

    try:
        os.makedirs(os.path.dirname(PORTFOLIO_PATH), exist_ok=True)
        data = {
            "portfolio": portfolio,
            "balance": balance,
            "last_updated": datetime.now().isoformat(),
            "trade_count": _trade_counter,
        }

        # Atomic write using temporary file
        temp_path = PORTFOLIO_PATH + ".tmp"
        with open(temp_path, "w") as f:
            json.dump(data, f, indent=2)

        # Atomic move
        os.replace(temp_path, PORTFOLIO_PATH)

        # Update cache
        _portfolio_cache = {"data": data, "timestamp": time.time()}

        # Update position manager
        position_manager.balance = balance

        logger.info(
            f"📊 Portfolio saved: {len(portfolio)} positions, balance: ${balance:.2f}"
        )

    except Exception as e:
        logger.error(f"Failed to save portfolio: {e}")
        logger.error(traceback.format_exc())
        # Clean up temp file if it exists
        temp_path = PORTFOLIO_PATH + ".tmp"
        if os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except:
                pass


# ===============================
# LOG TRADE TO CSV
# ===============================
def log_trade(
    token: str,
    side: str,
    amount: float,
    price: float,
    strategy: str = "AI",
    reason: str = "",
) -> None:
    """
    Log a trade to the trade log CSV file.
    """
    try:
        os.makedirs(os.path.dirname(TRADE_LOG_PATH), exist_ok=True)
        file_exists = os.path.isfile(TRADE_LOG_PATH)
        with open(TRADE_LOG_PATH, mode="a", newline="") as file:
            writer = csv.writer(file)
            if not file_exists:
                writer.writerow(
                    [
                        "timestamp",
                        "token",
                        "side",
                        "amount",
                        "price",
                        "value",
                        "strategy",
                        "reason",
                    ]
                )
            writer.writerow(
                [
                    datetime.now().isoformat(),
                    token.upper(),
                    side.upper(),
                    round(amount, 8),
                    round(price, 8),
                    round(amount * price, 2),
                    strategy,
                    reason,
                ]
            )
        logger.info(
            f"Trade logged: {side} {amount:.6f} {token} at ${price:.4f} ({reason})"
        )
    except Exception as e:
        logger.error(f"Failed to log trade: {e}")
        logger.error(traceback.format_exc())


# ===============================
# EXECUTE TRADE FUNCTION (LIVE TRADING)
# ===============================
from typing import Optional
from telegram.ext import Application as TelegramApplication
from telegram_utils import notify_trade
from config import TELEGRAM_BOT_TOKEN


def execute_trade(
    token_symbol: str,
    side: str,
    amount_usd: Optional[float] = None,
    price: Optional[float] = None,
    strategy: str = "AI",
    reason: str = "",
) -> dict:
    """
    Enhanced high-frequency live trade executor with improved performance and risk management.

    Args:
        token_symbol (str): The token symbol to trade.
        side (str): "BUY" or "SELL".
        amount_usd (float, optional): USD amount to trade. Defaults to BASE_ORDER_SIZE_USDT.
        price (float, optional): Price per token.
        strategy (str, optional): Strategy name. Defaults to "AI".
        reason (str, optional): Reason for trade.

    Returns:
        dict: Enhanced trade result with performance metrics.
    """
    global _trade_counter, _active_trades

    start_time = time.time()
    trade_id = f"{token_symbol}_{side}_{int(start_time)}"

    # Check if trade is already in progress
    if trade_id in _active_trades:
        return {
            "success": False,
            "pnl": 0.0,
            "message": f"Trade already in progress for {token_symbol}",
            "trade_id": trade_id,
        }

    _active_trades.add(trade_id)

    try:
        _trade_counter += 1
        token_symbol = token_symbol.upper()
        side = side.upper()

        # Enhanced input validation
        if price is None or price <= 0:
            msg = f"Invalid price for {token_symbol}: {price}"
            logger.warning(msg)
            return {
                "success": False,
                "pnl": 0.0,
                "message": msg,
                "trade_id": trade_id,
                "execution_time": time.time() - start_time,
            }

        if amount_usd is None:
            amount_usd = BASE_ORDER_SIZE_USDT

        # Enhanced risk checking using position manager
        risk_passed, risk_message = position_manager.check_risk_limits(
            token_symbol, amount_usd, side
        )
        if not risk_passed:
            logger.info(f"🚫 Trade rejected: {risk_message}")
            return {
                "success": False,
                "pnl": 0.0,
                "message": risk_message,
                "trade_id": trade_id,
                "execution_time": time.time() - start_time,
            }

        # Load portfolio with caching
        portfolio, balance = load_portfolio()
        amount_token = amount_usd / price
        pnl = 0.0
        realized_pnl = 0.0

        # Execute trade logic with enhanced position tracking
        if side == "BUY":
            if balance < amount_usd:
                msg = f"Insufficient balance: ${balance:.2f} < ${amount_usd:.2f}"
                logger.error(msg)
                return {
                    "success": False,
                    "pnl": 0.0,
                    "message": msg,
                    "trade_id": trade_id,
                    "execution_time": time.time() - start_time,
                }

            balance -= amount_usd
            portfolio[token_symbol] = portfolio.get(token_symbol, 0) + amount_token

            # Update position manager
            position_manager.update_position(token_symbol, amount_token, price, side)

        elif side == "SELL":
            holding = portfolio.get(token_symbol, 0)
            if holding < amount_token:
                msg = f"Insufficient holdings: {holding:.6f} < {amount_token:.6f}"
                logger.error(msg)
                return {
                    "success": False,
                    "pnl": 0.0,
                    "message": msg,
                    "trade_id": trade_id,
                    "execution_time": time.time() - start_time,
                }

            portfolio[token_symbol] = holding - amount_token
            balance += amount_usd

            # Calculate realized PnL (simplified - could be enhanced with FIFO/LIFO)
            realized_pnl = amount_usd  # This is the proceeds from sale
            pnl = realized_pnl

            if portfolio[token_symbol] <= 0:
                del portfolio[token_symbol]

            # Update position manager
            position_manager.update_position(token_symbol, amount_token, price, side)

        else:
            msg = f"Invalid trade side: {side}"
            logger.error(msg)
            return {
                "success": False,
                "pnl": 0.0,
                "message": msg,
                "trade_id": trade_id,
                "execution_time": time.time() - start_time,
            }

        # Enhanced logging and portfolio persistence
        log_trade(token_symbol, side, amount_token, price, strategy, reason)
        save_portfolio(portfolio, balance)

        # Update real-time metrics with enhanced tracking
        update_trade_counts(real_time_metrics)
        update_profit_loss(real_time_metrics, pnl if side == "SELL" else 0.0)

        # Calculate portfolio value more efficiently
        portfolio_value = balance
        for tok, qty in portfolio.items():
            if tok == token_symbol:
                portfolio_value += qty * price  # Use current price for this token
            else:
                portfolio_value += (
                    qty * price
                )  # Simplified - could fetch individual prices

        update_portfolio_value(real_time_metrics, portfolio_value)

        # Update position manager metrics
        position_manager.risk_metrics["daily_trades"] += 1
        position_manager.risk_metrics["daily_pnl"] += pnl

        execution_time = time.time() - start_time
        msg = f"✅ {side} {amount_token:.6f} {token_symbol} @ ${price:.4f} for ${amount_usd:.2f} | PnL: ${pnl:.2f} | Time: {execution_time:.3f}s"
        logger.info(msg)

        # Async Telegram notification (non-blocking)
        try:
            if TELEGRAM_BOT_TOKEN:
                application = (
                    TelegramApplication.builder().token(TELEGRAM_BOT_TOKEN).build()
                )
                # Use asyncio.create_task for non-blocking execution
                asyncio.create_task(
                    notify_trade(
                        application,
                        token_symbol,
                        side,
                        amount_token,
                        price,
                        strategy,
                        reason,
                    )
                )
        except Exception as e:
            logger.debug(f"Telegram notification failed: {e}")  # Reduced to debug level

        return {
            "success": True,
            "pnl": pnl,
            "message": msg,
            "trade_id": trade_id,
            "execution_time": execution_time,
            "portfolio_value": portfolio_value,
            "balance": balance,
            "position_size": portfolio.get(token_symbol, 0),
        }

    except Exception as e:
        logger.error(f"Exception during trade execution: {e}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "pnl": 0.0,
            "message": str(e),
            "trade_id": trade_id,
            "execution_time": time.time() - start_time,
        }

    finally:
        # Always remove from active trades
        _active_trades.discard(trade_id)


def execute_batch_trades(trade_requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Execute multiple trades in parallel for high-frequency trading.

    Args:
        trade_requests: List of trade request dictionaries with keys:
            - token_symbol, side, amount_usd, price, strategy, reason

    Returns:
        List of trade results
    """
    if not trade_requests:
        return []

    logger.info(f"🚀 Executing batch of {len(trade_requests)} trades")
    start_time = time.time()

    # Limit concurrent trades
    batch_size = min(len(trade_requests), MAX_CONCURRENT_TRADES)
    results = []

    # Process in batches to avoid overwhelming the system
    for i in range(0, len(trade_requests), batch_size):
        batch = trade_requests[i : i + batch_size]

        if len(batch) == 1:
            # Single trade - execute directly
            req = batch[0]
            result = execute_trade(
                req.get("token_symbol", ""),
                req.get("side", ""),
                req.get("amount_usd"),
                req.get("price"),
                req.get("strategy", "AI"),
                req.get("reason", ""),
            )
            results.append(result)
        else:
            # Multiple trades - use ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=batch_size) as executor:
                futures = []
                for req in batch:
                    future = executor.submit(
                        execute_trade,
                        req.get("token_symbol", ""),
                        req.get("side", ""),
                        req.get("amount_usd"),
                        req.get("price"),
                        req.get("strategy", "AI"),
                        req.get("reason", ""),
                    )
                    futures.append(future)

                # Collect results with timeout
                for future in as_completed(futures, timeout=TRADE_TIMEOUT):
                    try:
                        result = future.result(timeout=5)
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Batch trade failed: {e}")
                        results.append(
                            {
                                "success": False,
                                "pnl": 0.0,
                                "message": f"Batch execution error: {str(e)}",
                                "execution_time": 0,
                            }
                        )

    execution_time = time.time() - start_time
    successful_trades = sum(1 for r in results if r.get("success", False))
    total_pnl = sum(r.get("pnl", 0) for r in results)

    logger.info(
        f"📊 Batch execution completed: {successful_trades}/{len(trade_requests)} successful | "
        f"Total PnL: ${total_pnl:.2f} | Time: {execution_time:.2f}s"
    )

    return results


def get_trading_performance_metrics() -> Dict[str, Any]:
    """Get current trading performance metrics."""
    return {
        "active_trades": len(_active_trades),
        "trade_counter": _trade_counter,
        "position_cache_size": len(_position_cache),
        "risk_metrics": position_manager.risk_metrics.copy(),
        "balance": position_manager.balance,
        "cache_hit_ratio": len(_portfolio_cache) > 0,
        "timestamp": datetime.now().isoformat(),
    }


def clear_trading_cache():
    """Clear trading caches for fresh start."""
    global _portfolio_cache, _position_cache, _active_trades
    _portfolio_cache = {"data": None, "timestamp": 0}
    _position_cache.clear()
    _active_trades.clear()
    logger.info("🧹 Trading caches cleared")
