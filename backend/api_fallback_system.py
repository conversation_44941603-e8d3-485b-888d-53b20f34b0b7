#!/usr/bin/env python3
"""
🛡️ API Fallback Manager
Handles API failures gracefully with fallback data
"""

import logging
import json
import time
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class APIFallbackManager:
    """Manages API fallbacks and cached data"""
    
    def __init__(self):
        self.fallback_dir = Path("backend/data/fallbacks")
        self.fallback_dir.mkdir(parents=True, exist_ok=True)
        
        # Default fallback data
        self.default_trading_signals = [
            {
                "symbol": "BTC",
                "signal": "HOLD",
                "confidence": 0.7,
                "price_target": 45000,
                "timestamp": time.time()
            },
            {
                "symbol": "ETH", 
                "signal": "BUY",
                "confidence": 0.6,
                "price_target": 2800,
                "timestamp": time.time()
            }
        ]
        
        self.default_sentiment = {
            "overall_sentiment": "NEUTRAL",
            "fear_greed_index": 50,
            "market_trend": "SIDEWAYS",
            "timestamp": time.time()
        }
    
    def get_fallback_trading_signals(self) -> List[Dict[str, Any]]:
        """Get fallback trading signals"""
        try:
            fallback_file = self.fallback_dir / "trading_signals.json"
            
            if fallback_file.exists():
                with open(fallback_file, 'r') as f:
                    data = json.load(f)
                    
                # Check if data is recent (within 1 hour)
                if time.time() - data.get('timestamp', 0) < 3600:
                    return data.get('signals', self.default_trading_signals)
            
            return self.default_trading_signals
            
        except Exception as e:
            logger.error(f"Error loading fallback trading signals: {e}")
            return self.default_trading_signals
    
    def get_fallback_sentiment(self) -> Dict[str, Any]:
        """Get fallback market sentiment"""
        try:
            fallback_file = self.fallback_dir / "sentiment.json"
            
            if fallback_file.exists():
                with open(fallback_file, 'r') as f:
                    data = json.load(f)
                    
                # Check if data is recent (within 30 minutes)
                if time.time() - data.get('timestamp', 0) < 1800:
                    return data
            
            return self.default_sentiment
            
        except Exception as e:
            logger.error(f"Error loading fallback sentiment: {e}")
            return self.default_sentiment
    
    def save_api_data(self, data_type: str, data: Any):
        """Save API data for future fallback use"""
        try:
            fallback_file = self.fallback_dir / f"{data_type}.json"
            
            fallback_data = {
                'data': data,
                'timestamp': time.time()
            }
            
            with open(fallback_file, 'w') as f:
                json.dump(fallback_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving fallback data for {data_type}: {e}")

# Global fallback manager
fallback_manager = APIFallbackManager()
