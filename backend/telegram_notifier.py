import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import logging
import asyncio
from typing import Optional
import datetime

from telegram import Update
from telegram.ext import (
    Application as TelegramApp<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CallbackQueryHandler,
    ContextTypes,
)

from config import TELEGRAM_BOT_TOKEN
from telegram_utils import (
    AppType,
    notify_trade,
    notify_pnl_summary,
    notify_news_alert,
    notify_new_gem_suggestion,
    send_telegram_message,
)
from micro_bot import start_micro_bot, stop_micro_bot, get_micro_bot_status
from live_runner import start_alpha_bot, stop_alpha_bot, get_alpha_bot_status

logging.basicConfig(
    filename="backend/logs/telegram_notifier.log",
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
)
logger = logging.getLogger(__name__)

if TELEGRAM_BOT_TOKEN is None:
    raise ValueError("TELEGRAM_BOT_TOKEN is not set. Please provide a valid token in backend/config.py or your environment.")

async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Respond to /start command."""
    if update.message is not None:
        await update.message.reply_text("🤖 AlphaPredatorBot is online and ready!")

async def micro_start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Starts the micro-bot."""
    if update.message is not None:
        await update.message.reply_text("🚀 Starting micro-bot...")
        asyncio.create_task(start_micro_bot())

async def micro_stop_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Stops the micro-bot."""
    if update.message is not None:
        await update.message.reply_text("🛑 Stopping micro-bot...")
        await stop_micro_bot()

async def micro_status_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Gets the micro-bot's current status."""
    if update.message is not None:
        status = get_micro_bot_status()
        msg = (
            f"*Micro-bot Status:*\n"
            f"*Running:* {status['is_running']}\n"
            f"*Trades Today:* {status['trades_today']}\n"
            f"*PnL Today:* ${status['pnl_today']:.2f}\n"
            f"*Last Trade Day:* {status['last_trade_day'] or 'N/A'}\n"
            f"*Portfolio Size:* {status['portfolio_size']}"
        )
        await update.message.reply_markdown_v2(msg)

async def alpha_start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Starts the alpha-bot."""
    if update.message is not None:
        await update.message.reply_text("🚀 Starting alpha-bot...")
        asyncio.create_task(start_alpha_bot())

async def alpha_stop_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Stops the alpha-bot."""
    if update.message is not None:
        await update.message.reply_text("🛑 Stopping alpha-bot...")
        await stop_alpha_bot()

async def alpha_status_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Gets the alpha-bot's current status."""
    if update.message is not None:
        status = get_alpha_bot_status()
        msg = (
            f"*Alpha-bot Status:*\n"
            f"*Running:* {status['is_running']}\n"
            f"*Last Run:* {status['last_run'] or 'N/A'}\n"
        )
        await update.message.reply_markdown_v2(msg)

async def handle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle approval/rejection button clicks."""
    query = update.callback_query
    if query is None:
        logger.error("CallbackQuery is None in handle_callback.")
        return
    await query.answer()
    data = query.data
    token: Optional[str] = None

    if data is not None:
        if data.startswith("buy_"):
            token = data.replace("buy_", "")
            await query.edit_message_text(f"✅ Approved. Buying `{token}`")
            # Call the micro-bot's trade execution logic directly or via a separate task
            # For now, we'll just log it and let the micro-bot's own loop handle actual trades
            logger.info(f"[Telegram Callback] User approved BUY for {token}")
        elif data.startswith("skip_"):
            token = data.replace("skip_", "")
            await query.edit_message_text(f"❌ Skipped `{token}`")
        elif data.startswith("confirm_"):
            token = data.replace("confirm_", "")
            await query.edit_message_text(f"👍 Confirmed interest in `{token}`", parse_mode="Markdown")
        elif data.startswith("reject_"):
            token = data.replace("reject_", "")
            await query.edit_message_text(f"👎 Rejected trade for `{token}`", parse_mode="Markdown")
        elif data.startswith("trade_"):
            token = data.replace("trade_", "")
            await query.edit_message_text(f"🚀 Executing trade for `{token}`...", parse_mode="Markdown")
            # Call the micro-bot's trade execution logic directly or via a separate task
            logger.info(f"[Telegram Callback] User initiated trade for {token}")

    if token:
        pass # pending_decisions.pop(token, None) # Removed as pending_decisions is not used here anymore

def run_bot() -> None:
    """Build and run the Telegram bot application."""
    application = TelegramApplication.builder().token(TELEGRAM_BOT_TOKEN).build()
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("micro_start", micro_start_command))
    application.add_handler(CommandHandler("micro_stop", micro_stop_command))
    application.add_handler(CommandHandler("micro_status", micro_status_command))
    application.add_handler(CommandHandler("alpha_start", alpha_start_command))
    application.add_handler(CommandHandler("alpha_stop", alpha_stop_command))
    application.add_handler(CommandHandler("alpha_status", alpha_status_command))
    application.add_handler(CallbackQueryHandler(handle_callback))

    # Schedule daily PNL summary
    if application.job_queue:
        application.job_queue.run_daily(notify_pnl_summary, time=datetime.time(hour=9, minute=0))

    def run_all() -> None:
        application.run_polling()

    # Run the main function directly since it's now synchronous
    try:
        run_all()
    except KeyboardInterrupt:
        logger.info("Telegram bot stopped manually.")
    except Exception as e:
        logger.error(f"Telegram bot crashed: {e}", exc_info=True)

async def test_notify() -> None:
    application = TelegramApplication.builder().token(TELEGRAM_BOT_TOKEN).build()
    await notify_trade(
        application=application,
        token="TESTCOIN-USDT",
        action="BUY",
        quantity=500,
        price=0.123,
        strategy="Manual Test",
        reason="This is a test trade signal"
    )

if __name__ == "__main__":
    import sys
    if "test" in sys.argv:
        asyncio.run(test_notify())
    else:
        run_bot()