#!/usr/bin/env python3
"""
📰 TRADING NEWS FILTER
Filter news to show only trading-relevant content
"""

import re
import logging
from typing import List, Dict, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class TradingNewsFilter:
    """Filter news to show only trading-relevant content."""

    def __init__(self):
        # Trading-relevant keywords (positive indicators)
        self.trading_keywords = [
            # Price action
            "price",
            "pump",
            "dump",
            "surge",
            "spike",
            "rally",
            "crash",
            "dip",
            "moon",
            "bull",
            "bear",
            "breakout",
            "resistance",
            "support",
            "trend",
            "volume",
            "volatility",
            # Trading terms
            "buy",
            "sell",
            "trade",
            "trading",
            "signal",
            "analysis",
            "prediction",
            "forecast",
            "profit",
            "loss",
            "gain",
            "portfolio",
            "investment",
            "investor",
            # Market terms
            "market",
            "exchange",
            "listing",
            "delisting",
            "launch",
            "airdrop",
            "staking",
            "liquidity",
            "market cap",
            "mcap",
            "ath",
            "atl",
            "hodl",
            # News events
            "announcement",
            "partnership",
            "acquisition",
            "merger",
            "regulation",
            "sec",
            "adoption",
            "institutional",
            "whale",
            "news",
            "update",
            "upgrade",
            # Crypto specific
            "bitcoin",
            "btc",
            "ethereum",
            "eth",
            "altcoin",
            "defi",
            "nft",
            "dao",
            "blockchain",
            "crypto",
            "cryptocurrency",
            "token",
            "coin",
        ]

        # Non-trading keywords (negative indicators)
        self.non_trading_keywords = [
            # Development/technical
            "github",
            "repository",
            "repo",
            "code",
            "programming",
            "developer",
            "development",
            "commit",
            "pull request",
            "merge",
            "fork",
            "star",
            "issue",
            "bug",
            "fix",
            "documentation",
            "readme",
            "license",
            "api",
            "sdk",
            "library",
            "framework",
            # General tech
            "tutorial",
            "guide",
            "how to",
            "learn",
            "education",
            "course",
            "book",
            "conference",
            "meetup",
            "hackathon",
            "workshop",
            "webinar",
            # Off-topic
            "meme",
            "joke",
            "funny",
            "lol",
            "humor",
            "entertainment",
            "game",
            "gaming",
            "art",
            "music",
            "movie",
            "tv",
            "celebrity",
            "politics",
            "weather",
        ]

        # High-priority trading sources
        self.priority_sources = [
            "coindesk",
            "cointelegraph",
            "coinmarketcap",
            "coingecko",
            "binance",
            "kucoin",
            "kraken",
            "coinbase",
            "bloomberg",
            "reuters",
            "yahoo finance",
            "decrypt",
            "cryptonews",
            "newsbtc",
            "cryptopotato",
            "bitcoinist",
        ]

    def calculate_trading_relevance(self, item: Dict[str, Any]) -> float:
        """Calculate trading relevance score (0-1)."""
        title = item.get("title", "").lower()
        description = item.get("description", "").lower()
        source = item.get("source", "").lower()
        platform = item.get("platform", "").lower()

        # Combine all text for analysis
        text = f"{title} {description}".lower()

        # Start with base score
        score = 0.0

        # Check for trading keywords (positive)
        trading_matches = sum(1 for keyword in self.trading_keywords if keyword in text)
        score += min(trading_matches * 0.1, 0.7)  # Max 0.7 from keywords

        # Penalize non-trading keywords (negative)
        non_trading_matches = sum(
            1 for keyword in self.non_trading_keywords if keyword in text
        )
        score -= min(non_trading_matches * 0.15, 0.5)  # Max -0.5 penalty

        # Boost for priority sources
        if any(priority in source for priority in self.priority_sources):
            score += 0.2

        # Platform-specific adjustments
        if platform == "rss":
            score += 0.3  # RSS feeds from professional crypto news sources
        elif platform == "discord":
            score += 0.1  # Discord news is usually trading-focused
        elif platform == "github":
            score -= 0.3  # GitHub is usually development-focused
        elif platform == "reddit":
            # Reddit can be mixed, check subreddit context
            if any(
                term in text for term in ["trading", "signals", "analysis", "price"]
            ):
                score += 0.1
            else:
                score -= 0.1

        # Time relevance (newer is better)
        try:
            created_at = item.get("created_at", "")
            if created_at:
                # Parse different date formats
                try:
                    if "T" in created_at:
                        created_time = datetime.fromisoformat(
                            created_at.replace("Z", "+00:00")
                        )
                    else:
                        created_time = datetime.fromisoformat(created_at)

                    hours_old = (
                        datetime.now() - created_time.replace(tzinfo=None)
                    ).total_seconds() / 3600

                    if hours_old < 1:
                        score += 0.1  # Very recent
                    elif hours_old < 6:
                        score += 0.05  # Recent
                    elif hours_old > 24:
                        score -= 0.1  # Old news

                except:
                    pass  # Ignore date parsing errors
        except:
            pass

        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, score))

    def filter_trading_news(
        self, news_items: List[Dict[str, Any]], min_relevance: float = 0.3
    ) -> List[Dict[str, Any]]:
        """Filter news items to show only trading-relevant content."""
        filtered_news = []

        for item in news_items:
            relevance = self.calculate_trading_relevance(item)

            if relevance >= min_relevance:
                # Add relevance score to item
                item["trading_relevance"] = relevance
                filtered_news.append(item)

                logger.debug(
                    f"✅ Trading news: {item.get('title', '')[:50]}... (relevance: {relevance:.2f})"
                )
            else:
                logger.debug(
                    f"❌ Filtered out: {item.get('title', '')[:50]}... (relevance: {relevance:.2f})"
                )

        # Sort by relevance and recency
        filtered_news.sort(
            key=lambda x: (x.get("trading_relevance", 0), x.get("created_at", "")),
            reverse=True,
        )

        return filtered_news

    def get_trading_news_summary(
        self, news_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Get summary of trading news."""
        if not news_items:
            return {
                "total_items": 0,
                "avg_relevance": 0.0,
                "top_sources": [],
                "recent_count": 0,
            }

        total_items = len(news_items)
        avg_relevance = (
            sum(item.get("trading_relevance", 0) for item in news_items) / total_items
        )

        # Count sources
        source_counts = {}
        for item in news_items:
            source = item.get("platform", "unknown")
            source_counts[source] = source_counts.get(source, 0) + 1

        top_sources = sorted(source_counts.items(), key=lambda x: x[1], reverse=True)[
            :3
        ]

        # Count recent items (last 6 hours)
        recent_count = 0
        try:
            six_hours_ago = datetime.now() - timedelta(hours=6)
            for item in news_items:
                created_at = item.get("created_at", "")
                if created_at:
                    try:
                        if "T" in created_at:
                            created_time = datetime.fromisoformat(
                                created_at.replace("Z", "+00:00")
                            )
                        else:
                            created_time = datetime.fromisoformat(created_at)

                        if created_time.replace(tzinfo=None) > six_hours_ago:
                            recent_count += 1
                    except:
                        pass
        except:
            pass

        return {
            "total_items": total_items,
            "avg_relevance": round(avg_relevance, 3),
            "top_sources": top_sources,
            "recent_count": recent_count,
        }


# Global instance
trading_filter = TradingNewsFilter()


def filter_for_trading_news(
    news_items: List[Dict[str, Any]], min_relevance: float = 0.3
) -> List[Dict[str, Any]]:
    """Filter news items for trading relevance."""
    return trading_filter.filter_trading_news(news_items, min_relevance)


def get_trading_news_stats(news_items: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Get trading news statistics."""
    return trading_filter.get_trading_news_summary(news_items)


if __name__ == "__main__":
    # Test the filter
    test_news = [
        {
            "title": "Bitcoin price surges to new ATH",
            "description": "BTC breaks resistance",
            "platform": "discord",
        },
        {
            "title": "New Python crypto library released",
            "description": "GitHub repository for developers",
            "platform": "github",
        },
        {
            "title": "Ethereum trading signals bullish",
            "description": "Technical analysis shows buy signal",
            "platform": "reddit",
        },
        {
            "title": "Funny crypto memes compilation",
            "description": "LOL crypto jokes",
            "platform": "reddit",
        },
    ]

    print("📰 TESTING TRADING NEWS FILTER")
    print("=" * 40)

    filtered = filter_for_trading_news(test_news)
    stats = get_trading_news_stats(filtered)

    print(f"Original items: {len(test_news)}")
    print(f"Filtered items: {len(filtered)}")
    print(f"Average relevance: {stats['avg_relevance']}")

    for item in filtered:
        print(f"✅ {item['title']} (relevance: {item['trading_relevance']:.2f})")

    print("\n✅ Trading news filter working correctly!")
