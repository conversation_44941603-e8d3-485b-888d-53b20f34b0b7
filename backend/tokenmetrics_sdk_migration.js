/**
 * 🚀 TOKENMETRICS SDK MIGRATION
 * Replace custom Python client with official TypeScript SDK
 */

const { TokenMetricsClient } = require('tmai-api');
const fs = require('fs');
const path = require('path');

class TokenMetricsSDKWrapper {
    constructor() {
        this.client = new TokenMetricsClient(process.env.TOKENMETRICS_API_KEY);
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Get comprehensive token data using official SDK
     */
    async getTokenData(symbol) {
        try {
            // Check cache first
            const cacheKey = `token_${symbol}`;
            const cached = this.cache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }

            // Use official SDK
            const tokenData = await this.client.tokens.get({
                symbol: symbol,
                include: ['price', 'grade', 'signals', 'metrics']
            });

            // Transform to our expected format
            const transformedData = {
                symbol: symbol,
                price: tokenData.price?.current || 0,
                volume_24h: tokenData.metrics?.volume_24h || 0,
                market_cap: tokenData.metrics?.market_cap || 0,
                price_change_24h: tokenData.price?.change_24h || 0,
                price_change_percentage_24h: tokenData.price?.change_percentage_24h || 0,
                tm_grade: tokenData.grade?.letter || 'N/A',
                tm_score: tokenData.grade?.score || 0,
                volatility: tokenData.metrics?.volatility || 0,
                liquidity_score: tokenData.metrics?.liquidity_score || 0,
                social_sentiment: tokenData.sentiment?.social || 0,
                technical_analysis: tokenData.analysis?.technical || {},
                trading_signal: tokenData.signals?.trading || 'HOLD',
                confidence: tokenData.grade?.confidence || 0.5,
                last_updated: new Date(),
                source: 'tokenmetrics_sdk'
            };

            // Cache the result
            this.cache.set(cacheKey, {
                data: transformedData,
                timestamp: Date.now()
            });

            return transformedData;

        } catch (error) {
            console.error(`❌ TokenMetrics SDK error for ${symbol}:`, error);
            
            // Return fallback data
            return {
                symbol: symbol,
                price: 0,
                volume_24h: 0,
                market_cap: 0,
                price_change_24h: 0,
                price_change_percentage_24h: 0,
                tm_grade: 'N/A',
                tm_score: 50,
                volatility: 0,
                liquidity_score: 0,
                social_sentiment: 0,
                technical_analysis: {},
                trading_signal: 'HOLD',
                confidence: 0.5,
                last_updated: new Date(),
                source: 'tokenmetrics_fallback',
                error: error.message
            };
        }
    }

    /**
     * Get multiple tokens data efficiently
     */
    async getMultipleTokens(symbols) {
        try {
            // Use SDK batch request if available
            if (this.client.tokens.getBatch) {
                const batchData = await this.client.tokens.getBatch({
                    symbols: symbols,
                    include: ['price', 'grade', 'signals', 'metrics']
                });
                
                return symbols.map(symbol => {
                    const tokenData = batchData.find(t => t.symbol === symbol);
                    return this.transformTokenData(tokenData, symbol);
                });
            } else {
                // Fallback to individual requests with rate limiting
                const results = [];
                for (const symbol of symbols) {
                    const data = await this.getTokenData(symbol);
                    results.push(data);
                    
                    // Rate limiting - 200ms between requests
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                return results;
            }

        } catch (error) {
            console.error('❌ TokenMetrics batch request failed:', error);
            return symbols.map(symbol => this.getFallbackData(symbol));
        }
    }

    /**
     * Get trading signals using SDK
     */
    async getTradingSignals(symbols) {
        try {
            const signals = await this.client.tradingSignals.get({
                symbols: symbols,
                timeframe: '1d'
            });

            return signals.map(signal => ({
                symbol: signal.symbol,
                signal: signal.recommendation || 'HOLD',
                confidence: signal.confidence || 0.5,
                reason: signal.reason || 'TokenMetrics analysis',
                timestamp: new Date(),
                source: 'tokenmetrics_sdk'
            }));

        } catch (error) {
            console.error('❌ TokenMetrics trading signals error:', error);
            return symbols.map(symbol => ({
                symbol: symbol,
                signal: 'HOLD',
                confidence: 0.5,
                reason: 'SDK error - using fallback',
                timestamp: new Date(),
                source: 'tokenmetrics_fallback'
            }));
        }
    }

    /**
     * Transform token data to our format
     */
    transformTokenData(tokenData, symbol) {
        if (!tokenData) {
            return this.getFallbackData(symbol);
        }

        return {
            symbol: symbol,
            price: tokenData.price?.current || 0,
            volume_24h: tokenData.metrics?.volume_24h || 0,
            market_cap: tokenData.metrics?.market_cap || 0,
            price_change_24h: tokenData.price?.change_24h || 0,
            price_change_percentage_24h: tokenData.price?.change_percentage_24h || 0,
            tm_grade: tokenData.grade?.letter || 'N/A',
            tm_score: tokenData.grade?.score || 0,
            volatility: tokenData.metrics?.volatility || 0,
            liquidity_score: tokenData.metrics?.liquidity_score || 0,
            social_sentiment: tokenData.sentiment?.social || 0,
            technical_analysis: tokenData.analysis?.technical || {},
            trading_signal: tokenData.signals?.trading || 'HOLD',
            confidence: tokenData.grade?.confidence || 0.5,
            last_updated: new Date(),
            source: 'tokenmetrics_sdk'
        };
    }

    /**
     * Get fallback data when SDK fails
     */
    getFallbackData(symbol) {
        return {
            symbol: symbol,
            price: 0,
            volume_24h: 0,
            market_cap: 0,
            price_change_24h: 0,
            price_change_percentage_24h: 0,
            tm_grade: 'N/A',
            tm_score: 50,
            volatility: 0,
            liquidity_score: 0,
            social_sentiment: 0,
            technical_analysis: {},
            trading_signal: 'HOLD',
            confidence: 0.5,
            last_updated: new Date(),
            source: 'tokenmetrics_fallback'
        };
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get cache stats
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// Export singleton instance
const tokenMetricsSDK = new TokenMetricsSDKWrapper();
module.exports = tokenMetricsSDK;

// Python bridge function for compatibility
if (typeof process !== 'undefined' && process.argv[1] === __filename) {
    // Command line usage for Python integration
    const symbol = process.argv[2];
    if (symbol) {
        tokenMetricsSDK.getTokenData(symbol)
            .then(data => console.log(JSON.stringify(data)))
            .catch(error => console.error(JSON.stringify({ error: error.message })));
    }
}
