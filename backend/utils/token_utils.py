def format_token_name(token: str) -> str:
    """
    Format the token name to a standardized display format.
    For example, convert 'eth-usdt' or 'ETHUSDT' to 'ETH/USDT'.
    """
    token = token.upper()
    if '-' in token:
        parts = token.split('-')
        return f"{parts[0]}/{parts[1]}"
    elif len(token) > 6:
        # Attempt to split token like ETHUSDT into ETH/USDT
        base = token[:-4]
        quote = token[-4:]
        return f"{base}/{quote}"
    else:
        return token
