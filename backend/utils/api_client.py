import requests
import time
import logging
from functools import wraps
from typing import Callable, Any, Dict, Optional
from urllib.parse import urlparse

from cache import get_cached_data, set_cached_data
from config import (
    API_CLIENT_MIN_DELAY,
    API_CLIENT_RETRIES,
    API_CLIENT_BACKOFF_FACTOR,
    API_CLIENT_DEFAULT_CACHE_TTL,
)

logger = logging.getLogger(__name__)

# Import cost tracking
try:
    from real_time_cost_monitor import record_api_usage

    COST_TRACKING_AVAILABLE = True
except ImportError:
    COST_TRACKING_AVAILABLE = False

    def record_api_usage(*args, **kwargs):
        pass


# Simple in-memory store for last request time per host
_last_request_time: Dict[str, float] = {}
# Default minimum delay between requests to the same host (in seconds)
DEFAULT_MIN_DELAY = API_CLIENT_MIN_DELAY


def detect_service_from_url(url: str) -> str:
    """Detect the service name from URL for cost tracking."""
    url_lower = url.lower()

    if "coingecko" in url_lower:
        return "coingecko"
    elif "kucoin" in url_lower:
        return "kucoin"
    elif "tokenmetrics" in url_lower:
        return "tokenmetrics"
    elif "reddit.com" in url_lower:
        return "reddit"
    elif "github.com" in url_lower or "api.github.com" in url_lower:
        return "github"
    elif "cointelegraph" in url_lower:
        return "cointelegraph"
    elif "coindesk" in url_lower:
        return "coindesk"
    elif "decrypt.co" in url_lower:
        return "decrypt"
    elif "cryptonews" in url_lower:
        return "cryptonews"
    elif "newsbtc" in url_lower:
        return "newsbtc"
    elif "cryptopotato" in url_lower:
        return "cryptopotato"
    elif "bitcoinist" in url_lower:
        return "bitcoinist"
    else:
        return "external_api"


def rate_limited_request(
    min_delay: float = API_CLIENT_MIN_DELAY,
    retries: int = API_CLIENT_RETRIES,
    backoff_factor: float = API_CLIENT_BACKOFF_FACTOR,
    cache_ttl: Optional[int] = API_CLIENT_DEFAULT_CACHE_TTL,
) -> Callable:
    """
    Decorator to rate-limit and retry API requests, with optional caching.

    Args:
        min_delay (float): Minimum delay in seconds between requests to the same host.
        retries (int): Number of times to retry the request on failure.
        backoff_factor (float): Factor by which to increase delay between retries.
        cache_ttl (Optional[int]): Time-to-live for cache in seconds. If None, no caching.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            method = func.__name__.upper()  # e.g., 'GET', 'POST'
            url = kwargs.get("url") or (args[0] if args else None)

            if not url:
                raise ValueError(
                    "URL must be provided as a positional or keyword argument."
                )

            parsed_url = urlparse(url)
            host = parsed_url.netloc

            # Caching for GET requests
            cache_key = None
            if method == "GET" and cache_ttl is not None:
                cache_key = f"api_client_cache:{url}:{kwargs}"
                cached_response = get_cached_data(cache_key)
                if cached_response:
                    logger.debug(f"Cache hit for {url}")
                    return cached_response

            # Rate limiting
            current_time = time.time()
            last_time = _last_request_time.get(host, 0)
            elapsed = current_time - last_time

            if elapsed < min_delay:
                sleep_time = min_delay - elapsed
                logger.debug(
                    f"Rate limiting: Sleeping for {sleep_time:.2f}s for {host}"
                )
                time.sleep(sleep_time)

            _last_request_time[host] = (
                time.time()
            )  # Update last request time after potential sleep

            # Retry logic
            for i in range(retries + 1):
                try:
                    # Remove cache_ttl from kwargs before passing to requests methods
                    # as requests library does not recognize it.
                    local_kwargs = kwargs.copy()
                    local_kwargs.pop("cache_ttl", None)

                    response = func(*args, **local_kwargs)
                    response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)

                    # Track successful API call for cost monitoring
                    if COST_TRACKING_AVAILABLE:
                        service = detect_service_from_url(url)
                        endpoint = parsed_url.path or "/"
                        record_api_usage(service=service, endpoint=endpoint)

                    if (
                        method == "GET"
                        and cache_ttl is not None
                        and cache_key is not None
                    ):
                        set_cached_data(cache_key, response, cache_ttl)
                    return response
                except requests.exceptions.RequestException as e:
                    logger.warning(
                        f"Request to {url} failed (attempt {i+1}/{retries+1}): {e}"
                    )
                    if i < retries:
                        sleep_duration = backoff_factor * (2**i)
                        logger.info(f"Retrying in {sleep_duration:.2f} seconds...")
                        time.sleep(sleep_duration)
                    else:
                        logger.error(f"All retry attempts failed for {url}")
                        raise  # Re-raise the last exception if all retries fail

        return wrapper

    return decorator


@rate_limited_request()
def get(url: str, **kwargs) -> requests.Response:
    return requests.get(url, **kwargs)


@rate_limited_request()
def post(url: str, **kwargs) -> requests.Response:
    return requests.post(url, **kwargs)


@rate_limited_request()
def put(url: str, **kwargs) -> requests.Response:
    return requests.put(url, **kwargs)


@rate_limited_request()
def delete(url: str, **kwargs) -> requests.Response:
    return requests.delete(url, **kwargs)


# Example usage (for testing within this file)
if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)

    # Test rate limiting and caching
    print("\n--- Testing Rate Limiting and Caching ---")

    @rate_limited_request(min_delay=2, cache_ttl=10)
    def test_get_cached(url):
        print(f"Fetching {url}...")
        return requests.get(url)

    # First call, should fetch and cache
    response1 = test_get_cached("https://httpbin.org/delay/1")
    print(f"Response 1 status: {response1.status_code}")

    # Second call within min_delay, should sleep and then fetch (if not cached)
    # or use cache (if cached and not expired)
    response2 = test_get_cached("https://httpbin.org/delay/1")
    print(f"Response 2 status: {response2.status_code}")

    # Test retries
    print("\n--- Testing Retries ---")

    @rate_limited_request(retries=2, backoff_factor=1)
    def test_get_fail(url):
        print(f"Attempting to fetch {url} (will fail)...")
        # This URL will return a 500 error
        return requests.get("https://httpbin.org/status/500")

    try:
        test_get_fail("https://httpbin.org/status/500")
    except requests.exceptions.RequestException as e:
        print(f"Caught expected exception after retries: {e}")

    print("\n--- Testing Post Request (no caching) ---")

    @rate_limited_request(min_delay=1)
    def test_post_request(url, data):
        print(f"Posting to {url} with data {data}...")
        return requests.post(url, json=data)

    response_post = test_post_request("https://httpbin.org/post", {"key": "value"})
    print(f"Post Response status: {response_post.status_code}")
    print(f"Post Response JSON: {response_post.json()}")
