import json
import matplotlib.pyplot as plt  # type: ignore
from collections import defaultdict
import os
from datetime import datetime

INPUT_FILE = "backend/data/simulated_trades.json"

def load_trades():
    if not os.path.exists(INPUT_FILE):
        print(f"❌ File not found: {INPUT_FILE}")
        return []
    try:
        with open(INPUT_FILE, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        print("❌ Failed to parse trades JSON.")
        return []

def parse_time(t):
    try:
        return datetime.strptime(t, "%Y-%m-%dT%H:%M:%SZ")
    except:
        return datetime.utcnow()

def visualize():
    trades = load_trades()
    if not trades:
        print("No trades to visualize.")
        return

    # Sort by time
    trades = sorted(trades, key=lambda t: parse_time(t.get("time", "")))

    balance = 1000
    equity_curve = []
    times = []
    win, loss = 0, 0
    token_pnl = defaultdict(float)
    all_pnls = []

    for t in trades:
        if t.get("action") == "SELL":
            pnl = t.get("pnl", 0)
            balance += pnl
            all_pnls.append(pnl)
            token_pnl[t.get("symbol", "UNKNOWN")] += pnl
            if pnl > 0:
                win += 1
            else:
                loss += 1
        times.append(parse_time(t.get("time", "")))
        equity_curve.append(balance)

    # 🟩 Equity Curve
    plt.figure(figsize=(10, 4))
    plt.plot(times, equity_curve, label="Equity", color="blue")
    plt.title("Equity Curve")
    plt.xlabel("Time")
    plt.ylabel("Balance ($)")
    plt.grid(True)
    plt.tight_layout()
    plt.show()

    # 🥧 Win/Loss
    plt.figure()
    plt.pie([win, loss], labels=["Wins", "Losses"], autopct="%1.1f%%", colors=["green", "red"])
    plt.title("Win vs Loss Ratio")
    plt.tight_layout()
    plt.show()

    # 📉 PnL Histogram
    plt.figure()
    plt.hist(all_pnls, bins=10, color="skyblue", edgecolor="black")
    plt.title("PnL Distribution")
    plt.xlabel("PnL ($)")
    plt.ylabel("Frequency")
    plt.tight_layout()
    plt.show()

    # 📊 Token-wise PnL
    plt.figure(figsize=(8, 4))
    tokens = list(token_pnl.keys())
    pnls = list(token_pnl.values())
    bars = plt.bar(tokens, pnls, color=["green" if p >= 0 else "red" for p in pnls])
    plt.title("Total PnL per Token")
    plt.ylabel("PnL ($)")
    plt.xticks(rotation=45)
    plt.grid(axis="y")
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    visualize()