"""
Production-grade logging configuration for Alpha Predator Bot
"""
import os
import sys
import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional

class ProductionLogger:
    """Production-ready logger with structured output"""
    
    def __init__(self, name: str = "alpha-predator", level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        
        # Remove existing handlers to avoid duplicates
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create formatter
        env = os.getenv("ENV", "development")
        if env == "production":
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "level": "%(levelname)s", '
                '"logger": "%(name)s", "message": "%(message)s"}'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        # Create console handler
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def info(self, message: str, **kwargs):
        """Log info message with optional context"""
        if kwargs:
            message = f"{message} | Context: {json.dumps(kwargs)}"
        self.logger.info(message)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with optional context"""
        if kwargs:
            message = f"{message} | Context: {json.dumps(kwargs)}"
        self.logger.warning(message)
    
    def error(self, message: str, **kwargs):
        """Log error message with optional context"""
        if kwargs:
            message = f"{message} | Context: {json.dumps(kwargs)}"
        self.logger.error(message)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with optional context"""
        if kwargs:
            message = f"{message} | Context: {json.dumps(kwargs)}"
        self.logger.debug(message)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with optional context"""
        if kwargs:
            message = f"{message} | Context: {json.dumps(kwargs)}"
        self.logger.critical(message)

def get_logger(name: Optional[str] = None) -> ProductionLogger:
    """
    Get a configured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured ProductionLogger
    """
    log_level = os.getenv("LOG_LEVEL", "INFO")
    return ProductionLogger(name or "alpha-predator", log_level)

def log_api_request(
    logger: ProductionLogger,
    method: str,
    endpoint: str,
    status_code: Optional[int] = None,
    duration_ms: Optional[float] = None,
    user_id: Optional[str] = None,
    request_id: Optional[str] = None,
    **kwargs
) -> None:
    """
    Log API request with standardized format.
    """
    logger.info(
        f"API Request: {method} {endpoint}",
        status_code=status_code,
        duration_ms=duration_ms,
        user_id=user_id,
        request_id=request_id,
        **kwargs
    )

def log_trade_event(
    logger: ProductionLogger,
    event_type: str,
    symbol: str,
    action: str,
    amount: Optional[float] = None,
    price: Optional[float] = None,
    success: bool = True,
    error_message: Optional[str] = None,
    **kwargs
) -> None:
    """
    Log trading events with standardized format.
    """
    status = "SUCCESS" if success else "FAILED"
    logger.info(
        f"Trade Event [{status}]: {event_type} - {action} {symbol}",
        amount=amount,
        price=price,
        error_message=error_message,
        **kwargs
    )

def log_ai_decision(
    logger: ProductionLogger,
    model: str,
    symbol: str,
    decision: str,
    confidence: Optional[float] = None,
    reasoning: Optional[str] = None,
    **kwargs
) -> None:
    """
    Log AI decision events with standardized format.
    """
    logger.info(
        f"AI Decision: {model} -> {decision} for {symbol}",
        confidence=confidence,
        reasoning=reasoning,
        **kwargs
    )

# Export default logger
logger = get_logger("alpha-predator")
