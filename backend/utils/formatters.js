// Format a number to 2 decimal places with comma separators
// Example: 12345.678 => "12,345.68"
export function formatNumber(num) {
  if (num === null || num === undefined || isNaN(num)) return '-';
  return Number(num).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// Format a number as a percentage
// Example: 0.8567 => "85.67%"
export function formatPercentage(num) {
  if (num === null || num === undefined || isNaN(num)) return '-';
  return `${(num * 100).toFixed(2)}%`;
}

// Format large numbers into compact form
// Example: 1500000 => "1.5M"
export function formatCompactNumber(num) {
  if (num === null || num === undefined || isNaN(num)) return '-';
  return Intl.NumberFormat('en', { notation: 'compact', style: 'decimal' }).format(num);
}