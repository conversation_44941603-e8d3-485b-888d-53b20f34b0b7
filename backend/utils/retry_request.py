# backend/utils/retry_request.py

import time
import requests
import logging

logger = logging.getLogger("retry_request")

def retry_request(url, headers=None, params=None, max_retries=3, delay=2):
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            if isinstance(e, requests.exceptions.HTTPError) and e.response.status_code == 429:
                current_delay = delay * (2 ** attempt) * 5 # More aggressive backoff for 429
                logger.warning(f"[Retry {attempt+1}] Rate limit hit for {url}. Retrying in {current_delay}s...")
                time.sleep(current_delay)
            else:
                current_delay = delay * (2 ** attempt)
                logger.warning(f"[Retry {attempt+1}] Error fetching {url}: {e}. Retrying in {current_delay}s...")
                time.sleep(current_delay)
    logger.error(f"[FAILURE] Max retries exceeded for {url}")
    raise Exception(f"[FAILURE] Max retries exceeded for {url}")