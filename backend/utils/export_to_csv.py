import json
import csv
import os
import logging
from datetime import datetime

# Setup logger
logger = logging.getLogger("export_to_csv")
logging.basicConfig(level=logging.INFO)

# Paths
INPUT_PATH = "backend/data/simulated_trades.json"
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
OUTPUT_PATH = f"backend/data/simulated_trades_{timestamp}.csv"

def export_json_to_csv():
    if not os.path.exists(INPUT_PATH):
        logger.error(f"❌ No file found at {INPUT_PATH}")
        return

    try:
        with open(INPUT_PATH, "r") as f:
            trades = json.load(f)
    except json.JSONDecodeError:
        logger.error("❌ Failed to parse JSON input.")
        return

    if not trades or not isinstance(trades, list):
        logger.warning("⚠️ No valid trades to export.")
        return

    headers = sorted({k for trade in trades if isinstance(trade, dict) for k in trade.keys()})

    try:
        with open(OUTPUT_PATH, "w", newline='') as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            writer.writerows(trade for trade in trades if isinstance(trade, dict))
        logger.info(f"✅ Exported {len(trades)} trades to {OUTPUT_PATH}")
    except Exception as e:
        logger.error(f"❌ Failed to write CSV: {e}")

if __name__ == "__main__":
    export_json_to_csv()