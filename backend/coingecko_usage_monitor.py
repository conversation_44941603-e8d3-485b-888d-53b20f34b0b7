"""
CoinGecko Usage Monitor
Tracks and optimizes CoinGecko MCP API usage within free tier limits
"""

import json
import time
import logging
import os
from typing import Dict, Any, List
from datetime import datetime, timedelta
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UsageStats:
    """Usage statistics for CoinGecko API"""
    calls_today: int
    calls_this_month: int
    monthly_limit: int
    rate_limit_per_minute: int
    last_call_time: float
    cost_per_call: float
    estimated_monthly_cost: float

class CoinGeckoUsageMonitor:
    """
    Monitor and optimize CoinGecko MCP API usage
    Free tier: 30 calls/min, 10k calls/month
    """
    
    def __init__(self):
        self.usage_file = "data/coingecko_usage_detailed.json"
        self.monthly_limit = 10000  # Free tier
        self.rate_limit_per_minute = 30  # Free tier
        self.cost_per_call = 0.0  # Free tier
        
        # Load existing usage data
        self.usage_data = self._load_usage_data()
        
    def _load_usage_data(self) -> Dict[str, Any]:
        """Load usage data from file"""
        try:
            if os.path.exists(self.usage_file):
                with open(self.usage_file, 'r') as f:
                    data = json.load(f)
                    
                # Reset monthly data if new month
                current_month = datetime.now().strftime("%Y-%m")
                if data.get("current_month") != current_month:
                    data = self._reset_monthly_data(current_month)
                    
                return data
        except Exception as e:
            logger.warning(f"Failed to load CoinGecko usage data: {e}")
        
        # Return default data structure
        return self._reset_monthly_data(datetime.now().strftime("%Y-%m"))
    
    def _reset_monthly_data(self, current_month: str) -> Dict[str, Any]:
        """Reset monthly usage data"""
        return {
            "current_month": current_month,
            "monthly_calls": 0,
            "daily_calls": {},
            "endpoint_usage": {},
            "hourly_distribution": {},
            "cost_tracking": {
                "total_cost": 0.0,
                "daily_costs": {}
            },
            "performance_metrics": {
                "avg_response_time": 0.0,
                "success_rate": 100.0,
                "error_count": 0
            },
            "last_updated": time.time()
        }
    
    def _save_usage_data(self):
        """Save usage data to file"""
        try:
            os.makedirs("data", exist_ok=True)
            self.usage_data["last_updated"] = time.time()
            
            with open(self.usage_file, 'w') as f:
                json.dump(self.usage_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save CoinGecko usage data: {e}")
    
    def can_make_call(self, endpoint: str = "general") -> bool:
        """Check if we can make an API call within limits"""
        current_time = time.time()
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_hour = datetime.now().strftime("%H")
        
        # Check monthly limit
        if self.usage_data["monthly_calls"] >= self.monthly_limit:
            logger.warning(f"CoinGecko monthly limit reached: {self.usage_data['monthly_calls']}/{self.monthly_limit}")
            return False
        
        # Check rate limiting (calls per minute)
        recent_calls = self._get_recent_calls_count(60)  # Last 60 seconds
        if recent_calls >= self.rate_limit_per_minute:
            logger.warning(f"CoinGecko rate limit reached: {recent_calls}/{self.rate_limit_per_minute} per minute")
            return False
        
        # Check daily distribution for optimization
        daily_calls = self.usage_data["daily_calls"].get(current_date, 0)
        daily_limit = self.monthly_limit // 30  # Rough daily limit
        
        if daily_calls >= daily_limit:
            logger.warning(f"CoinGecko daily limit reached: {daily_calls}/{daily_limit}")
            return False
        
        return True
    
    def _get_recent_calls_count(self, seconds: int) -> int:
        """Get number of calls made in the last N seconds"""
        current_time = time.time()
        cutoff_time = current_time - seconds
        
        # This is a simplified version - in production, you'd track individual call timestamps
        # For now, we'll use a conservative estimate
        current_minute = datetime.now().strftime("%Y-%m-%d %H:%M")
        return self.usage_data.get("minute_calls", {}).get(current_minute, 0)
    
    def record_api_call(self, endpoint: str, response_time: float, success: bool = True):
        """Record an API call for usage tracking"""
        current_time = time.time()
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_hour = datetime.now().strftime("%H")
        current_minute = datetime.now().strftime("%Y-%m-%d %H:%M")
        
        # Update monthly calls
        self.usage_data["monthly_calls"] += 1
        
        # Update daily calls
        if current_date not in self.usage_data["daily_calls"]:
            self.usage_data["daily_calls"][current_date] = 0
        self.usage_data["daily_calls"][current_date] += 1
        
        # Update endpoint usage
        if endpoint not in self.usage_data["endpoint_usage"]:
            self.usage_data["endpoint_usage"][endpoint] = 0
        self.usage_data["endpoint_usage"][endpoint] += 1
        
        # Update hourly distribution
        if current_hour not in self.usage_data["hourly_distribution"]:
            self.usage_data["hourly_distribution"][current_hour] = 0
        self.usage_data["hourly_distribution"][current_hour] += 1
        
        # Update minute tracking for rate limiting
        if "minute_calls" not in self.usage_data:
            self.usage_data["minute_calls"] = {}
        if current_minute not in self.usage_data["minute_calls"]:
            self.usage_data["minute_calls"][current_minute] = 0
        self.usage_data["minute_calls"][current_minute] += 1
        
        # Clean old minute data (keep only last hour)
        self._clean_old_minute_data()
        
        # Update cost tracking
        call_cost = self.cost_per_call
        self.usage_data["cost_tracking"]["total_cost"] += call_cost
        
        if current_date not in self.usage_data["cost_tracking"]["daily_costs"]:
            self.usage_data["cost_tracking"]["daily_costs"][current_date] = 0.0
        self.usage_data["cost_tracking"]["daily_costs"][current_date] += call_cost
        
        # Update performance metrics
        metrics = self.usage_data["performance_metrics"]
        if success:
            # Update average response time
            current_avg = metrics.get("avg_response_time", 0.0)
            total_calls = self.usage_data["monthly_calls"]
            metrics["avg_response_time"] = ((current_avg * (total_calls - 1)) + response_time) / total_calls
        else:
            metrics["error_count"] += 1
        
        # Calculate success rate
        total_calls = self.usage_data["monthly_calls"]
        error_count = metrics["error_count"]
        metrics["success_rate"] = ((total_calls - error_count) / total_calls) * 100 if total_calls > 0 else 100.0
        
        # Save updated data
        self._save_usage_data()
        
        logger.info(f"CoinGecko API call recorded: {endpoint} ({response_time:.2f}s) - Monthly: {self.usage_data['monthly_calls']}/{self.monthly_limit}")
    
    def _clean_old_minute_data(self):
        """Clean minute data older than 1 hour"""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(hours=1)
        
        minute_calls = self.usage_data.get("minute_calls", {})
        keys_to_remove = []
        
        for minute_key in minute_calls.keys():
            try:
                minute_time = datetime.strptime(minute_key, "%Y-%m-%d %H:%M")
                if minute_time < cutoff_time:
                    keys_to_remove.append(minute_key)
            except ValueError:
                keys_to_remove.append(minute_key)  # Remove invalid keys
        
        for key in keys_to_remove:
            del minute_calls[key]
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage summary"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        return {
            "provider": "CoinGecko MCP",
            "plan": "Free Tier",
            "monthly_usage": {
                "calls_made": self.usage_data["monthly_calls"],
                "calls_limit": self.monthly_limit,
                "calls_remaining": self.monthly_limit - self.usage_data["monthly_calls"],
                "usage_percentage": (self.usage_data["monthly_calls"] / self.monthly_limit) * 100,
                "cost_spent": self.usage_data["cost_tracking"]["total_cost"]
            },
            "daily_usage": {
                "calls_today": self.usage_data["daily_calls"].get(current_date, 0),
                "cost_today": self.usage_data["cost_tracking"]["daily_costs"].get(current_date, 0.0)
            },
            "rate_limiting": {
                "calls_per_minute_limit": self.rate_limit_per_minute,
                "current_minute_calls": self._get_recent_calls_count(60)
            },
            "performance": self.usage_data["performance_metrics"],
            "top_endpoints": self._get_top_endpoints(),
            "optimization_suggestions": self._get_optimization_suggestions(),
            "status": self._get_status()
        }
    
    def _get_top_endpoints(self) -> List[Dict[str, Any]]:
        """Get top used endpoints"""
        endpoint_usage = self.usage_data.get("endpoint_usage", {})
        sorted_endpoints = sorted(endpoint_usage.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {"endpoint": endpoint, "calls": calls, "percentage": (calls / self.usage_data["monthly_calls"]) * 100}
            for endpoint, calls in sorted_endpoints[:5]
        ]
    
    def _get_optimization_suggestions(self) -> List[str]:
        """Get optimization suggestions based on usage patterns"""
        suggestions = []
        
        usage_percentage = (self.usage_data["monthly_calls"] / self.monthly_limit) * 100
        
        if usage_percentage > 80:
            suggestions.append("Consider upgrading to Pro plan - approaching monthly limit")
        
        if usage_percentage > 50:
            suggestions.append("Implement more aggressive caching to reduce API calls")
        
        # Check for rate limiting issues
        recent_calls = self._get_recent_calls_count(60)
        if recent_calls > (self.rate_limit_per_minute * 0.8):
            suggestions.append("Implement request queuing to avoid rate limits")
        
        # Check error rate
        error_rate = 100 - self.usage_data["performance_metrics"]["success_rate"]
        if error_rate > 5:
            suggestions.append("High error rate detected - implement better error handling")
        
        if not suggestions:
            suggestions.append("Usage is within optimal limits")
        
        return suggestions
    
    def _get_status(self) -> str:
        """Get current status"""
        usage_percentage = (self.usage_data["monthly_calls"] / self.monthly_limit) * 100
        
        if usage_percentage >= 100:
            return "limit_reached"
        elif usage_percentage >= 90:
            return "critical"
        elif usage_percentage >= 70:
            return "warning"
        else:
            return "healthy"

# Global monitor instance
coingecko_usage_monitor = CoinGeckoUsageMonitor()
