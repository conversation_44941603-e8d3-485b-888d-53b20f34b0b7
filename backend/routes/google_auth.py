from fastapi import APIRouter, Request, Depends
from fastapi.responses import RedirectResponse, JSONResponse
from authlib.integrations.starlette_client import OAuth
from authlib.integrations.base_client.errors import OAuthError
from starlette.config import Config
import os
from dotenv import load_dotenv
from config import ALLOWED_EMAILS, SECRET_KEY, ALG<PERSON><PERSON>H<PERSON>
from auth import create_access_token
from error_codes import error_response

load_dotenv()

google_auth_router = APIRouter()

# Load environment variables
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI")

if not GOOGLE_CLIENT_ID or not GOOGLE_CLIENT_SECRET:
    raise RuntimeError("Missing GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET.")

if not GOOGLE_REDIRECT_URI:
    raise RuntimeError("Missing GOOGLE_REDIRECT_URI.")

config_data = {
    "GOOGLE_CLIENT_ID": GOOGLE_CLIENT_ID,
    "GOOGLE_CLIENT_SECRET": GOOGLE_CLIENT_SECRET,
    "SECRET_KEY": SECRET_KEY # Use SECRET_KEY from config.py
}
config = Config(environ=config_data)

oauth = OAuth(config)
oauth.register(
    name='google',
    client_id=GOOGLE_CLIENT_ID,
    client_secret=GOOGLE_CLIENT_SECRET,
    access_token_url='https://accounts.google.com/o/oauth2/token',
    access_token_params=None,
    authorize_url='https://accounts.google.com/o/oauth2/auth',
    authorize_params=None,
    api_base_url='https://www.googleapis.com/oauth2/v1/',
    userinfo_endpoint='https://openidconnect.googleapis.com/v1/userinfo',
    client_kwargs={'scope': 'openid email profile'}
)

@google_auth_router.get("/login/google")
async def login_via_google(request: Request):
    redirect_uri = GOOGLE_REDIRECT_URI
    google = oauth.create_client('google')
    if google is None:
        return JSONResponse({"error": "Google OAuth client not registered."}, status_code=500)
    return await google.authorize_redirect(request, redirect_uri)

@google_auth_router.get("/auth/callback")
async def auth_callback(request: Request):
    try:
        google = oauth.create_client('google')
        if google is None:
            return JSONResponse({"error": "Google OAuth client not registered."}, status_code=500)
        token = await google.authorize_access_token(request)
        user_info = await google.parse_id_token(request, token)
        user_email = user_info.get("email")
        if user_email not in ALLOWED_EMAILS:
            return error_response("UNAUTHORIZED_EMAIL", "Unauthorized email address.", 403)

        jwt_token = create_access_token(data={"sub": user_email})
        response = RedirectResponse(url="/logic")
        response.set_cookie(key="access_token", value=jwt_token, httponly=True, max_age=60 * 60 * 24 * 5)
        return response

    except OAuthError as error:
        return error_response("OAUTH_ERROR", str(error), 400)