import csv
from pathlib import Path
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from error_codes import error_response
from auth import get_current_user
from trade_executor import execute_trade
from price_fetcher import fetch_kucoin_price
from utils.logger import get_logger, log_trade_event

# Initialize logger
logger = get_logger(__name__)

router = APIRouter()

class TradeRequest(BaseModel):
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float

from fastapi import HTTPException

@router.post("/api/trades/live", tags=["Trades"])
async def post_live_trade(trade: TradeRequest, request: Request, current_user: str = Depends(get_current_user)):
    if trade.side.lower() not in ["buy", "sell"]:
        raise HTTPException(status_code=400, detail="Invalid trade side. Must be 'buy' or 'sell'.")

    try:
        execute_trade(trade.symbol, trade.side.lower(), trade.quantity)
        return {"status": "success", "message": f"Trade executed: {trade.side} {trade.quantity} {trade.symbol}"}
    except Exception as e:
        return error_response("TRADE_EXECUTION_FAILED", f"Trade execution failed: {str(e)}", 500)

@router.post("/api/trades/live/strategy-trade", tags=["Trades"])
async def strategy_trade(request: Request, current_user: str = Depends(get_current_user)):
    """
    Execute a live trade of $1 on KuCoin using spot_grid_strategy.
    """
    from price_fetcher import fetch_kucoin_price
    from kucoin_strategies import (
        spot_grid_strategy,
        value_investing_strategy,
        momentum_strategy,
        contrarian_strategy,
        growth_investing_strategy,
        turtle_trading_strategy,
        moving_average_crossover_strategy,
        bollinger_bands_strategy,
        rsi_strategy,
        macd_strategy,
        breakout_strategy,
    )
    import json
    import os
    from pathlib import Path

    # Load top 100 tokens from news, sentiment etc (assuming stored in backend/data/top_tokens.json)
    try:
        base_path = Path(__file__).resolve().parent.parent
        top_tokens_path = base_path / "data" / "live_trades.json"
        with open(top_tokens_path, "r") as f:
            content = f.read()
            logger.info("Loading top tokens for strategy trade", 
                       path=str(top_tokens_path), 
                       content_length=len(content))
            top_tokens = json.loads(content)
    except FileNotFoundError:
        return error_response("TOP_TOKENS_FILE_NOT_FOUND", "Top tokens file not found.", 404)
    except json.JSONDecodeError as je:
        return error_response("INVALID_TOP_TOKENS_JSON", f"Invalid JSON in top tokens file: {je}", 500)
    except Exception as e:
        return error_response("LOAD_TOP_TOKENS_FAILED", f"Failed to load top tokens: {str(e)}", 500)

    for token_entry in top_tokens:
        token = token_entry.get("symbol")
        if not token:
            continue

        price = fetch_kucoin_price(token)
        if price is None or price <= 0:
            continue

        price_data = {
            "price": price,
            "low": price * 0.95,
            "high": price * 1.05
        }

        # Collect decisions and reasons from multiple strategies
        decisions_reasons = []

        # spot_grid_strategy
        dec, reas = spot_grid_strategy(price_data)
        decisions_reasons.append(("spot_grid_strategy", dec, reas))

        # For demonstration, dummy data for other strategies
        fundamentals = {"pe_ratio": 10, "debt_to_equity": 0.3}
        dec = value_investing_strategy(fundamentals)
        decisions_reasons.append(("value_investing_strategy", dec, "N/A"))

        price_history = [price * 0.98, price * 0.99, price]
        dec = momentum_strategy(price_history)
        decisions_reasons.append(("momentum_strategy", dec, "N/A"))

        sentiment_score = -0.6  # dummy sentiment
        dec = contrarian_strategy(sentiment_score)
        decisions_reasons.append(("contrarian_strategy", dec, "N/A"))

        earnings_growth = 0.2  # dummy growth
        dec = growth_investing_strategy(earnings_growth)
        decisions_reasons.append(("growth_investing_strategy", dec, "N/A"))

        # Aggregate decisions: if any strategy says BUY, buy; else if any says SELL, sell; else HOLD
        final_decision = "HOLD"
        reasons = []
        for strat, dec, reas in decisions_reasons:
            reasons.append(f"{strat}: {reas}")
            if dec == "BUY":
                final_decision = "BUY"
            elif dec == "SELL" and final_decision != "BUY":
                final_decision = "SELL"

        log_trade_event(
            logger=logger,
            event_type="strategy_analysis",
            symbol=token,
            action=final_decision,
            price=price,
            success=True,
            strategies=reasons
        )

        if final_decision == "HOLD":
            continue

        quantity = round(1 / price, 6)

        try:
            result = execute_trade(token, final_decision.lower(), quantity * price, price)
            if result.get("success"):
                return {
                    "status": "success",
                    "message": f"Strategy trade executed: {final_decision} {quantity} {token}",
                    "price": price,
                    "quantity": quantity,
                    "decision": final_decision,
                    "reason": reasons
                }
            else:
                log_trade_event(
                    logger=logger,
                    event_type="execution",
                    symbol=token,
                    action=final_decision.lower(),
                    price=price,
                    amount=quantity * price,
                    success=False,
                    error_message=result.get('message')
                )
        except Exception as e:
            log_trade_event(
                logger=logger,
                event_type="execution",
                symbol=token,
                action=final_decision.lower(),
                price=price,
                amount=quantity * price,
                success=False,
                error_message=str(e)
            )
            continue

    return {"status": "no_action", "message": "Strategy decided to HOLD all top tokens or trade execution failed."}
