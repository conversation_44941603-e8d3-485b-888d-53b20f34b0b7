import csv
from pathlib import Path
from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from error_codes import error_response
from auth import get_current_user

router = APIRouter()

@router.get("/api/trades/summary", tags=["Trades"])
def get_trade_summary(current_user: str = Depends(get_current_user)):
    """Generate trade summary with profit metrics."""
    try:
        log_path = Path(__file__).resolve().parent.parent / "trade_logs.csv"
        if not log_path.exists():
            return JSONResponse(status_code=200, content={
                "error_code": "NO_LOGS",
                "message": "No trades yet.",
                "total_trades": 0,
                "net_profit": 0,
                "best_performing": {"symbol": "N/A", "profit": 0.0},
                "worst_performing": {"symbol": "N/A", "profit": 0.0}
            })

        with open(log_path, "r") as f:
            trades = list(csv.DictReader(f))

        if not trades:
            raise ValueError("Empty trade log")

        profits = [float(t.get("profit", 0)) for t in trades if "profit" in t]
        sorted_trades = sorted(trades, key=lambda x: float(x.get("profit", 0)))
        best = sorted_trades[-1]
        worst = sorted_trades[0]

        return JSONResponse(status_code=200, content={
            "total_trades": len(trades),
            "net_profit": sum(profits),
            "best_performing": {"symbol": best["symbol"], "profit": float(best["profit"])},
            "worst_performing": {"symbol": worst["symbol"], "profit": float(worst["profit"])}
        })
    except Exception as e:
        return error_response("TRADE_SUMMARY_ERROR", str(e), 500)
