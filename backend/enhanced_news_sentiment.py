#!/usr/bin/env python3
"""
Enhanced News Sentiment Analysis with Breaking News Priority and Market Correlation
"""

import asyncio
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedNewsSentiment:
    def __init__(self):
        # Critical breaking news keywords with high impact weights
        self.breaking_news_keywords = {
            # Political & Geopolitical (CRITICAL IMPACT)
            'political': {
                'keywords': ['election', 'president', 'government', 'policy', 'regulation', 'ban', 'legal', 'congress', 'senate'],
                'weight': 2.0,
                'urgency': 'CRITICAL'
            },
            'war_conflict': {
                'keywords': ['war', 'nuclear', 'conflict', 'invasion', 'military', 'attack', 'missile', 'bomb', 'sanctions'],
                'weight': 2.5,
                'urgency': 'CRITICAL'
            },
            
            # Economic Crashes & Major Events
            'market_crash': {
                'keywords': ['crash', 'collapse', 'plunge', 'panic', 'recession', 'depression', 'crisis', 'emergency'],
                'weight': 2.0,
                'urgency': 'CRITICAL'
            },
            'financial_system': {
                'keywords': ['bank', 'fed', 'federal reserve', 'interest rate', 'inflation', 'gdp', 'unemployment'],
                'weight': 1.8,
                'urgency': 'HIGH'
            },
            
            # Crypto-Specific Breaking News
            'crypto_regulation': {
                'keywords': ['sec', 'cftc', 'regulatory', 'compliance', 'lawsuit', 'investigation', 'fine'],
                'weight': 1.9,
                'urgency': 'CRITICAL'
            },
            'crypto_adoption': {
                'keywords': ['institutional', 'etf', 'approval', 'adoption', 'mainstream', 'corporate', 'treasury'],
                'weight': 1.7,
                'urgency': 'HIGH'
            },
            'crypto_security': {
                'keywords': ['hack', 'breach', 'exploit', 'stolen', 'vulnerability', 'security', 'funds lost'],
                'weight': 1.8,
                'urgency': 'HIGH'
            }
        }
        
        # Market correlation indicators
        self.correlation_indicators = {
            'stock_crypto_positive': ['risk-on', 'growth', 'tech rally', 'nasdaq up', 'sp500 gains'],
            'stock_crypto_negative': ['risk-off', 'flight to safety', 'tech selloff', 'nasdaq down', 'sp500 falls'],
            'inverse_correlation': ['safe haven', 'gold up', 'bonds rally', 'dollar strength', 'vix spike']
        }

    def classify_news_urgency(self, headline: str, content: str = "") -> Dict:
        """Classify news urgency and impact level"""
        text = f"{headline} {content}".lower()
        
        max_weight = 1.0
        urgency_level = 'NORMAL'
        categories_matched = []
        
        for category, data in self.breaking_news_keywords.items():
            keyword_matches = sum(1 for keyword in data['keywords'] if keyword in text)
            
            if keyword_matches > 0:
                categories_matched.append(category)
                if data['weight'] > max_weight:
                    max_weight = data['weight']
                    urgency_level = data['urgency']
        
        return {
            'urgency_level': urgency_level,
            'impact_weight': max_weight,
            'categories': categories_matched,
            'is_breaking': urgency_level in ['CRITICAL', 'HIGH']
        }

    def analyze_market_correlation(self, headline: str, content: str = "") -> Dict:
        """Analyze potential stock market correlation"""
        text = f"{headline} {content}".lower()
        
        correlation_type = 'NEUTRAL'
        correlation_strength = 0.5
        
        # Check for positive correlation indicators
        positive_matches = sum(1 for indicator in self.correlation_indicators['stock_crypto_positive'] if indicator in text)
        negative_matches = sum(1 for indicator in self.correlation_indicators['stock_crypto_negative'] if indicator in text)
        inverse_matches = sum(1 for indicator in self.correlation_indicators['inverse_correlation'] if indicator in text)
        
        if positive_matches > 0:
            correlation_type = 'POSITIVE'
            correlation_strength = min(0.9, 0.5 + (positive_matches * 0.2))
        elif negative_matches > 0:
            correlation_type = 'NEGATIVE'
            correlation_strength = min(0.9, 0.5 + (negative_matches * 0.2))
        elif inverse_matches > 0:
            correlation_type = 'INVERSE'
            correlation_strength = min(0.9, 0.5 + (inverse_matches * 0.2))
        
        return {
            'correlation_type': correlation_type,
            'correlation_strength': correlation_strength,
            'stock_market_impact': correlation_strength > 0.7
        }

    def enhanced_sentiment_analysis(self, headline: str, content: str = "") -> Dict:
        """Enhanced sentiment analysis with breaking news priority"""
        text = f"{headline} {content}".lower()
        
        # Basic sentiment keywords
        bullish_keywords = [
            'surge', 'rally', 'bull', 'gain', 'rise', 'up', 'high', 'breakthrough', 'adoption',
            'institutional', 'approval', 'upgrade', 'success', 'positive', 'growth', 'momentum'
        ]
        
        bearish_keywords = [
            'crash', 'fall', 'drop', 'bear', 'down', 'low', 'decline', 'sell', 'dump',
            'hack', 'breach', 'ban', 'regulation', 'concern', 'fear', 'panic', 'crisis'
        ]
        
        # Calculate base sentiment scores
        bullish_score = sum(1 for word in bullish_keywords if word in text)
        bearish_score = sum(1 for word in bearish_keywords if word in text)
        
        # Get urgency classification
        urgency_data = self.classify_news_urgency(headline, content)
        
        # Get market correlation
        correlation_data = self.analyze_market_correlation(headline, content)
        
        # Calculate weighted sentiment
        base_sentiment_score = bullish_score - bearish_score
        
        # Apply urgency weight multiplier
        weighted_sentiment_score = base_sentiment_score * urgency_data['impact_weight']
        
        # Determine final sentiment
        if weighted_sentiment_score > 1:
            sentiment = 'BULLISH'
            confidence = min(0.95, 0.6 + (abs(weighted_sentiment_score) * 0.1))
        elif weighted_sentiment_score < -1:
            sentiment = 'BEARISH'
            confidence = min(0.95, 0.6 + (abs(weighted_sentiment_score) * 0.1))
        else:
            sentiment = 'NEUTRAL'
            confidence = 0.5
        
        # Boost confidence for breaking news
        if urgency_data['is_breaking']:
            confidence = min(0.98, confidence + 0.2)
        
        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'base_score': base_sentiment_score,
            'weighted_score': weighted_sentiment_score,
            'urgency_data': urgency_data,
            'correlation_data': correlation_data,
            'timestamp': datetime.now().isoformat()
        }

    async def fetch_stock_market_data(self) -> Dict:
        """Fetch current stock market indicators for correlation analysis"""
        try:
            # Mock stock market data - in production, use real APIs like Alpha Vantage, Yahoo Finance
            stock_data = {
                'sp500_change': 0.5,  # Mock percentage change
                'nasdaq_change': 0.8,
                'vix_level': 18.5,
                'dollar_index': 103.2,
                'correlation_strength': 0.75,
                'market_sentiment': 'RISK_ON',
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info("Stock market data fetched successfully")
            return stock_data
            
        except Exception as e:
            logger.error(f"Error fetching stock market data: {e}")
            return {
                'sp500_change': 0.0,
                'nasdaq_change': 0.0,
                'vix_level': 20.0,
                'dollar_index': 100.0,
                'correlation_strength': 0.5,
                'market_sentiment': 'NEUTRAL',
                'timestamp': datetime.now().isoformat()
            }

    def calculate_trade_urgency_score(self, news_analysis: Dict, market_data: Dict) -> Dict:
        """Calculate trade urgency score based on news and market correlation"""
        
        base_urgency = 0.5
        
        # News impact factor
        news_factor = news_analysis['confidence'] * news_analysis['urgency_data']['impact_weight']
        
        # Market correlation factor
        correlation_factor = news_analysis['correlation_data']['correlation_strength']
        
        # Breaking news multiplier
        breaking_multiplier = 2.0 if news_analysis['urgency_data']['is_breaking'] else 1.0
        
        # Calculate final urgency score
        urgency_score = (base_urgency + news_factor + correlation_factor) * breaking_multiplier
        urgency_score = min(1.0, urgency_score)  # Cap at 1.0
        
        # Determine action priority
        if urgency_score >= 0.9:
            priority = 'IMMEDIATE'
        elif urgency_score >= 0.7:
            priority = 'HIGH'
        elif urgency_score >= 0.5:
            priority = 'MEDIUM'
        else:
            priority = 'LOW'
        
        return {
            'urgency_score': urgency_score,
            'priority': priority,
            'news_factor': news_factor,
            'correlation_factor': correlation_factor,
            'breaking_multiplier': breaking_multiplier,
            'trade_eligible': urgency_score >= 0.6
        }

    async def process_breaking_news(self, headline: str, content: str = "") -> Dict:
        """Complete breaking news processing pipeline"""
        
        # Step 1: Enhanced sentiment analysis
        sentiment_analysis = self.enhanced_sentiment_analysis(headline, content)
        
        # Step 2: Fetch market correlation data
        market_data = await self.fetch_stock_market_data()
        
        # Step 3: Calculate trade urgency
        urgency_analysis = self.calculate_trade_urgency_score(sentiment_analysis, market_data)
        
        # Step 4: Generate comprehensive analysis
        analysis_result = {
            'headline': headline,
            'content_preview': content[:200] if content else "",
            'sentiment_analysis': sentiment_analysis,
            'market_data': market_data,
            'urgency_analysis': urgency_analysis,
            'processing_timestamp': datetime.now().isoformat(),
            'requires_immediate_action': urgency_analysis['priority'] in ['IMMEDIATE', 'HIGH']
        }
        
        # Log critical breaking news
        if sentiment_analysis['urgency_data']['is_breaking']:
            logger.critical(f"BREAKING NEWS DETECTED: {headline[:100]}... | "
                          f"Sentiment: {sentiment_analysis['sentiment']} | "
                          f"Urgency: {urgency_analysis['priority']} | "
                          f"Trade Eligible: {urgency_analysis['trade_eligible']}")
        
        return analysis_result

# Global instance for easy import
enhanced_news_sentiment = EnhancedNewsSentiment()

# Convenience functions
async def analyze_breaking_news(headline: str, content: str = "") -> Dict:
    """Analyze breaking news with enhanced sentiment and correlation"""
    return await enhanced_news_sentiment.process_breaking_news(headline, content)

def get_news_urgency(headline: str, content: str = "") -> Dict:
    """Get news urgency classification"""
    return enhanced_news_sentiment.classify_news_urgency(headline, content)

def get_market_correlation(headline: str, content: str = "") -> Dict:
    """Get market correlation analysis"""
    return enhanced_news_sentiment.analyze_market_correlation(headline, content)

if __name__ == "__main__":
    # Test the enhanced news sentiment system
    async def test_enhanced_system():
        print("🚀 Testing Enhanced News Sentiment System\n")
        
        # Test breaking news scenarios
        test_scenarios = [
            {
                'headline': 'BREAKING: Nuclear tensions escalate as military conflict spreads',
                'content': 'Global markets panic as war fears grip investors'
            },
            {
                'headline': 'SEC announces major cryptocurrency regulation crackdown',
                'content': 'New compliance requirements could impact major exchanges'
            },
            {
                'headline': 'Federal Reserve emergency meeting called amid banking crisis',
                'content': 'Interest rate decisions could affect all financial markets'
            },
            {
                'headline': 'Bitcoin ETF approval drives institutional adoption surge',
                'content': 'Major corporations adding crypto to treasury reserves'
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"📰 TEST SCENARIO {i}:")
            print(f"Headline: {scenario['headline']}")
            
            result = await analyze_breaking_news(scenario['headline'], scenario['content'])
            
            print(f"🎯 Sentiment: {result['sentiment_analysis']['sentiment']}")
            print(f"📊 Confidence: {result['sentiment_analysis']['confidence']:.1%}")
            print(f"🚨 Urgency: {result['sentiment_analysis']['urgency_data']['urgency_level']}")
            print(f"⚡ Priority: {result['urgency_analysis']['priority']}")
            print(f"📈 Trade Eligible: {result['urgency_analysis']['trade_eligible']}")
            print(f"🔗 Correlation: {result['sentiment_analysis']['correlation_data']['correlation_type']}")
            print("-" * 80)
            print()
    
    asyncio.run(test_enhanced_system())
