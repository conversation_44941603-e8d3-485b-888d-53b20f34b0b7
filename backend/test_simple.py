#!/usr/bin/env python3
"""
🔧 SIMPLE MODULE TEST
Test basic imports and functionality
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic module imports"""
    print("🔧 TESTING BASIC IMPORTS")
    print("-" * 40)
    
    modules_to_test = [
        "config",
        "tokenmetrics_client", 
        "reddit_github_alpha",
        "sentiment_engine",
        "discord_news_bot",
        "price_fetcher",
        "ai_core"
    ]
    
    results = {}
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module}: PASS")
            results[module] = True
        except Exception as e:
            print(f"❌ {module}: FAIL - {e}")
            results[module] = False
    
    return results

def test_tokenmetrics():
    """Test TokenMetrics specifically"""
    print("\n🔧 TESTING TOKENMETRICS")
    print("-" * 40)
    
    try:
        from tokenmetrics_client import TokenMetricsClient
        client = TokenMetricsClient()
        print("✅ TokenMetrics client created")
        
        # Test fallback data
        import asyncio
        async def test_data():
            data = await client.get_token_data("BTC-USDT")
            return data
        
        result = asyncio.run(test_data())
        if result:
            print(f"✅ TokenMetrics data: {result.symbol} (source: {result.source})")
            return True
        else:
            print("❌ No TokenMetrics data returned")
            return False
            
    except Exception as e:
        print(f"❌ TokenMetrics test failed: {e}")
        return False

def test_sentiment():
    """Test sentiment engine"""
    print("\n🧠 TESTING SENTIMENT ENGINE")
    print("-" * 40)
    
    try:
        from sentiment_engine import get_sentiment_score, get_combined_sentiment
        
        # Test basic sentiment
        score = get_sentiment_score("BTC")
        print(f"✅ Basic sentiment for BTC: {score}")
        
        # Test combined sentiment
        combined = get_combined_sentiment("BTC")
        print(f"✅ Combined sentiment: {combined.get('combined_sentiment', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sentiment test failed: {e}")
        return False

def test_reddit_github():
    """Test Reddit/GitHub integration"""
    print("\n🔗 TESTING REDDIT/GITHUB")
    print("-" * 40)
    
    try:
        from reddit_github_alpha import fetch_reddit_signals, fetch_github_signals
        
        # Test basic functions
        reddit_signals = fetch_reddit_signals()
        github_signals = fetch_github_signals()
        
        print(f"✅ Reddit signals: {len(reddit_signals)} items")
        print(f"✅ GitHub signals: {len(github_signals)} items")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit/GitHub test failed: {e}")
        return False

def test_discord():
    """Test Discord integration"""
    print("\n📱 TESTING DISCORD")
    print("-" * 40)
    
    try:
        from discord_news_bot import get_latest_discord_news, simulate_discord_news
        
        # Simulate some news
        simulate_discord_news()
        
        # Get news
        news = get_latest_discord_news(limit=3)
        print(f"✅ Discord news: {len(news)} items")
        
        if news:
            sample = news[0]
            print(f"   Sample: {sample.get('title', '')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Discord test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 SIMPLE MODULE FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test basic imports
    import_results = test_basic_imports()
    
    # Test specific functionality
    tests = [
        ("TokenMetrics", test_tokenmetrics),
        ("Sentiment Engine", test_sentiment),
        ("Reddit/GitHub", test_reddit_github),
        ("Discord News", test_discord)
    ]
    
    test_results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            test_results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("-" * 25)
    
    # Import summary
    import_passed = sum(import_results.values())
    print(f"📦 Imports: {import_passed}/{len(import_results)} passed")
    
    # Function summary
    func_passed = sum(test_results.values())
    print(f"🔧 Functions: {func_passed}/{len(test_results)} passed")
    
    # Overall
    total_passed = import_passed + func_passed
    total_tests = len(import_results) + len(test_results)
    
    print(f"📈 Overall: {total_passed}/{total_tests} ({(total_passed/total_tests)*100:.1f}%)")
    
    if total_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("⚠️ Some tests failed - checking individual issues...")
        
        # Show failed imports
        failed_imports = [k for k, v in import_results.items() if not v]
        if failed_imports:
            print(f"❌ Failed imports: {', '.join(failed_imports)}")
        
        # Show failed functions
        failed_functions = [k for k, v in test_results.items() if not v]
        if failed_functions:
            print(f"❌ Failed functions: {', '.join(failed_functions)}")

if __name__ == "__main__":
    main()
