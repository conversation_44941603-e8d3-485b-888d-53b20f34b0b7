"""
Sentiment Analyzer - Alias for sentiment_engine.py
"""

# Import all functions from sentiment_engine
try:
    from sentiment_engine import analyze_sentiment as _engine_analyze_sentiment

    print("✅ Sentiment analyzer loaded from sentiment_engine")

    def analyze_sentiment(text):
        """Wrapper to extract float sentiment score from engine's result"""
        try:
            result = _engine_analyze_sentiment(text)
            # Assume result is a dict with a 'score' key; adjust as needed
            if isinstance(result, dict):
                return float(result.get("score", 0.5))
            elif isinstance(result, (int, float)):
                return float(result)
            else:
                return 0.5  # Neutral fallback
        except Exception as e:
            print(f"⚠️ Sentiment analysis error: {e}")
            return 0.5  # Neutral fallback

except ImportError:
    # Fallback to basic sentiment analysis
    def analyze_sentiment(text):
        """Basic sentiment analysis fallback"""
        positive_words = ["good", "great", "excellent", "bullish", "up", "rise", "gain"]
        negative_words = ["bad", "terrible", "bearish", "down", "fall", "loss", "crash"]

        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            return 0.7
        elif negative_count > positive_count:
            return 0.3
        else:
            return 0.5

    print("⚠️ Using basic sentiment analysis fallback")
