
import requests
from utils.api_client import get
from typing import List, Dict, Any, Optional
from config import COINMARKETCAL_API_KEY
from error_codes import error_response
import logging

logger = logging.getLogger(__name__)

class CoinMarketCalAPI:
    def __init__(self):
        self.base_url = "https://api.coinmarketcal.com/v1"
        self.api_key = COINMARKETCAL_API_KEY

    def get_events(self, coin_ids: Optional[List[str]] = None, **kwargs) -> List[Dict[str, Any]] | Dict[str, Any]:
        """
        Fetches events from the CoinMarketCal API with robust error handling.

        Args:
            coin_ids: A list of CoinGecko IDs to filter events by.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            A list of events or empty list on failure.
        """
        if not self.api_key:
            logger.warning("CoinMarketCal API key not provided, returning empty events list.")
            return []

        headers = {
            "x-api-key": self.api_key,
            "Accept": "application/json"
        }
        
        params = kwargs
        if coin_ids:
            params["coins"] = ",".join(coin_ids)

        try:
            response = get(f"{self.base_url}/events", headers=headers, params=params, cache_ttl=3600) # Cache CoinMarketCal events for 1 hour
            response.raise_for_status()
            data = response.json()
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'body' in data:
                return data.get('body', [])
            else:
                return []
        except requests.exceptions.ConnectionError as e:
            logger.warning(f"CoinMarketCal connection failed: {e}. Returning empty events list.")
            return []
        except requests.exceptions.Timeout as e:
            logger.warning(f"CoinMarketCal request timeout: {e}. Returning empty events list.")
            return []
        except requests.exceptions.RequestException as e:
            logger.warning(f"CoinMarketCal API error: {e}. Returning empty events list.")
            return []
        except Exception as e:
            logger.warning(f"Unexpected error fetching CoinMarketCal events: {e}. Returning empty events list.")
            return []
