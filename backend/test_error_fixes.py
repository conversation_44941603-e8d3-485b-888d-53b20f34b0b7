#!/usr/bin/env python3
"""
Test script to verify all backend error fixes are working
"""

import sys
import os
import asyncio

# Add backend to path
sys.path.append(os.path.dirname(__file__))


def test_sentiment_analysis():
    """Test sentiment analysis fixes"""
    print("\n1. 🧪 TESTING SENTIMENT ANALYSIS FIXES...")
    try:
        from sentiment_analyzer import analyze_sentiment

        # Test with sample text
        result = analyze_sentiment("Bitcoin is showing bullish momentum")
        print(f"   ✅ Sentiment analysis working: {result}")

        # Test with empty text
        result2 = analyze_sentiment("")
        print(f"   ✅ Empty text handling: {result2}")

        return True
    except Exception as e:
        print(f"   ❌ Sentiment analysis test failed: {e}")
        return False


def test_coingecko_sdk():
    """Test CoinGecko SDK parameter fix"""
    print("\n2. 🧪 TESTING COINGECKO SDK FIXES...")
    try:
        from coingecko_sdk_migration import coingecko_sdk

        # Test basic price fetch
        price_data = coingecko_sdk.get_price(["bitcoin"], ["usd"])
        if price_data and "bitcoin" in price_data:
            print(
                f'   ✅ CoinGecko SDK working: BTC = ${price_data["bitcoin"]["usd"]:,.2f}'
            )
            return True
        else:
            print("   ⚠️ CoinGecko SDK returned no data")
            return False
    except Exception as e:
        print(f"   ❌ CoinGecko SDK test failed: {e}")
        return False


async def test_async_tokenmetrics():
    """Test async TokenMetrics fix"""
    print("\n3. 🧪 TESTING ASYNC TOKENMETRICS FIXES...")
    try:
        from smart_tokenmetrics_client import get_optimized_trading_data

        # Test async call
        result = await get_optimized_trading_data(["BTC"])
        if result:
            print(f"   ✅ Async TokenMetrics working: {len(result)} fields returned")
            return True
        else:
            print("   ⚠️ TokenMetrics returned no data")
            return False
    except Exception as e:
        print(f"   ❌ Async TokenMetrics test failed: {e}")
        return False


def test_cost_monitor():
    """Test cost monitor Reddit KeyError fix"""
    print("\n4. 🧪 TESTING COST MONITOR FIXES...")
    try:
        from real_time_cost_monitor import RealTimeCostMonitor

        # Create monitor instance
        monitor = RealTimeCostMonitor()

        # Test recording Reddit API call
        monitor.record_api_call("reddit", "/api/test", 0, 0.0)
        print("   ✅ Cost monitor Reddit recording working")

        # Test recording unknown service
        monitor.record_api_call("unknown_service", "/api/test", 0, 0.0)
        print("   ✅ Cost monitor unknown service handling working")

        return True
    except Exception as e:
        print(f"   ❌ Cost monitor test failed: {e}")
        return False


def test_token_selection():
    """Test token selection threshold fixes"""
    print("\n5. 🧪 TESTING TOKEN SELECTION FIXES...")
    try:
        from token_selector import is_valid_token, VOLUME_THRESHOLD

        print(f"   📊 Volume threshold: ${VOLUME_THRESHOLD:,}")

        # Test with sample token data
        sample_token = {
            "symbol": "BTC-USDT",
            "volValue": "500000",  # 500K volume
            "last": "50000",
            "changeRate": "0.05",  # 5% change
        }

        is_valid = is_valid_token("BTC-USDT", sample_token)
        print(f"   ✅ Token validation working: {is_valid}")

        return True
    except Exception as e:
        print(f"   ❌ Token selection test failed: {e}")
        return False


def test_kryptomerch_scraper():
    """Test kryptomerch scraper indentation fix"""
    print("\n6. 🧪 TESTING KRYPTOMERCH SCRAPER FIXES...")
    try:
        # Just try to import - if indentation is fixed, this should work
        import kryptomerch_scraper  # noqa: F401

        print("   ✅ Kryptomerch scraper import working (indentation fixed)")
        return True
    except SyntaxError as e:
        print(f"   ❌ Kryptomerch scraper syntax error: {e}")
        return False
    except Exception as e:
        print(f"   ⚠️ Kryptomerch scraper other error: {e}")
        return True  # Other errors are OK, syntax errors are not


async def run_all_tests():
    """Run all error fix tests"""
    print("🔧 BACKEND ERROR FIXES VERIFICATION")
    print("=" * 60)

    results = []

    # Run synchronous tests
    results.append(test_sentiment_analysis())
    results.append(test_coingecko_sdk())
    results.append(test_cost_monitor())
    results.append(test_token_selection())
    results.append(test_kryptomerch_scraper())

    # Run async tests
    results.append(await test_async_tokenmetrics())

    # Summary
    passed = sum(results)
    total = len(results)

    print(f"\n🎯 TEST RESULTS SUMMARY:")
    print("=" * 50)
    print(f"✅ Tests passed: {passed}/{total}")
    print(f"❌ Tests failed: {total - passed}/{total}")
    print(f"📊 Success rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("\n🎉 ALL ERROR FIXES VERIFIED!")
        print("✅ Backend should now run without major errors")
    else:
        print("\n⚠️ SOME FIXES NEED ATTENTION")
        print("❌ Check failed tests above")

    return passed == total


if __name__ == "__main__":
    # Run the tests
    success = asyncio.run(run_all_tests())

    if success:
        print("\n💡 NEXT STEPS:")
        print("   1. Restart the backend server")
        print("   2. Monitor logs for remaining errors")
        print("   3. Verify dashboard functionality")
        print("   4. Check recent trades display")
    else:
        print("\n🔧 ADDITIONAL FIXES NEEDED:")
        print("   1. Review failed test outputs above")
        print("   2. Fix remaining issues")
        print("   3. Re-run this test script")
