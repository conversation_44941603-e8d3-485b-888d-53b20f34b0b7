import pandas as pd
import numpy as np

# --- Moving Averages ---
def calculate_sma(prices: list, period: int) -> float:
    """
    Calculates Simple Moving Average (SMA).
    """
    if len(prices) < period:
        return np.nan
    return float(np.mean(prices[-period:]))

def calculate_ema(prices: list, period: int) -> float:
    """
    Calculates Exponential Moving Average (EMA).
    """
    if len(prices) < period:
        return np.nan
    # Using pandas for EMA calculation for simplicity and accuracy
    s = pd.Series(prices)
    ema = s.ewm(span=period, adjust=False).mean().iloc[-1]
    return ema

# --- Relative Strength Index (RSI) ---
def calculate_rsi(prices: list, period: int = 14) -> float:
    """
    Calculates the Relative Strength Index (RSI).
    """
    if len(prices) < period + 1:
        return np.nan

    deltas = np.diff(prices)
    gains = deltas[(deltas > 0)]
    losses = -deltas[(deltas < 0)]

    avg_gain = np.mean(gains[:period]) if len(gains[:period]) > 0 else 0
    avg_loss = np.mean(losses[:period]) if len(losses[:period]) > 0 else 0

    # Initial RS
    rs = avg_gain / avg_loss if avg_loss != 0 else (avg_gain / 1e-10 if avg_gain != 0 else 0)
    rsi = 100 - (100 / (1 + rs))

    # Subsequent RS (Wilder's smoothing)
    for i in range(period, len(deltas)):
        gain = deltas[i] if deltas[i] > 0 else 0
        loss = -deltas[i] if deltas[i] < 0 else 0

        avg_gain = (avg_gain * (period - 1) + gain) / period
        avg_loss = (avg_loss * (period - 1) + loss) / period

        rs = avg_gain / avg_loss if avg_loss != 0 else (avg_gain / 1e-10 if avg_gain != 0 else 0)
        rsi = 100 - (100 / (1 + rs))

    return float(rsi)

# --- Moving Average Convergence Divergence (MACD) ---
def calculate_macd(prices: list, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> tuple:
    """
    Calculates MACD and MACD Signal Line.
    Returns (macd, signal_line).
    """
    if len(prices) < slow_period + signal_period:
        return np.nan, np.nan

    ema_fast = pd.Series(prices).ewm(span=fast_period, adjust=False).mean()
    ema_slow = pd.Series(prices).ewm(span=slow_period, adjust=False).mean()

    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

    return macd_line.iloc[-1], signal_line.iloc[-1]

def calculate_breakout_level(prices: list, period: int = 20) -> float:
    """
    Calculates the breakout level (highest high in the period).
    """
    if len(prices) < period:
        return np.nan
    return float(np.max(prices[-period:]))

# --- Bollinger Bands ---
def calculate_bollinger_bands(prices: list, period: int = 20, num_std_dev: int = 2) -> tuple:
    """
    Calculates Bollinger Bands (Middle, Upper, Lower).
    Returns (middle_band, upper_band, lower_band).
    """
    if len(prices) < period:
        return np.nan, np.nan, np.nan

    middle_band = pd.Series(prices).rolling(window=period).mean().iloc[-1]
    std_dev = pd.Series(prices).rolling(window=period).std().iloc[-1]

    upper_band = middle_band + (std_dev * num_std_dev)
    lower_band = middle_band - (std_dev * num_std_dev)

    return middle_band, upper_band, lower_band

# --- Volume Oscillator ---
def calculate_volume_oscillator(volumes: list, short_period: int = 14, long_period: int = 28) -> float:
    """
    Calculates the Volume Oscillator.
    """
    if len(volumes) < long_period:
        return np.nan

    short_ma = pd.Series(volumes).rolling(window=short_period).mean().iloc[-1]
    long_ma = pd.Series(volumes).rolling(window=long_period).mean().iloc[-1]

    if long_ma == 0:
        return 0.0

    volume_oscillator = ((short_ma - long_ma) / long_ma) * 100
    return float(volume_oscillator)

# --- Stochastic Oscillator ---
def calculate_stochastic_oscillator(high_prices: list, low_prices: list, close_prices: list, k_period: int = 14, d_period: int = 3) -> tuple:
    """
    Calculates the Stochastic Oscillator (%K and %D).
    Returns (%K, %D).
    """
    if len(close_prices) < k_period:
        return np.nan, np.nan

    lowest_low = pd.Series(low_prices).rolling(window=k_period).min().iloc[-1]
    highest_high = pd.Series(high_prices).rolling(window=k_period).max().iloc[-1]
    current_close = close_prices[-1]

    if (highest_high - lowest_low) == 0:
        percent_k = 0.0
    else:
        percent_k = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100

    # Calculate %D (3-period SMA of %K)
    k_values = []
    for i in range(len(close_prices) - k_period + 1):
        ll = min(low_prices[i:i+k_period])
        hh = max(high_prices[i:i+k_period])
        cc = close_prices[i+k_period-1]
        if (hh - ll) == 0:
            k_values.append(0.0)
        else:
            k_values.append(((cc - ll) / (hh - ll)) * 100)

    if len(k_values) < d_period:
        percent_d = np.nan
    else:
        percent_d = pd.Series(k_values).rolling(window=d_period).mean().iloc[-1]

    return float(percent_k), float(percent_d)

# --- Ichimoku Cloud ---
def calculate_ichimoku_cloud(high_prices: list, low_prices: list, close_prices: list,
                             tenkan_period: int = 9, kijun_period: int = 26, senkou_period: int = 52) -> dict:
    """
    Calculates Ichimoku Cloud components.
    Returns a dictionary with Tenkan-sen, Kijun-sen, Senkou Span A, Senkou Span B, and Chikou Span.
    """
    if len(close_prices) < senkou_period + kijun_period: # Need enough data for all components
        return {"tenkan_sen": np.nan, "kijun_sen": np.nan, "senkou_span_a": np.nan,
                "senkou_span_b": np.nan, "chikou_span": np.nan}

    # Tenkan-sen (Conversion Line): (Highest High + Lowest Low) / 2 over the last 9 periods
    tenkan_sen = (pd.Series(high_prices).rolling(window=tenkan_period).max() +
                  pd.Series(low_prices).rolling(window=tenkan_period).min()) / 2

    # Kijun-sen (Base Line): (Highest High + Lowest Low) / 2 over the last 26 periods
    kijun_sen = (pd.Series(high_prices).rolling(window=kijun_period).max() +
                 pd.Series(low_prices).rolling(window=kijun_period).min()) / 2

    # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2 projected 26 periods ahead
    senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(kijun_period)

    # Senkou Span B (Leading Span B): (Highest High + Lowest Low) / 2 over the last 52 periods projected 26 periods ahead
    senkou_span_b = ((pd.Series(high_prices).rolling(window=senkou_period).max() +
                      pd.Series(low_prices).rolling(window=senkou_period).min()) / 2).shift(kijun_period)

    # Chikou Span (Lagging Span): Closing price projected 26 periods back
    chikou_span = pd.Series(close_prices).shift(-kijun_period)

    return {
        "tenkan_sen": float(tenkan_sen.iloc[-1]),
        "kijun_sen": float(kijun_sen.iloc[-1]),
        "senkou_span_a": float(senkou_span_a.iloc[-1]),
        "senkou_span_b": float(senkou_span_b.iloc[-1]),
        "chikou_span": float(chikou_span.iloc[-1]) if pd.notna(chikou_span.iloc[-1]) else np.nan
    }

# --- Parabolic SAR ---
def calculate_parabolic_sar(high_prices: list, low_prices: list, close_prices: list,
                            acceleration_factor: float = 0.02, max_acceleration_factor: float = 0.2) -> list:
    """
    Calculates Parabolic SAR values.
    """
    sar = [np.nan] * len(close_prices)
    if len(close_prices) < 2:
        return sar

    # Initial SAR and EP (Extreme Point)
    if close_prices[1] > close_prices[0]:
        sar[1] = low_prices[0]
        ep = high_prices[1]
        trend = 1  # Uptrend
    else:
        sar[1] = high_prices[0]
        ep = low_prices[1]
        trend = -1 # Downtrend

    af = acceleration_factor

    for i in range(2, len(close_prices)):
        current_high = high_prices[i]
        current_low = low_prices[i]
        current_close = close_prices[i]

        prev_sar = sar[i-1]
        prev_ep = ep

        if trend == 1:  # Uptrend
            calculated_sar = prev_sar + af * (prev_ep - prev_sar)
            sar[i] = min(calculated_sar, low_prices[i-1], low_prices[i-2]) if i > 1 else calculated_sar

            if current_high > prev_ep:
                ep = current_high
                af = min(af + acceleration_factor, max_acceleration_factor)

            if current_low < sar[i]: # Trend reversal
                trend = -1
                sar[i] = prev_ep
                ep = current_low
                af = acceleration_factor

        else:  # Downtrend
            calculated_sar = prev_sar - af * (prev_sar - prev_ep)
            sar[i] = max(calculated_sar, high_prices[i-1], high_prices[i-2]) if i > 1 else calculated_sar

            if current_low < prev_ep:
                ep = current_low
                af = min(af + acceleration_factor, max_acceleration_factor)

            if current_high > sar[i]: # Trend reversal
                trend = 1
                sar[i] = prev_ep
                ep = current_high
                af = acceleration_factor

    return [float(s) if pd.notna(s) else np.nan for s in sar]

# --- Fibonacci Retracement ---
def calculate_fibonacci_retracement(high_price: float, low_price: float) -> dict:
    """
    Calculates Fibonacci Retracement levels.
    """
    diff = high_price - low_price
    levels = {
        "0%": high_price,
        "23.6%": high_price - 0.236 * diff,
        "38.2%": high_price - 0.382 * diff,
        "50%": high_price - 0.5 * diff,
        "61.8%": high_price - 0.618 * diff,
        "78.6%": high_price - 0.786 * diff,
        "100%": low_price
    }
    return {k: float(v) for k, v in levels.items()}

# --- ATR (Average True Range) ---
def calculate_atr(high_prices: list, low_prices: list, close_prices: list, period: int = 14) -> float:
    """
    Calculates the Average True Range (ATR).
    """
    if len(close_prices) < period:
        return np.nan

    tr_values = []
    for i in range(1, len(close_prices)):
        tr1 = high_prices[i] - low_prices[i]
        tr2 = abs(high_prices[i] - close_prices[i-1])
        tr3 = abs(low_prices[i] - close_prices[i-1])
        tr_values.append(max(tr1, tr2, tr3))

    atr = pd.Series(tr_values).rolling(window=period).mean().iloc[-1]
    return float(atr)
