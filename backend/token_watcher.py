# backend/token_watcher.py

from utils.api_client import get
import json
import logging
from typing import List, Dict
import os
import requests

from kucoin_data import fetch_kucoin_listed_tokens
from sentiment_engine import get_sentiment_score  # Update to the correct function name if needed

COINGECKO_API = "https://api.coingecko.com/api/v3/coins/list"
WATCHLIST_FILE = "backend/data/auto_watchlist.json"

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def fetch_new_tokens() -> List[Dict]:
    """Fetch the full list of tokens from CoinGecko."""
    try:
        response = get(COINGECKO_API, cache_ttl=86400) # Cache CoinGecko token list for 24 hours
        if response.status_code == 200:
            return response.json()
        logger.warning(f"Failed to fetch tokens. Status code: {response.status_code}")
    except requests.RequestException as e:
        logger.error(f"Request to CoinGecko failed: {e}")
    return []

def filter_kucoin_tokens(token_list: List[Dict], kucoin_tokens: List[Dict]) -> List[Dict]:
    """Return only tokens that are also listed on KuCoin."""
    kucoin_symbols = [t['token'].lower() for t in kucoin_tokens]
    return [t for t in token_list if t['symbol'].lower() in kucoin_symbols]

def update_watchlist(new_entries: List[Dict]) -> None:
    """Append new tokens with positive sentiment to the persistent watchlist."""
    os.makedirs("backend/data", exist_ok=True)
    if os.path.exists(WATCHLIST_FILE):
        with open(WATCHLIST_FILE, "r") as f:
            existing = json.load(f)
    else:
        existing = []

    combined = {entry['id']: entry for entry in existing + new_entries}
    with open(WATCHLIST_FILE, "w") as f:
        json.dump(list(combined.values()), f, indent=2)

def run_token_watch() -> None:
    """Main token watcher logic to fetch, filter, score, and save token sentiment data."""
    logger.info("👁️ Scanning for new tokens listed on CoinGecko...")
    all_tokens = fetch_new_tokens()
    kucoin_tokens = fetch_kucoin_listed_tokens()
    tradable = filter_kucoin_tokens(all_tokens, kucoin_tokens)

    scored_tokens = []
    for token in tradable:
        name = token['name']
        # Fetch or construct news_data for the token; replace the following line with actual news data fetching logic as needed
        news_data = []  # Example: fetch_news_for_token(name)
        sentiment = get_sentiment_score(name)
        if sentiment > 0:
            token_entry = {
                "id": token['id'],
                "symbol": token['symbol'],
                "name": name,
                "sentiment": sentiment
            }
            logger.info(f"🧠 {name}: Sentiment Score = {sentiment}")
            scored_tokens.append(token_entry)

    update_watchlist(scored_tokens)
    logger.info(f"✅ Watchlist updated with {len(scored_tokens)} tokens.")
