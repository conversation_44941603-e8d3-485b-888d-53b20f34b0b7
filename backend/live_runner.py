"""
live_runner.py
---------------
This file is the heart of the AlphaPredatorBot's real-time trading system.

It:
1. Selects the top tokens using multi-factor scoring.
2. Builds structured prompts for AI evaluation.
3. Gets BUY/SELL/HOLD signals from the AI validation engine.
4. Executes paper/live trades via the trading engine.
5. Sends interactive Telegram alerts with decision details.

Runs continuously in a timed loop for real-time market reaction.
"""

import time
import logging
import asyncio
import aiohttp
from datetime import datetime
from typing import List, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

# Enhanced imports with proper error handling
try:
    from token_selector import get_top_tokens_for_trading
except ImportError:
    from token_selector import get_top_tokens as get_top_tokens_for_trading

try:
    from prompt_builder import build_trade_prompt
except ImportError:

    def build_trade_prompt(
        token: str,
        news_data: str = "",
        price_change_24h: Optional[float] = None,
        news_snippets: Optional[List[str]] = None,
        short_ma: Optional[float] = None,
        long_ma: Optional[float] = None,
        rsi: Optional[float] = None,
        macd: Optional[float] = None,
        macd_signal: Optional[float] = None,
        price: Optional[float] = None,
        upper_band: Optional[float] = None,
        lower_band: Optional[float] = None,
        breakout_level: Optional[float] = None,
        volume_oscillator: Optional[float] = None,
        stochastic_k: Optional[float] = None,
        stochastic_d: Optional[float] = None,
        ichimoku_tenkan: Optional[float] = None,
        ichimoku_kijun: Optional[float] = None,
        ichimoku_senkou_a: Optional[float] = None,
        ichimoku_senkou_b: Optional[float] = None,
        ichimoku_chikou: Optional[float] = None,
        fib_levels: Optional[dict] = None,
        parabolic_sar: Optional[float] = None,
        atr: Optional[float] = None,
    ) -> str:
        return "Enhanced trading prompt for high-frequency analysis"


try:
    from ai_clients.ai_request_manager import get_ai_decisions_parallel
except ImportError:
    from ai_validation_engine import get_final_ai_decision as get_ai_decisions_parallel

try:
    from trade_executor import execute_trade, execute_batch_trades
except ImportError:
    from trade_engine import trade_token as execute_trade

    def execute_batch_trades(
        trade_requests: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """Fallback batch trade execution."""
        return [execute_trade(**trade) for trade in trade_requests]


try:
    from telegram_utils import notify_trade
except ImportError:

    async def notify_trade(
        application,
        token: str,
        action: str,
        quantity: float,
        price: float,
        strategy: str,
        reason: str,
    ) -> bool:
        return True


try:
    from kucoin_data import fetch_kucoin_candlestick_data
except ImportError:

    async def fetch_kucoin_candlestick_data(
        symbol: str, interval: str = "1hour", limit: int = 90
    ) -> List[List[Any]]:
        return []


try:
    from indicators import (
        calculate_sma,
        calculate_rsi,
        calculate_macd,
        calculate_bollinger_bands,
    )
except ImportError:
    # Fallback indicator functions
    def calculate_sma(prices: list, period: int) -> float:
        return prices[-1] if prices else 0.0

    def calculate_rsi(prices: list, period: int = 14) -> float:
        return 50.0

    def calculate_macd(
        prices: list,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
    ) -> tuple:
        return (0.0, 0.0)

    def calculate_bollinger_bands(
        prices: list, period: int = 20, num_std_dev: int = 2
    ) -> tuple:
        return (0.0, 0.0, 0.0)


from config import TRADING_MODE, MAX_BUY

# Enhanced configuration for high-frequency trading
TRADING_INTERVAL = 60  # ⚡ Reduced to 1 minute for high-frequency
FAST_INTERVAL = 30  # 30 seconds for rapid opportunities
MAX_TOKENS = 50  # 🚀 INCREASED: Monitor up to 50 tokens for better opportunities
BATCH_SIZE = 20  # 🚀 INCREASED: Process 20 tokens in parallel batches
MAX_CONCURRENT_TRADES = 10  # 🚀 INCREASED: Allow up to 10 concurrent trades

# Performance optimization settings
CONNECTION_POOL_SIZE = 100
CONNECTION_TIMEOUT = 10
REQUEST_TIMEOUT = 30


@dataclass
class OptimizedTradeRequest:
    """Optimized trade request structure."""

    token_symbol: str
    action: str
    side: str
    price: float
    amount_usd: float
    strategy: str
    reason: str
    confidence: float
    timestamp: float


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("live_runner")

# --- State Management for the Bot ---
bot_task: Optional[asyncio.Task] = None
is_running = False
# ---


async def send_telegram_alert(
    application, token: str, trade_result: dict, decision: str
) -> None:
    """
    Sends the executed trade alert to Telegram.
    """
    try:
        price = trade_result.get("price", 0.0)
        quantity = trade_result.get("quantity", 0.0)
        try:
            amount = float(quantity)
        except (TypeError, ValueError):
            amount = 0.0
        strategy = "AI Decision"
        reason = trade_result.get("reason", "Live signal")
        await notify_trade(
            application, token, decision, float(price), float(amount), strategy, reason
        )
    except Exception as e:
        logger.warning(f"⚠️ Telegram alert failed for {token}: {e}")


async def run_live_cycle() -> None:
    """🎯 ALPHA PREDATOR LIVE TRADING CYCLE - Complete Flow Implementation"""
    cycle_start = time.time()
    logger.info(f"🚀 ALPHA PREDATOR LIVE CYCLE INITIATED at {datetime.now()}")

    try:
        # Execute the complete Alpha Predator trading flow
        from token_selector import alpha_predator_trading_flow

        flow_result = await alpha_predator_trading_flow()

        if flow_result.get("status") == "success":
            logger.info("✅ ALPHA PREDATOR FLOW COMPLETED SUCCESSFULLY")
            logger.info(f"📊 Flow Summary:")
            logger.info(
                f"   • KuCoin tokens fetched: {flow_result.get('kucoin_tokens_fetched', 0)}"
            )
            logger.info(
                f"   • Tokens selected: {flow_result.get('tokens_selected', 0)}"
            )
            logger.info(
                f"   • Tokens analyzed: {flow_result.get('tokens_analyzed', 0)}"
            )
            logger.info(
                f"   • Trading decisions: {flow_result.get('trading_decisions', 0)}"
            )
            logger.info(
                f"   • Trades executed: {flow_result.get('trades_executed', 0)}"
            )

            # Process execution results for portfolio management
            execution_results = flow_result.get("execution_results", [])
            await _process_execution_results(execution_results)

        else:
            logger.error(
                f"❌ ALPHA PREDATOR FLOW FAILED: {flow_result.get('error', 'Unknown error')}"
            )

        cycle_duration = time.time() - cycle_start
        logger.info(f"⏱️ Alpha Predator cycle completed in {cycle_duration:.2f}s")

    except Exception as e:
        logger.error(f"❌ Alpha Predator live cycle failed: {e}")
        cycle_duration = time.time() - cycle_start
        logger.info(f"⏱️ Failed cycle duration: {cycle_duration:.2f}s")


async def _process_execution_results(execution_results: List[Dict]) -> None:
    """Process trade execution results for portfolio management"""
    try:
        logger.info(f"📊 Processing {len(execution_results)} execution results...")

        successful_trades = []
        failed_trades = []

        for result in execution_results:
            if result.get("status") == "success":
                successful_trades.append(result)

                # Log successful trade
                symbol = result.get("symbol", "")
                amount = result.get("amount", 0)
                logger.info(f"✅ Trade logged: {symbol} - ${amount}")

                # Update portfolio
                await _update_portfolio_with_trade(result)

                # Set up profit-taking strategy (10% portfolio selling)
                await _setup_profit_taking_strategy(result)

            else:
                failed_trades.append(result)
                logger.warning(
                    f"❌ Failed trade: {result.get('symbol', 'UNKNOWN')} - {result.get('error', 'Unknown error')}"
                )

        # Update trading statistics
        await _update_trading_statistics(successful_trades, failed_trades)

        logger.info(
            f"📈 Portfolio processing complete: {len(successful_trades)} successful, {len(failed_trades)} failed"
        )

    except Exception as e:
        logger.error(f"❌ Portfolio processing failed: {e}")


async def _update_portfolio_with_trade(trade_result: Dict) -> None:
    """Update portfolio with new trade"""
    try:
        # Implementation for portfolio updates
        # This would integrate with your existing portfolio management
        pass
    except Exception as e:
        logger.error(f"Portfolio update failed: {e}")


async def _setup_profit_taking_strategy(trade_result: Dict) -> None:
    """Set up 10% portfolio profit-taking strategy"""
    try:
        symbol = trade_result.get("symbol", "")
        amount = trade_result.get("amount", 0)

        # Calculate 10% selling strategy
        # This implements the "sell 10% of portfolio every time there's profit" logic
        profit_threshold = 0.05  # 5% profit trigger
        sell_percentage = 0.10  # Sell 10% of position

        logger.info(
            f"📈 Setting up profit-taking for {symbol}: {sell_percentage*100}% at {profit_threshold*100}% profit"
        )

        # This would integrate with your existing trading engine
        # to set up automated profit-taking orders

    except Exception as e:
        logger.error(f"Profit-taking setup failed: {e}")


async def _update_trading_statistics(
    successful_trades: List[Dict], failed_trades: List[Dict]
) -> None:
    """Update trading statistics and performance metrics"""
    try:
        total_trades = len(successful_trades) + len(failed_trades)
        success_rate = len(successful_trades) / total_trades if total_trades > 0 else 0

        logger.info(f"📊 Trading Statistics:")
        logger.info(f"   • Total trades: {total_trades}")
        logger.info(f"   • Successful: {len(successful_trades)}")
        logger.info(f"   • Failed: {len(failed_trades)}")
        logger.info(f"   • Success rate: {success_rate:.2%}")

    except Exception as e:
        logger.error(f"Statistics update failed: {e}")


# Legacy function cleanup - remove old trading logic
async def run_legacy_cycle():
    """Legacy trading cycle - deprecated, use run_live_cycle() instead"""
    logger.warning(
        "⚠️ Legacy cycle called - use run_live_cycle() for Alpha Predator flow"
    )
    await run_live_cycle()


# Enhanced technical analysis functions - removed duplicate calculate_rsi


def analyze_token_for_trading(token_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Analyze a single token and return trade request if applicable."""
    try:
        token = (
            token_info.get("symbol")
            if isinstance(token_info, dict)
            else str(token_info)
        )
        if not isinstance(token, str) or not token:
            logger.warning(f"⚠️ Skipping invalid token: {token}")
            return None

        # Get current price
        price = token_info.get("price", 0.0)
        if price <= 0:
            logger.warning(f"⚠️ Invalid price for {token}: {price}")
            return None

        # Fetch historical data for indicator calculation (synchronous version)
        try:
            # Use asyncio.run for sync context or implement sync version
            try:
                candlestick_data = asyncio.run(
                    fetch_kucoin_candlestick_data(token, interval="1hour", limit=90)
                )
            except Exception:
                # Fallback to empty data if async fails
                candlestick_data = []

            if not candlestick_data:
                logger.warning(
                    f"⚠️ No candlestick data for {token}. Using basic analysis."
                )
                # Use basic analysis without indicators
                return analyze_token_basic(token_info)

            # Extract prices for indicator calculations
            closes = [float(entry[2]) for entry in candlestick_data]
            highs = [float(entry[3]) for entry in candlestick_data]
            lows = [float(entry[4]) for entry in candlestick_data]
            volumes = [float(entry[5]) for entry in candlestick_data]

            # Calculate key indicators efficiently
            short_ma = calculate_sma(closes, 10)
            long_ma = calculate_sma(closes, 50)
            rsi = calculate_rsi(closes)
            macd, macd_signal = calculate_macd(closes)
            middle_band, upper_band, lower_band = calculate_bollinger_bands(closes)

            # Build enhanced prompt with indicators
            prompt = build_enhanced_trading_prompt(
                token,
                price,
                closes[-1],
                short_ma,
                long_ma,
                rsi,
                macd,
                macd_signal,
                upper_band,
                lower_band,
                token_info,
            )

        except Exception as e:
            logger.debug(f"Indicator calculation failed for {token}: {e}")
            # Fallback to basic analysis
            return analyze_token_basic(token_info)

        # Get AI decision using parallel processing
        try:
            # Try to use the parallel AI system
            try:
                from ai_clients.ai_request_manager import (
                    get_ai_decisions_parallel as parallel_ai,
                )

                decision_result = parallel_ai(prompt, token)
            except ImportError:
                # Fallback to basic AI decision
                decision_result = {
                    "decision": "HOLD",
                    "confidence": 0.5,
                    "reasoning": "Fallback decision",
                }

            decision = decision_result.get("decision", "HOLD").upper()
            confidence = decision_result.get("confidence", 0.0)

            # Only proceed with high-confidence decisions
            if confidence < 0.6:
                logger.debug(f"Low confidence decision for {token}: {confidence}")
                return None

        except Exception as e:
            logger.error(f"AI decision failed for {token}: {e}")
            return None

        # Create trade request
        if decision in ["BUY", "SELL"]:
            return {
                "token_symbol": token,
                "side": decision,
                "price": price,
                "amount_usd": MAX_BUY,
                "strategy": "Enhanced AI",
                "reason": f"AI decision with {confidence:.2f} confidence",
                "action": decision,
            }

        return None

    except Exception as e:
        logger.error(f"Token analysis failed for {token_info}: {e}")
        return None


def analyze_token_basic(token_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Basic token analysis without complex indicators."""
    try:
        token = token_info.get("symbol", "")
        price = token_info.get("price", 0.0)
        volume = token_info.get("volume", 0)
        change_rate = token_info.get("change_rate", 0)
        score = token_info.get("score", 0)

        # Simple decision logic based on token metrics
        if score > 1.0 and change_rate > 0.02 and volume > 1000000:
            return {
                "token_symbol": token,
                "side": "BUY",
                "price": price,
                "amount_usd": MAX_BUY,
                "strategy": "Basic Analysis",
                "reason": f"High score ({score:.2f}) with positive momentum",
                "action": "BUY",
            }

        return None

    except Exception as e:
        logger.error(f"Basic analysis failed: {e}")
        return None


def build_enhanced_trading_prompt(
    token,
    price,
    _current_close,
    short_ma,
    long_ma,
    rsi,
    macd,
    macd_signal,
    upper_band,
    lower_band,
    token_info,
):
    """Build enhanced trading prompt with technical indicators."""
    try:
        volume = token_info.get("volume", 0)
        change_rate = token_info.get("change_rate", 0)
        sentiment = token_info.get("sentiment", 0)

        prompt = f"""
        ENHANCED TRADING ANALYSIS FOR {token}

        Current Price: ${price:.6f}
        24h Change: {change_rate:.2%}
        Volume: ${volume:,.0f}

        Technical Indicators:
        - Short MA (10): ${short_ma:.6f}
        - Long MA (50): ${long_ma:.6f}
        - RSI: {rsi:.2f}
        - MACD: {macd:.6f}
        - MACD Signal: {macd_signal:.6f}
        - Bollinger Upper: ${upper_band:.6f}
        - Bollinger Lower: ${lower_band:.6f}

        Market Sentiment: {sentiment:.2f}
        Token Score: {token_info.get('score', 0):.2f}

        Based on this analysis, should we BUY, SELL, or HOLD {token}?
        Consider: trend direction, momentum, volatility, and risk management.

        Respond with JSON: {{"decision": "BUY/SELL/HOLD", "confidence": 0.0-1.0, "reason": "explanation"}}
        """

        return prompt.strip()

    except Exception as e:
        logger.error(f"Prompt building failed: {e}")
        return f"Analyze {token} for trading decision at price ${price:.6f}"


def run_trade(symbol: str, action: str, price: float, amount: float) -> dict:
    """Execute a trade with the given parameters."""
    try:
        # Use basic parameters that should work with most trade functions
        return execute_trade(symbol, action, price, amount)
    except Exception as e:
        logger.error(f"Trade execution failed for {symbol}: {e}")
        return {"success": False, "error": str(e)}


# ============================================================================
# OPTIMIZED ASYNC FUNCTIONS FOR HIGH-FREQUENCY TRADING
# ============================================================================


async def analyze_token_async_optimized(
    token_info: Dict[str, Any], session: aiohttp.ClientSession
) -> Optional[OptimizedTradeRequest]:
    """
    Ultra-optimized async token analysis with connection pooling.
    """
    token = token_info.get("symbol", "")
    price = float(token_info.get("price", 0))

    if not token or price <= 0:
        return None

    try:
        start_time = time.time()

        # Build optimized prompt (cached if possible)
        prompt = build_optimized_prompt(token, token_info)

        # Get AI decision with async processing
        decision_result = await get_ai_decision_async(prompt, token, session)

        decision = decision_result.get("decision", "HOLD").upper()
        confidence = decision_result.get("confidence", 0.0)
        reasoning = decision_result.get("reasoning", "No reasoning provided")

        # Only return actionable trades with high confidence
        if decision in ["BUY", "SELL"] and confidence > 0.6:
            return OptimizedTradeRequest(
                token_symbol=token,
                action=decision,
                side=decision,
                price=price,
                amount_usd=float(token_info.get("amount_usd", MAX_BUY)),
                strategy="AI_OPTIMIZED",
                reason=reasoning,
                confidence=confidence,
                timestamp=time.time(),
            )

        analysis_time = time.time() - start_time
        logger.debug(
            f"⚡ {token} analyzed in {analysis_time:.3f}s: {decision} ({confidence:.2f})"
        )
        return None

    except Exception as e:
        logger.error(f"🔥 Optimized token analysis failed for {token}: {e}")
        return None


def build_optimized_prompt(token: str, token_info: Dict[str, Any]) -> str:
    """Build optimized trading prompt with minimal processing."""
    try:
        price = token_info.get("price", 0)
        volume = token_info.get("volume", 0)
        change_24h = token_info.get("volume_ratio", 0)

        # Simplified prompt for faster processing
        prompt = f"""
TOKEN: {token}
PRICE: ${price:.6f}
24H CHANGE: {change_24h:.2%}
VOLUME: ${volume:,.0f}

Quick trading decision needed: BUY, SELL, or HOLD?
Focus on: Price momentum, volume surge, risk/reward.
"""
        return prompt.strip()

    except Exception as e:
        logger.error(f"Optimized prompt building failed for {token}: {e}")
        return f"Quick analysis for {token} at ${token_info.get('price', 0):.6f}"


async def get_ai_decision_async(
    prompt: str, token: str, _session: aiohttp.ClientSession
) -> Dict[str, Any]:
    """Get AI decision with async processing and connection pooling."""
    try:
        # Use the existing AI decision system but with async wrapper
        loop = asyncio.get_event_loop()

        # Create a wrapper function to handle the different function signatures
        def ai_decision_wrapper():
            try:
                # Try the parallel AI decision first
                from ai_clients.ai_request_manager import (
                    get_ai_decisions_parallel as parallel_ai,
                )

                return parallel_ai(prompt, token)
            except Exception:
                # Fallback to basic AI decision if available
                try:
                    from ai_validation_engine import get_final_ai_decision

                    return get_final_ai_decision(prompt, [], token)
                except Exception:
                    return {
                        "decision": "HOLD",
                        "confidence": 0.5,
                        "reasoning": "AI system unavailable",
                    }

        # Run the AI decision in a thread pool to avoid blocking
        decision_result = await loop.run_in_executor(None, ai_decision_wrapper)
        return decision_result

    except Exception as e:
        logger.error(f"Async AI decision failed for {token}: {e}")
        return {
            "decision": "HOLD",
            "confidence": 0.0,
            "reasoning": f"AI error: {str(e)}",
        }


async def execute_trades_batch_optimized(
    trade_requests: List[OptimizedTradeRequest],
) -> List[Dict[str, Any]]:
    """Execute trades in optimized batches with async processing."""
    try:
        logger.info(f"🚀 Executing {len(trade_requests)} optimized trades")

        # Convert to format expected by execute_batch_trades
        trade_dicts = []
        for req in trade_requests:
            trade_dicts.append(
                {
                    "token_symbol": req.token_symbol,
                    "side": req.side,
                    "amount_usd": req.amount_usd,
                    "price": req.price,
                    "strategy": req.strategy,
                    "reason": req.reason,
                }
            )

        # Execute trades in parallel
        loop = asyncio.get_event_loop()
        results = await loop.run_in_executor(None, execute_batch_trades, trade_dicts)

        # Send notifications asynchronously
        notification_tasks = []
        for result in results:
            if result.get("success", False):
                task = send_telegram_alert(
                    None, result.get("symbol", ""), result, result.get("action", "")
                )
                notification_tasks.append(task)

        # Send all notifications concurrently
        if notification_tasks:
            await asyncio.gather(*notification_tasks, return_exceptions=True)

        return results

    except Exception as e:
        logger.error(f"🔥 Optimized batch trade execution failed: {e}")
        return []


async def run_bot_loop():
    """The main loop for the trading bot."""
    global is_running
    logger.info("🎯 Starting AlphaPredatorBot Live Mode...")
    is_running = True
    error_count = 0
    max_consecutive_errors = 5

    while is_running:
        try:
            logger.info("🏃‍♂️ Starting new live cycle...")
            await run_live_cycle()
            logger.info(
                f"✅ Live cycle finished. Waiting for {TRADING_INTERVAL} seconds..."
            )
            error_count = 0  # Reset error count on successful cycle
            await asyncio.sleep(TRADING_INTERVAL)
        except asyncio.CancelledError:
            logger.info("🛑 Bot loop cancelled.")
            break
        except Exception as e:
            error_count += 1
            logger.exception(
                f"⚠️ Error in live cycle (attempt {error_count}/{max_consecutive_errors}): {e}"
            )

            # If too many consecutive errors, implement circuit breaker with recovery
            if error_count >= max_consecutive_errors:
                logger.error(
                    f"🔥 CIRCUIT BREAKER: {max_consecutive_errors} consecutive errors. Entering recovery mode..."
                )
                # Instead of stopping permanently, wait longer and reset error count
                await asyncio.sleep(300)  # Wait 5 minutes
                error_count = 0  # Reset error count for recovery attempt
                logger.info(
                    "🔄 Circuit breaker recovery: Resetting error count and continuing..."
                )

            # Attempt to send Telegram alert for critical errors
            try:
                # TODO: Replace with actual telegram app instance if available
                telegram_app_instance = None  # Set to the correct instance if available
                if telegram_app_instance:
                    await notify_trade(
                        telegram_app_instance,
                        "SYSTEM",
                        "ERROR",
                        0.0,
                        0.0,
                        "System Error",
                        f"Error in Alpha Bot live cycle (attempt {error_count}): {e}",
                    )
            except Exception as telegram_error:
                logger.warning(f"Failed to send Telegram alert: {telegram_error}")

            # Progressive backoff: wait longer after each error
            backoff_time = min(5 * error_count, 30)  # Max 30 seconds
            logger.info(f"⏳ Waiting {backoff_time} seconds before retry...")
            await asyncio.sleep(backoff_time)

    logger.info("👋 AlphaPredatorBot has stopped.")
    is_running = False


async def start_alpha_bot():
    """Starts the Alpha Predator bot if it's not already running."""
    global bot_task, is_running
    if is_running:
        logger.warning("⚠️ Alpha bot is already running.")
        return {"status": "warning", "message": "Alpha bot is already running."}

    # Using asyncio.create_task to run the bot loop in the background
    bot_task = asyncio.create_task(run_bot_loop())
    return {"status": "success", "message": "Alpha Predator bot started."}


async def stop_alpha_bot():
    """Stops the Alpha Predator bot if it's running."""
    global bot_task, is_running
    if not is_running or bot_task is None:
        logger.warning("⚠️ Alpha bot is not running.")
        return {"status": "warning", "message": "Alpha bot is not running."}

    is_running = False
    bot_task.cancel()
    try:
        await bot_task
    except asyncio.CancelledError:
        logger.info("Bot task successfully cancelled.")

    bot_task = None
    return {"status": "success", "message": "Alpha Predator bot stopped."}


def get_alpha_bot_status():
    """Gets the current status of the Alpha Predator bot."""
    global is_running, bot_task
    status = "running" if is_running and bot_task and not bot_task.done() else "stopped"
    return {"status": status}
