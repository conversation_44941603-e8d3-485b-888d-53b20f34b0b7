
# AI Confidence Configuration
DEFAULT_CONFIDENCE_THRESHOLD = 0.3  # Lowered from 0.7 to 0.3
FALLBACK_CONFIDENCE = 0.5  # Default confidence when AI fails

def calculate_confidence(ai_response: dict, technical_signals: dict) -> float:
    """Calculate confidence score based on AI response and technical signals"""
    try:
        base_confidence = FALLBACK_CONFIDENCE
        
        # If AI provided a response, increase confidence
        if ai_response and ai_response.get("decision"):
            base_confidence += 0.2
            
        # Technical signal alignment
        if technical_signals:
            signal_count = len([s for s in technical_signals.values() if s in ["BUY", "SELL"]])
            if signal_count >= 3:
                base_confidence += 0.2
                
        return min(base_confidence, 1.0)
        
    except Exception:
        return FALLBACK_CONFIDENCE
