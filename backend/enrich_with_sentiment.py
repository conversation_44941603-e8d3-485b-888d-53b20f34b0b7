import json
import logging
from pathlib import Path
# Update the import path below if 'classifier.py' is in the same directory or adjust as needed
try:
    from news_classifier import classify_sentiment
except ModuleNotFoundError:
    from news_classifier import classify_sentiment
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("sentiment_enricher")

DATA_PATH = Path("backend/data")
NEWS_FILE = DATA_PATH / "news.json"
OUTPUT_FILE = DATA_PATH / "news_with_sentiment.json"

def enrich_articles_with_sentiment():
    """
    Loads articles from a JSON file, classifies sentiment for each title,
    and saves the enriched results to a new file.
    """
    try:
        with NEWS_FILE.open("r", encoding="utf-8") as f:
            articles = json.load(f)
    except Exception as e:
        logger.error(f"❌ Failed to load news.json: {e}")
        return

    for article in articles:
        sentiment = classify_sentiment(article.get("title", ""))
        article["sentiment"] = sentiment
        logger.info(f"[{sentiment.upper()}] {article.get('title')}")

    try:
        with OUTPUT_FILE.open("w", encoding="utf-8") as f:
            json.dump(articles, f, indent=2)
        logger.info("✅ Saved enriched articles to news_with_sentiment.json")
    except Exception as e:
        logger.error(f"❌ Failed to write output file: {e}")

if __name__ == "__main__":
    enrich_articles_with_sentiment()
