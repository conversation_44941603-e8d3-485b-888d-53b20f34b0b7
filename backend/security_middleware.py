"""
Security Middleware for AlphaPredatorBot
Implements comprehensive security measures including rate limiting,
input validation, CSRF protection, and security headers.
"""

import time
import hashlib
import secrets
from typing import Dict, List, Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from collections import defaultdict, deque
import logging

logger = logging.getLogger("security")

class SecurityMiddleware:
    def __init__(self):
        # Rate limiting storage
        self.rate_limit_storage: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Dict[str, float] = {}
        
        # CSRF tokens
        self.csrf_tokens: Dict[str, float] = {}
        
        # Security configuration
        self.rate_limit_requests = 100  # requests per window
        self.rate_limit_window = 60     # seconds
        self.block_duration = 300       # 5 minutes
        self.csrf_token_expiry = 3600   # 1 hour
        
        # Suspicious patterns
        self.suspicious_patterns = [
            "script", "javascript:", "onload", "onerror", "eval(",
            "document.cookie", "window.location", "alert(",
            "../", "..\\", "/etc/passwd", "cmd.exe", "powershell",
            "union select", "drop table", "insert into", "delete from",
            "exec(", "system(", "__import__", "subprocess"
        ]

    def get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers"""
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection IP
        return request.client.host if request.client else "unknown"

    def is_rate_limited(self, client_ip: str) -> bool:
        """Check if client IP is rate limited"""
        current_time = time.time()
        
        # Check if IP is currently blocked
        if client_ip in self.blocked_ips:
            if current_time < self.blocked_ips[client_ip]:
                return True
            else:
                # Block expired, remove it
                del self.blocked_ips[client_ip]
        
        # Clean old requests from the deque
        requests = self.rate_limit_storage[client_ip]
        while requests and requests[0] < current_time - self.rate_limit_window:
            requests.popleft()
        
        # Check if rate limit exceeded
        if len(requests) >= self.rate_limit_requests:
            # Block the IP
            self.blocked_ips[client_ip] = current_time + self.block_duration
            logger.warning(f"Rate limit exceeded for IP {client_ip}. Blocking for {self.block_duration} seconds.")
            return True
        
        # Add current request
        requests.append(current_time)
        return False

    def validate_input(self, data: str) -> bool:
        """Validate input for suspicious patterns"""
        if not isinstance(data, str):
            return True
        
        data_lower = data.lower()
        for pattern in self.suspicious_patterns:
            if pattern in data_lower:
                logger.warning(f"Suspicious pattern detected: {pattern}")
                return False
        
        return True

    def generate_csrf_token(self) -> str:
        """Generate a new CSRF token"""
        token = secrets.token_urlsafe(32)
        self.csrf_tokens[token] = time.time() + self.csrf_token_expiry
        return token

    def validate_csrf_token(self, token: str) -> bool:
        """Validate CSRF token"""
        if not token or token not in self.csrf_tokens:
            return False
        
        current_time = time.time()
        if current_time > self.csrf_tokens[token]:
            # Token expired
            del self.csrf_tokens[token]
            return False
        
        return True

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers to add to responses"""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }

    async def __call__(self, request: Request, call_next):
        """Main middleware function"""
        client_ip = self.get_client_ip(request)
        
        # Rate limiting check
        if self.is_rate_limited(client_ip):
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={"detail": "Rate limit exceeded. Please try again later."},
                headers=self.get_security_headers()
            )
        
        # Input validation for POST requests
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                # Read body for validation
                body = await request.body()
                if body:
                    body_str = body.decode('utf-8')
                    if not self.validate_input(body_str):
                        logger.warning(f"Malicious input detected from IP {client_ip}")
                        return JSONResponse(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            content={"detail": "Invalid input detected"},
                            headers=self.get_security_headers()
                        )
                
                # Note: Request body has been validated, continue with original request
                pass
                
            except Exception as e:
                logger.error(f"Error validating input: {e}")
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        for header, value in self.get_security_headers().items():
            response.headers[header] = value
        
        return response

# Global security middleware instance
security_middleware = SecurityMiddleware()
