#!/usr/bin/env python3
"""
⚡ System Performance Optimizer
Reduces API calls and improves response times
"""

import asyncio
import logging
from typing import Dict, Any
import time

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """System performance optimizer"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def cache_result(self, key: str, data: Any, ttl: int = None):
        """Cache API result with TTL"""
        ttl = ttl or self.cache_ttl
        self.cache[key] = {
            'data': data,
            'expires': time.time() + ttl
        }
        
        # Cleanup old entries periodically
        if time.time() - self.last_cleanup > 300:  # Every 5 minutes
            self.cleanup_cache()
    
    def get_cached_result(self, key: str) -> Any:
        """Get cached result if still valid"""
        if key in self.cache:
            entry = self.cache[key]
            if time.time() < entry['expires']:
                return entry['data']
            else:
                del self.cache[key]
        return None
    
    def cleanup_cache(self):
        """Remove expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time >= entry['expires']
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        self.last_cleanup = current_time
        logger.info(f"🧹 Cache cleanup: removed {len(expired_keys)} expired entries")

# Global optimizer instance
performance_optimizer = PerformanceOptimizer()
