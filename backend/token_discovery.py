# backend/token_discovery.py

import time
import logging
from typing import List, Dict
from kucoin_data import fetch_all_kucoin_tokens

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

async def get_discover_tokens(limit: int = 10) -> List[Dict[str, str]]:
    """
    Fetches and filters KuCoin tokens by 24h volume, returning top tokens with reasons.

    Args:
        limit (int): Number of top tokens to return.

    Returns:
        List[Dict[str, str]]: A list of tokens with volume-based discovery reasoning.
    """
    logger.info("🟡 STARTED get_discover_tokens()")
    try:
        start_time = time.time()

        all_tokens = await fetch_all_kucoin_tokens()

        if not all_tokens:
            logger.error("🔴 No tokens returned from KuCoin fetch")
            return []

        logger.info(f"Fetched {len(all_tokens)} tokens from KuCoin")

        valid_tokens = []
        for token in all_tokens:
            if not isinstance(token, dict):
                logger.warning(f"⚠️ Skipping invalid token: {token}")
                continue
            if 'vol' not in token:
                logger.warning(f"⚠️ Token missing 'vol': {token.get('symbol', 'unknown')}")
                continue
            if not isinstance(token['vol'], (int, float)):
                logger.warning(f"⚠️ 'vol' is not a number for {token.get('symbol', 'unknown')}: {token['vol']}")
                continue
            valid_tokens.append(token)

        if not valid_tokens:
            logger.error("🔴 No valid tokens after filtering")
            return []

        top_tokens = sorted(valid_tokens, key=lambda x: x['vol'], reverse=True)[:limit]

        discovered = [
            {
                "symbol": token['symbol'],
                "reason": f"Top {limit} by 24h volume: ${round(token['vol'], 2):,}"
            }
            for token in top_tokens
        ]

        duration = round(time.time() - start_time, 2)
        logger.info(f"🟢 COMPLETED get_discover_tokens() in {duration}s")
        return discovered
    except Exception as e:
        logger.exception(f"🔴 ERROR in get_discover_tokens: {e}")
        return []