import asyncio
import logging
import os
import time
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Enhanced imports with error handling
try:
    from telegram_utils import notify_pnl_summary, AppType
except ImportError:

    async def notify_pnl_summary(context) -> bool:
        return True

    AppType = None

try:
    from config import (
        MAX_DAILY_TRADES,
        TRADING_MODE,
        TRADE_INTERVAL_SECONDS,
        MICRO_STOP_LOSS_PCT,
        MICRO_RISK_PER_TRADE_PCT,
        MICRO_PROFIT_TARGET_USD,
        MICRO_TRADE_SIZE_USD,
    )
except ImportError:
    # Enhanced defaults for high-frequency $1 profit trading
    MAX_DAILY_TRADES = 300  # High frequency trading
    TRADING_MODE = "LIVE"  # Live trading mode
    TRADE_INTERVAL_SECONDS = 30  # Quick trades
    MICRO_STOP_LOSS_PCT = 0.02  # Tight stop loss
    MICRO_RISK_PER_TRADE_PCT = 0.01  # Low risk per trade
    MICRO_PROFIT_TARGET_USD = 1.0  # $1 profit target
    MICRO_TRADE_SIZE_USD = 10.0  # Small trade size

try:
    from price_fetcher import get_price
except ImportError:

    async def get_price(token: str) -> Optional[float]:
        return 1.0


try:
    from token_selector import get_top_tokens_for_trading
except ImportError:
    from token_selector import generate_top_token_list as get_top_tokens_for_trading

try:
    from trade_executor import execute_trade, execute_batch_trades
except ImportError:

    def execute_trade(*args, **kwargs):
        return {"success": False}

    def execute_batch_trades(trade_requests):
        return [{"success": False} for _ in trade_requests]


try:
    from trade_logger import load_portfolio_json, save_portfolio_json, log_trade
except ImportError:

    def load_portfolio_json():
        return {}, 1000.0

    def save_portfolio_json(portfolio, balance) -> bool:
        return True

    def log_trade(*args, **kwargs) -> bool:
        return True


try:
    from ai_clients.ai_request_manager import get_ai_decisions_parallel

    async def run_ai_trade(symbol):
        result = get_ai_decisions_parallel(
            f"Analyze {symbol} for micro trading", symbol
        )
        return result

except ImportError:
    try:
        from ai_core import run_ai_trade

        def get_ai_decisions_parallel(*_args, **_kwargs):
            # Return synchronous result for compatibility
            return {"decision": "HOLD", "confidence": 0.5}

    except ImportError:

        async def run_ai_trade(symbol):
            return {"decision": "HOLD", "confidence": 0.5}

        def get_ai_decisions_parallel_fallback(*_args, **_kwargs):
            return {"decision": "HOLD", "confidence": 0.5}

        # Assign to the expected name
        get_ai_decisions_parallel = get_ai_decisions_parallel_fallback


try:
    from sentiment_engine import get_sentiment_feed, analyze_sentiment_batch
except ImportError:

    def get_sentiment_feed():
        return []

    def analyze_sentiment_batch(texts):
        return [{"sentiment": "neutral", "score": 0.0} for _ in texts]


logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

MICRO_BOT_PORTFOLIO_PATH = "backend/data/micro_bot_portfolio.json"
MICRO_BOT_TRADE_LOG_PATH = "backend/data/micro_bot_trades.csv"


class EnhancedMicroBot:
    """Enhanced MicroBot optimized for 200+ daily trades with improved performance."""

    def __init__(self):
        self.is_running = False
        self.trades_today = 0
        self.pnl_today = 0.0
        self.last_trade_day = None
        self.portfolio: Dict[str, Any] = {}
        self.balance = 0.0

        # Enhanced configuration for high-frequency trading
        self.max_portfolio_size = 15  # Increased for more opportunities
        self.min_confidence_score = 0.6  # Slightly lower for more trades
        self.trailing_stop_enabled = True
        self.dynamic_position_sizing = True
        self.sentiment_weight = 0.25  # Reduced weight for faster decisions
        self.batch_size = 10  # Process tokens in batches
        self.max_concurrent_analysis = 5  # Parallel analysis limit

        # Enhanced performance tracking
        self.performance_metrics = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_pnl": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "avg_trade_time": 0.0,
            "success_rate": 0.0,
            "daily_target": 200,
            "trades_per_hour": 0.0,
        }

        # High-frequency trading optimizations for $1 profit trades
        self.fast_mode = True  # Enable fast decision making
        self.quick_profit_target = MICRO_PROFIT_TARGET_USD  # $1 profit target
        self.micro_stop_loss = MICRO_STOP_LOSS_PCT  # Tight stop loss for quick trades
        self.position_hold_time_limit = (
            1800  # Max 30 minutes hold time for quick trades
        )
        self.trade_size = MICRO_TRADE_SIZE_USD  # Small trade size for frequent trades

        # Caching for performance
        self.price_cache = {}
        self.sentiment_cache = {}
        self.last_cache_update = 0
        self.cache_ttl = 60  # 1 minute cache

        self._load_state()

    def _load_state(self):
        if os.path.exists(MICRO_BOT_PORTFOLIO_PATH):
            with open(MICRO_BOT_PORTFOLIO_PATH, "r") as f:
                state = json.load(f)
                self.portfolio = state.get("portfolio", {})
                self.balance = state.get("balance", 0.0)
                self.trades_today = state.get("trades_today", 0)
                self.pnl_today = state.get("pnl_today", 0.0)
                last_trade_day_str = state.get("last_trade_day")
                if last_trade_day_str:
                    self.last_trade_day = datetime.fromisoformat(
                        last_trade_day_str
                    ).date()
        else:
            self.balance = 1000.0  # Initial balance

    def _save_state(self):
        with open(MICRO_BOT_PORTFOLIO_PATH, "w") as f:
            json.dump(
                {
                    "portfolio": self.portfolio,
                    "balance": self.balance,
                    "trades_today": self.trades_today,
                    "pnl_today": self.pnl_today,
                    "last_trade_day": (
                        self.last_trade_day.isoformat() if self.last_trade_day else None
                    ),
                },
                f,
                indent=2,
            )

    async def _reset_daily_stats(self):
        today = datetime.now().date()
        if not self.last_trade_day or self.last_trade_day < today:
            logger.info("Resetting daily trade stats.")
            self.trades_today = 0
            self.pnl_today = 0.0
            self.last_trade_day = today
            self._save_state()

    async def start(self):
        """Enhanced start method with improved performance monitoring."""
        logger.info("🚀 Enhanced Micro-bot started for 200+ daily trades.")
        self.is_running = True
        error_count = 0
        max_consecutive_errors = 5
        cycle_count = 0
        start_time = time.time()

        while self.is_running:
            try:
                cycle_start = time.time()
                cycle_count += 1

                await self._reset_daily_stats()

                # Dynamic trade limit checking
                if self.trades_today >= MAX_DAILY_TRADES:
                    logger.info(
                        f"Daily trade limit ({MAX_DAILY_TRADES}) reached. Skipping trades for today."
                    )
                    await asyncio.sleep(TRADE_INTERVAL_SECONDS)
                    continue

                # Enhanced trade cycle with performance tracking
                await self._enhanced_trade_cycle()
                self._save_state()

                # Performance logging
                cycle_time = time.time() - cycle_start
                self.performance_metrics["avg_trade_time"] = (
                    self.performance_metrics["avg_trade_time"] * 0.9 + cycle_time * 0.1
                )

                # Calculate trades per hour
                elapsed_hours = (time.time() - start_time) / 3600
                if elapsed_hours > 0:
                    self.performance_metrics["trades_per_hour"] = (
                        self.trades_today / elapsed_hours
                    )

                # Log performance every 50 cycles
                if cycle_count % 50 == 0:
                    self._log_performance_metrics(cycle_count)

                error_count = 0  # Reset error count on successful cycle

                # Dynamic sleep interval based on performance
                sleep_time = self._calculate_dynamic_sleep_interval()
                await asyncio.sleep(sleep_time)

            except asyncio.CancelledError:
                logger.info("🛑 Enhanced Micro-bot cancelled.")
                break
            except Exception as e:
                error_count += 1
                logger.error(
                    f"❌ Error in enhanced micro-bot cycle (attempt {error_count}/{max_consecutive_errors}): {e}",
                    exc_info=True,
                )

                # Circuit breaker with recovery
                if error_count >= max_consecutive_errors:
                    logger.error(
                        f"🔥 Circuit breaker: {max_consecutive_errors} consecutive errors. Entering recovery mode..."
                    )
                    await asyncio.sleep(300)  # Wait 5 minutes
                    error_count = 0  # Reset error count for recovery attempt
                    logger.info(
                        "🔄 Circuit breaker recovery: Resetting error count and continuing..."
                    )

                # Progressive backoff
                backoff_time = min(5 * error_count, 60)  # Max 60 seconds
                logger.info(f"⏳ Waiting {backoff_time} seconds before retry...")
                await asyncio.sleep(backoff_time)

    def _calculate_dynamic_sleep_interval(self) -> float:
        """Calculate dynamic sleep interval based on performance and market conditions."""
        base_interval = TRADE_INTERVAL_SECONDS

        # Reduce interval if we're behind target
        target_rate = (
            self.performance_metrics["daily_target"] / 24
        )  # Trades per hour target
        current_rate = self.performance_metrics["trades_per_hour"]

        if current_rate < target_rate * 0.8:  # Behind target
            return max(base_interval * 0.5, 15)  # Minimum 15 seconds
        elif current_rate > target_rate * 1.2:  # Ahead of target
            return min(base_interval * 1.5, 120)  # Maximum 2 minutes
        else:
            return base_interval

    def _log_performance_metrics(self, cycle_count: int):
        """Log enhanced performance metrics."""
        success_rate = (
            self.performance_metrics["winning_trades"]
            / max(self.performance_metrics["total_trades"], 1)
            * 100
        )

        logger.info(
            f"📊 Performance Update [Cycle {cycle_count}] | "
            f"Trades Today: {self.trades_today}/{MAX_DAILY_TRADES} | "
            f"Success Rate: {success_rate:.1f}% | "
            f"PnL Today: ${self.pnl_today:.2f} | "
            f"Trades/Hour: {self.performance_metrics['trades_per_hour']:.1f} | "
            f"Portfolio: {len(self.portfolio)}/{self.max_portfolio_size}"
        )

    def stop(self):
        logger.info("Micro-bot stopped.")
        self.is_running = False

    async def _enhanced_trade_cycle(self):
        """Enhanced trade cycle with parallel processing for 200+ daily trades."""
        logger.info("🔄 Enhanced micro-bot trade cycle initiated.")

        # Get more tokens for increased opportunities
        top_tokens = get_top_tokens_for_trading(limit=self.batch_size * 2)
        if not top_tokens:
            logger.warning("⚠️ No top tokens found for enhanced micro-bot.")
            return

        # Separate held and new tokens for different processing
        held_tokens = [symbol for symbol in self.portfolio.keys()]
        new_tokens = [
            token for token in top_tokens if token.get("symbol") not in self.portfolio
        ][: self.batch_size]

        # Process held positions in parallel
        if held_tokens:
            await self._process_held_positions_parallel(held_tokens)

        # Process new trading opportunities in parallel
        if new_tokens and self.trades_today < MAX_DAILY_TRADES:
            await self._process_new_opportunities_parallel(new_tokens)

    async def _process_held_positions_parallel(self, held_symbols: List[str]):
        """Process held positions in parallel for faster execution."""
        try:
            with ThreadPoolExecutor(max_workers=min(len(held_symbols), 3)) as executor:
                tasks = [
                    asyncio.create_task(self._manage_held_position(symbol))
                    for symbol in held_symbols
                ]

                # Wait for all position management tasks
                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"Error in parallel position management: {e}")

    async def _process_new_opportunities_parallel(
        self, new_tokens: List[Dict[str, Any]]
    ):
        """Process new trading opportunities in parallel."""
        try:
            # Limit concurrent analysis
            semaphore = asyncio.Semaphore(self.max_concurrent_analysis)

            async def analyze_token_with_semaphore(token_info):
                async with semaphore:
                    return await self._evaluate_new_trade_enhanced(
                        token_info.get("symbol")
                    )

            # Create tasks for parallel analysis
            tasks = [
                analyze_token_with_semaphore(token_info)
                for token_info in new_tokens
                if len(self.portfolio) < self.max_portfolio_size
            ]

            if tasks:
                # Execute analysis in parallel
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process successful results
                successful_analyses = [
                    result
                    for result in results
                    if not isinstance(result, Exception) and result
                ]

                logger.info(
                    f"📊 Analyzed {len(tasks)} tokens, {len(successful_analyses)} opportunities found"
                )

        except Exception as e:
            logger.error(f"Error in parallel opportunity processing: {e}")

    async def _trade_cycle(self):
        """Legacy trade cycle method for backward compatibility."""
        await self._enhanced_trade_cycle()

    async def _manage_held_position(self, symbol: str):
        current_price = await get_price(symbol)
        if current_price is None:
            logger.warning(
                f"Could not get current price for {symbol}. Cannot manage held position."
            )
            return

        held_price = self.portfolio[symbol]["buy_price"]
        quantity = self.portfolio[symbol]["amount"]

        # Take Profit Logic - $1 profit target
        target_sell_price = held_price + MICRO_PROFIT_TARGET_USD / quantity
        if current_price >= target_sell_price:
            logger.info(
                f"[MicroBot] Taking profit on {symbol}. Current: {current_price}, Target: {target_sell_price}"
            )
            pnl = (current_price - held_price) * quantity
            trade_result = execute_trade(
                token_symbol=symbol,
                side="SELL",
                amount_usd=current_price * quantity,  # Sell all held quantity
                price=current_price,
                strategy="MicroBot_TP",
                reason=f"Take profit (${pnl:.2f})",
            )
            if trade_result.get("success"):
                self.pnl_today += pnl
                self.trades_today += 1
                del self.portfolio[symbol]
                self._save_state()
                log_trade(
                    symbol,
                    "SELL",
                    quantity,
                    current_price,
                    "MicroBot_TP",
                    f"Take profit (${pnl:.2f})",
                    pnl,
                )
                return

        # Stop Loss Logic
        stop_loss_price = held_price * (1 - MICRO_STOP_LOSS_PCT)
        if current_price <= stop_loss_price:
            logger.info(
                f"[MicroBot] Triggering stop loss on {symbol}. Current: {current_price}, Stop: {stop_loss_price}"
            )
            pnl = (current_price - held_price) * quantity
            trade_result = execute_trade(
                token_symbol=symbol,
                side="SELL",
                amount_usd=current_price * quantity,  # Sell all held quantity
                price=current_price,
                strategy="MicroBot_SL",
                reason=f"Stop loss (${pnl:.2f})",
            )
            if trade_result.get("success"):
                self.pnl_today += pnl
                self.trades_today += 1
                del self.portfolio[symbol]
                self._save_state()
                log_trade(
                    symbol,
                    "SELL",
                    quantity,
                    current_price,
                    "MicroBot_SL",
                    f"Stop loss (${pnl:.2f})",
                    pnl,
                )
                return

        # AI-driven Sell Signal for held positions
        decision_result = await run_ai_trade(symbol)
        decision = decision_result.get("decision")

        if decision == "SELL":
            logger.info(f"[MicroBot] AI recommends selling {symbol}.")
            pnl = (current_price - held_price) * quantity
            trade_result = execute_trade(
                token_symbol=symbol,
                side="SELL",
                amount_usd=current_price * quantity,
                price=current_price,
                strategy="MicroBot_AI_Sell",
                reason="AI SELL signal for held position",
            )
            if trade_result.get("success"):
                self.pnl_today += pnl
                self.trades_today += 1
                del self.portfolio[symbol]
                self._save_state()
                log_trade(
                    symbol,
                    "SELL",
                    quantity,
                    current_price,
                    "MicroBot_AI_Sell",
                    "AI SELL signal for held position",
                    pnl,
                )
                return

    async def _evaluate_new_trade(self, symbol: str):
        if self.trades_today >= MAX_DAILY_TRADES:
            return

        # Check portfolio size limit
        if len(self.portfolio) >= self.max_portfolio_size:
            logger.info(
                f"Portfolio size limit ({self.max_portfolio_size}) reached. Skipping new trades."
            )
            return

        # Get AI decision with confidence score
        decision_result = await run_ai_trade(symbol)
        decision = decision_result.get("decision")
        confidence = decision_result.get("confidence", 0.5)

        # Check minimum confidence threshold
        if confidence < self.min_confidence_score:
            logger.info(
                f"AI confidence ({confidence:.2f}) below threshold ({self.min_confidence_score}) for {symbol}"
            )
            return

        # Get sentiment analysis
        sentiment_score = await self._get_sentiment_score(symbol)

        # Combine AI decision with sentiment
        combined_score = (
            1 - self.sentiment_weight
        ) * confidence + self.sentiment_weight * sentiment_score

        if decision == "BUY" and combined_score > self.min_confidence_score:
            current_price = await get_price(symbol)
            if current_price is None or current_price <= 0:
                logger.warning(
                    f"Could not get current price for {symbol}. Skipping buy."
                )
                return

            # Enhanced position sizing based on confidence and volatility
            position_size = self._calculate_position_size(
                symbol, current_price, combined_score
            )

            if position_size <= 0:
                logger.warning(
                    f"Calculated position size for {symbol} is zero or negative. Skipping buy."
                )
                return

            quantity_to_buy = position_size / current_price

            trade_result = execute_trade(
                token_symbol=symbol,
                side="BUY",
                amount_usd=position_size,
                price=current_price,
                strategy="MicroBot_Enhanced",
                reason=f"Enhanced AI BUY signal (confidence: {combined_score:.2f})",
            )

            if trade_result.get("success"):
                self.portfolio[symbol] = {
                    "buy_price": current_price,
                    "amount": quantity_to_buy,
                    "timestamp": datetime.now().isoformat(),
                    "confidence": combined_score,
                    "trailing_stop": current_price * (1 - MICRO_STOP_LOSS_PCT),
                    "highest_price": current_price,
                }
                self.trades_today += 1
                self.balance -= position_size
                self._update_performance_metrics("BUY", position_size)
                self._save_state()
                log_trade(
                    symbol,
                    "BUY",
                    quantity_to_buy,
                    current_price,
                    "MicroBot_Enhanced",
                    f"Enhanced AI BUY signal (confidence: {combined_score:.2f})",
                    0.0,
                )
                logger.info(
                    f"[MicroBot] Executed enhanced BUY for {symbol} with confidence {combined_score:.2f}"
                )
            else:
                logger.warning(
                    f"[MicroBot] Failed to execute BUY for {symbol}: {trade_result.get('message')}"
                )

    async def _get_sentiment_score(self, symbol: str) -> float:
        """Get sentiment score for a token (0.0 to 1.0)"""
        try:
            sentiment_feed = get_sentiment_feed()
            if isinstance(sentiment_feed, list):
                for item in sentiment_feed:
                    if (
                        isinstance(item, dict)
                        and item.get("symbol", "").upper() == symbol.upper()
                    ):
                        sentiment = item.get("sentiment", "neutral").lower()
                        if sentiment == "positive":
                            return 0.8
                        elif sentiment == "negative":
                            return 0.2
                        else:
                            return 0.5
            return 0.5  # Neutral if no sentiment data
        except Exception as e:
            logger.warning(f"Error getting sentiment for {symbol}: {e}")
            return 0.5

    def _calculate_position_size(
        self, symbol: str, price: float, confidence: float
    ) -> float:
        """Calculate position size based on confidence, volatility, and risk management"""
        if not self.dynamic_position_sizing:
            return min(self.balance * MICRO_RISK_PER_TRADE_PCT, MICRO_TRADE_SIZE_USD)

        # Base position size
        base_size = self.balance * MICRO_RISK_PER_TRADE_PCT

        # Adjust based on confidence (higher confidence = larger position)
        confidence_multiplier = 0.5 + (confidence * 1.5)  # Range: 0.5 to 2.0

        # Adjust based on portfolio diversification
        diversification_factor = max(0.5, 1.0 - (len(self.portfolio) * 0.1))

        # Calculate final position size
        position_size = base_size * confidence_multiplier * diversification_factor

        # Cap at maximum allowed
        return min(position_size, MICRO_TRADE_SIZE_USD)

    def _update_performance_metrics(self, action: str, amount: float, pnl: float = 0.0):
        """Update performance tracking metrics"""
        if action == "BUY":
            self.performance_metrics["total_trades"] += 1
        elif action == "SELL" and pnl != 0.0:
            if pnl > 0:
                self.performance_metrics["winning_trades"] += 1
            else:
                self.performance_metrics["losing_trades"] += 1
            self.performance_metrics["total_pnl"] += pnl

            # Update max drawdown
            if pnl < 0:
                current_drawdown = abs(pnl) / self.balance
                self.performance_metrics["max_drawdown"] = max(
                    self.performance_metrics["max_drawdown"], current_drawdown
                )

    def _update_trailing_stop(self, symbol: str, current_price: float):
        """Update trailing stop for a position"""
        if not self.trailing_stop_enabled or symbol not in self.portfolio:
            return

        position = self.portfolio[symbol]
        highest_price = position.get("highest_price", position["buy_price"])

        # Update highest price if current price is higher
        if current_price > highest_price:
            position["highest_price"] = current_price
            # Update trailing stop (percentage below highest price)
            position["trailing_stop"] = current_price * (1 - MICRO_STOP_LOSS_PCT)
            logger.info(
                f"Updated trailing stop for {symbol}: {position['trailing_stop']:.6f}"
            )

    async def analyze_token(self, symbol: str) -> Dict[str, Any]:
        """Analyze a single token and return analysis results"""
        try:
            # Get AI decision
            decision_result = await run_ai_trade(symbol)
            decision = decision_result.get("decision", "HOLD")
            confidence = decision_result.get("confidence", 0.5)

            # Get current price
            current_price = await get_price(symbol)

            # Get sentiment score
            sentiment_score = await self._get_sentiment_score(symbol)

            # Calculate combined score
            combined_score = (
                1 - self.sentiment_weight
            ) * confidence + self.sentiment_weight * sentiment_score

            # Check if we hold this token
            is_held = symbol in self.portfolio

            return {
                "symbol": symbol,
                "decision": decision,
                "confidence": confidence,
                "sentiment_score": sentiment_score,
                "combined_score": combined_score,
                "current_price": current_price,
                "is_held": is_held,
                "portfolio_info": self.portfolio.get(symbol) if is_held else None,
                "analysis_timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"Error analyzing token {symbol}: {e}")
            return {
                "symbol": symbol,
                "error": str(e),
                "analysis_timestamp": datetime.now().isoformat(),
            }

    async def _evaluate_new_trade_enhanced(
        self, symbol: str
    ) -> Optional[Dict[str, Any]]:
        """Enhanced evaluation for new trading opportunities."""
        try:
            # Use the existing analyze_token method
            analysis = await self.analyze_token(symbol)

            if analysis and analysis.get("decision") == "BUY":
                confidence = analysis.get("confidence", 0.5)
                if confidence >= self.min_confidence_score:
                    # Execute the trade
                    await self._evaluate_new_trade(symbol)
                    return analysis

            return None

        except Exception as e:
            logger.error(f"Enhanced trade evaluation failed for {symbol}: {e}")
            return None


# Create enhanced micro bot instance
micro_bot_instance = EnhancedMicroBot()


async def start_micro_bot():
    await micro_bot_instance.start()


async def stop_micro_bot():
    micro_bot_instance.stop()


async def analyze_token(symbol: str) -> Dict[str, Any]:
    """Analyze a single token and return analysis results"""
    return await micro_bot_instance.analyze_token(symbol)


def get_micro_bot_status():
    return {
        "is_running": micro_bot_instance.is_running,
        "trades_today": micro_bot_instance.trades_today,
        "pnl_today": micro_bot_instance.pnl_today,
        "last_trade_day": (
            micro_bot_instance.last_trade_day.isoformat()
            if micro_bot_instance.last_trade_day
            else None
        ),
        "portfolio_size": len(micro_bot_instance.portfolio),
        "balance": micro_bot_instance.balance,
    }
