import logging
import asyncio
from typing import Optional, List
from price_fetcher import get_price
from sentiment_engine import get_sentiment_score
from kucoin_data import get_token_volume_ratio
from chart_signals import get_chart_signal_sync
from kucoin_strategies import (
    moving_average_crossover_strategy,
    rsi_strategy,
    macd_strategy,
    bollinger_bands_strategy,
    breakout_strategy
)

# Setup logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def build_trade_prompt(
    token: str,
    news_data: str = "",
    price_change_24h: Optional[float] = None,
    news_snippets: Optional[List[str]] = None,
    # New parameters for strategy integration
    short_ma: Optional[float] = None,
    long_ma: Optional[float] = None,
    rsi: Optional[float] = None,
    macd: Optional[float] = None,
    macd_signal: Optional[float] = None,
    price: Optional[float] = None, # Price is already fetched, but needed for BB/Breakout
    upper_band: Optional[float] = None,
    lower_band: Optional[float] = None,
    breakout_level: Optional[float] = None,
    volume_oscillator: Optional[float] = None,
    stochastic_k: Optional[float] = None,
    stochastic_d: Optional[float] = None,
    ichimoku_tenkan: Optional[float] = None,
    ichimoku_kijun: Optional[float] = None,
    ichimoku_senkou_a: Optional[float] = None,
    ichimoku_senkou_b: Optional[float] = None,
    ichimoku_chikou: Optional[float] = None,
    fib_levels: Optional[dict] = None,
    parabolic_sar: Optional[float] = None,
    atr: Optional[float] = None,
) -> str:
    """
    Builds a unified AI prompt string combining price, sentiment, volume, chart data,
    and signals from traditional trading strategies.

    Args:
        token (str): The token symbol to analyze.
        news_data (str, optional): Preprocessed news string for sentiment scoring.
        price_change_24h (float, optional): 24-hour price change percentage.
        news_snippets (List[str], optional): List of relevant news headlines.
        short_ma (float, optional): Short-period Moving Average.
        long_ma (float, optional): Long-period Moving Average.
        rsi (float, optional): Relative Strength Index.
        macd (float, optional): Moving Average Convergence Divergence.
        macd_signal (float, optional): MACD Signal Line.
        price (float, optional): Current price (needed for Bollinger Bands/Breakout).
        upper_band (float, optional): Bollinger Band Upper Band.
        lower_band (float, optional): Bollinger Band Lower Band.
        breakout_level (float, optional): Breakout level for breakout strategy.
        volume_oscillator (float, optional): Volume Oscillator value.
        stochastic_k (float, optional): Stochastic %K value.
        stochastic_d (float, optional): Stochastic %D value.
        ichimoku_tenkan (float, optional): Ichimoku Tenkan-sen.
        ichimoku_kijun (float, optional): Ichimoku Kijun-sen.
        ichimoku_senkou_a (float, optional): Ichimoku Senkou Span A.
        ichimoku_senkou_b (float, optional): Ichimoku Senkou Span B.
        ichimoku_chikou (float, optional): Ichimoku Chikou Span.
        fib_levels (dict, optional): Dictionary of Fibonacci Retracement levels.
        parabolic_sar (float, optional): Parabolic SAR value.
        atr (float, optional): Average True Range value.

    Returns:
        str: A structured prompt for AI model decision-making.
    """
    try:
        token = token.upper()

        # Fetch price if not provided (for live trading)
        if price is not None:
            current_price = price
        else:
            try:
                # Use synchronous price fetching to avoid async issues
                from price_fetcher import get_prices_batch_coingecko
                prices = get_prices_batch_coingecko([token])
                current_price = prices.get(token)
                if current_price is None:
                    logger.warning(f"CoinGecko batch failed for {token}, trying async fetch")
                    try:
                        current_price = asyncio.run(get_price(token))
                    except Exception as async_e:
                        logger.warning(f"Async price fetch failed for {token}: {async_e}")
                        current_price = None
            except Exception as e:
                logger.warning(f"Price fetching failed for {token}: {e}, using fallback")
                try:
                    current_price = asyncio.run(get_price(token))
                except Exception as fallback_e:
                    logger.error(f"All price fetching methods failed for {token}: {fallback_e}")
                    current_price = None
        
        # If price is still None, use a default value to prevent prompt build failure
        if current_price is None:
            logger.warning(f"[Prompt Builder] Price unavailable for {token}, using default $1.00")
            current_price = 1.0  # Use default price to prevent complete failure

        # Fetch sentiment and news snippets with robust error handling
        try:
            sentiment_data = get_sentiment_score(token)
            if isinstance(sentiment_data, (int, float)):
                sentiment = float(sentiment_data)
            elif isinstance(sentiment_data, dict) and 'score' in sentiment_data:
                sentiment = float(sentiment_data['score'])
            else:
                sentiment = 0.5
        except Exception as e:
            logger.warning(f"Sentiment analysis failed for {token}: {e}, using default 0.5")
            sentiment = 0.5
        
        # Ensure sentiment is within valid range
        sentiment = max(0.0, min(1.0, sentiment))

        if news_snippets is None:
            news_snippets = []

        # Fetch volume ratio with error handling
        try:
            volume_ratio = get_token_volume_ratio({"symbol": token})
            if volume_ratio is None or not isinstance(volume_ratio, (int, float)):
                volume_ratio = 1.0
            else:
                volume_ratio = float(volume_ratio)
        except Exception as e:
            logger.warning(f"Volume ratio fetch failed for {token}: {e}, using 1.0")
            volume_ratio = 1.0

        # Fetch chart signal with error handling
        try:
            chart = get_chart_signal_sync(token)
            if chart is None or not isinstance(chart, str):
                chart = "NEUTRAL"
        except Exception as e:
            logger.warning(f"Chart signal fetch failed for {token}: {e}, using NEUTRAL")
            chart = "NEUTRAL"

        # Format price_change_24h safely
        formatted_price_change = f"{price_change_24h:.2f}%" if price_change_24h is not None else "N/A"

        # --- Integrate Traditional Strategy Signals ---
        strategy_signals = []

        if short_ma is not None and long_ma is not None:
            ma_crossover_signal = moving_average_crossover_strategy(short_ma, long_ma)
            strategy_signals.append(f"MA Crossover: {ma_crossover_signal}")

        if rsi is not None:
            rsi_signal = rsi_strategy(rsi)
            strategy_signals.append(f"RSI: {rsi_signal}")

        if macd is not None and macd_signal is not None:
            macd_signal_val = macd_strategy(macd, macd_signal)
            strategy_signals.append(f"MACD: {macd_signal_val}")

        if current_price is not None and upper_band is not None and lower_band is not None:
            bb_signal = bollinger_bands_strategy(current_price, upper_band, lower_band)
            strategy_signals.append(f"Bollinger Bands: {bb_signal}")

        if current_price is not None and breakout_level is not None:
            breakout_signal = breakout_strategy(current_price, breakout_level)
            strategy_signals.append(f"Breakout: {breakout_signal}")

        if volume_oscillator is not None:
            strategy_signals.append(f"Volume Oscillator: {volume_oscillator:.2f}%")

        if stochastic_k is not None and stochastic_d is not None:
            strategy_signals.append(f"Stochastic Oscillator: %K={stochastic_k:.2f}, %D={stochastic_d:.2f}")

        if ichimoku_tenkan is not None:
            try:
                # Handle NaN values in Ichimoku indicators
                tenkan = "nan" if ichimoku_tenkan is None or str(ichimoku_tenkan).lower() == 'nan' else f"{ichimoku_tenkan:.4f}"
                kijun = "nan" if ichimoku_kijun is None or str(ichimoku_kijun).lower() == 'nan' else f"{ichimoku_kijun:.4f}"
                senkou_a = "nan" if ichimoku_senkou_a is None or str(ichimoku_senkou_a).lower() == 'nan' else f"{ichimoku_senkou_a:.4f}"
                senkou_b = "nan" if ichimoku_senkou_b is None or str(ichimoku_senkou_b).lower() == 'nan' else f"{ichimoku_senkou_b:.4f}"
                chikou = "nan" if ichimoku_chikou is None or str(ichimoku_chikou).lower() == 'nan' else f"{ichimoku_chikou:.4f}"
                
                ichimoku_summary = (
                    f"Ichimoku: Tenkan={tenkan}, Kijun={kijun}, "
                    f"Senkou A={senkou_a}, Senkou B={senkou_b}, "
                    f"Chikou={chikou}"
                )
                strategy_signals.append(ichimoku_summary)
            except Exception as e:
                logger.warning(f"Error formatting Ichimoku data for {token}: {e}")
                strategy_signals.append("Ichimoku: Data unavailable")

        if fib_levels is not None:
            try:
                fib_summary = ", ".join([f"{k}: {v:.4f}" for k, v in fib_levels.items() if v is not None])
                strategy_signals.append(f"Fibonacci Retracement Levels: {fib_summary}")
            except Exception as e:
                logger.warning(f"Error formatting Fibonacci levels for {token}: {e}")
                strategy_signals.append("Fibonacci Retracement Levels: Data unavailable")

        if parabolic_sar is not None:
            strategy_signals.append(f"Parabolic SAR: {parabolic_sar:.6f}")

        if atr is not None:
            strategy_signals.append(f"ATR: {atr:.4f}")

        strategy_summary = "\nTRADITIONAL STRATEGY SIGNALS:\n" + "\n".join(strategy_signals) if strategy_signals else ""

        # Compose the AI prompt
        prompt = (
            "### AI Crypto Trading Prompt for AlphaPredatorBot\n"
            "Use the following structured market data and traditional strategy signals to determine a trade action.\n\n"
            f"TOKEN: {token}\n"
            f"CURRENT PRICE: ${current_price:.6f} USDT\n"
            f"PRICE CHANGE (24H): {formatted_price_change}\n"
            f"MARKET SENTIMENT SCORE: {sentiment:.1f}% bullish based on news & social data\n"
            f"RECENT NEWS HEADLINES: {' | '.join(news_snippets[:3])}\n"
            f"VOLUME SURGE: {volume_ratio:.2f}x average (indicates market activity anomaly)\n"
            f"CHART ANALYSIS: {chart} (technical chart-based signal)\n"
            f"{strategy_summary}\n\n"
            "TRADE TIME HORIZON: 12 hours ahead\n\n"
            "OBJECTIVE:\n"
            "Evaluate whether the token is a good BUY, should be SOLD, or kept on HOLD.\n"
            "Factors to consider include:\n"
            "- Sentiment alignment with price action\n"
            "- Unusual trading volume or social buzz\n"
            "- Technical indicators alignment with momentum\n"
            "- Consistency or divergence with traditional strategy signals\n\n"
            "RESPONSE FORMAT:\n"
            "You MUST respond with valid JSON in this exact format:\n"
            "{\n"
            '  "decision": "BUY" or "SELL" or "HOLD",\n'
            '  "confidence": 0.0 to 1.0,\n'
            '  "reason": "Brief explanation of your decision based on the data provided"\n'
            "}\n\n"
            "IMPORTANT: Your response must be valid JSON only. Do not include any other text.\n"
            "NOTE: Be concise and logical. Avoid speculation. Base your decision on the provided data.\n"
        )

        logger.info(f"[Prompt Builder] Prompt successfully created for {token}")
        return prompt


    except Exception as e:
        logger.exception(f"[Prompt Builder] Exception for {token}: {e}")
        return f"[Prompt Build Error] for {token}: {e}"

# Alias for backward compatibility
def build_trading_prompt(symbol: str, price=None, price_change=None, volume_ratio=None, 
                        sentiment_score=None, chart_signal=None, traditional_signals=None, 
                        news_headlines=None, tokenmetrics_signal=None, sentiment_data=None, 
                        news_data=None, technical_data=None, **kwargs) -> str:
    """
    Backward compatibility function for build_trading_prompt.
    Maps to the main build_trade_prompt function with flexible parameter handling.
    """
    try:
        # Extract data from dictionaries if provided
        news_snippets = []
        news_text = ""
        
        if news_data and isinstance(news_data, dict):
            news_snippets = news_data.get('headlines', [])
            news_text = news_data.get('text', '')
        
        if news_headlines and isinstance(news_headlines, list):
            news_snippets = news_headlines
        
        # Extract technical data if provided
        tech_params = {}
        if technical_data and isinstance(technical_data, dict):
            tech_params.update(technical_data)
        
        # Build prompt with all available data
        return build_trade_prompt(
            token=symbol,
            price=price,
            price_change_24h=price_change,
            news_data=news_text,
            news_snippets=news_snippets,
            **tech_params,
            **kwargs
        )
    except Exception as e:
        logger.error(f"Error in build_trading_prompt for {symbol}: {e}")
        return f"Error building prompt for {symbol}: {e}"
