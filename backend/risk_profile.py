import logging
from typing import Dict

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Risk configuration profiles for different token types
RISK_CONFIG: Dict[str, Dict[str, float]] = {
    "major": {
        "max_buy": 100,
        "take_profit_pct": 0.10,  # 10% TP
        "stop_loss_pct": 0.03     # 3% SL
    },
    "alt": {
        "max_buy": 100,
        "take_profit_pct": 0.20,
        "stop_loss_pct": 0.10
    },
    "meme": {
        "max_buy": 20,
        "track_peak": True,
        "stop_loss_from_peak": 0.20  # Sell if it drops 20% from ATH
    }
}

def get_risk_params(token_type: str) -> Dict[str, float]:
    """
    Retrieve the risk configuration parameters for a given token type.

    Args:
        token_type (str): The classification of the token ('major', 'alt', or 'meme').

    Returns:
        dict: Risk parameters dictionary for the given token type.
    """
    if token_type not in RISK_CONFIG:
        logger.warning(f"[Risk Profile] Token type '{token_type}' not explicitly defined. Using 'alt' fallback.")
    return RISK_CONFIG.get(token_type, RISK_CONFIG["alt"])
