#!/usr/bin/env python3
"""
🚀 KUCOIN SDK MIGRATION
Replace custom HTTP client with official KuCoin Python SDK
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

# KuCoin SDK imports with fallback stubs
try:
    from kucoin.client import Market, Trade, User  # type: ignore

    KUCOIN_SDK_AVAILABLE = True
except ImportError:
    # Create stub classes for when KuCoin SDK is not available
    KUCOIN_SDK_AVAILABLE = False
    Market = None  # type: ignore
    Trade = None  # type: ignore
    User = None  # type: ignore

    class Market:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_24hr_stats(self, symbol: str):
            return {}

        def get_klines(self, symbol: str, **kwargs):
            return []

        def get_all_tickers(self):
            return {"ticker": []}

        def get_ticker(self, symbol: str):
            return {"price": "0"}

        def get_kline(self, **kwargs):
            return []

    class Trade:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def create_market_order(self, **kwargs):
            return {"orderId": "stub_order_id"}

        def create_order(self, **kwargs):
            return {"orderId": "stub_order_id"}

        def get_order(self, order_id: str):
            return {"id": order_id, "status": "done"}

        def cancel_order(self, order_id: str):
            return {"cancelledOrderIds": [order_id]}

        def get_orders(self, **kwargs):
            return {"items": []}

    class User:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_account_list(self):
            return []

        def get_account(self, account_id: str):
            return {"id": account_id, "balance": "0"}


# Note: kucoin-python doesn't have exceptions module, use generic Exception
from config import KUCOIN_API_KEY, KUCOIN_API_SECRET, KUCOIN_API_PASSPHRASE

logger = logging.getLogger(__name__)


class KuCoinSDKWrapper:
    """Enhanced KuCoin client using official SDK"""

    def __init__(self):
        """Initialize KuCoin SDK client"""
        self.api_key = KUCOIN_API_KEY
        self.api_secret = KUCOIN_API_SECRET
        self.api_passphrase = KUCOIN_API_PASSPHRASE

        try:
            # Initialize official SDK clients
            if Market is None:
                raise ImportError("kucoin package not installed")

            # Initialize market client (public endpoints don't need credentials)
            self.market_client = Market()
            if (
                all([self.api_key, self.api_secret, self.api_passphrase])
                and Trade is not None
                and User is not None
            ):
                self.trade_client = Trade(
                    key=self.api_key,
                    secret=self.api_secret,
                    passphrase=self.api_passphrase,
                )
                self.user_client = User(
                    key=self.api_key,
                    secret=self.api_secret,
                    passphrase=self.api_passphrase,
                )
                self.authenticated = True
            else:
                self.trade_client = None
                self.user_client = None
                self.authenticated = False
                logger.warning("❌ KuCoin API credentials not fully configured")
            logger.info("✅ KuCoin SDK clients initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize KuCoin SDK: {e}")
            self.market_client = None
            self.trade_client = None
            self.user_client = None
            self.authenticated = False

    def get_all_tickers(self) -> List[Dict[str, Any]]:
        """Get all market tickers using direct API call for accuracy"""
        try:
            # Use direct API call for most reliable data
            import requests

            response = requests.get(
                "https://api.kucoin.com/api/v1/market/allTickers", timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("data") and data["data"].get("ticker"):
                    tickers_raw = data["data"]["ticker"]

                    # Transform to our expected format
                    transformed_tickers = []
                    for ticker in tickers_raw:
                        if ticker["symbol"].endswith("-USDT"):
                            try:
                                transformed_tickers.append(
                                    {
                                        "symbol": ticker["symbol"],
                                        "price": float(ticker.get("last", 0)),
                                        "volume": float(ticker.get("vol", 0)),
                                        "change_24h": float(ticker.get("changeRate", 0))
                                        * 100,
                                        "high_24h": float(ticker.get("high", 0)),
                                        "low_24h": float(ticker.get("low", 0)),
                                        "volume_value": float(
                                            ticker.get("volValue", 0)
                                        ),
                                        "source": "kucoin_api_direct",
                                    }
                                )
                            except (ValueError, TypeError) as e:
                                logger.warning(
                                    f"⚠️ Skipping invalid ticker data for {ticker.get('symbol', 'unknown')}: {e}"
                                )
                                continue

                    logger.info(
                        f"✅ Fetched {len(transformed_tickers)} live tickers from KuCoin API"
                    )
                    return transformed_tickers

            logger.warning(
                "⚠️ KuCoin API returned invalid response, trying SDK fallback"
            )

            # Fallback to SDK if direct API fails
            if self.market_client:
                tickers = self.market_client.get_all_tickers()
                transformed_tickers = []
                for ticker in tickers.get("ticker", []):
                    if ticker["symbol"].endswith("-USDT"):
                        try:
                            transformed_tickers.append(
                                {
                                    "symbol": ticker["symbol"],
                                    "price": float(ticker.get("last", 0)),
                                    "volume": float(ticker.get("vol", 0)),
                                    "change_24h": float(ticker.get("changeRate", 0))
                                    * 100,
                                    "high_24h": float(ticker.get("high", 0)),
                                    "low_24h": float(ticker.get("low", 0)),
                                    "volume_value": float(ticker.get("volValue", 0)),
                                    "source": "kucoin_sdk",
                                }
                            )
                        except (ValueError, TypeError):
                            continue

                if transformed_tickers:
                    logger.info(
                        f"✅ SDK fallback fetched {len(transformed_tickers)} tickers"
                    )
                    return transformed_tickers

            logger.error("❌ Both KuCoin API and SDK failed")
            return []

        except Exception as e:
            logger.error(f"❌ KuCoin tickers error: {e}")
            return []

    def get_token_price(self, symbol: str) -> Optional[float]:
        """Get single token price using SDK"""
        try:
            if not self.market_client:
                return None

            # Ensure symbol format
            if not symbol.endswith("-USDT"):
                symbol = f"{symbol}-USDT"

            ticker = self.market_client.get_ticker(symbol)
            return float(ticker.get("price", 0))

        except Exception as e:
            logger.error(f"❌ KuCoin SDK price error for {symbol}: {e}")
            return None

    def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get ticker data for a specific symbol using SDK"""
        try:
            if not self.market_client:
                return None

            # Ensure symbol format
            if not symbol.endswith("-USDT"):
                symbol = f"{symbol}-USDT"

            # Use SDK to get ticker data
            ticker = self.market_client.get_ticker(symbol)
            if ticker:
                return {
                    "symbol": symbol,
                    "price": ticker.get("price", "0"),
                    "volume": ticker.get("vol", "0"),
                    "change_24h": float(ticker.get("changeRate", 0)) * 100,
                    "high_24h": ticker.get("high", "0"),
                    "low_24h": ticker.get("low", "0"),
                }
            return None

        except Exception as e:
            logger.error(f"❌ KuCoin SDK ticker error for {symbol}: {e}")
            return None

    def get_klines(
        self, symbol: str, interval: str = "1hour", _limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get candlestick data using SDK"""
        try:
            if not self.market_client:
                logger.error("KuCoin market client not initialized")
                return []

            # Ensure symbol format
            if not symbol.endswith("-USDT"):
                symbol = f"{symbol}-USDT"

            # Map our interval format to KuCoin format
            interval_map = {
                "1min": "1min",
                "5min": "5min",
                "15min": "15min",
                "30min": "30min",
                "1hour": "1hour",
                "4hour": "4hour",
                "1day": "1day",
            }

            kucoin_interval = interval_map.get(interval, "1hour")

            # Get klines using SDK
            klines = self.market_client.get_kline(
                symbol=symbol, kline_type=kucoin_interval
            )

            # Transform to our expected format
            transformed_klines = []
            for kline in klines:
                transformed_klines.append(
                    {
                        "timestamp": int(kline[0]),
                        "open": float(kline[1]),
                        "close": float(kline[2]),
                        "high": float(kline[3]),
                        "low": float(kline[4]),
                        "volume": float(kline[5]),
                        "turnover": float(kline[6]),
                    }
                )

            logger.info(f"✅ Fetched {len(transformed_klines)} klines for {symbol}")
            return transformed_klines

        except Exception as e:
            logger.error(f"❌ KuCoin SDK klines error for {symbol}: {e}")
            return []

    def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        size: str,
        price: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """Place order using SDK (LIVE TRADING)"""
        try:
            if not self.trade_client or not self.authenticated:
                logger.error("❌ KuCoin SDK not authenticated for trading")
                return None

            # Ensure symbol format
            if not symbol.endswith("-USDT"):
                symbol = f"{symbol}-USDT"

            # Place order using SDK
            order_data = {
                "symbol": symbol,
                "side": side.lower(),  # 'buy' or 'sell'
                "type": order_type.lower(),  # 'market' or 'limit'
                "size": size,
            }

            if order_type.lower() == "limit" and price:
                order_data["price"] = price

            result = self.trade_client.create_order(**order_data)

            logger.info(f"✅ Order placed successfully: {result}")
            return {
                "order_id": result.get("orderId"),
                "symbol": symbol,
                "side": side,
                "type": order_type,
                "size": size,
                "price": price,
                "status": "submitted",
                "timestamp": datetime.now(),
                "source": "kucoin_sdk",
            }

        except Exception as e:
            logger.error(f"❌ KuCoin SDK order error: {e}")
            return None

    def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance using SDK"""
        try:
            if not self.user_client or not self.authenticated:
                return {"error": "Not authenticated"}

            accounts = self.user_client.get_account_list()

            # Process balance data
            balances = {}
            total_value = 0

            for account in accounts:
                currency = account.get("currency")
                balance = float(account.get("balance", 0))
                available = float(account.get("available", 0))
                holds = float(account.get("holds", 0))

                if balance > 0:
                    balances[currency] = {
                        "balance": balance,
                        "available": available,
                        "holds": holds,
                        "currency": currency,
                    }

                    # Add to total value (simplified - would need price conversion)
                    if currency == "USDT":
                        total_value += balance

            return {
                "balances": balances,
                "total_value_usdt": total_value,
                "timestamp": datetime.now(),
                "source": "kucoin_sdk",
            }

        except Exception as e:
            logger.error(f"❌ KuCoin SDK balance error: {e}")
            return {"error": str(e)}

    def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status using SDK"""
        try:
            if not self.trade_client or not self.authenticated:
                return None

            order = self.trade_client.get_order(order_id)

            return {
                "order_id": order.get("id"),
                "symbol": order.get("symbol"),
                "side": order.get("side"),
                "type": order.get("type"),
                "size": order.get("size"),
                "price": order.get("price"),
                "status": order.get("isActive"),
                "filled_size": order.get("dealSize"),
                "filled_value": order.get("dealFunds"),
                "timestamp": datetime.fromtimestamp(
                    int(order.get("createdAt", 0)) / 1000
                ),
                "source": "kucoin_sdk",
            }

        except Exception as e:
            logger.error(f"❌ KuCoin SDK order status error: {e}")
            return None


# Create singleton instance
kucoin_sdk = KuCoinSDKWrapper()


# Compatibility functions for existing code
def get_kucoin_tickers():
    """Compatibility function"""
    return kucoin_sdk.get_all_tickers()


def get_kucoin_price(symbol: str):
    """Compatibility function"""
    return kucoin_sdk.get_token_price(symbol)


def place_kucoin_order(
    symbol: str, side: str, order_type: str, size: str, price: Optional[str] = None
):
    """Compatibility function"""
    return kucoin_sdk.place_order(symbol, side, order_type, size, price)


if __name__ == "__main__":
    # Test the SDK wrapper
    print("🧪 Testing KuCoin SDK Wrapper")
    print("=" * 40)

    # Test tickers
    tickers = kucoin_sdk.get_all_tickers()
    print(f"✅ Fetched {len(tickers)} tickers")

    # Test single price
    btc_price = kucoin_sdk.get_token_price("BTC-USDT")
    print(f"✅ BTC Price: ${btc_price}")

    # Test balance (if authenticated)
    if kucoin_sdk.authenticated:
        balance = kucoin_sdk.get_account_balance()
        print(f"✅ Account balance: {balance}")
    else:
        print("⚠️ Not authenticated - skipping balance test")
