"""
Bot Manager - Enhanced Bot Management System
============================================

This module provides robust bot management with:
- Automatic error recovery and restarts
- Health monitoring and status tracking
- Graceful shutdown handling
- Persistent background operation
- Circuit breaker with auto-recovery
- Comprehensive logging and alerting
"""

import asyncio
import logging
import time
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json
import os

# Bot imports
from live_runner import start_alpha_bot, stop_alpha_bot, get_alpha_bot_status
from micro_bot import start_micro_bot, stop_micro_bot, get_micro_bot_status

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backend/logs/bot_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BotState(Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    RECOVERING = "recovering"

@dataclass
class BotStatus:
    name: str
    state: BotState
    last_started: Optional[datetime] = None
    last_stopped: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None
    restart_count: int = 0
    uptime: timedelta = timedelta()

class BotManager:
    """Enhanced bot management system with automatic recovery and monitoring."""
    
    def __init__(self):
        self.bots: Dict[str, BotStatus] = {
            "alpha_bot": BotStatus("Alpha Predator Bot", BotState.STOPPED),
            "micro_bot": BotStatus("Micro Bot", BotState.STOPPED)
        }
        self.bot_tasks: Dict[str, Optional[asyncio.Task]] = {
            "alpha_bot": None,
            "micro_bot": None
        }
        self.bot_functions = {
            "alpha_bot": {
                "start": start_alpha_bot,
                "stop": stop_alpha_bot,
                "status": get_alpha_bot_status
            },
            "micro_bot": {
                "start": start_micro_bot,
                "stop": stop_micro_bot,
                "status": get_micro_bot_status
            }
        }
        self.is_running = False
        self.manager_task: Optional[asyncio.Task] = None
        self.status_file = "backend/data/bot_manager_status.json"
        
        # Configuration
        self.max_error_count = 5
        self.restart_delay = 30  # seconds
        self.health_check_interval = 60  # seconds
        self.circuit_breaker_timeout = 300  # 5 minutes
        
        # Load previous state
        self._load_state()
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}. Initiating graceful shutdown...")
            asyncio.create_task(self.shutdown_all())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def _load_state(self):
        """Load bot manager state from file."""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r') as f:
                    data = json.load(f)
                    for bot_name, bot_data in data.get('bots', {}).items():
                        if bot_name in self.bots:
                            self.bots[bot_name].error_count = bot_data.get('error_count', 0)
                            self.bots[bot_name].restart_count = bot_data.get('restart_count', 0)
                            if bot_data.get('last_error'):
                                self.bots[bot_name].last_error = bot_data['last_error']
                logger.info("Bot manager state loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load bot manager state: {e}")

    def _save_state(self):
        """Save bot manager state to file."""
        try:
            os.makedirs(os.path.dirname(self.status_file), exist_ok=True)
            data = {
                'bots': {
                    bot_name: {
                        'state': bot.state.value,
                        'error_count': bot.error_count,
                        'restart_count': bot.restart_count,
                        'last_error': bot.last_error,
                        'last_started': bot.last_started.isoformat() if bot.last_started else None,
                        'last_stopped': bot.last_stopped.isoformat() if bot.last_stopped else None
                    }
                    for bot_name, bot in self.bots.items()
                },
                'manager_running': self.is_running,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.status_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save bot manager state: {e}")

    async def start_bot(self, bot_type: str = "alpha") -> Dict[str, Any]:
        """Start bot with immediate response for UI"""
        try:
            logger.info(f"🚀 Starting {bot_type} bot...")
            
            # Immediate response to UI
            response = {
                "success": True,
                "message": f"{bot_type.title()} bot starting...",
                "status": "starting",
                "timestamp": datetime.now().isoformat()
            }
            
            # Start bot in background thread for responsiveness
            import threading
            
            def start_bot_background():
                try:
                    if bot_type == "alpha":
                        self.alpha_bot_active = True
                        self.alpha_bot_thread = threading.Thread(target=self._run_alpha_bot)
                        self.alpha_bot_thread.daemon = True
                        self.alpha_bot_thread.start()
                        logger.info("✅ Alpha bot started successfully")
                    elif bot_type == "micro":
                        self.micro_bot_active = True
                        self.micro_bot_thread = threading.Thread(target=self._run_micro_bot)
                        self.micro_bot_thread.daemon = True
                        self.micro_bot_thread.start()
                        logger.info("✅ Micro bot started successfully")
                except Exception as e:
                    logger.error(f"❌ Bot start failed: {e}")
                    if bot_type == "alpha":
                        self.alpha_bot_active = False
                    elif bot_type == "micro":
                        self.micro_bot_active = False
            
            # Start in background
            threading.Thread(target=start_bot_background, daemon=True).start()
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Failed to start {bot_type} bot: {e}")
            return {
                "success": False,
                "message": f"Failed to start {bot_type} bot: {str(e)}",
                "status": "error",
                "timestamp": datetime.now().isoformat()
            }
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all bots and the manager."""
        return {
            "manager_running": self.is_running,
            "bots": {
                bot_name: {
                    "name": bot.name,
                    "state": bot.state.value,
                    "error_count": bot.error_count,
                    "restart_count": bot.restart_count,
                    "last_error": bot.last_error,
                    "last_started": bot.last_started.isoformat() if bot.last_started else None,
                    "last_stopped": bot.last_stopped.isoformat() if bot.last_stopped else None,
                    "uptime_seconds": (datetime.now() - bot.last_started).total_seconds() if bot.last_started and bot.state == BotState.RUNNING else 0
                }
                for bot_name, bot in self.bots.items()
            }
        }

    async def start_all_bots(self) -> Dict[str, Any]:
        """Start all bots."""
        results = {}
        for bot_name in self.bots.keys():
            results[bot_name] = await self.start_bot(bot_name)
        return {"results": results}

    async def stop_all_bots(self) -> Dict[str, Any]:
        """Stop all bots."""
        results = {}
        for bot_name in self.bots.keys():
            results[bot_name] = await self.stop_bot(bot_name)
        return {"results": results}


# Global bot manager instance
bot_manager = BotManager()

# Convenience functions for API endpoints
async def start_bot_manager():
    """Start the bot manager."""
    return await bot_manager.start_manager()

async def stop_bot_manager():
    """Stop the bot manager."""
    return await bot_manager.stop_manager()

async def start_alpha_predator_bot():
    """Start the Alpha Predator bot."""
    return await bot_manager.start_bot("alpha_bot")

async def stop_alpha_predator_bot():
    """Stop the Alpha Predator bot."""
    return await bot_manager.stop_bot("alpha_bot")

async def start_micro_trading_bot():
    """Start the Micro Trading bot."""
    return await bot_manager.start_bot("micro_bot")

async def stop_micro_trading_bot():
    """Stop the Micro Trading bot."""
    return await bot_manager.stop_bot("micro_bot")

async def restart_alpha_predator_bot():
    """Restart the Alpha Predator bot."""
    return await bot_manager.restart_bot("alpha_bot")

async def restart_micro_trading_bot():
    """Restart the Micro Trading bot."""
    return await bot_manager.restart_bot("micro_bot")

def get_bot_manager_status():
    """Get the status of all bots and the manager."""
    return bot_manager.get_status()

async def shutdown_all_bots():
    """Shutdown all bots gracefully."""
    return await bot_manager.shutdown_all()

if __name__ == "__main__":
    """Run the bot manager as a standalone service."""
    async def main():
        try:
            # Start the bot manager
            await bot_manager.start_manager()
            
            # Start both bots
            await bot_manager.start_bot("alpha_bot")
            await bot_manager.start_bot("micro_bot")
            
            logger.info("Bot Manager is running. Press Ctrl+C to stop.")
            
            # Keep running until interrupted
            while bot_manager.is_running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal. Shutting down...")
        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)
        finally:
            await bot_manager.shutdown_all()
    
    # Run the main function
    asyncio.run(main())
