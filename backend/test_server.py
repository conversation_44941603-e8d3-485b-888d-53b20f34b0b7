#!/usr/bin/env python3
"""
Simple test server to verify our fast endpoints work
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import json
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Alpha Predator Test API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple cache
_cache = {}
_cache_timestamps = {}
CACHE_DURATION = 180

def get_cached_or_fresh(cache_key: str, fetch_function):
    """Get data from cache or fetch fresh if expired"""
    current_time = time.time()
    
    if (cache_key in _cache and 
        cache_key in _cache_timestamps and 
        current_time - _cache_timestamps[cache_key] < CACHE_DURATION):
        logger.info(f"✅ Cache hit for {cache_key}")
        return _cache[cache_key]
    
    logger.info(f"🔄 Cache miss for {cache_key} - fetching fresh data")
    try:
        fresh_data = fetch_function()
        _cache[cache_key] = fresh_data
        _cache_timestamps[cache_key] = current_time
        return fresh_data
    except Exception as e:
        logger.error(f"❌ Failed to fetch {cache_key}: {e}")
        if cache_key in _cache:
            logger.info(f"🔄 Returning stale cache for {cache_key}")
            return _cache[cache_key]
        return {"error": f"Failed to fetch {cache_key}", "timestamp": current_time}

@app.get("/")
def root():
    return {"message": "Alpha Predator Test API", "status": "running", "timestamp": time.time()}

@app.get("/api/dashboard/summary-fast")
def get_summary_fast():
    """Get trading summary with caching (fast)"""
    def fetch_summary():
        return {
            "trades_today": 5,
            "total_pnl": 150.75,
            "active_positions": 3,
            "timestamp": time.time()
        }
    return get_cached_or_fresh("summary", fetch_summary)

@app.get("/api/dashboard/discover-fast")
def get_discover_fast():
    """Get discover tokens with caching (fast)"""
    def fetch_discover_data():
        try:
            discover_file = "data/discover_tokens.json"
            if os.path.exists(discover_file):
                with open(discover_file, "r") as f:
                    tokens = json.load(f)
                    if tokens and len(tokens) > 0:
                        return {"tokens": tokens[:30], "source": "file_cache"}
        except Exception as e:
            logger.warning(f"Failed to load discover from file: {e}")
        
        # Fallback mock data
        return {
            "tokens": [
                {"symbol": "BTC-USDT", "price": 43000, "volume": 1000000, "score": 1.5},
                {"symbol": "ETH-USDT", "price": 2600, "volume": 800000, "score": 1.4},
                {"symbol": "SOL-USDT", "price": 100, "volume": 500000, "score": 1.3}
            ],
            "source": "mock_data"
        }
    
    return get_cached_or_fresh("discover", fetch_discover_data)

@app.get("/api/dashboard/ai-signals-fast")
def get_ai_signals_fast():
    """Get AI signals with caching (fast)"""
    def fetch_ai_signals():
        try:
            ai_signals_file = "data/ai_signals_cache.json"
            if os.path.exists(ai_signals_file):
                with open(ai_signals_file, "r") as f:
                    cached_signals = json.load(f)
                    if cached_signals and time.time() - cached_signals.get("timestamp", 0) < 300:
                        return cached_signals
        except Exception as e:
            logger.warning(f"Failed to load AI signals cache: {e}")
        
        # Generate mock AI signals
        signals = [
            {"symbol": "BTC-USDT", "signal": "BUY", "confidence": 75, "reason": "Strong momentum", "source": "AI Analysis"},
            {"symbol": "ETH-USDT", "signal": "HOLD", "confidence": 60, "reason": "Neutral trend", "source": "AI Analysis"},
            {"symbol": "SOL-USDT", "signal": "BUY", "confidence": 70, "reason": "Bullish pattern", "source": "AI Analysis"}
        ]
        
        result = {
            "signals": signals,
            "source": "mock_analysis",
            "timestamp": time.time(),
        }
        
        return result
    
    return get_cached_or_fresh("ai_signals", fetch_ai_signals)

@app.get("/api/dashboard/all-fast")
def get_all_dashboard_fast():
    """Get all dashboard data with caching (ultra fast)"""
    try:
        return {
            "summary": get_cached_or_fresh("summary", lambda: {
                "trades_today": 5,
                "total_pnl": 150.75,
                "active_positions": 3,
                "timestamp": time.time()
            }),
            "discover": get_cached_or_fresh("discover", lambda: {
                "tokens": [
                    {"symbol": "BTC-USDT", "price": 43000, "volume": 1000000, "score": 1.5},
                    {"symbol": "ETH-USDT", "price": 2600, "volume": 800000, "score": 1.4}
                ],
                "source": "mock_data"
            }),
            "ai_signals": get_cached_or_fresh("ai_signals", lambda: {
                "signals": [
                    {"symbol": "BTC-USDT", "signal": "BUY", "confidence": 75}
                ],
                "source": "mock_analysis",
                "timestamp": time.time()
            }),
            "cache_info": {
                "cached_sections": list(_cache.keys()),
                "cache_ages": {
                    key: time.time() - timestamp
                    for key, timestamp in _cache_timestamps.items()
                },
                "cache_duration": CACHE_DURATION,
            },
        }
    except Exception as e:
        logger.error(f"❌ Failed to get dashboard data: {e}")
        return {"error": str(e), "timestamp": time.time()}

if __name__ == "__main__":
    import uvicorn
    logger.info("🚀 Starting Alpha Predator Test Server on port 3005")
    uvicorn.run(app, host="0.0.0.0", port=3005)
