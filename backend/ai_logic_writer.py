import json
from pathlib import Path
import logging

AI_LOGIC_FILE = Path("backend/data/ai_logic.json")
logger = logging.getLogger("ai_logic_writer")

def append_ai_logic_entry(entry):
    """
    Safely appends a new decision to the ai_logic.json file for frontend display.
    Automatically initializes the file if it does not exist.
    """
    try:
        if AI_LOGIC_FILE.exists():
            with AI_LOGIC_FILE.open("r") as f:
                data = json.load(f)
        else:
            data = []

        # Ensure all reasoning fields exist
        entry.setdefault("openai_reason", "Reasoning from OpenAI not provided.")
        entry.setdefault("gemini_reason", "Reasoning from Gemini not provided.")
        entry.setdefault("deepseek_reason", "Reasoning from DeepSeek not provided.")

        data.append(entry)

        AI_LOGIC_FILE.parent.mkdir(parents=True, exist_ok=True)
        with AI_LOGIC_FILE.open("w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"[✅] Appended decision for {entry.get('symbol')} to ai_logic.json")

    except Exception as e:
        logger.error(f"[ERROR] Failed to update ai_logic.json: {e}")
        return {
            "error": "AI_LOGIC_WRITE_FAILED",
            "message": str(e),
            "status": 500
        }