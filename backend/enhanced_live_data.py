"""
Enhanced Live Data Fetching System
Optimized for real-time token data, trade storage, and API performance
"""

import asyncio
import aiohttp
import json
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import os

from kucoin_data import fetch_kucoin_listed_tokens, get_24h_volume
from token_selector import get_top_tokens_for_trading
from trade_logger import _save_json_file, _load_json_file, _storage_lock

logger = logging.getLogger(__name__)

# Enhanced configuration
CACHE_DIR = "backend/data"
LIVE_TOKENS_FILE = os.path.join(CACHE_DIR, "live_tokens.json")
TOKEN_PRICES_FILE = os.path.join(CACHE_DIR, "token_prices.json")
MARKET_DATA_FILE = os.path.join(CACHE_DIR, "market_data.json")

# Cache settings
CACHE_DURATION = 60  # 1 minute for live data
PRICE_CACHE_DURATION = 30  # 30 seconds for prices
TOKEN_LIST_CACHE_DURATION = 300  # 5 minutes for token list

class EnhancedLiveDataManager:
    """Enhanced live data manager with caching and optimization."""
    
    def __init__(self):
        self.price_cache = {}
        self.token_cache = {}
        self.last_update = {}
        self._lock = threading.RLock()
        
    def _is_cache_valid(self, cache_key: str, duration: int) -> bool:
        """Check if cache is still valid."""
        if cache_key not in self.last_update:
            return False
        return (time.time() - self.last_update[cache_key]) < duration
    
    def _update_cache_timestamp(self, cache_key: str):
        """Update cache timestamp."""
        self.last_update[cache_key] = time.time()
    
    async def fetch_live_kucoin_tokens(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Fetch live tokens from KuCoin with enhanced caching."""
        cache_key = f"kucoin_tokens_{limit}"
        
        with self._lock:
            # Check cache first
            if (cache_key in self.token_cache and 
                self._is_cache_valid(cache_key, TOKEN_LIST_CACHE_DURATION)):
                logger.debug(f"Returning cached KuCoin tokens ({len(self.token_cache[cache_key])} tokens)")
                return self.token_cache[cache_key]
        
        try:
            logger.info(f"Fetching {limit} live tokens from KuCoin...")
            
            # Use existing optimized function
            tokens = fetch_kucoin_listed_tokens(limit=limit)
            
            if tokens:
                # Enhance with additional live data
                enhanced_tokens = await self._enhance_token_data(tokens)
                
                with self._lock:
                    self.token_cache[cache_key] = enhanced_tokens
                    self._update_cache_timestamp(cache_key)
                
                # Save to file for persistence
                _save_json_file(LIVE_TOKENS_FILE, {
                    "tokens": enhanced_tokens,
                    "timestamp": datetime.now().isoformat(),
                    "count": len(enhanced_tokens)
                })
                
                logger.info(f"✅ Fetched and cached {len(enhanced_tokens)} live tokens")
                return enhanced_tokens
            else:
                logger.warning("No tokens received from KuCoin")
                return []
                
        except Exception as e:
            logger.error(f"Failed to fetch live KuCoin tokens: {e}")
            
            # Try to return cached data even if expired
            with self._lock:
                if cache_key in self.token_cache:
                    logger.info("Returning stale cached data due to fetch failure")
                    return self.token_cache[cache_key]
            
            return []
    
    async def _enhance_token_data(self, tokens: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance token data with additional metrics."""
        enhanced_tokens = []
        
        for token in tokens:
            try:
                # Add enhanced metrics
                enhanced_token = token.copy()
                
                # Calculate additional metrics
                price = float(token.get("price", 0))
                volume = float(token.get("volume", 0))
                change_rate = float(token.get("volume_ratio", 0))
                
                # Add trading readiness score
                trading_score = self._calculate_trading_score(price, volume, change_rate)
                enhanced_token["trading_score"] = trading_score
                
                # Add market cap estimate if available
                if "cg_market_cap" in token:
                    enhanced_token["market_cap"] = token["cg_market_cap"]
                
                # Add timestamp
                enhanced_token["last_updated"] = datetime.now().isoformat()
                
                enhanced_tokens.append(enhanced_token)
                
            except Exception as e:
                logger.debug(f"Failed to enhance token {token.get('symbol', 'unknown')}: {e}")
                enhanced_tokens.append(token)  # Add original if enhancement fails
        
        return enhanced_tokens
    
    def _calculate_trading_score(self, price: float, volume: float, change_rate: float) -> float:
        """Calculate trading readiness score (0-100)."""
        try:
            # Volume score (0-40 points)
            volume_score = min(40, (volume / 1_000_000) * 10)
            
            # Price stability score (0-30 points)
            price_score = 30 if price > 0.0001 else (price / 0.0001) * 30
            
            # Volatility score (0-30 points)
            volatility_score = min(30, abs(change_rate) * 100)
            
            total_score = volume_score + price_score + volatility_score
            return min(100, total_score)
            
        except Exception:
            return 0.0
    
    async def get_live_token_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Get live prices for specific tokens."""
        cache_key = "token_prices"
        
        with self._lock:
            # Check cache
            if (cache_key in self.price_cache and 
                self._is_cache_valid(cache_key, PRICE_CACHE_DURATION)):
                cached_prices = self.price_cache[cache_key]
                # Return cached prices for requested symbols
                return {symbol: cached_prices.get(symbol, 0.0) for symbol in symbols}
        
        try:
            # Fetch fresh prices
            prices = {}
            
            # Use KuCoin API to get current prices
            tokens = await self.fetch_live_kucoin_tokens(limit=200)
            
            for token in tokens:
                symbol = token.get("symbol", "")
                price = float(token.get("price", 0))
                if symbol and price > 0:
                    prices[symbol] = price
            
            with self._lock:
                self.price_cache[cache_key] = prices
                self._update_cache_timestamp(cache_key)
            
            # Save to file
            _save_json_file(TOKEN_PRICES_FILE, {
                "prices": prices,
                "timestamp": datetime.now().isoformat(),
                "count": len(prices)
            })
            
            # Return requested symbols
            return {symbol: prices.get(symbol, 0.0) for symbol in symbols}
            
        except Exception as e:
            logger.error(f"Failed to fetch live prices: {e}")
            return {symbol: 0.0 for symbol in symbols}
    
    def get_top_trading_tokens(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get top tokens optimized for trading."""
        try:
            # Use enhanced token selector
            tokens = get_top_tokens_for_trading(limit=limit)
            
            if tokens:
                logger.info(f"✅ Retrieved {len(tokens)} top trading tokens")
                return tokens
            else:
                logger.warning("No trading tokens available, using fallback")
                # Fallback to cached data
                cached_data = _load_json_file(LIVE_TOKENS_FILE, {})
                return cached_data.get("tokens", [])[:limit]
                
        except Exception as e:
            logger.error(f"Failed to get top trading tokens: {e}")
            return []
    
    def get_market_summary(self) -> Dict[str, Any]:
        """Get comprehensive market summary."""
        try:
            # Get live tokens
            tokens = self.get_top_trading_tokens(limit=50)
            
            if not tokens:
                return {"error": "No market data available"}
            
            # Calculate market metrics
            total_volume = sum(float(token.get("volume", 0)) for token in tokens)
            avg_change = sum(float(token.get("volume_ratio", 0)) for token in tokens) / len(tokens)
            
            # Count trending tokens
            bullish_tokens = len([t for t in tokens if float(t.get("volume_ratio", 0)) > 0.05])
            bearish_tokens = len([t for t in tokens if float(t.get("volume_ratio", 0)) < -0.05])
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "total_tokens": len(tokens),
                "total_volume": total_volume,
                "average_change": avg_change,
                "bullish_tokens": bullish_tokens,
                "bearish_tokens": bearish_tokens,
                "neutral_tokens": len(tokens) - bullish_tokens - bearish_tokens,
                "top_tokens": tokens[:10],  # Top 10 for summary
                "market_sentiment": "bullish" if avg_change > 0.02 else "bearish" if avg_change < -0.02 else "neutral"
            }
            
            # Save market summary
            _save_json_file(MARKET_DATA_FILE, summary)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate market summary: {e}")
            return {"error": str(e)}

# Global instance
live_data_manager = EnhancedLiveDataManager()

# Convenience functions for API endpoints
async def get_live_tokens(limit: int = 50) -> List[Dict[str, Any]]:
    """Get live tokens for API endpoints."""
    return await live_data_manager.fetch_live_kucoin_tokens(limit)

def get_trading_tokens(limit: int = 20) -> List[Dict[str, Any]]:
    """Get top trading tokens for API endpoints."""
    return live_data_manager.get_top_trading_tokens(limit)

async def get_token_prices(symbols: List[str]) -> Dict[str, float]:
    """Get live token prices for API endpoints."""
    return await live_data_manager.get_live_token_prices(symbols)

def get_market_data() -> Dict[str, Any]:
    """Get market summary for API endpoints."""
    return live_data_manager.get_market_summary()
