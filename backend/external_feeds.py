from utils.api_client import get
import requests # Keep requests for now, as CoinMarketCalAPI might still use it directly
import feedparser
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === Reddit ===
def fetch_reddit_news(subreddit="cryptocurrency", limit=10):
    """
    Fetch latest posts from a Reddit subreddit using Reddit's public JSON API.
    
    Args:
        subreddit (str): Subreddit to fetch from.
        limit (int): Number of posts to retrieve.

    Returns:
        List[dict]: News items with title, url, author, timestamp, and source.
    """
    url = f"https://www.reddit.com/r/{subreddit}/new.json?limit={limit}"
    headers = {"User-Agent": "AlphaPredatorBot/1.0"}
    try:
        resp = get(url, headers=headers, timeout=10, cache_ttl=300) # Cache Reddit news for 5 minutes
        resp.raise_for_status()
        posts = resp.json().get("data", {}).get("children", [])
        news_items = []
        for post in posts:
            data = post.get("data", {})
            news_items.append({
                "title": data.get("title", "No Title"),
                "url": f"https://reddit.com{data.get('permalink', '')}",
                "author": data.get("author", "unknown"),
                "timestamp": datetime.fromtimestamp(data.get("created_utc", 0), tz=timezone.utc),
                "source": f"Reddit/r/{subreddit}"
            })
        logger.info(f"✅ Fetched {len(news_items)} Reddit posts from r/{subreddit}")
        return news_items
    except requests.RequestException as e:
        logger.error(f"❌ Failed to fetch Reddit news: {e}")
        return []

# === GitHub ===
def fetch_github_releases(owner, repo, limit=5):
    """
    Fetch latest releases from a GitHub repo via GitHub's API.
    
    Args:
        owner (str): GitHub username or organization.
        repo (str): Repository name.
        limit (int): Number of releases to return.

    Returns:
        List[dict]: Release data including title, url, author, timestamp, and source.
    """
    url = f"https://api.github.com/repos/{owner}/{repo}/releases"
    try:
        resp = get(url, timeout=10, cache_ttl=3600) # Cache GitHub releases for 1 hour
        resp.raise_for_status()
        releases = resp.json()[:limit]
        news_items = []
        for release in releases:
            published_str = release.get("published_at")
            if not published_str:
                continue
            published_at = datetime.strptime(published_str, "%Y-%m-%dT%H:%M:%SZ").replace(tzinfo=timezone.utc)
            news_items.append({
                "title": release.get("name") or release.get("tag_name", "No Title"),
                "url": release.get("html_url", ""),
                "author": release.get("author", {}).get("login", "unknown") if release.get("author") else "unknown",
                "timestamp": published_at,
                "source": f"GitHub/{owner}/{repo}"
            })
        logger.info(f"✅ Fetched {len(news_items)} GitHub releases from {owner}/{repo}")
        return news_items
    except requests.RequestException as e:
        logger.error(f"❌ Failed to fetch GitHub releases: {e}")
        return []

# Simple in-memory cache for demonstration purposes
_rss_cache = {}

def get_cached_data(key):
    entry = _rss_cache.get(key)
    if entry:
        value, expires_at = entry
        if datetime.now(timezone.utc) < expires_at:
            return value
        else:
            del _rss_cache[key]
    return None

def set_cached_data(key, value, ttl=600):
    expires_at = datetime.now(timezone.utc) + timedelta(seconds=ttl)
    _rss_cache[key] = (value, expires_at)

# === RSS Feed ===
def fetch_rss_news(feed_url, limit=10):
    """
    Fetch latest news from an RSS feed URL using feedparser.

    Args:
        feed_url (str): RSS feed URL.
        limit (int): Number of items to return.

    Returns:
        List[dict]: Parsed news entries with title, url, author, timestamp, and source.
    """
    try:
        cache_key = f"rss_feed_cache:{feed_url}"
        cached_feed = get_cached_data(cache_key)
        if cached_feed:
            logger.info(f"Returning cached RSS news from {feed_url}")
            feed = cached_feed
        else:
            feed = feedparser.parse(feed_url)
            set_cached_data(cache_key, feed, ttl=600) # Cache RSS feeds for 10 minutes
        news_items = []
        for entry in feed.entries[:limit]:
            import time
            published_parsed = entry.get("published_parsed") or entry.get("updated_parsed")
            if published_parsed and isinstance(published_parsed, time.struct_time):
                published_at = datetime(*published_parsed[:6], tzinfo=timezone.utc)
            else:
                published_at = None
            news_items.append({
                "title": entry.get("title", "No Title"),
                "url": entry.get("link", ""),
                "author": entry.get("author", "unknown"),
                "timestamp": published_at,
                "source": feed.feed.get("title", "RSS Feed") if isinstance(feed.feed, dict) else "RSS Feed"
            })
        logger.info(f"✅ Fetched {len(news_items)} RSS news items from {feed_url}")
        return news_items
    except Exception as e:
        logger.error(f"❌ Failed to fetch RSS news: {e}")
        return []

# === CoinMarketCal ===
def fetch_coinmarketcal_events(coin_symbols: Optional[List[str]] = None, limit=50):
    """
    Fetch upcoming events from CoinMarketCal API and format as news items.
    
    Args:
        coin_symbols (List[str]): List of token symbols (e.g., ['BTC', 'ETH']) to filter events by.
        limit (int): Number of events to retrieve.

    Returns:
        List[dict]: News items with title, url, author, timestamp, and source.
    """
    from coinmarketcal_api import CoinMarketCalAPI
    from price_fetcher import _get_coingecko_id_map
    from datetime import datetime, timedelta

    api = CoinMarketCalAPI()
    coingecko_id_map = _get_coingecko_id_map()
    coin_ids = []
    if coin_symbols:
        for symbol in coin_symbols:
            coingecko_id = coingecko_id_map.get(symbol.upper())
            if coingecko_id:
                coin_ids.append(coingecko_id)
            else:
                logger.warning(f"Could not find CoinGecko ID for CoinMarketCal event filtering: {symbol}")

    # Fetch events for the next 30 days
    events = api.get_events(coin_ids=coin_ids, dateRangeStart=datetime.now().strftime('%d/%m/%Y'), 
                            dateRangeEnd=(datetime.now() + timedelta(days=30)).strftime('%d/%m/%Y'),
                            max=limit)
    news_items = []
    for event in events:
        # Ensure event is a dictionary before accessing with .get()
        if isinstance(event, dict):
            # CoinMarketCal API returns date_event in DD/MM/YYYY format
            event_date_str = event.get("date_event")
            if event_date_str:
                try:
                    # Convert to datetime object, assuming UTC for consistency
                    event_datetime = datetime.strptime(event_date_str, "%d/%m/%Y").replace(tzinfo=timezone.utc)
                except ValueError:
                    event_datetime = None
            else:
                event_datetime = None

            news_items.append({
                "title": event.get("title", "No Title"),
                "url": event.get("URL", ""), # CoinMarketCal uses 'URL' key
                "author": event.get("source", "CoinMarketCal"), # Use source as author
                "timestamp": event_datetime,
                "source": "CoinMarketCal"
            })
        else:
            logger.warning(f"Expected dict for event, got {type(event)}: {event}")
    logger.info(f"✅ Fetched {len(news_items)} CoinMarketCal events.")
    return news_items

# === Aggregator ===
def get_all_news(subreddit="cryptocurrency", reddit_limit=10, github_owner="", github_repo="", github_limit=5, rss_url="", rss_limit=10, coinmarketcal_limit=50, coinmarketcal_symbols: Optional[List[str]] = None):
    """
    Fetch news from all available sources.
    """
    all_news = []
    all_news.extend(fetch_reddit_news(subreddit=subreddit, limit=reddit_limit))
    if github_owner and github_repo:
        all_news.extend(fetch_github_releases(owner=github_owner, repo=github_repo, limit=github_limit))
    if rss_url:
        all_news.extend(fetch_rss_news(feed_url=rss_url, limit=rss_limit))
    all_news.extend(fetch_coinmarketcal_events(coin_symbols=coinmarketcal_symbols, limit=coinmarketcal_limit))
    
    # Sort by timestamp, newest first
    all_news.sort(key=lambda x: x['timestamp'] if x['timestamp'] else datetime.min.replace(tzinfo=timezone.utc), reverse=True)
    
    return all_news
