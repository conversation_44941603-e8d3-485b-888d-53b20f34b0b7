"""
Optimized AI Core - Production-grade AI trading logic with enhanced performance,
caching, error handling, and multi-threading capabilities.
"""

import json
import os
import asyncio
import time
import functools
import logging
import concurrent.futures
from typing import Dict, List, Optional, Any, Tuple, Union, cast
from dataclasses import dataclass
from datetime import datetime, timedelta
import threading
from contextlib import asynccontextmanager

from error_codes import error_response
from token_selector import generate_top_token_list
from prompt_builder import build_trade_prompt
from ai_validation_engine import get_final_ai_decision
from trade_executor import execute_trade
from nlp_filter import is_relevant_news
from news_ingestion.sentiment_loader import load_sentiment_score_for_token
from reddit_github_alpha import fetch_signal_sentiment
from telegram_utils import notify_trade
from volume_ratio import get_volume_ratio_kucoin
from ai_logic_writer import append_ai_logic_entry

# get_ai_decision import not available, using fallback
# from ai_clients.ai_request_manager import get_ai_decision
from event_driven_signals import EventDrivenSignals
from tokenmetrics_api import TokenMetricsAPI
from indicators import *
from cache import get_cached_data, set_cached_data
from utils.logger import get_logger

logger = get_logger("optimized_ai_core")

# Configuration constants
DECISION_LOG = "backend/data/ai_decisions.jsonl"
AI_SIGNALS_CACHE = "backend/data/ai_signals_cache.json"
AI_LOGIC_FILE = "backend/data/ai_logic.json"
CACHE_TTL_DECISIONS = 300  # 5 minutes
CACHE_TTL_INDICATORS = 600  # 10 minutes
CACHE_TTL_SENTIMENT = 180  # 3 minutes
MAX_CONCURRENT_TOKENS = 5
BATCH_SIZE = 10


@dataclass
class TokenAnalysis:
    """Data class for token analysis results"""

    symbol: str
    decision: str
    confidence: float
    reason: str
    sentiment_score: float
    indicators: Dict[str, Any]
    timestamp: float
    processing_time: float


class OptimizedAICore:
    """
    Optimized AI Core with enhanced performance features:
    - Intelligent caching with TTL
    - Batch processing capabilities
    - Concurrent execution
    - Circuit breaker pattern
    - Performance monitoring
    - Memory optimization
    """

    def __init__(self):
        self.cache_lock = threading.Lock()
        self.processing_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "avg_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
        }
        self.circuit_breaker = {
            "failure_count": 0,
            "last_failure_time": 0,
            "threshold": 5,
            "timeout": 300,  # 5 minutes
        }

    def _is_circuit_open(self) -> bool:
        """Check if circuit breaker is open"""
        if self.circuit_breaker["failure_count"] >= self.circuit_breaker["threshold"]:
            if (
                time.time() - self.circuit_breaker["last_failure_time"]
                < self.circuit_breaker["timeout"]
            ):
                return True
            else:
                # Reset circuit breaker after timeout
                self.circuit_breaker["failure_count"] = 0
        return False

    def _record_failure(self):
        """Record a failure for circuit breaker"""
        self.circuit_breaker["failure_count"] += 1
        self.circuit_breaker["last_failure_time"] = int(time.time())

    def _record_success(self):
        """Record a success for circuit breaker"""
        self.circuit_breaker["failure_count"] = max(
            0, self.circuit_breaker["failure_count"] - 1
        )

    async def _fetch_indicators_cached(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch technical indicators with caching"""
        cache_key = f"indicators_{symbol}"

        with self.cache_lock:
            cached_data = get_cached_data(cache_key)
            if cached_data:
                self.processing_stats["cache_hits"] += 1
                logger.debug(f"Cache hit for indicators: {symbol}")
                return cached_data

            self.processing_stats["cache_misses"] += 1

        try:
            # Fetch candlestick data
            from kucoin_data import fetch_kucoin_candlestick_data

            candlestick_data = await fetch_kucoin_candlestick_data(
                symbol, interval="1hour", limit=90
            )

            if not candlestick_data:
                return None

            # Extract OHLCV data
            close_prices = [float(entry[2]) for entry in candlestick_data]
            high_prices = [float(entry[3]) for entry in candlestick_data]
            low_prices = [float(entry[4]) for entry in candlestick_data]
            volumes = [float(entry[5]) for entry in candlestick_data]

            # Calculate all indicators in parallel
            indicators = await self._calculate_indicators_parallel(
                close_prices, high_prices, low_prices, volumes
            )

            # Cache the results
            with self.cache_lock:
                set_cached_data(cache_key, indicators, CACHE_TTL_INDICATORS)

            return indicators

        except Exception as e:
            logger.error(f"Failed to fetch indicators for {symbol}: {e}")
            return None

    async def _calculate_indicators_parallel(
        self,
        close_prices: List[float],
        high_prices: List[float],
        low_prices: List[float],
        volumes: List[float],
    ) -> Dict[str, Any]:
        """Calculate technical indicators in parallel"""

        loop = asyncio.get_event_loop()

        # Use thread pool for CPU-intensive calculations
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            tasks = [
                loop.run_in_executor(executor, calculate_sma, close_prices, 10),
                loop.run_in_executor(executor, calculate_sma, close_prices, 50),
                loop.run_in_executor(executor, calculate_rsi, close_prices),
                loop.run_in_executor(executor, calculate_macd, close_prices),
                loop.run_in_executor(executor, calculate_bollinger_bands, close_prices),
                loop.run_in_executor(executor, calculate_volume_oscillator, volumes),
                loop.run_in_executor(
                    executor,
                    calculate_stochastic_oscillator,
                    high_prices,
                    low_prices,
                    close_prices,
                ),
                loop.run_in_executor(
                    executor,
                    calculate_ichimoku_cloud,
                    high_prices,
                    low_prices,
                    close_prices,
                ),
                loop.run_in_executor(
                    executor,
                    calculate_fibonacci_retracement,
                    max(close_prices),
                    min(close_prices),
                ),
                loop.run_in_executor(
                    executor,
                    calculate_parabolic_sar,
                    high_prices,
                    low_prices,
                    close_prices,
                ),
                loop.run_in_executor(
                    executor, calculate_atr, high_prices, low_prices, close_prices
                ),
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle results and exceptions
            (
                short_ma,
                long_ma,
                rsi,
                macd_result,
                bb_result,
                vol_osc,
                stoch_result,
                ichimoku,
                fib_levels,
                psar,
                atr,
            ) = results

            # Extract MACD components
            macd, macd_signal = (
                macd_result if isinstance(macd_result, tuple) else (0, 0)
            )

            # Extract Bollinger Bands
            middle_band, upper_band, lower_band = (
                bb_result if isinstance(bb_result, tuple) else (0, 0, 0)
            )

            # Extract Stochastic
            stochastic_k, stochastic_d = (
                stoch_result if isinstance(stoch_result, tuple) else (0, 0)
            )

            # Extract Parabolic SAR latest value
            if isinstance(psar, Exception):
                parabolic_sar_val = 0
            else:
                # Cast to list type after exception check
                psar_list = cast(List[float], psar)
                if isinstance(psar_list, list) and len(psar_list) > 0:
                    parabolic_sar_val = psar_list[-1]
                else:
                    parabolic_sar_val = 0

            return {
                "short_ma": short_ma,
                "long_ma": long_ma,
                "rsi": rsi,
                "macd": macd,
                "macd_signal": macd_signal,
                "upper_band": upper_band,
                "lower_band": lower_band,
                "middle_band": middle_band,
                "volume_oscillator": vol_osc,
                "stochastic_k": stochastic_k,
                "stochastic_d": stochastic_d,
                "ichimoku": ichimoku,
                "fib_levels": fib_levels,
                "parabolic_sar": parabolic_sar_val,
                "atr": atr,
                "current_price": close_prices[-1],
                "breakout_level": max(close_prices[-20:]),
            }

    async def _fetch_sentiment_cached(self, symbol: str) -> Tuple[float, float]:
        """Fetch sentiment scores with caching"""
        cache_key = f"sentiment_{symbol}"

        with self.cache_lock:
            cached_data = get_cached_data(cache_key)
            if cached_data:
                self.processing_stats["cache_hits"] += 1
                return cached_data["cryptopanic"], cached_data["reddit"]

            self.processing_stats["cache_misses"] += 1

        try:
            # Fetch sentiment scores in parallel
            tasks = [
                asyncio.create_task(self._safe_fetch_sentiment(symbol)),
                asyncio.create_task(self._safe_fetch_reddit_sentiment(symbol)),
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle exceptions and ensure float types
            sentiment_result = results[0]
            reddit_result = results[1]

            if isinstance(sentiment_result, Exception):
                sentiment_score = 0.0
            else:
                try:
                    # Cast to the expected type and ensure we have a valid value before converting
                    sentiment_val = cast(Union[float, int], sentiment_result)
                    if sentiment_val is not None:
                        sentiment_score = float(sentiment_val)
                    else:
                        sentiment_score = 0.0
                except (ValueError, TypeError):
                    sentiment_score = 0.0

            if isinstance(reddit_result, Exception):
                reddit_score = 0.0
            else:
                try:
                    # Cast to the expected type and ensure we have a valid value before converting
                    reddit_val = cast(Union[float, int], reddit_result)
                    if reddit_val is not None:
                        reddit_score = float(reddit_val)
                    else:
                        reddit_score = 0.0
                except (ValueError, TypeError):
                    reddit_score = 0.0

            # Cache the results
            sentiment_data = {"cryptopanic": sentiment_score, "reddit": reddit_score}
            with self.cache_lock:
                set_cached_data(cache_key, sentiment_data, CACHE_TTL_SENTIMENT)

            return sentiment_score, reddit_score

        except Exception as e:
            logger.error(f"Failed to fetch sentiment for {symbol}: {e}")
            return 0.0, 0.0

    async def _safe_fetch_sentiment(self, symbol: str) -> float:
        """Safely fetch sentiment score"""
        try:
            return load_sentiment_score_for_token(symbol)
        except Exception as e:
            logger.warning(f"Sentiment fetch failed for {symbol}: {e}")
            return 0.0

    async def _safe_fetch_reddit_sentiment(self, symbol: str) -> float:
        """Safely fetch Reddit sentiment score"""
        try:
            return fetch_signal_sentiment(symbol)
        except Exception as e:
            logger.warning(f"Reddit sentiment fetch failed for {symbol}: {e}")
            return 0.0

    async def analyze_token_optimized(self, symbol: str) -> Optional[TokenAnalysis]:
        """
        Optimized token analysis with caching, error handling, and performance monitoring
        """
        start_time = time.time()

        if self._is_circuit_open():
            logger.warning(f"Circuit breaker open, skipping {symbol}")
            return None

        try:
            # Check cache first
            cache_key = f"token_analysis_{symbol}"
            with self.cache_lock:
                cached_analysis = get_cached_data(cache_key)
                if cached_analysis:
                    self.processing_stats["cache_hits"] += 1
                    logger.debug(f"Cache hit for token analysis: {symbol}")
                    return TokenAnalysis(**cached_analysis)

                self.processing_stats["cache_misses"] += 1

            # Fetch data in parallel
            indicators_task = self._fetch_indicators_cached(symbol)
            sentiment_task = self._fetch_sentiment_cached(symbol)

            results = await asyncio.gather(
                indicators_task, sentiment_task, return_exceptions=True
            )

            indicators_result = results[0]
            sentiment_result = results[1]

            # Handle exceptions for indicators
            if isinstance(indicators_result, Exception):
                logger.error(
                    f"Indicators fetch failed for {symbol}: {indicators_result}"
                )
                self._record_failure()
                return None

            # Cast indicators to proper type
            indicators = cast(Optional[Dict[str, Any]], indicators_result)
            if not indicators:
                logger.warning(f"No indicators data for {symbol}")
                return None

            # Handle exceptions for sentiment
            if isinstance(sentiment_result, Exception):
                logger.warning(
                    f"Sentiment fetch failed for {symbol}: {sentiment_result}"
                )
                sentiment_score, reddit_score = 0.0, 0.0
            elif (
                isinstance(sentiment_result, (tuple, list))
                and len(sentiment_result) == 2
            ):
                sentiment_score, reddit_score = sentiment_result
            else:
                logger.warning(
                    f"Unexpected sentiment_result type for {symbol}: {type(sentiment_result)}"
                )
                sentiment_score, reddit_score = 0.0, 0.0

            # Calculate combined sentiment
            combined_sentiment = round((sentiment_score + reddit_score) / 2, 2)

            # Early filter for negative sentiment
            if combined_sentiment < -0.5:
                logger.info(
                    f"Skipping {symbol} due to negative sentiment: {combined_sentiment}"
                )
                return TokenAnalysis(
                    symbol=symbol,
                    decision="HOLD",
                    confidence=0.0,
                    reason="Negative sentiment filter",
                    sentiment_score=combined_sentiment,
                    indicators=indicators,
                    timestamp=time.time(),
                    processing_time=time.time() - start_time,
                )

            # Build prompt with all indicators
            prompt = self._build_optimized_prompt(
                symbol, indicators, combined_sentiment
            )

            # NLP filtering
            if not await self._safe_nlp_filter(prompt):
                logger.info(f"Skipping {symbol} due to NLP filter")
                return TokenAnalysis(
                    symbol=symbol,
                    decision="HOLD",
                    confidence=0.0,
                    reason="Failed NLP filter",
                    sentiment_score=combined_sentiment,
                    indicators=indicators,
                    timestamp=time.time(),
                    processing_time=time.time() - start_time,
                )

            # Get AI decision
            decision_data = await self._get_ai_decision_safe(
                prompt, symbol, indicators, combined_sentiment
            )

            if not decision_data:
                self._record_failure()
                return None

            # Create analysis result
            analysis = TokenAnalysis(
                symbol=symbol,
                decision=decision_data.get("decision", "HOLD"),
                confidence=decision_data.get("confidence", 0.0),
                reason=decision_data.get("reason", "No reasoning provided"),
                sentiment_score=combined_sentiment,
                indicators=indicators,
                timestamp=time.time(),
                processing_time=time.time() - start_time,
            )

            # Cache the analysis
            with self.cache_lock:
                set_cached_data(cache_key, analysis.__dict__, CACHE_TTL_DECISIONS)

            # Update stats
            self.processing_stats["successful"] += 1
            self.processing_stats["total_processed"] += 1
            self.processing_stats["avg_processing_time"] = (
                self.processing_stats["avg_processing_time"]
                * (self.processing_stats["total_processed"] - 1)
                + analysis.processing_time
            ) / self.processing_stats["total_processed"]

            self._record_success()
            logger.info(
                f"Successfully analyzed {symbol} in {analysis.processing_time:.2f}s"
            )

            return analysis

        except Exception as e:
            logger.error(f"Token analysis failed for {symbol}: {e}")
            self.processing_stats["failed"] += 1
            self.processing_stats["total_processed"] += 1
            self._record_failure()
            return None

    def _build_optimized_prompt(
        self, symbol: str, indicators: Dict[str, Any], sentiment: float
    ) -> str:
        """Build optimized prompt with all indicators"""
        return (
            build_trade_prompt(
                token=symbol,
                price=indicators["current_price"],
                short_ma=indicators["short_ma"],
                long_ma=indicators["long_ma"],
                rsi=indicators["rsi"],
                macd=indicators["macd"],
                macd_signal=indicators["macd_signal"],
                upper_band=indicators["upper_band"],
                lower_band=indicators["lower_band"],
                breakout_level=indicators["breakout_level"],
                volume_oscillator=indicators["volume_oscillator"],
                stochastic_k=indicators["stochastic_k"],
                stochastic_d=indicators["stochastic_d"],
                ichimoku_tenkan=indicators["ichimoku"].get("tenkan_sen"),
                ichimoku_kijun=indicators["ichimoku"].get("kijun_sen"),
                ichimoku_senkou_a=indicators["ichimoku"].get("senkou_span_a"),
                ichimoku_senkou_b=indicators["ichimoku"].get("senkou_span_b"),
                ichimoku_chikou=indicators["ichimoku"].get("chikou_span"),
                fib_levels=indicators["fib_levels"],
                parabolic_sar=indicators["parabolic_sar"],
                atr=indicators["atr"],
            )
            + f"\n\nCombined Sentiment Score: {sentiment}"
        )

    async def _safe_nlp_filter(self, prompt: str) -> bool:
        """Safely apply NLP filter"""
        try:
            return is_relevant_news(prompt)
        except Exception as e:
            logger.warning(f"NLP filter failed: {e}")
            return True  # Default to allowing through if filter fails

    async def _get_ai_decision_safe(
        self, prompt: str, symbol: str, indicators: Dict[str, Any], sentiment: float
    ) -> Optional[Dict[str, Any]]:
        """Safely get AI decision with timeout and error handling"""
        try:
            # Get upcoming events
            event_signals_instance = EventDrivenSignals()
            upcoming_events_raw = event_signals_instance.get_upcoming_events()
            upcoming_events = (
                upcoming_events_raw if isinstance(upcoming_events_raw, list) else []
            )

            # Get AI decision with timeout
            decision_data = await asyncio.wait_for(
                asyncio.create_task(
                    self._async_get_final_ai_decision(
                        prompt, symbol, indicators, sentiment, upcoming_events
                    )
                ),
                timeout=30.0,  # 30 second timeout
            )

            return decision_data

        except asyncio.TimeoutError:
            logger.error(f"AI decision timeout for {symbol}")
            return None
        except Exception as e:
            logger.error(f"AI decision failed for {symbol}: {e}")
            return None

    async def _async_get_final_ai_decision(
        self,
        prompt: str,
        symbol: str,
        indicators: Dict[str, Any],
        sentiment: float,
        events: List[Dict],
    ) -> Dict[str, Any]:
        """Async wrapper for AI decision"""
        loop = asyncio.get_event_loop()

        # Run in thread pool to avoid blocking
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            decision_data = await loop.run_in_executor(
                executor, get_final_ai_decision, prompt, events, symbol
            )

            if isinstance(decision_data, dict):
                return {
                    "decision": decision_data.get("decision", "HOLD"),
                    "confidence": decision_data.get("confidence", 0.0),
                    "reason": decision_data.get("reason", "No reasoning provided."),
                }
            else:
                return {
                    "decision": (
                        decision_data if isinstance(decision_data, str) else "HOLD"
                    ),
                    "confidence": 0.0,
                    "reason": "No reasoning provided.",
                }

    async def batch_analyze_tokens(self, symbols: List[str]) -> List[TokenAnalysis]:
        """
        Batch analyze multiple tokens with concurrency control
        """
        logger.info(f"Starting batch analysis of {len(symbols)} tokens")

        # Process in batches to control memory usage
        results = []

        for i in range(0, len(symbols), BATCH_SIZE):
            batch = symbols[i : i + BATCH_SIZE]
            logger.info(f"Processing batch {i//BATCH_SIZE + 1}: {batch}")

            # Limit concurrent executions
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_TOKENS)

            async def analyze_with_semaphore(symbol):
                async with semaphore:
                    return await self.analyze_token_optimized(symbol)

            # Process batch concurrently
            batch_tasks = [analyze_with_semaphore(symbol) for symbol in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Filter out None results and exceptions
            valid_results = [
                result
                for result in batch_results
                if result is not None and not isinstance(result, Exception)
            ]

            results.extend(valid_results)

            # Small delay between batches to prevent overwhelming APIs
            await asyncio.sleep(1)

        logger.info(
            f"Batch analysis complete. Processed {len(results)} tokens successfully"
        )
        return results

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            **self.processing_stats,
            "cache_hit_rate": (
                self.processing_stats["cache_hits"]
                / (
                    self.processing_stats["cache_hits"]
                    + self.processing_stats["cache_misses"]
                )
                if (
                    self.processing_stats["cache_hits"]
                    + self.processing_stats["cache_misses"]
                )
                > 0
                else 0
            ),
            "success_rate": (
                self.processing_stats["successful"]
                / self.processing_stats["total_processed"]
                if self.processing_stats["total_processed"] > 0
                else 0
            ),
            "circuit_breaker_status": "OPEN" if self._is_circuit_open() else "CLOSED",
        }

    def reset_stats(self):
        """Reset performance statistics"""
        self.processing_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "avg_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
        }


# Global instance
optimized_ai_core = OptimizedAICore()


# Backward compatibility functions
async def run_ai_trade_optimized(symbol: str) -> Dict[str, Any]:
    """Optimized version of run_ai_trade"""
    analysis = await optimized_ai_core.analyze_token_optimized(symbol)

    if not analysis:
        return {"symbol": symbol, "decision": "HOLD"}

    # Execute trade if decision is BUY or SELL
    if analysis.decision == "BUY":
        execute_trade(symbol, "buy")
    elif analysis.decision == "SELL":
        execute_trade(symbol, "sell")

    return {
        "symbol": analysis.symbol,
        "decision": analysis.decision,
        "confidence": analysis.confidence,
        "reason": analysis.reason,
    }


async def batch_run_ai_trades(symbols: List[str]) -> List[Dict[str, Any]]:
    """Batch process multiple tokens"""
    analyses = await optimized_ai_core.batch_analyze_tokens(symbols)

    results = []
    for analysis in analyses:
        # Execute trades
        if analysis.decision == "BUY":
            execute_trade(analysis.symbol, "buy")
        elif analysis.decision == "SELL":
            execute_trade(analysis.symbol, "sell")

        results.append(
            {
                "symbol": analysis.symbol,
                "decision": analysis.decision,
                "confidence": analysis.confidence,
                "reason": analysis.reason,
            }
        )

    return results


def get_ai_performance_stats() -> Dict[str, Any]:
    """Get AI performance statistics"""
    return optimized_ai_core.get_performance_stats()
