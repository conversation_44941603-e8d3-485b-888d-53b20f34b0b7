from utils.api_client import get
import time  # Keep time for now, as it's used in the async function
import requests  # Keep requests for now, as aiohttp is used in the async function
import logging
from error_codes import error_response

logger = logging.getLogger("coingecko_data")
logging.basicConfig(level=logging.INFO)


def fetch_coingecko_volumes(retries=3, backoff_factor=2):
    """
    Fetch top 250 token volume, price, and market data from CoinGecko.

    Returns a dictionary keyed by symbol, each containing:
    id, volume, price, name, market_cap, change_24h
    """
    url = "https://api.coingecko.com/api/v3/coins/markets"
    params = {
        "vs_currency": "usd",
        "order": "market_cap_desc",
        "per_page": 250,
        "page": 1,
        "sparkline": False,
    }

    for attempt in range(retries):
        try:
            # 🔧 FIX: Add proper headers and error handling
            headers = {
                "User-Agent": "AlphaPredatorBot/1.0",
                "Accept": "application/json",
            }

            # Add API key if available
            from config import COINGECKO_API_KEY

            if COINGECKO_API_KEY:
                if COINGECKO_API_KEY.startswith("CG-"):
                    headers["x-cg-pro-api-key"] = COINGECKO_API_KEY
                else:
                    headers["x-cg-demo-api-key"] = COINGECKO_API_KEY

            response = get(
                url, params=params, headers=headers, timeout=15, cache_ttl=60
            )

            # 🔧 FIX: Handle rate limiting properly
            if response.status_code == 429:
                wait_time = int(response.headers.get("Retry-After", 60))
                logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                time.sleep(wait_time)
                continue

            response.raise_for_status()
            data = response.json()
            token_data = {}

            for coin in data:
                symbol = coin["symbol"].lower()
                token_data[symbol] = {
                    "id": coin.get("id"),
                    "volume": coin.get("total_volume", 0),
                    "price": coin.get("current_price"),
                    "name": coin.get("name"),
                    "market_cap": coin.get("market_cap"),
                    "change_24h": coin.get("price_change_percentage_24h"),
                }

            return token_data
        except requests.RequestException as e:
            logger.warning(f"⚠️ CoinGecko API error (attempt {attempt}): {e}")
            time.sleep(backoff_factor**attempt)

    logger.error("❌ Failed to fetch CoinGecko data after retries.")
    return error_response(
        "COINGECKO_FETCH_FAILED", "Failed to fetch CoinGecko data after retries.", 500
    )


async def fetch_coingecko_data(token_name: str):
    """
    Async fetch for a single token's data from CoinGecko.
    """
    url = f"https://api.coingecko.com/api/v3/coins/{token_name.lower()}"
    try:
        import aiohttp

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    return None
                data = await response.json()
                market_data = data.get("market_data", {})
                return {
                    "price": market_data.get("current_price", {}).get("usd", 0),
                    "volume": market_data.get("total_volume", {}).get("usd", 0),
                    "market_cap": market_data.get("market_cap", {}).get("usd", 0),
                }
    except Exception as e:
        logger.warning(f"⚠️ Async fetch error for {token_name}: {e}")
        return None
