"""
DeepSeek AI Client via Together API
Provides access to DeepSeek V3 models for trading decisions through Together AI
"""

import os
import logging
from typing import Dict, Any
import requests
import json
from .model_selector import (
    TaskType,
    Urgency,
    Complexity,
    select_optimal_model,
    optimize_prompt_for_model,
)

logger = logging.getLogger(__name__)


def call_deepseek(
    prompt: str,
    task_type: TaskType = TaskType.TRADE_DECISION,
    urgency: Urgency = Urgency.MEDIUM,
    complexity: Complexity = Complexity.SIMPLE,
) -> Dict[str, Any]:
    """
    Call DeepSeek V3 via Together API with optimized model selection

    DeepSeek V3 via Together: Very cost-effective and fast
    """
    try:
        # Use Together API key for DeepSeek access
        api_key = os.getenv("TOGETHER_API_KEY")
        if not api_key or api_key.startswith("YOUR_"):
            raise ValueError("Together API key not properly configured for DeepSeek")

        # DeepSeek V3 model via Together
        model = "deepseek-ai/deepseek-v3"
        max_tokens = 300 if complexity == Complexity.COMPLEX else 200

        # Optimize prompt for DeepSeek
        model_config = {"cost_tier": "fast"}
        optimized_prompt = optimize_prompt_for_model(prompt, model_config)

        # Add trading-specific system prompt
        system_prompt = """You are an expert cryptocurrency trading AI powered by DeepSeek V3. Analyze the given information and provide a JSON response with:
{
  "decision": "BUY|SELL|HOLD",
  "confidence": 0.0-100.0,
  "reason": "Brief explanation of your decision"
}

Focus on technical analysis, market sentiment, and risk assessment. Always respond with valid JSON only."""

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}",
        }

        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": optimized_prompt},
            ],
            "max_tokens": max_tokens,
            "temperature": 0.3,
            "top_p": 0.9,
            "stream": False,
        }

        # Make request to Together API
        response = requests.post(
            "https://api.together.xyz/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"].strip()

            # Parse JSON response with cleanup
            try:
                # Clean up response (remove markdown formatting if present)
                if content.startswith("```json"):
                    content = content[7:]
                if content.endswith("```"):
                    content = content[:-3]
                content = content.strip()

                parsed_response = json.loads(content)

                # Validate required fields
                if not all(
                    key in parsed_response
                    for key in ["decision", "confidence", "reason"]
                ):
                    raise ValueError("Missing required fields in AI response")

                # Normalize decision and confidence
                decision = str(parsed_response["decision"]).upper()
                if decision not in ["BUY", "SELL", "HOLD"]:
                    decision = "HOLD"

                confidence = float(parsed_response["confidence"])
                confidence = max(0.0, min(100.0, confidence))  # Clamp to 0-100

                logger.info(
                    f"✅ DeepSeek via Together: {decision} (confidence: {confidence})"
                )

                return {
                    "decision": decision,
                    "confidence": confidence,
                    "reason": str(parsed_response["reason"]),
                }

            except json.JSONDecodeError:
                logger.error(f"DeepSeek JSON parse error. Raw content: {content}")
                return _fallback_response("JSON parsing failed")

        else:
            error_msg = f"Together API error: {response.status_code} - {response.text}"
            logger.error(error_msg)
            raise Exception(error_msg)

    except Exception as e:
        logger.error(f"DeepSeek client error: {e}")
        return _fallback_response(str(e))


def _fallback_response(error_msg: str) -> Dict[str, Any]:
    """Fallback response when DeepSeek fails"""
    return {
        "decision": "HOLD",
        "confidence": 50.0,
        "reason": f"DeepSeek unavailable: {error_msg}",
    }
