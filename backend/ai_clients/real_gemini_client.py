"""
Real Google Gemini AI Client
Provides access to Gemini 1.5 models for trading decisions
"""

import os
import logging
import time
from typing import Dict, Any
import requests
import json
from dotenv import load_dotenv
from .model_selector import (
    TaskType,
    Urgency,
    Complexity,
    select_optimal_model,
    optimize_prompt_for_model,
)

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


def call_gemini(
    prompt: str,
    task_type: TaskType = TaskType.TRADE_DECISION,
    urgency: Urgency = Urgency.MEDIUM,
    complexity: Complexity = Complexity.SIMPLE,
) -> Dict[str, Any]:
    """
    Call Google Gemini API with optimized model selection

    Gemini 1.5 Flash: $0.075/1M input, $0.30/1M output (very cheap, fast)
    Gemini 1.5 Pro: $1.25/1M input, $5.00/1M output (balanced)
    """
    try:
        api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        if not api_key or api_key.startswith("YOUR_"):
            raise ValueError("Google/Gemini API key not properly configured")

        # Select optimal Gemini model - always use Flash for free tier efficiency
        model = "gemini-1.5-flash-latest"  # Always use Flash to conserve quota
        max_tokens = 100  # Minimal tokens for JSON response to save quota

        # Optimize prompt for Gemini
        model_config = {"cost_tier": "fast" if "flash" in model else "premium"}
        optimized_prompt = optimize_prompt_for_model(prompt, model_config)

        # Optimized prompt for fewer tokens (Gemini free tier has 50 requests/day limit)
        full_prompt = f"""Crypto trading analysis. Respond ONLY with JSON:
{{"decision": "BUY|SELL|HOLD", "confidence": 0-100, "reason": "brief explanation"}}

Data: {optimized_prompt}

JSON:"""

        url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"

        headers = {"Content-Type": "application/json"}

        data = {
            "contents": [{"parts": [{"text": full_prompt}]}],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": max_tokens,
                "candidateCount": 1,
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE",
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE",
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE",
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE",
                },
            ],
        }

        # Enhanced retry logic for rate limits
        max_retries = 3
        base_delay = 2

        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=30)

                # Handle rate limiting and quota exhaustion
                if response.status_code == 429:
                    error_text = response.text
                    logger.debug(f"Gemini 429 response: {error_text}")

                    # Check if it's quota exhaustion (daily limit) vs rate limiting
                    if (
                        "quota" in error_text.lower()
                        or "exceeded your current quota" in error_text
                        or "resource has been exhausted" in error_text.lower()
                    ):
                        logger.error(
                            "🚫 Gemini daily quota exhausted (50 requests/day for free tier)"
                        )
                        logger.info(
                            "💡 Consider upgrading to paid tier or wait until quota resets"
                        )
                        # Return fallback response instead of crashing
                        return {
                            "decision": "HOLD",
                            "confidence": 45.0,
                            "reason": "Gemini quota exhausted - using conservative fallback",
                        }

                    # Regular rate limiting - retry with backoff
                    if attempt < max_retries - 1:
                        delay = base_delay * (2**attempt)
                        logger.warning(
                            f"Gemini rate limited, retrying in {delay}s (attempt {attempt + 1}/{max_retries})"
                        )
                        time.sleep(delay)
                        continue
                    else:
                        logger.error("Gemini rate limit exceeded after all retries")
                        raise Exception("Rate limit exceeded")

                # Success or other error - break the retry loop
                break

            except requests.exceptions.Timeout:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"Gemini timeout, retrying (attempt {attempt + 1}/{max_retries})"
                    )
                    time.sleep(1)
                    continue
                else:
                    raise Exception("Timeout after retries")
            except requests.exceptions.RequestException as e:
                if "rate" in str(e).lower() and attempt < max_retries - 1:
                    delay = base_delay * (2**attempt)
                    logger.warning(
                        f"Gemini request error (likely rate limit), retrying in {delay}s"
                    )
                    time.sleep(delay)
                    continue
                else:
                    raise

        response.raise_for_status()
        result = response.json()

        # Extract content from Gemini response
        if "candidates" in result and result["candidates"]:
            content = result["candidates"][0]["content"]["parts"][0]["text"]
        else:
            raise ValueError("No content in Gemini response")

        # Try to parse as JSON
        try:
            # Clean up the response (remove markdown formatting if present)
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            parsed_response = json.loads(content)

            # Validate required fields
            if "decision" not in parsed_response:
                parsed_response["decision"] = "HOLD"
            if "confidence" not in parsed_response:
                parsed_response["confidence"] = 50.0
            if "reason" not in parsed_response:
                parsed_response["reason"] = "Gemini analysis completed"

            return parsed_response

        except json.JSONDecodeError:
            # Fallback parsing for non-JSON responses
            decision = "HOLD"
            confidence = 50.0
            reason = content[:200]

            # Simple keyword extraction
            content_lower = content.lower()
            if "buy" in content_lower and "sell" not in content_lower:
                decision = "BUY"
                confidence = 75.0
            elif "sell" in content_lower and "buy" not in content_lower:
                decision = "SELL"
                confidence = 75.0
            elif "hold" in content_lower:
                decision = "HOLD"
                confidence = 60.0

            return {"decision": decision, "confidence": confidence, "reason": reason}

    except requests.exceptions.RequestException as e:
        logger.error(f"Gemini API request failed: {e}")
        return {
            "decision": "ERROR",
            "confidence": 0.0,
            "reason": f"Gemini API failed: {str(e)}",
        }
    except Exception as e:
        logger.error(f"Gemini client error: {e}")
        # Return HOLD with 0 confidence so it doesn't affect consensus
        return {
            "decision": "HOLD",
            "confidence": 0.0,
            "reason": f"Gemini unavailable: {str(e)}",
        }


def get_gemini_cost_estimate(
    input_tokens: int, output_tokens: int, model: str = "flash"
) -> float:
    """
    Estimate Gemini API costs
    """
    pricing = {
        "flash": {"input": 0.075, "output": 0.30},  # Per 1M tokens - Very cheap!
        "pro": {"input": 1.25, "output": 5.00},  # Per 1M tokens
    }

    model_key = "flash" if "flash" in model else "pro"
    rates = pricing[model_key]

    input_cost = (input_tokens / 1_000_000) * rates["input"]
    output_cost = (output_tokens / 1_000_000) * rates["output"]

    return input_cost + output_cost


# Test function
if __name__ == "__main__":
    test_prompt = "Bitcoin is showing strong bullish signals with RSI at 65 and breaking resistance. Should I buy?"

    result = call_gemini(
        test_prompt, TaskType.TRADE_DECISION, Urgency.MEDIUM, Complexity.SIMPLE
    )
    print("Gemini Response:", result)

    cost = get_gemini_cost_estimate(100, 50, "flash")
    print(f"Estimated cost: ${cost:.6f}")
