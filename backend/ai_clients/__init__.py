"""
AI Clients Package
Provides access to multiple AI providers for trading decisions
"""

# Safe imports with error handling
try:
    from .openai_client import call_openai
except ImportError as e:
    print(f"Warning: OpenAI client not available: {e}")

    def call_openai(*args, **kwargs):
        return {"decision": "HOLD", "confidence": 50.0, "reason": "OpenAI unavailable"}


try:
    from .real_gemini_client import call_gemini
except ImportError as e:
    print(f"Warning: Gemini client not available: {e}")

    def call_gemini(*args, **kwargs):
        return {"decision": "HOLD", "confidence": 50.0, "reason": "Gemini unavailable"}


try:
    from .real_deepseek_client import call_deepseek
except ImportError as e:
    print(f"Warning: DeepSeek client not available: {e}")

    def call_deepseek(*args, **kwargs):
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": "DeepSeek unavailable",
        }


try:
    from .claude_client import call_claude
except ImportError as e:
    print(f"Warning: Claude client not available: {e}")

    def call_claude(*args, **kwargs):
        return {"decision": "HOLD", "confidence": 50.0, "reason": "<PERSON> unavailable"}


try:
    from .ai_request_manager import (
        get_ai_decision_with_consensus as get_ai_consensus,
        make_ai_request,
    )
except ImportError as e:
    print(f"Warning: AI request manager not available: {e}")

    def get_ai_consensus(*args, **kwargs):
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": "AI consensus unavailable",
        }


__all__ = [
    "call_openai",
    "call_gemini",
    "call_deepseek",
    "call_claude",
    "get_ai_consensus",
    "make_ai_request",
]
