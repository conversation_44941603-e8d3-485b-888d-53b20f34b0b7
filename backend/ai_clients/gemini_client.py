import openai
import os

# Create client alias for compatibility
gemini_client = None


def call_gemini(prompt: str) -> dict:
    try:
        response = openai.ChatCompletion.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert crypto trading advisor. Provide decisions based on real technical and sentiment analysis.",
                },
                {"role": "user", "content": prompt},
            ],
            temperature=0.7,
            max_tokens=500,
        )

        content = response["choices"][0]["message"]["content"]  # type: ignore
        # Extract structured data from the response
        # Expecting response format like:
        # Decision: BUY
        # Confidence: 82%
        # Reason: Based on RSI and news sentiment...

        lines = content.strip().split("\n")
        decision = "UNKNOWN"
        confidence = 50.0
        reason = ""

        for line in lines:
            if "decision" in line.lower():
                decision = line.split(":")[-1].strip().upper()
            elif "confidence" in line.lower():
                confidence_str = line.split(":")[-1].strip().replace("%", "")
                confidence = float(confidence_str)
            elif "reason" in line.lower():
                reason = line.split(":", 1)[-1].strip()

        return {"decision": decision, "confidence": confidence, "reason": reason}

    except Exception as e:
        return {
            "decision": "UNKNOWN",
            "confidence": 0.0,
            "reason": f"Error during Gemini call: {str(e)}",
        }
