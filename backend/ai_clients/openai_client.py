import os
import json
import logging
from typing import Any, Dict, Union
from dotenv import load_dotenv
from .model_selector import (
    select_optimal_model,
    TaskType,
    Urgency,
    Complexity,
    optimize_prompt_for_model,
)

# Load environment variables
load_dotenv(".env.production")
load_dotenv(".env")

logger = logging.getLogger(__name__)

# Create client alias for compatibility
openai_client = None


def call_openai(
    prompt: str,
    task_type: TaskType = TaskType.TRADE_DECISION,
    urgency: Urgency = Urgency.MEDIUM,
    complexity: Complexity = Complexity.SIMPLE,
) -> Dict[str, Any]:
    """
    Call OpenAI API with optimized model selection and proper error handling
    Returns structured JSON response with decision, confidence, and reason
    """
    try:
        from openai import OpenAI

        # Get API key from environment
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key or api_key.startswith("YOUR_"):
            raise ValueError("OpenAI API key not properly configured")

        client = OpenAI(api_key=api_key)

        # Add trading-specific system prompt for structured response
        system_prompt = """You are an expert cryptocurrency trading AI. Analyze the given information and provide a JSON response with:
{
  "decision": "BUY|SELL|HOLD",
  "confidence": 0.0-100.0,
  "reason": "Brief explanation of your decision"
}

Focus on technical analysis, market sentiment, and risk assessment. Always respond with valid JSON only."""

        # Select optimal model configuration
        model_config = select_optimal_model(task_type, urgency, complexity)
        optimized_prompt = optimize_prompt_for_model(prompt, model_config)

        response = client.chat.completions.create(
            model=model_config["model"],
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": optimized_prompt},
            ],
            max_tokens=model_config["max_tokens"],
            temperature=model_config["temperature"],
        )

        # Extract content from response
        content = None
        try:
            # Try direct attribute access first (OpenAI object)
            if hasattr(response, "choices"):
                choices = getattr(response, "choices")
                if choices and len(choices) > 0:
                    first_choice = choices[0]
                    if hasattr(first_choice, "message"):
                        message = getattr(first_choice, "message")
                        if hasattr(message, "content"):
                            content = str(getattr(message, "content"))

            # Try dictionary access
            if not content and isinstance(response, dict):
                if "choices" in response and response["choices"]:
                    first_choice = response["choices"][0]
                    if isinstance(first_choice, dict) and "message" in first_choice:
                        message = first_choice["message"]
                        if isinstance(message, dict) and "content" in message:
                            content = str(message["content"])

            # Try to convert to dict if it has to_dict method
            if not content:
                try:
                    if hasattr(response, "to_dict"):
                        to_dict_method = getattr(response, "to_dict")
                        if callable(to_dict_method):
                            response_dict = to_dict_method()
                            if (
                                isinstance(response_dict, dict)
                                and "choices" in response_dict
                            ):
                                if response_dict["choices"]:
                                    first_choice = response_dict["choices"][0]
                                    if (
                                        isinstance(first_choice, dict)
                                        and "message" in first_choice
                                    ):
                                        message = first_choice["message"]
                                        if (
                                            isinstance(message, dict)
                                            and "content" in message
                                        ):
                                            content = str(message["content"])
                except (AttributeError, TypeError, KeyError, IndexError):
                    pass  # to_dict method failed, continue to fallback

            if not content:
                raise ValueError(f"Unexpected response format: {type(response)}")

        except (AttributeError, KeyError, IndexError, TypeError) as parse_error:
            raise ValueError(f"Failed to parse OpenAI response: {parse_error}")

        # Parse JSON response
        try:
            # Try to parse as JSON first
            result = json.loads(content.strip())
            if isinstance(result, dict):
                # Validate required fields
                if (
                    "decision" in result
                    and "confidence" in result
                    and "reason" in result
                ):
                    return result
                else:
                    logger.warning(f"OpenAI response missing required fields: {result}")
                    return {
                        "decision": result.get("decision", "HOLD"),
                        "confidence": float(result.get("confidence", 0)),
                        "reason": result.get("reason", "Invalid response format"),
                    }
            else:
                raise ValueError("Response is not a JSON object")

        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract decision from text
            logger.warning(f"OpenAI returned non-JSON response: {content[:100]}...")

            # Simple text parsing fallback
            content_upper = content.upper()
            if "BUY" in content_upper:
                decision = "BUY"
            elif "SELL" in content_upper:
                decision = "SELL"
            else:
                decision = "HOLD"

            return {
                "decision": decision,
                "confidence": 50.0,  # Default confidence for text responses
                "reason": "Extracted from text response (non-JSON)",
            }

    except Exception as e:
        print(f"❌ OpenAI API Error: {e}")
        raise e
