"""
<PERSON> (Anthropic) AI Client
Provides access to Claude 3.5 models for trading decisions
"""

import os
import logging
from typing import Dict, Any
import requests
import json
from .model_selector import (
    TaskType,
    Urgency,
    Complexity,
    select_optimal_model,
    optimize_prompt_for_model,
)

logger = logging.getLogger(__name__)

# Create client alias for compatibility
claude_client = None


def call_claude(
    prompt: str,
    task_type: TaskType = TaskType.TRADE_DECISION,
    urgency: Urgency = Urgency.MEDIUM,
    complexity: Complexity = Complexity.SIMPLE,
) -> Dict[str, Any]:
    """
    Call Claude API with optimized model selection

    Claude 3.5 Haiku: $0.25/1M input, $1.25/1M output (fast, cheap)
    Claude 3.5 Sonnet: $3.00/1M input, $15.00/1M output (balanced)
    """
    try:
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key or api_key.startswith("YOUR_"):
            raise ValueError("Anthropic API key not properly configured")

        # Select optimal Claude model based on task
        if urgency == Urgency.CRITICAL or complexity == Complexity.COMPLEX:
            model = "claude-3-5-sonnet-20241022"
            max_tokens = 400
        else:
            model = "claude-3-5-haiku-20241022"  # Much cheaper for simple tasks
            max_tokens = 200

        # Optimize prompt for Claude
        model_config = {"cost_tier": "fast" if "haiku" in model else "premium"}
        optimized_prompt = optimize_prompt_for_model(prompt, model_config)

        # Add trading-specific system prompt
        system_prompt = """You are an expert cryptocurrency trading AI. Analyze the given information and provide a JSON response with:
{
  "decision": "BUY|SELL|HOLD",
  "confidence": 0.0-100.0,
  "reason": "Brief explanation of your decision"
}"""

        headers = {
            "Content-Type": "application/json",
            "x-api-key": api_key,
            "anthropic-version": "2023-06-01",
        }

        data = {
            "model": model,
            "max_tokens": max_tokens,
            "system": system_prompt,
            "messages": [{"role": "user", "content": optimized_prompt}],
            "temperature": 0.3,
        }

        response = requests.post(
            "https://api.anthropic.com/v1/messages",
            headers=headers,
            json=data,
            timeout=30,
        )

        if response.status_code == 429:
            logger.warning("Claude API rate limited")
            raise requests.exceptions.RequestException("Rate limited")

        response.raise_for_status()
        result = response.json()

        # Extract content from Claude response
        content = result["content"][0]["text"]

        # Try to parse as JSON
        try:
            parsed_response = json.loads(content)
            return parsed_response
        except json.JSONDecodeError:
            # Fallback parsing for non-JSON responses
            decision = "HOLD"
            confidence = 50.0
            reason = content

            # Simple keyword extraction
            content_lower = content.lower()
            if "buy" in content_lower and "sell" not in content_lower:
                decision = "BUY"
                confidence = 70.0
            elif "sell" in content_lower and "buy" not in content_lower:
                decision = "SELL"
                confidence = 70.0

            return {
                "decision": decision,
                "confidence": confidence,
                "reason": reason[:200],  # Truncate long responses
            }

    except requests.exceptions.RequestException as e:
        logger.error(f"Claude API request failed: {e}")
        return {
            "decision": "ERROR",
            "confidence": 0.0,
            "reason": f"Claude API failed: {str(e)}",
        }
    except Exception as e:
        logger.error(f"Claude client error: {e}")
        return {
            "decision": "ERROR",
            "confidence": 0.0,
            "reason": f"Claude client error: {str(e)}",
        }


def get_claude_cost_estimate(
    input_tokens: int, output_tokens: int, model: str = "haiku"
) -> float:
    """
    Estimate Claude API costs
    """
    pricing = {
        "haiku": {"input": 0.25, "output": 1.25},  # Per 1M tokens
        "sonnet": {"input": 3.00, "output": 15.00},  # Per 1M tokens
    }

    model_key = "haiku" if "haiku" in model else "sonnet"
    rates = pricing[model_key]

    input_cost = (input_tokens / 1_000_000) * rates["input"]
    output_cost = (output_tokens / 1_000_000) * rates["output"]

    return input_cost + output_cost


# Test function
if __name__ == "__main__":
    test_prompt = "Bitcoin is showing strong bullish signals with RSI at 65 and breaking resistance. Should I buy?"

    result = call_claude(
        test_prompt, TaskType.TRADE_DECISION, Urgency.MEDIUM, Complexity.SIMPLE
    )
    print("Claude Response:", result)

    cost = get_claude_cost_estimate(100, 50, "haiku")
    print(f"Estimated cost: ${cost:.6f}")
