"""
Smart AI Model Selection for Cost-Effective Trading
Optimizes model choice based on task complexity and urgency
"""

import logging
from typing import Dict, Any
from enum import Enum

logger = logging.getLogger(__name__)

class TaskType(Enum):
    NEWS_SENTIMENT = "news_sentiment"
    BREAKING_NEWS = "breaking_news"
    TRADE_DECISION = "trade_decision"
    ARBITRAGE_ANALYSIS = "arbitrage_analysis"
    TECHNICAL_ANALYSIS = "technical_analysis"
    RISK_ASSESSMENT = "risk_assessment"

class Urgency(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class Complexity(Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"

def select_optimal_model(
    task_type: TaskType, 
    urgency: Urgency = Urgency.MEDIUM, 
    complexity: Complexity = Complexity.SIMPLE
) -> Dict[str, Any]:
    """
    Select the most cost-effective model based on task requirements
    
    Returns:
        dict: Model configuration with model name, max_tokens, temperature
    """
    
    # High-priority breaking news or critical situations
    if urgency == Urgency.CRITICAL or task_type == TaskType.BREAKING_NEWS:
        if complexity == Complexity.COMPLEX:
            return {
                "model": "gpt-4o",
                "max_tokens": 400,
                "temperature": 0.2,
                "cost_tier": "premium",
                "reasoning": "Critical/breaking news with complex analysis"
            }
        else:
            return {
                "model": "gpt-4o-mini",
                "max_tokens": 250,
                "temperature": 0.3,
                "cost_tier": "balanced",
                "reasoning": "Critical/breaking news with simple analysis"
            }
    
    # Complex arbitrage or multi-factor analysis
    elif task_type == TaskType.ARBITRAGE_ANALYSIS and complexity == Complexity.COMPLEX:
        return {
            "model": "gpt-4o",
            "max_tokens": 350,
            "temperature": 0.3,
            "cost_tier": "premium",
            "reasoning": "Complex arbitrage analysis requires advanced reasoning"
        }
    
    # Standard trading decisions
    elif task_type == TaskType.TRADE_DECISION:
        if complexity == Complexity.COMPLEX or urgency == Urgency.HIGH:
            return {
                "model": "gpt-4o-mini",
                "max_tokens": 200,
                "temperature": 0.4,
                "cost_tier": "fast",
                "reasoning": "Standard trade decision with good speed/cost balance"
            }
        else:
            return {
                "model": "gpt-4o-mini",
                "max_tokens": 150,
                "temperature": 0.4,
                "cost_tier": "fast",
                "reasoning": "Simple trade decision optimized for speed"
            }
    
    # News sentiment analysis (most common)
    elif task_type == TaskType.NEWS_SENTIMENT:
        return {
            "model": "gpt-4o-mini",
            "max_tokens": 120,
            "temperature": 0.3,
            "cost_tier": "fast",
            "reasoning": "News sentiment optimized for speed and cost"
        }
    
    # Technical analysis
    elif task_type == TaskType.TECHNICAL_ANALYSIS:
        if complexity == Complexity.COMPLEX:
            return {
                "model": "gpt-4o-mini",
                "max_tokens": 250,
                "temperature": 0.3,
                "cost_tier": "balanced",
                "reasoning": "Complex technical analysis with moderate tokens"
            }
        else:
            return {
                "model": "gpt-4o-mini",
                "max_tokens": 150,
                "temperature": 0.3,
                "cost_tier": "fast",
                "reasoning": "Simple technical analysis optimized for speed"
            }
    
    # Risk assessment
    elif task_type == TaskType.RISK_ASSESSMENT:
        return {
            "model": "gpt-4o-mini",
            "max_tokens": 180,
            "temperature": 0.2,
            "cost_tier": "balanced",
            "reasoning": "Risk assessment with conservative temperature"
        }
    
    # Default fallback
    else:
        return {
            "model": "gpt-4o-mini",
            "max_tokens": 150,
            "temperature": 0.3,
            "cost_tier": "fast",
            "reasoning": "Default configuration for unknown task type"
        }

def get_cost_estimate(model_config: Dict[str, Any], input_tokens: int = 1000) -> Dict[str, Any]:
    """
    Estimate the cost of an API call based on model and token usage
    
    Args:
        model_config: Model configuration from select_optimal_model
        input_tokens: Estimated input tokens
        
    Returns:
        dict: Cost breakdown in USD
    """
    
    model = model_config["model"]
    output_tokens = model_config["max_tokens"]
    
    # Pricing per 1M tokens (as of 2024)
    pricing = {
        "gpt-4o-mini": {"input": 0.15, "output": 0.60},
        "gpt-4o": {"input": 2.50, "output": 10.00},
        "gpt-3.5-turbo": {"input": 0.50, "output": 1.50},  # Legacy
    }
    
    if model not in pricing:
        model = "gpt-4o-mini"  # Default fallback
    
    input_cost = (input_tokens / 1_000_000) * pricing[model]["input"]
    output_cost = (output_tokens / 1_000_000) * pricing[model]["output"]
    total_cost = input_cost + output_cost
    
    return {
        "input_cost": round(input_cost, 6),
        "output_cost": round(output_cost, 6),
        "total_cost": round(total_cost, 6),
        "model": model,
        "input_tokens": input_tokens,
        "output_tokens": output_tokens
    }

def optimize_prompt_for_model(prompt: str, model_config: Dict[str, Any]) -> str:
    """
    Optimize prompt based on the selected model to reduce token usage
    
    Args:
        prompt: Original prompt
        model_config: Model configuration
        
    Returns:
        str: Optimized prompt
    """
    
    # For fast/cheap models, compress the prompt
    if model_config["cost_tier"] == "fast":
        # Remove unnecessary words and compress
        optimizations = [
            ("Please analyze", "Analyze"),
            ("I would like you to", ""),
            ("Based on the following information", "Given:"),
            ("cryptocurrency", "crypto"),
            ("technical analysis", "TA"),
            ("sentiment analysis", "sentiment"),
            ("trading decision", "trade decision"),
            ("  ", " "),  # Remove double spaces
        ]
        
        optimized = prompt
        for old, new in optimizations:
            optimized = optimized.replace(old, new)
        
        return optimized.strip()
    
    return prompt

# Example usage and testing
if __name__ == "__main__":
    # Test different scenarios
    scenarios = [
        (TaskType.NEWS_SENTIMENT, Urgency.LOW, Complexity.SIMPLE),
        (TaskType.BREAKING_NEWS, Urgency.CRITICAL, Complexity.COMPLEX),
        (TaskType.TRADE_DECISION, Urgency.HIGH, Complexity.MEDIUM),
        (TaskType.ARBITRAGE_ANALYSIS, Urgency.MEDIUM, Complexity.COMPLEX),
    ]
    
    print("AI Model Selection Test Results:")
    print("=" * 50)
    
    for task_type, urgency, complexity in scenarios:
        config = select_optimal_model(task_type, urgency, complexity)
        cost = get_cost_estimate(config, 1000)
        
        print(f"\nTask: {task_type.value}")
        print(f"Urgency: {urgency.value}, Complexity: {complexity.value}")
        print(f"Model: {config['model']}")
        print(f"Max Tokens: {config['max_tokens']}")
        print(f"Cost Tier: {config['cost_tier']}")
        print(f"Estimated Cost: ${cost['total_cost']:.6f}")
        print(f"Reasoning: {config['reasoning']}")
