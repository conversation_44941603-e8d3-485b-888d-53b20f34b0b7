import openai
import os

openai.api_key = os.getenv("OPENAI_API_KEY")

# Create client alias for compatibility
deepseek_client = None


def call_deepseek(prompt: str) -> dict:
    try:
        response = openai.chat.completions.create(  # type: ignore
            model="gpt-4o-0613",
            messages=[
                {
                    "role": "system",
                    "content": "You are a financial AI assistant simulating DeepSeek. Analyze the given crypto prompt and provide a trade decision with reasoning and confidence.",
                },
                {"role": "user", "content": prompt},
            ],
            temperature=0.3,
            max_tokens=500,
        )

        content = response.choices[0].message.content

        # Parse expected JSON response format from model output
        import json

        try:
            parsed = json.loads(content or "")
            return parsed
        except json.JSONDecodeError:
            # Fallback: return wrapped raw content
            return {
                "decision": "UNKNOWN",
                "confidence": 50.0,
                "reason": (content or "").strip(),
            }

    except Exception as e:
        return {
            "decision": "ERROR",
            "confidence": 0.0,
            "reason": f"DeepSeek API failed: {str(e)}",
        }
