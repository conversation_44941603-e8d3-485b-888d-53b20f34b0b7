#!/bin/bash
# Script to update news data files by running ingestion modules

# Activate virtual environment if needed
# source /path/to/venv/bin/activate

# Run CoinMarketCap news fetcher
python3 backend/news_ingestion/cmc_news.py

# Run Reddit/GitHub/RSS news fetcher
python3 backend/reddit_github_alpha.py

# Note: Discord news bot should be run as a persistent service separately

echo "News data update completed at $(date)"
