#!/usr/bin/env python3
"""
🎯 Strategy Orchestrator for Alpha Predator Bot
Combines all strategies for maximum profitability and optimal decision making
"""

import logging
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)

@dataclass
class StrategyResult:
    """Combined strategy result with confidence weighting"""
    symbol: str
    final_action: str  # BUY, SELL, HOLD
    combined_confidence: float
    expected_profit: float
    risk_score: float
    strategies_used: List[str]
    strategy_votes: Dict[str, str]
    strategy_confidences: Dict[str, float]
    reasoning: str
    timestamp: datetime

class StrategyOrchestrator:
    """
    Master strategy coordinator that combines all trading strategies
    for optimal profitability and risk management
    """
    
    def __init__(self):
        self.strategy_weights = {
            # CFA-Level Strategies (40% total weight)
            "momentum_factor": 0.12,
            "mean_reversion": 0.10,
            "volatility_breakout": 0.08,
            "risk_parity": 0.10,
            
            # Profitable Strategies (35% total weight)
            "scalping": 0.15,
            "arbitrage": 0.10,
            "news_momentum": 0.10,
            
            # Existing Strategies (25% total weight)
            "trend_following": 0.08,
            "breakout_trading": 0.07,
            "swing_trading": 0.05,
            "grid_trading": 0.05
        }
        
        self.min_confidence_threshold = 0.6
        self.min_profit_threshold = 1.0  # $1 minimum profit
        self.max_risk_per_trade = 0.02  # 2% max risk
        
    async def get_optimal_trading_decision(self, symbol: str, market_data: Dict[str, Any]) -> StrategyResult:
        """
        Get optimal trading decision by combining all strategies
        Returns the best possible trade with highest profit potential
        """
        try:
            logger.info(f"🎯 Orchestrating strategies for {symbol}")
            
            # Collect comprehensive data if not provided
            if not market_data.get("comprehensive_data"):
                from advanced_data_collector import collect_200_data_points
                comprehensive_data = await collect_200_data_points(symbol)
                market_data["comprehensive_data"] = comprehensive_data
            
            # Run all strategies in parallel
            strategy_tasks = [
                self._run_cfa_strategies(symbol, market_data),
                self._run_profitable_strategies(symbol, market_data),
                self._run_existing_strategies(symbol, market_data)
            ]
            
            strategy_results = await asyncio.gather(*strategy_tasks, return_exceptions=True)
            
            # Combine results
            all_signals = {}
            for result_group in strategy_results:
                if isinstance(result_group, dict):
                    all_signals.update(result_group)
            
            # Calculate weighted decision
            final_decision = self._calculate_weighted_decision(all_signals)
            
            # Validate decision against risk and profit criteria
            validated_decision = self._validate_decision(final_decision, market_data)
            
            logger.info(f"✅ Final decision for {symbol}: {validated_decision.final_action} "
                       f"(confidence: {validated_decision.combined_confidence:.2f}, "
                       f"profit: ${validated_decision.expected_profit:.2f})")
            
            return validated_decision
            
        except Exception as e:
            logger.error(f"❌ Strategy orchestration failed for {symbol}: {e}")
            return self._create_hold_result(symbol, f"Orchestration error: {e}")
    
    async def _run_cfa_strategies(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run CFA-level strategies"""
        try:
            from cfa_trading_strategies import get_cfa_trading_signal
            
            signals = {}
            comprehensive_data = market_data.get("comprehensive_data", {})
            kucoin_data = comprehensive_data.get("kucoin_data", {})
            
            # Extract price and volume data
            price_data = market_data.get("price_history", [kucoin_data.get("current_price", 0)])
            volume_data = market_data.get("volume_history", [kucoin_data.get("volume_24h", 0)])
            
            # Run each CFA strategy
            cfa_strategies = ["momentum", "mean_reversion", "volatility_breakout", "risk_parity"]
            
            for strategy_type in cfa_strategies:
                try:
                    signal = get_cfa_trading_signal(symbol, price_data, volume_data, strategy_type)
                    signals[f"cfa_{strategy_type}"] = {
                        "action": signal.action,
                        "confidence": signal.confidence,
                        "profit_potential": getattr(signal, 'profit_potential', 0),
                        "reasoning": signal.reasoning
                    }
                except Exception as e:
                    logger.warning(f"CFA strategy {strategy_type} failed for {symbol}: {e}")
            
            return signals
            
        except Exception as e:
            logger.error(f"CFA strategies error for {symbol}: {e}")
            return {}
    
    async def _run_profitable_strategies(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run profit-focused strategies"""
        try:
            from profitable_strategies import get_profitable_signal
            
            signals = {}
            comprehensive_data = market_data.get("comprehensive_data", {})
            
            # Scalping strategy
            try:
                scalping_signal = get_profitable_signal(
                    symbol, "scalping",
                    price_data=market_data.get("price_history", []),
                    volume_data=market_data.get("volume_history", []),
                    order_book=market_data.get("order_book")
                )
                signals["scalping"] = {
                    "action": scalping_signal.action,
                    "confidence": scalping_signal.confidence,
                    "profit_potential": scalping_signal.profit_potential,
                    "reasoning": scalping_signal.reasoning
                }
            except Exception as e:
                logger.warning(f"Scalping strategy failed for {symbol}: {e}")
            
            # Arbitrage strategy
            try:
                exchange_prices = market_data.get("exchange_prices", {})
                if len(exchange_prices) >= 2:
                    arbitrage_signal = get_profitable_signal(
                        symbol, "arbitrage",
                        price_data=exchange_prices,
                        exchange_fees=market_data.get("exchange_fees")
                    )
                    signals["arbitrage"] = {
                        "action": arbitrage_signal.action,
                        "confidence": arbitrage_signal.confidence,
                        "profit_potential": arbitrage_signal.profit_potential,
                        "reasoning": arbitrage_signal.reasoning
                    }
            except Exception as e:
                logger.warning(f"Arbitrage strategy failed for {symbol}: {e}")
            
            # News momentum strategy
            try:
                news_data = comprehensive_data.get("tokenmetrics_data", {})
                news_signal = get_profitable_signal(
                    symbol, "news_momentum",
                    price_data=market_data.get("price_history", []),
                    news_sentiment=news_data.get("news_sentiment", 0.5),
                    news_impact_score=news_data.get("ai_news_impact", 0.5),
                    time_since_news=market_data.get("time_since_news", 3600)
                )
                signals["news_momentum"] = {
                    "action": news_signal.action,
                    "confidence": news_signal.confidence,
                    "profit_potential": news_signal.profit_potential,
                    "reasoning": news_signal.reasoning
                }
            except Exception as e:
                logger.warning(f"News momentum strategy failed for {symbol}: {e}")
            
            return signals
            
        except Exception as e:
            logger.error(f"Profitable strategies error for {symbol}: {e}")
            return {}
    
    async def _run_existing_strategies(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run existing proven strategies"""
        try:
            from strategy_evaluator import (
                evaluate_trend_following, evaluate_breakout_trading,
                evaluate_swing_trading, evaluate_grid_trading
            )
            
            signals = {}
            price_data = market_data.get("price_history", [])
            volume_data = market_data.get("volume_history", [])
            
            if len(price_data) >= 50:  # Minimum data for existing strategies
                
                # Trend following
                try:
                    trend_signal = evaluate_trend_following(price_data, 10, 20)
                    signals["trend_following"] = {
                        "action": trend_signal,
                        "confidence": 0.7,  # Default confidence
                        "profit_potential": 2.0,  # Estimated
                        "reasoning": "Trend following signal"
                    }
                except Exception as e:
                    logger.warning(f"Trend following failed for {symbol}: {e}")
                
                # Breakout trading
                try:
                    breakout_signal = evaluate_breakout_trading(price_data, volume_data, 20)
                    signals["breakout_trading"] = {
                        "action": breakout_signal,
                        "confidence": 0.75,
                        "profit_potential": 3.0,
                        "reasoning": "Breakout trading signal"
                    }
                except Exception as e:
                    logger.warning(f"Breakout trading failed for {symbol}: {e}")
                
                # Add more existing strategies as needed
            
            return signals
            
        except Exception as e:
            logger.error(f"Existing strategies error for {symbol}: {e}")
            return {}
    
    def _calculate_weighted_decision(self, all_signals: Dict[str, Any]) -> StrategyResult:
        """Calculate weighted decision from all strategy signals"""
        try:
            if not all_signals:
                return self._create_hold_result("", "No signals available")
            
            # Count votes and calculate weighted confidence
            buy_weight = 0.0
            sell_weight = 0.0
            hold_weight = 0.0
            
            total_profit_potential = 0.0
            strategy_votes = {}
            strategy_confidences = {}
            strategies_used = []
            
            for strategy_name, signal in all_signals.items():
                action = signal.get("action", "HOLD")
                confidence = signal.get("confidence", 0.5)
                profit_potential = signal.get("profit_potential", 0.0)
                
                # Get strategy weight
                weight = self.strategy_weights.get(strategy_name, 0.05)  # Default 5% weight
                
                # Apply weight to vote
                weighted_confidence = confidence * weight
                
                if action == "BUY":
                    buy_weight += weighted_confidence
                elif action == "SELL":
                    sell_weight += weighted_confidence
                else:
                    hold_weight += weighted_confidence
                
                total_profit_potential += profit_potential * weight
                strategy_votes[strategy_name] = action
                strategy_confidences[strategy_name] = confidence
                strategies_used.append(strategy_name)
            
            # Determine final action
            if buy_weight > sell_weight and buy_weight > hold_weight:
                final_action = "BUY"
                combined_confidence = buy_weight
            elif sell_weight > buy_weight and sell_weight > hold_weight:
                final_action = "SELL"
                combined_confidence = sell_weight
            else:
                final_action = "HOLD"
                combined_confidence = hold_weight
            
            # Create reasoning
            reasoning = f"Weighted decision: BUY({buy_weight:.2f}) SELL({sell_weight:.2f}) HOLD({hold_weight:.2f})"
            
            return StrategyResult(
                symbol="",
                final_action=final_action,
                combined_confidence=combined_confidence,
                expected_profit=total_profit_potential,
                risk_score=1.0 - combined_confidence,  # Inverse of confidence
                strategies_used=strategies_used,
                strategy_votes=strategy_votes,
                strategy_confidences=strategy_confidences,
                reasoning=reasoning,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Weighted decision calculation error: {e}")
            return self._create_hold_result("", f"Decision calculation error: {e}")
    
    def _validate_decision(self, decision: StrategyResult, market_data: Dict[str, Any]) -> StrategyResult:
        """Validate decision against risk and profit criteria"""
        try:
            # Check minimum confidence threshold
            if decision.combined_confidence < self.min_confidence_threshold:
                decision.final_action = "HOLD"
                decision.reasoning += f" | Low confidence ({decision.combined_confidence:.2f} < {self.min_confidence_threshold})"
            
            # Check minimum profit threshold
            if decision.expected_profit < self.min_profit_threshold:
                decision.final_action = "HOLD"
                decision.reasoning += f" | Low profit potential (${decision.expected_profit:.2f} < ${self.min_profit_threshold})"
            
            # Check maximum risk threshold
            if decision.risk_score > self.max_risk_per_trade:
                decision.final_action = "HOLD"
                decision.reasoning += f" | High risk ({decision.risk_score:.2%} > {self.max_risk_per_trade:.2%})"
            
            # Market condition checks
            comprehensive_data = market_data.get("comprehensive_data", {})
            risk_metrics = comprehensive_data.get("risk_metrics", {})
            
            if risk_metrics.get("overall_risk", 0.5) > 0.8:
                decision.final_action = "HOLD"
                decision.reasoning += " | High market risk detected"
            
            return decision
            
        except Exception as e:
            logger.error(f"Decision validation error: {e}")
            decision.reasoning += f" | Validation error: {e}"
            return decision
    
    def _create_hold_result(self, symbol: str, reason: str) -> StrategyResult:
        """Create a HOLD result with reasoning"""
        return StrategyResult(
            symbol=symbol,
            final_action="HOLD",
            combined_confidence=0.5,
            expected_profit=0.0,
            risk_score=0.5,
            strategies_used=[],
            strategy_votes={},
            strategy_confidences={},
            reasoning=reason,
            timestamp=datetime.now()
        )

# Global instance
strategy_orchestrator = StrategyOrchestrator()

async def get_optimal_trade_decision(symbol: str, market_data: Optional[Dict[str, Any]] = None) -> StrategyResult:
    """
    Main function to get optimal trading decision
    Combines all strategies for maximum profitability
    """
    if market_data is None:
        market_data = {}
    
    return await strategy_orchestrator.get_optimal_trading_decision(symbol, market_data)

def get_strategy_performance_summary() -> Dict[str, Any]:
    """Get performance summary of all strategies"""
    return {
        "total_strategies": len(strategy_orchestrator.strategy_weights),
        "strategy_weights": strategy_orchestrator.strategy_weights,
        "confidence_threshold": strategy_orchestrator.min_confidence_threshold,
        "profit_threshold": strategy_orchestrator.min_profit_threshold,
        "risk_threshold": strategy_orchestrator.max_risk_per_trade,
        "cfa_strategies": ["momentum_factor", "mean_reversion", "volatility_breakout", "risk_parity"],
        "profitable_strategies": ["scalping", "arbitrage", "news_momentum"],
        "existing_strategies": ["trend_following", "breakout_trading", "swing_trading", "grid_trading"]
    }
