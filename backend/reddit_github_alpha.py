import os
import time
from utils.api_client import get
import requests  # Keep requests for now, as feedparser might use it internally
import feedparser
from datetime import datetime, timedelta  # Import timedelta
import json
import logging

from config import (
    RSS_FEEDS,
    GITHUB_OAUTH_TOKEN,
)  # Import RSS_FEEDS and GITHUB_OAUTH_TOKEN from config

# Setup logging
LOG_FILE = "backend/logs/news_fetch.log"
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
)

# NLTK data is pre-downloaded in Docker image

REDDIT_USER_AGENT = (
    "Mozilla/5.0 (compatible; AlphaPredatorBot/1.0; +https://kryptomerch.io)"
)
REDDIT_URL = "https://www.reddit.com/r/CryptoMarkets+CryptoCurrency+CryptoMoonShots/new.json?limit=100"
GITHUB_API_URL = "https://api.github.com/search/repositories?q=crypto+language:python&sort=stars&order=desc&per_page=30"
NEWS_CACHE_FILE = "backend/data/news_signals.json"
CACHE_RETENTION_DAYS = 7  # Days to keep news in cache


def fetch_reddit_signals_for_token(token: str) -> list:
    """
    Fetch Reddit signals specifically filtered for a token
    """
    all_signals = fetch_reddit_signals()
    token_signals = []

    token_keywords = [
        token.upper(),
        token.lower(),
        token.replace("-USDT", ""),
        token.replace("-USD", ""),
        f"${token.upper()}",
        f"#{token.upper()}",
    ]

    for signal in all_signals:
        title = signal.get("title", "").upper()
        description = signal.get("description", "").upper()

        # Check if any token keyword appears in title or description
        if any(
            keyword.upper() in title or keyword.upper() in description
            for keyword in token_keywords
        ):
            signal["relevance_score"] = calculate_token_relevance(signal, token)
            token_signals.append(signal)

    # Sort by relevance and recency
    token_signals.sort(
        key=lambda x: (x.get("relevance_score", 0), x.get("created_at", "")),
        reverse=True,
    )
    return token_signals[:10]  # Return top 10 most relevant


def fetch_reddit_signals() -> list:
    """
    Fetch recent posts from specified Reddit subreddits and return as signals.
    Handles rate limiting by sleeping if necessary.
    """
    headers = {"User-Agent": REDDIT_USER_AGENT}
    signals = []

    try:
        res = get(
            REDDIT_URL, headers=headers, timeout=10, cache_ttl=300
        )  # Cache Reddit signals for 5 minutes

        data = res.json()
        posts = data.get("data", {}).get("children", [])

        for post in posts:
            pdata = post.get("data", {})
            title = pdata.get("title", "").strip()
            url = "https://reddit.com" + pdata.get("permalink", "")
            created_utc = pdata.get("created_utc", None)
            if title and url and created_utc:
                created_dt = datetime.utcfromtimestamp(created_utc).isoformat() + "Z"
                signals.append(
                    {
                        "source": "reddit",
                        "title": title,
                        "url": url,
                        "created_at": created_dt,
                        "credibility": min(0.6 + (pdata.get("ups", 0) / 1000), 0.9),
                    }
                )
        logging.info(f"[Reddit] Fetched {len(signals)} posts.")
    except requests.exceptions.RequestException as e:
        logging.error(f"[Reddit] Network or HTTP error: {e}")
    except json.JSONDecodeError:
        logging.error(f"[Reddit] Failed to decode JSON response.")
    except Exception as e:
        logging.error(f"[Reddit] Unexpected error: {e}", exc_info=True)
    return signals


def fetch_github_signals() -> list:
    """
    Fetch popular Python crypto-related repositories from GitHub and return as signals.
    """
    headers = {}
    if not GITHUB_OAUTH_TOKEN:
        logging.warning("GITHUB_OAUTH_TOKEN is not set. Skipping GitHub API calls.")
        return []

    signals = []
    try:
        res = get(
            GITHUB_API_URL, headers=headers, timeout=10, cache_ttl=3600
        )  # Cache GitHub signals for 1 hour
        res.raise_for_status()
        data = res.json()
        repos = data.get("items", [])

        for repo in repos:
            stars = repo.get("stargazers_count", 0)
            if stars < 100:
                continue
            name = repo.get("name", "").strip()
            description = repo.get("description", "")
            url = repo.get("html_url", "")
            created_at = repo.get("created_at", "")

            if name and url:
                signals.append(
                    {
                        "source": "github",
                        "title": name,
                        "description": description or "",
                        "url": url,
                        "created_at": created_at,
                        "credibility": min(0.7 + (stars / 5000), 0.95),
                    }
                )
        logging.info(f"[GitHub] Fetched {len(signals)} repos.")
    except requests.exceptions.RequestException as e:
        logging.error(f"[GitHub] Network or HTTP error: {e}")
    except json.JSONDecodeError:
        logging.error(f"[GitHub] Failed to decode JSON response.")
    except Exception as e:
        logging.error(f"[GitHub] Unexpected error: {e}", exc_info=True)
    return signals


def fetch_rss_signals() -> list:
    """
    Fetch articles from predefined RSS feeds and return as signals.
    """
    signals = []
    for feed_url in RSS_FEEDS:
        try:
            # Skip known problematic feeds
            if "coinmarketcap.com/headlines/news/feed" in feed_url:
                logging.info(f"[RSS] Skipping problematic feed: {feed_url}")
                continue

            feed = feedparser.parse(feed_url)
            if feed.bozo:  # Check for parsing errors
                logging.warning(
                    f"[RSS] Parsing error for {feed_url}: {feed.bozo_exception}"
                )
                continue

            for entry in feed.entries:
                title = str(entry.get("title", "")).strip()
                url = entry.get("link", "")
                published = entry.get("published", "") or entry.get("updated", "")
                published_dt = None

                if published:
                    try:
                        if (
                            hasattr(entry, "published_parsed")
                            and entry.published_parsed is not None
                        ):
                            import time

                            if isinstance(entry.published_parsed, time.struct_time):
                                published_dt = (
                                    datetime(*entry.published_parsed[:6]).isoformat()
                                    + "Z"
                                )
                            else:
                                published_dt = published
                        else:
                            published_dt = published
                    except Exception:
                        published_dt = published

                if title and url:
                    signals.append(
                        {
                            "source": "rss",
                            "title": title,
                            "url": url,
                            "created_at": published_dt,
                            "credibility": 0.75,
                        }
                    )
        except requests.exceptions.RequestException as e:
            logging.error(f"[RSS] Network or HTTP error for {feed_url}: {e}")
        except Exception as e:
            logging.error(f"[RSS] Unexpected error for {feed_url}: {e}", exc_info=True)
    logging.info(f"[RSS] Fetched {len(signals)} articles.")
    return signals


def fetch_all_signals() -> list:
    """
    Fetch signals from Reddit, GitHub, and RSS feeds, merge with cache,
    remove duplicates, prune old entries, and return sorted combined list.
    """
    reddit_signals = fetch_reddit_signals()
    github_signals = fetch_github_signals()
    rss_signals = fetch_rss_signals()
    all_signals = reddit_signals + github_signals + rss_signals

    seen_titles = set()
    pruned_cached_signals = []
    cutoff_date = datetime.utcnow() - timedelta(days=CACHE_RETENTION_DAYS)

    if os.path.exists(NEWS_CACHE_FILE):
        with open(NEWS_CACHE_FILE, "r") as f:
            try:
                cached = json.load(f)
                for entry in cached:
                    # Prune old entries and collect seen titles
                    created_at_str = entry.get("created_at")
                    if created_at_str:
                        try:
                            # Handle potential Z suffix for UTC
                            if created_at_str.endswith("Z"):
                                created_dt = datetime.fromisoformat(created_at_str[:-1])
                            else:
                                created_dt = datetime.fromisoformat(created_at_str)

                            if created_dt > cutoff_date:
                                pruned_cached_signals.append(entry)
                                seen_titles.add(entry.get("title", ""))
                            else:
                                logging.info(
                                    f"Pruning old news: {entry.get('title', '')}"
                                )
                        except ValueError:
                            logging.warning(
                                f"Could not parse date for pruning: {created_at_str}"
                            )
                            pruned_cached_signals.append(
                                entry
                            )  # Keep if date unparseable
                    else:
                        pruned_cached_signals.append(entry)  # Keep if no date

            except json.JSONDecodeError:
                logging.error(
                    f"Corrupted NEWS_CACHE_FILE: {NEWS_CACHE_FILE}. Starting fresh."
                )
                pruned_cached_signals = []

    combined = pruned_cached_signals  # Start with pruned cache

    new_signals_added_count = 0
    for signal in all_signals:
        if signal["title"] in seen_titles:
            continue
        signal["fetched_at"] = datetime.utcnow().isoformat() + "Z"  # Add Z for UTC
        combined.append(signal)
        new_signals_added_count += 1

    logging.info(
        f"Added {new_signals_added_count} new signals. Total in cache: {len(combined)}"
    )

    # Save the combined signals to cache file
    try:
        with open(NEWS_CACHE_FILE, "w") as f:
            json.dump(combined, f, indent=2)
    except Exception as e:
        logging.error(f"Failed to write to cache file: {e}")

    def parse_dt(signal):
        dt_str = signal.get("created_at")
        try:
            # Handle potential Z suffix for UTC
            if dt_str and dt_str.endswith("Z"):
                return datetime.fromisoformat(dt_str[:-1])
            elif dt_str:
                return datetime.fromisoformat(dt_str)
            return datetime.min
        except Exception:
            return datetime.min

    combined.sort(key=parse_dt, reverse=True)
    return combined


def calculate_token_relevance(signal: dict, token: str) -> float:
    """Calculate relevance score for a signal based on token mentions"""
    title = signal.get("title", "").upper()
    description = signal.get("description", "").upper()
    token_upper = token.upper().replace("-USDT", "").replace("-USD", "")

    score = 0.0

    # Direct token mention in title (highest weight)
    if token_upper in title:
        score += 1.0

    # Token mention in description
    if token_upper in description:
        score += 0.5

    # Exact symbol match (e.g., $BTC, #BTC)
    if f"${token_upper}" in title or f"#{token_upper}" in title:
        score += 0.8

    # Credibility boost
    credibility = signal.get("credibility", 0.5)
    score *= 1 + credibility

    return score


def fetch_github_signals_for_token(token: str) -> list:
    """
    Fetch GitHub signals specifically filtered for a token
    """
    all_signals = fetch_github_signals()
    token_signals = []

    token_keywords = [
        token.upper(),
        token.lower(),
        token.replace("-USDT", ""),
        token.replace("-USD", ""),
    ]

    for signal in all_signals:
        title = signal.get("title", "").upper()
        description = signal.get("description", "").upper()

        # Check if any token keyword appears in title or description
        if any(
            keyword.upper() in title or keyword.upper() in description
            for keyword in token_keywords
        ):
            signal["relevance_score"] = calculate_token_relevance(signal, token)
            token_signals.append(signal)

    # Sort by relevance and stars
    token_signals.sort(
        key=lambda x: (x.get("relevance_score", 0), x.get("credibility", 0)),
        reverse=True,
    )
    return token_signals[:5]  # Return top 5 most relevant repos


def fetch_signal_sentiment(token: str) -> float:
    """
    Compute a sentiment score for a given token by analyzing
    titles and descriptions of all fetched signals.
    """
    try:
        # Ensure NLTK data is available
        from download_nltk import ensure_nltk_data

        ensure_nltk_data()

        # Import NLTK only when needed
        from nltk.sentiment.vader import SentimentIntensityAnalyzer

        analyzer = SentimentIntensityAnalyzer()

        all_signals = fetch_all_signals()
        relevant_signals = [
            s for s in all_signals if token.lower() in s.get("title", "").lower()
        ]

        if not relevant_signals:
            return 0.0

        total_score = 0
        for signal in relevant_signals:
            text = f"{signal.get('title', '')} {signal.get('description', '')}"
            score = analyzer.polarity_scores(text)["compound"]
            total_score += score

        avg_score = total_score / len(relevant_signals)
        return round(avg_score, 2)
    except Exception as e:
        logging.error(f"Error in sentiment analysis: {e}")
        return 0.0  # Return neutral sentiment on error


if __name__ == "__main__":
    signals = fetch_all_signals()
    print(f"Fetched {len(signals)} signals:")
    for signal in signals[:20]:
        print(
            f"[{signal.get('source', '')}] {signal.get('title')} | {signal.get('created_at')} | {signal.get('url')}"
        )
