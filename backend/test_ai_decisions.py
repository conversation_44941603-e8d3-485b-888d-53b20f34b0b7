#!/usr/bin/env python3
"""
AI Decision Logic Testing and Fine-tuning Script
Tests all AI models and consensus logic for trading decisions
"""

import json
import time
from typing import Dict, List, Any
from datetime import datetime

# Import AI clients
from ai_clients import call_openai, call_gemini, call_deepseek, call_claude
from ai_clients.ai_request_manager import get_ai_decision_with_consensus


def test_individual_ai_models():
    """Test each AI model individually"""
    print("🤖 Testing Individual AI Models")
    print("=" * 50)

    # Test prompt for Bitcoin
    test_prompt = """
    Analyze Bitcoin (BTC-USDT) for trading decision:
    
    Current Data:
    - Price: $43,250.00
    - 24h Change: +2.5%
    - Volume: $1,200,000,000
    - RSI: 65
    - Market Sentiment: Bullish
    - News: Positive institutional adoption news
    
    Provide trading recommendation with confidence level.
    """

    models = [
        ("OpenAI", call_openai),
        ("Gemini", call_gemini),
        ("DeepSeek", call_deepseek),
        ("<PERSON>", call_claude),
    ]

    results = {}

    for model_name, model_func in models:
        print(f"\n🔍 Testing {model_name}...")
        try:
            start_time = time.time()
            result = model_func(test_prompt)
            response_time = time.time() - start_time

            print(
                f"✅ {model_name}: {result.get('decision', 'N/A')} "
                f"(Confidence: {result.get('confidence', 0):.1f}%) "
                f"[{response_time:.2f}s]"
            )
            print(f"   Reason: {result.get('reason', 'No reason provided')[:100]}...")

            results[model_name] = {
                **result,
                "response_time": response_time,
                "status": "success",
            }

        except Exception as e:
            print(f"❌ {model_name}: Error - {e}")
            results[model_name] = {
                "decision": "ERROR",
                "confidence": 0,
                "reason": str(e),
                "response_time": 0,
                "status": "error",
            }

        time.sleep(1)  # Brief pause between calls

    return results


def test_ai_consensus():
    """Test AI consensus mechanism"""
    print("\n🎯 Testing AI Consensus Logic")
    print("=" * 50)

    test_scenarios = [
        {
            "name": "Strong Bullish Signal",
            "prompt": """
            Bitcoin Analysis:
            - Price: $45,000 (+5% today)
            - Volume: $2B (high)
            - Breaking resistance at $44,500
            - Positive news flow
            - RSI: 70 (overbought but momentum strong)
            """,
            "expected": "BUY",
        },
        {
            "name": "Strong Bearish Signal",
            "prompt": """
            Ethereum Analysis:
            - Price: $2,800 (-8% today)
            - Volume: $1.5B (high selling pressure)
            - Breaking support at $2,850
            - Negative regulatory news
            - RSI: 25 (oversold)
            """,
            "expected": "SELL",
        },
        {
            "name": "Neutral/Uncertain Signal",
            "prompt": """
            Cardano Analysis:
            - Price: $0.45 (+0.2% today)
            - Volume: $200M (average)
            - Trading in range $0.44-$0.46
            - Mixed news sentiment
            - RSI: 50 (neutral)
            """,
            "expected": "HOLD",
        },
    ]

    consensus_results = []

    for scenario in test_scenarios:
        print(f"\n📊 Scenario: {scenario['name']}")
        print(f"Expected: {scenario['expected']}")

        try:
            start_time = time.time()
            # Extract symbol from scenario for consensus function
            symbol = (
                "BTC-USDT"
                if "Bitcoin" in scenario["prompt"]
                else "ETH-USDT" if "Ethereum" in scenario["prompt"] else "ADA-USDT"
            )
            consensus = get_ai_decision_with_consensus(scenario["prompt"], symbol)
            response_time = time.time() - start_time

            decision = consensus.get("decision", "UNKNOWN")
            confidence = consensus.get("confidence", 0)

            # Check if consensus matches expectation
            match = decision == scenario["expected"]
            match_icon = "✅" if match else "⚠️"

            print(
                f"{match_icon} Consensus: {decision} (Confidence: {confidence:.1f}%) [{response_time:.2f}s]"
            )
            print(f"   Reason: {consensus.get('reason', 'No reason')[:100]}...")

            consensus_results.append(
                {
                    "scenario": scenario["name"],
                    "expected": scenario["expected"],
                    "actual": decision,
                    "confidence": confidence,
                    "match": match,
                    "response_time": response_time,
                    "full_result": consensus,
                }
            )

        except Exception as e:
            print(f"❌ Consensus Error: {e}")
            consensus_results.append(
                {
                    "scenario": scenario["name"],
                    "expected": scenario["expected"],
                    "actual": "ERROR",
                    "confidence": 0,
                    "match": False,
                    "response_time": 0,
                    "error": str(e),
                }
            )

        time.sleep(2)  # Pause between scenarios

    return consensus_results


def analyze_ai_performance(individual_results: Dict, consensus_results: List):
    """Analyze AI performance and provide recommendations"""
    print("\n📈 AI Performance Analysis")
    print("=" * 50)

    # Individual model analysis
    working_models = [
        name
        for name, result in individual_results.items()
        if result["status"] == "success"
    ]
    failed_models = [
        name
        for name, result in individual_results.items()
        if result["status"] == "error"
    ]

    print(f"✅ Working Models: {len(working_models)}/4")
    for model in working_models:
        result = individual_results[model]
        print(f"   • {model}: {result['response_time']:.2f}s avg response")

    if failed_models:
        print(f"❌ Failed Models: {len(failed_models)}/4")
        for model in failed_models:
            print(f"   • {model}: {individual_results[model]['reason']}")

    # Consensus analysis
    successful_consensus = [r for r in consensus_results if r.get("actual") != "ERROR"]
    matching_consensus = [r for r in successful_consensus if r.get("match", False)]

    print(f"\n🎯 Consensus Performance:")
    print(f"   • Successful: {len(successful_consensus)}/{len(consensus_results)}")
    print(f"   • Accurate: {len(matching_consensus)}/{len(successful_consensus)}")

    if successful_consensus:
        avg_confidence = sum(r["confidence"] for r in successful_consensus) / len(
            successful_consensus
        )
        avg_response_time = sum(r["response_time"] for r in successful_consensus) / len(
            successful_consensus
        )
        print(f"   • Avg Confidence: {avg_confidence:.1f}%")
        print(f"   • Avg Response Time: {avg_response_time:.2f}s")

    # Recommendations
    print(f"\n💡 Recommendations:")
    if len(working_models) >= 2:
        print("   ✅ Sufficient AI models for consensus trading")
    else:
        print("   ⚠️ Need at least 2 working AI models for reliable consensus")

    if len(matching_consensus) >= 2:
        print("   ✅ AI consensus logic appears to be working correctly")
    else:
        print("   ⚠️ AI consensus may need tuning - check model weights")

    return {
        "working_models": len(working_models),
        "failed_models": len(failed_models),
        "consensus_accuracy": len(matching_consensus)
        / max(len(successful_consensus), 1),
        "avg_confidence": sum(r["confidence"] for r in successful_consensus)
        / max(len(successful_consensus), 1),
        "ready_for_trading": len(working_models) >= 2 and len(matching_consensus) >= 2,
    }


def main():
    print("🚀 Alpha Predator AI Decision Logic Testing")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Test individual models
    individual_results = test_individual_ai_models()

    # Test consensus logic
    consensus_results = test_ai_consensus()

    # Analyze performance
    analysis = analyze_ai_performance(individual_results, consensus_results)

    # Save results
    full_results = {
        "timestamp": datetime.now().isoformat(),
        "individual_results": individual_results,
        "consensus_results": consensus_results,
        "analysis": analysis,
    }

    with open("ai_decision_test_results.json", "w") as f:
        json.dump(full_results, f, indent=2)

    print(f"\n💾 Results saved to ai_decision_test_results.json")

    # Final status
    if analysis["ready_for_trading"]:
        print("\n🎉 AI SYSTEM READY FOR LIVE TRADING!")
    else:
        print("\n⚠️ AI SYSTEM NEEDS ATTENTION BEFORE LIVE TRADING")

    return analysis


if __name__ == "__main__":
    main()
