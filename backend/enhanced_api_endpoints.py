"""
Enhanced API Endpoints for Live Data
Optimized endpoints for real-time trading data, portfolio, and market information
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Dict, List, Any, Optional
import asyncio
import logging
from datetime import datetime

from enhanced_live_data import (
    get_live_tokens, 
    get_trading_tokens, 
    get_token_prices, 
    get_market_data,
    live_data_manager
)
from trade_logger import (
    load_live_trades, 
    load_portfolio_json, 
    load_trade_summary,
    log_trade
)
from pnl_dashboard import get_pnl_summary
from auth import get_current_user

logger = logging.getLogger(__name__)

# Create router for enhanced endpoints
router = APIRouter(prefix="/api/v2", tags=["Enhanced Live Data"])

@router.get("/tokens/live")
async def get_live_tokens_endpoint(
    limit: int = 50,
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get live tokens from KuCoin with enhanced metrics.
    
    Args:
        limit: Number of tokens to return (default: 50, max: 200)
    
    Returns:
        JSON response with live token data
    """
    try:
        # Validate limit
        limit = min(max(1, limit), 200)
        
        # Fetch live tokens
        tokens = await get_live_tokens(limit)
        
        if not tokens:
            return JSONResponse(
                status_code=404,
                content={"error": "No live tokens available", "tokens": []}
            )
        
        response_data = {
            "success": True,
            "data": tokens,
            "count": len(tokens),
            "timestamp": datetime.now().isoformat(),
            "cache_info": {
                "source": "live_kucoin_api",
                "refresh_interval": "60_seconds"
            }
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Failed to fetch live tokens: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch live tokens: {str(e)}"}
        )

@router.get("/tokens/trading")
def get_trading_tokens_endpoint(
    limit: int = 20,
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get top tokens optimized for trading.
    
    Args:
        limit: Number of tokens to return (default: 20, max: 100)
    
    Returns:
        JSON response with trading-ready tokens
    """
    try:
        # Validate limit
        limit = min(max(1, limit), 100)
        
        # Get trading tokens
        tokens = get_trading_tokens(limit)
        
        if not tokens:
            return JSONResponse(
                status_code=404,
                content={"error": "No trading tokens available", "tokens": []}
            )
        
        # Calculate additional metrics
        total_volume = sum(float(token.get("volume", 0)) for token in tokens)
        avg_score = sum(float(token.get("score", 0)) for token in tokens) / len(tokens)
        
        response_data = {
            "success": True,
            "data": tokens,
            "count": len(tokens),
            "timestamp": datetime.now().isoformat(),
            "metrics": {
                "total_volume": total_volume,
                "average_score": avg_score,
                "high_score_tokens": len([t for t in tokens if float(t.get("score", 0)) > 0.5])
            }
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Failed to fetch trading tokens: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch trading tokens: {str(e)}"}
        )

@router.get("/prices")
async def get_token_prices_endpoint(
    symbols: str,
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get live prices for specific tokens.
    
    Args:
        symbols: Comma-separated list of token symbols (e.g., "BTC-USDT,ETH-USDT")
    
    Returns:
        JSON response with token prices
    """
    try:
        # Parse symbols
        symbol_list = [s.strip().upper() for s in symbols.split(",") if s.strip()]
        
        if not symbol_list:
            return JSONResponse(
                status_code=400,
                content={"error": "No valid symbols provided"}
            )
        
        if len(symbol_list) > 50:
            return JSONResponse(
                status_code=400,
                content={"error": "Too many symbols requested (max: 50)"}
            )
        
        # Get prices
        prices = await get_token_prices(symbol_list)
        
        response_data = {
            "success": True,
            "data": prices,
            "count": len(prices),
            "timestamp": datetime.now().isoformat(),
            "requested_symbols": symbol_list
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Failed to fetch token prices: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch token prices: {str(e)}"}
        )

@router.get("/market/summary")
def get_market_summary_endpoint(
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get comprehensive market summary.
    
    Returns:
        JSON response with market data and trends
    """
    try:
        market_data = get_market_data()
        
        if "error" in market_data:
            return JSONResponse(
                status_code=500,
                content={"error": market_data["error"]}
            )
        
        return JSONResponse(content={
            "success": True,
            "data": market_data,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to fetch market summary: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch market summary: {str(e)}"}
        )

@router.get("/trades/live")
def get_live_trades_endpoint(
    limit: int = 100,
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get live trades with enhanced metadata.
    
    Args:
        limit: Number of trades to return (default: 100, max: 500)
    
    Returns:
        JSON response with live trade data
    """
    try:
        # Validate limit
        limit = min(max(1, limit), 500)
        
        # Load live trades
        trades_data = load_live_trades()
        
        if not trades_data or "trades" not in trades_data:
            return JSONResponse(content={
                "success": True,
                "data": {"trades": []},
                "count": 0,
                "timestamp": datetime.now().isoformat()
            })
        
        # Limit trades
        trades = trades_data["trades"][:limit]
        
        # Calculate trade metrics
        total_value = sum(float(trade.get("value_usd", 0)) for trade in trades)
        buy_trades = len([t for t in trades if t.get("side", "").upper() == "BUY"])
        sell_trades = len([t for t in trades if t.get("side", "").upper() == "SELL"])
        
        response_data = {
            "success": True,
            "data": {
                "trades": trades,
                "metadata": {
                    "total_trades": len(trades),
                    "total_value": total_value,
                    "buy_trades": buy_trades,
                    "sell_trades": sell_trades,
                    "last_updated": trades_data.get("last_updated"),
                    "source": trades_data.get("source", "local_storage")
                }
            },
            "count": len(trades),
            "timestamp": datetime.now().isoformat()
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Failed to fetch live trades: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch live trades: {str(e)}"}
        )

@router.get("/portfolio/enhanced")
def get_enhanced_portfolio_endpoint(
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get enhanced portfolio with real-time valuations.
    
    Returns:
        JSON response with portfolio data and metrics
    """
    try:
        # Load portfolio
        portfolio, balance = load_portfolio_json()
        
        # Get current prices for portfolio tokens
        if portfolio:
            symbols = list(portfolio.keys())
            # Note: This would need to be made async in a real implementation
            # For now, we'll use cached prices or estimates
            current_prices = {}
            for symbol in symbols:
                # Use avg_price as fallback for current_price
                pos = portfolio[symbol]
                current_prices[symbol] = pos.get("current_price", pos.get("avg_price", 0))
        else:
            current_prices = {}
        
        # Calculate enhanced metrics
        total_invested = sum(
            pos.get("qty", 0) * pos.get("avg_price", 0)
            for pos in portfolio.values()
        )
        
        current_value = sum(
            pos.get("qty", 0) * current_prices.get(symbol, pos.get("avg_price", 0))
            for symbol, pos in portfolio.items()
        )
        
        unrealized_pnl = current_value - total_invested
        unrealized_pnl_percent = (unrealized_pnl / total_invested * 100) if total_invested > 0 else 0
        
        # Enhance portfolio positions
        enhanced_portfolio = {}
        for symbol, pos in portfolio.items():
            current_price = current_prices.get(symbol, pos.get("avg_price", 0))
            qty = pos.get("qty", 0)
            avg_price = pos.get("avg_price", 0)
            
            position_value = qty * current_price
            position_cost = qty * avg_price
            position_pnl = position_value - position_cost
            position_pnl_percent = (position_pnl / position_cost * 100) if position_cost > 0 else 0
            
            enhanced_portfolio[symbol] = {
                **pos,
                "current_price": current_price,
                "position_value": position_value,
                "position_cost": position_cost,
                "unrealized_pnl": position_pnl,
                "unrealized_pnl_percent": position_pnl_percent,
                "last_updated": datetime.now().isoformat()
            }
        
        response_data = {
            "success": True,
            "data": {
                "portfolio": enhanced_portfolio,
                "balance": balance,
                "metrics": {
                    "total_positions": len(portfolio),
                    "total_invested": total_invested,
                    "current_value": current_value,
                    "unrealized_pnl": unrealized_pnl,
                    "unrealized_pnl_percent": unrealized_pnl_percent,
                    "total_portfolio_value": current_value + balance
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Failed to fetch enhanced portfolio: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch enhanced portfolio: {str(e)}"}
        )

@router.get("/pnl/enhanced")
def get_enhanced_pnl_endpoint(
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Get enhanced P&L data with detailed metrics.
    
    Returns:
        JSON response with comprehensive P&L analysis
    """
    try:
        # Get basic PnL data
        pnl_data = get_pnl_summary()
        
        # Get trade summary
        trade_summary = load_trade_summary()
        
        # Combine and enhance
        enhanced_pnl = {}
        if isinstance(pnl_data, dict):
            enhanced_pnl.update(pnl_data)
        enhanced_pnl.update({
            "trade_summary": trade_summary,
            "timestamp": datetime.now().isoformat(),
            "data_source": "enhanced_local_storage"
        })
        
        return JSONResponse(content={
            "success": True,
            "data": enhanced_pnl,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to fetch enhanced PnL: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch enhanced PnL: {str(e)}"}
        )

# Background task for cache warming
@router.post("/admin/warm-cache")
async def warm_cache_endpoint(
    background_tasks: BackgroundTasks,
    current_user: str = Depends(get_current_user)
) -> JSONResponse:
    """
    Warm up data caches for better performance.
    
    Returns:
        JSON response confirming cache warming started
    """
    try:
        # Add background task to warm caches
        background_tasks.add_task(warm_data_caches)
        
        return JSONResponse(content={
            "success": True,
            "message": "Cache warming started in background",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Failed to start cache warming: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to start cache warming: {str(e)}"}
        )

async def warm_data_caches():
    """Background task to warm up data caches."""
    try:
        logger.info("Starting cache warming...")
        
        # Warm token cache
        await live_data_manager.fetch_live_kucoin_tokens(100)
        
        # Warm trading tokens cache
        live_data_manager.get_top_trading_tokens(50)
        
        # Warm market data cache
        live_data_manager.get_market_summary()
        
        logger.info("✅ Cache warming completed")
        
    except Exception as e:
        logger.error(f"Cache warming failed: {e}")
