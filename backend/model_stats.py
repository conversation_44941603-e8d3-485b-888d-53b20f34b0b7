import json
import os
import logging
from typing import Dict

# Setup logging
logger = logging.getLogger("model_stats")
logging.basicConfig(level=logging.INFO)

STATS_FILE = "backend/data/model_stats.json"

def load_stats() -> Dict[str, dict]:
    """
    Load the model statistics from the JSON file.

    Returns:
        dict: Dictionary of stats per model.
    """
    if not os.path.exists(STATS_FILE):
        logger.warning(f"Stats file not found: {STATS_FILE}")
        return {}
    try:
        with open(STATS_FILE, "r") as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode model stats JSON: {e}")
        return {}

def save_stats(stats: Dict[str, dict]) -> None:
    """
    Save model statistics to the JSON file.

    Args:
        stats (dict): Stats dictionary to save.
    """
    try:
        with open(STATS_FILE, "w") as f:
            json.dump(stats, f, indent=2)
        logger.info(f"Saved model stats to {STATS_FILE}")
    except Exception as e:
        logger.error(f"Failed to save stats: {e}")

def update_model_accuracy(model_name: str, correct: bool) -> None:
    """
    Update the accuracy statistics for a given model.

    Args:
        model_name (str): Name of the model.
        correct (bool): Whether the model's prediction was correct.
    """
    stats = load_stats()
    if model_name not in stats:
        stats[model_name] = {"total": 0, "correct": 0, "accuracy": 0.0}

    stats[model_name]["total"] += 1
    if correct:
        stats[model_name]["correct"] += 1

    total = stats[model_name]["total"]
    correct_count = stats[model_name]["correct"]
    stats[model_name]["accuracy"] = round(correct_count / total, 4) if total > 0 else 0.0

    save_stats(stats)
    logger.info(f"Updated accuracy for {model_name}: {stats[model_name]}")

def get_model_accuracies() -> Dict[str, float]:
    """
    Get the accuracy values of all tracked models.

    Returns:
        dict: Dictionary of model names and their accuracy values.
    """
    stats = load_stats()
    return {model: stat.get("accuracy", 0.0) for model, stat in stats.items()}