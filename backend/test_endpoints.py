#!/usr/bin/env python3
"""
Alpha Predator API Endpoint Testing Script
Tests all critical endpoints with proper authentication
"""

import requests
import json
import time
from typing import Dict, Any

# Server configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "admin123"  # You may need to adjust this

class APITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.headers = {}
        
    def login(self) -> bool:
        """Login and get JWT token"""
        try:
            login_data = {
                "email": TEST_EMAIL,
                "password": TEST_PASSWORD
            }
            
            response = requests.post(
                f"{self.base_url}/api/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.headers = {"Authorization": f"Bearer {self.token}"}
                print("✅ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def test_endpoint(self, endpoint: str, method: str = "GET", timeout: int = 30) -> Dict[str, Any]:
        """Test a specific endpoint"""
        try:
            print(f"🔍 Testing {method} {endpoint}...")
            
            if method == "GET":
                response = requests.get(
                    f"{self.base_url}{endpoint}",
                    headers=self.headers,
                    timeout=timeout
                )
            elif method == "POST":
                response = requests.post(
                    f"{self.base_url}{endpoint}",
                    headers=self.headers,
                    timeout=timeout
                )
            
            result = {
                "endpoint": endpoint,
                "method": method,
                "status_code": response.status_code,
                "success": 200 <= response.status_code < 300,
                "response_time": response.elapsed.total_seconds(),
                "error": None,
                "data_preview": None
            }
            
            if result["success"]:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        result["data_preview"] = f"Array with {len(data)} items"
                    elif isinstance(data, dict):
                        result["data_preview"] = f"Object with keys: {list(data.keys())[:5]}"
                    else:
                        result["data_preview"] = str(data)[:100]
                except:
                    result["data_preview"] = "Non-JSON response"
                    
                print(f"✅ {endpoint}: {response.status_code} ({result['response_time']:.2f}s)")
            else:
                result["error"] = response.text[:200]
                print(f"❌ {endpoint}: {response.status_code} - {result['error']}")
                
            return result
            
        except requests.exceptions.Timeout:
            print(f"⏰ {endpoint}: Timeout after {timeout}s")
            return {
                "endpoint": endpoint,
                "method": method,
                "status_code": 408,
                "success": False,
                "response_time": timeout,
                "error": "Timeout",
                "data_preview": None
            }
        except Exception as e:
            print(f"❌ {endpoint}: Exception - {e}")
            return {
                "endpoint": endpoint,
                "method": method,
                "status_code": 500,
                "success": False,
                "response_time": 0,
                "error": str(e),
                "data_preview": None
            }

def main():
    print("🚀 Alpha Predator API Endpoint Testing")
    print("=" * 50)
    
    tester = APITester()
    
    # Login first
    if not tester.login():
        print("❌ Cannot proceed without authentication")
        return
    
    # Critical endpoints to test
    endpoints = [
        # Core data endpoints
        ("/api/discover", "GET", 60),  # Token discovery (might be slow)
        ("/api/tokens", "GET", 30),   # Token list
        ("/api/ai-logic", "GET", 45), # AI signals (might be slow)
        ("/api/analytics", "GET", 20), # Analytics
        ("/api/pnl-data", "GET", 15),  # PnL data
        
        # News endpoints
        ("/api/news/live", "GET", 30), # Live news
        ("/api/news", "GET", 10),      # Saved news
        
        # Trading endpoints
        ("/api/portfolio", "GET", 15), # Portfolio status
        ("/api/summary", "GET", 20),   # Trading summary
        
        # Monitoring endpoints
        ("/api/cost-monitoring", "GET", 15), # Cost monitoring
        ("/api/ai-optimization", "GET", 10), # AI optimization
        
        # Fast cached endpoints
        ("/api/dashboard/pnl-fast", "GET", 10),
        ("/api/dashboard/trades-fast", "GET", 10),
        ("/api/dashboard/ai-signals-fast", "GET", 20),
    ]
    
    results = []
    total_tests = len(endpoints)
    passed_tests = 0
    
    print(f"\n🧪 Testing {total_tests} endpoints...")
    print("-" * 50)
    
    for endpoint, method, timeout in endpoints:
        result = tester.test_endpoint(endpoint, method, timeout)
        results.append(result)
        if result["success"]:
            passed_tests += 1
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Failed endpoints
    failed_endpoints = [r for r in results if not r["success"]]
    if failed_endpoints:
        print(f"\n❌ FAILED ENDPOINTS ({len(failed_endpoints)}):")
        for result in failed_endpoints:
            print(f"  • {result['endpoint']}: {result['status_code']} - {result['error']}")
    
    # Slow endpoints
    slow_endpoints = [r for r in results if r["success"] and r["response_time"] > 10]
    if slow_endpoints:
        print(f"\n⏰ SLOW ENDPOINTS (>10s):")
        for result in slow_endpoints:
            print(f"  • {result['endpoint']}: {result['response_time']:.1f}s")
    
    # Save detailed results
    with open("endpoint_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Detailed results saved to endpoint_test_results.json")

if __name__ == "__main__":
    main()
