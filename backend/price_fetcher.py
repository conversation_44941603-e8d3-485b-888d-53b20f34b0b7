# NOTE: This module exclusively fetches price and volume data using SDKs.
# SDK-based approach for optimal performance and reliability.

# SDK-based imports for optimal performance
from kucoin_sdk_migration import kucoin_sdk
from coingecko_sdk_migration import coingecko_sdk

import logging
import json
from typing import List, Dict, Optional, Any
from utils import retry_request
from config import KUCOIN_BASE_URL, COINGECKO_API_KEY
from exchange_data import fetch_kucoin_price

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def format_symbol(token: str) -> str:
    """
    Ensure token symbol is KuCoin-compatible. Adds '-USDT' suffix if missing.

    Args:
        token (str): Token symbol (e.g., 'BTC', 'ETH-USDT')

    Returns:
        str: Formatted token symbol compatible with KuCoin
    """
    token = token.strip().upper()
    if not token.endswith("-USDT"):
        return f"{token}-USDT"
    return token


import functools

# Cache for CoinGecko ID map
_coingecko_id_map = None


@functools.lru_cache(maxsize=1)  # Cache the result of this function
def _get_coingecko_id_map() -> Dict[str, str]:
    """
    Fetches and returns a mapping from cryptocurrency symbols to CoinGecko IDs.
    Caches the result to avoid repeated API calls.
    """
    global _coingecko_id_map
    if _coingecko_id_map is not None:
        return _coingecko_id_map

    logger.info("Fetching CoinGecko coin list for ID mapping...")
    url = "https://api.coingecko.com/api/v3/coins/list"
    headers = {"Accept": "application/json"}
    try:
        response = retry_request(url, headers=headers)
        if response is None:
            logger.warning("No response from CoinGecko /coins/list.")
            return {}

        coin_list = response.json()
        id_map = {}
        for coin in coin_list:
            coin_id = coin.get("id")
            symbol = coin.get("symbol")
            name = coin.get("name")

            if symbol:
                id_map[symbol.upper()] = coin_id
            if name:
                id_map[name.upper()] = coin_id

        # Hardcoded mappings for common symbols to ensure correctness
        # These will override any potentially incorrect mappings from the API list
        id_map["BTC"] = "bitcoin"
        id_map["ETH"] = "ethereum"
        id_map["XRP"] = "ripple"
        id_map["ADA"] = "cardano"
        id_map["LINK"] = "chainlink"
        id_map["LINA"] = "linear-protocol"  # Fix for LINA token
        id_map["TRX"] = "tron"
        id_map["AAVE"] = "aave"
        id_map["SEI"] = "sei-network"
        # Add more common symbols as needed

        _coingecko_id_map = id_map  # Cache the map globally
        logger.info(
            f"Successfully fetched and cached CoinGecko ID map with {len(id_map)} entries."
        )
        return id_map
    except Exception as e:
        logger.exception(f"[❌] Failed to fetch CoinGecko coin list: {e}")
        return {}


def get_prices_batch_coingecko(tokens: List[str]) -> Dict[str, float]:
    """
    Fetches current prices for multiple tokens from CoinGecko in a single batch.

    Args:
        tokens (List[str]): List of token symbols (e.g., ['BTC', 'ETH']).

    Returns:
        Dict[str, float]: A dictionary mapping token symbols to their prices.
    """
    if not tokens:
        return {}

    id_map = _get_coingecko_id_map()
    coingecko_ids = []
    token_to_id_map = {}

    for token in tokens:
        # Strip '-USDT' suffix if present for CoinGecko lookup
        token_key = token.upper()
        if token_key.endswith("-USDT"):
            token_key = token_key[:-5]

        coingecko_id = id_map.get(token_key)
        logger.debug(f"Mapping {token_key} to CoinGecko ID: {coingecko_id}")

        if coingecko_id:
            # Validate that the ID doesn't contain problematic characters
            if coingecko_id and isinstance(coingecko_id, str) and len(coingecko_id) > 0:
                coingecko_ids.append(coingecko_id)
                token_to_id_map[token] = coingecko_id
            else:
                logger.warning(
                    f"Invalid CoinGecko ID for token {token}: {coingecko_id}"
                )
        else:
            logger.warning(f"Could not find CoinGecko ID for token: {token}")

    if not coingecko_ids:
        return {}

    # Split into smaller batches to avoid URL length limits and API errors
    batch_size = 50
    all_prices = {}

    for i in range(0, len(coingecko_ids), batch_size):
        batch_ids = coingecko_ids[i : i + batch_size]
        ids_param = ",".join(batch_ids)

        logger.debug(f"CoinGecko IDs parameter for batch fetch: {ids_param}")
        url = f"https://api.coingecko.com/api/v3/simple/price?ids={ids_param}&vs_currencies=usdt"
        headers = {"Accept": "application/json"}
        if COINGECKO_API_KEY:
            headers["x-cg-pro-api-key"] = COINGECKO_API_KEY

        try:
            response = retry_request(url, headers=headers)
            if response is None:
                logger.warning(f"No response from CoinGecko for batch: {batch_ids}")
                continue

            data = response.json()

            # Map back to token symbols
            for token_symbol in tokens:
                coingecko_id = token_to_id_map.get(token_symbol)
                if coingecko_id and coingecko_id in batch_ids:
                    price_data = data.get(coingecko_id, {})
                    if isinstance(price_data, dict):
                        price = price_data.get("usdt")
                        if price is not None:
                            all_prices[token_symbol] = float(price)
                        else:
                            logger.warning(
                                f"No price returned from CoinGecko for {token_symbol} (ID: {coingecko_id})"
                            )
                    else:
                        logger.warning(
                            f"Unexpected price data format for {token_symbol}: {price_data}"
                        )

        except Exception as e:
            logger.warning(
                f"[⚠️] CoinGecko batch fetch failed for batch {batch_ids}: {e}"
            )
            continue

    return all_prices


async def get_current_price(token: str) -> float | None:
    """
    Alias for get_price to maintain backward compatibility.
    """
    return await get_price(token)


async def get_price(token: str) -> float | None:
    """
    Fetch the current price of a token, trying KuCoin first, then CoinGecko as a fallback.

    Args:
        token (str): Token symbol (e.g., 'BTC', 'ETH')

    Returns:
        float | None: Current token price or None if error from all sources
    """
    kucoin_symbol = format_symbol(token)
    price = None

    # Try KuCoin SDK first for optimal performance
    try:
        price = kucoin_sdk.get_token_price(kucoin_symbol)
        if price is not None and price > 0:
            logger.info(f"✅ Fetched price for {token} from KuCoin SDK: ${price:.6f}")
            return float(price)
    except Exception as e:
        logger.warning(f"[⚠️] KuCoin SDK failed for {token}: {e}")

    # Fallback to legacy KuCoin method
    try:
        price = fetch_kucoin_price(kucoin_symbol)
        if price is not None:
            logger.info(f"✅ Fetched price for {token} from KuCoin (legacy).")
            return price
    except Exception as e:
        logger.warning(
            f"[⚠️] KuCoin legacy fetch failed for {token}: {e}. Attempting CoinGecko fallback."
        )

    # Fallback to CoinGecko using the new batch function
    coingecko_prices = get_prices_batch_coingecko([token])
    price = coingecko_prices.get(token)

    if price is not None:
        logger.info(f"✅ Fetched price for {token} from CoinGecko (fallback).")
        return price
    else:
        logger.error(
            f"[❌] Failed to fetch price for {token} from both KuCoin and CoinGecko."
        )
        return None


def get_price_sync(token: str) -> Optional[float]:
    """
    🎯 SYNC VERSION: Get current price for a token (synchronous)

    Args:
        token: Token symbol (e.g., 'BTC', 'ETH')

    Returns:
        Current price as float, or None if unavailable
    """
    try:
        import asyncio

        # Try to get existing event loop
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, use thread executor
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, get_price(token))
                    return future.result(timeout=10)
            else:
                # If no loop running, use asyncio.run
                return asyncio.run(get_price(token))
        except RuntimeError:
            # No event loop, use asyncio.run
            return asyncio.run(get_price(token))

    except Exception as e:
        logger.error(f"❌ Sync price fetch failed for {token}: {e}")
        return None


def get_24h_volume_kucoin(token: str) -> float:
    """
    Fetch the 24-hour trading volume in USD for a specific token on KuCoin.

    Args:
        token (str): Token symbol

    Returns:
        float: 24h trading volume in USD (0.0 if failed)
    """
    symbol = format_symbol(token)
    try:
        url = f"{KUCOIN_BASE_URL}/api/v1/market/stats?symbol={symbol}"
        headers = {"Accept": "application/json"}
        response = retry_request(url, headers=headers)
        if response is None:
            logger.warning(f"No response from KuCoin for {symbol}")
            return 0.0
        try:
            data = response.json()
        except json.JSONDecodeError:
            logger.warning(
                f"Invalid JSON response from KuCoin for {symbol}: {response.text}"
            )
            return 0.0

        if not isinstance(data, dict):
            logger.warning(f"Unexpected data format from KuCoin for {symbol}: {data}")
            return 0.0

        if data.get("code") != "200000":
            logger.warning(
                f"KuCoin API error for {symbol}: {data.get('msg', 'Unknown error')}"
            )
            return 0.0

        volume = data.get("data", {}).get("volValue")
        if volume is None:
            logger.warning(f"No volume returned for {symbol}: {data}")
            return 0.0

        return float(volume)
    except Exception as e:
        logger.exception(f"[❌] KuCoin volume fetch failed for {symbol}")
        return 0.0


# Enhanced price fetching with connection pooling and intelligent caching

import asyncio
import aiohttp
from datetime import datetime, timedelta
from dataclasses import dataclass
from cache import get_cached_data, set_cached_data

from tokenmetrics_client import get_tokenmetrics_data

# Enhanced configuration for LIVE DATA
MAX_RETRIES = 3
TIMEOUT_SECONDS = 5  # 🔴 REDUCED: Faster timeouts for live data
CONNECTION_POOL_SIZE = 30  # 🚀 INCREASED: More connections for live data
CACHE_TTL_PRICES = 5  # 🔴 REDUCED: 5 seconds for real-time prices
RATE_LIMIT_DELAY = 0.05  # 🔴 REDUCED: 50ms between requests for faster updates


@dataclass
class EnhancedPriceData:
    """Enhanced price data with comprehensive market information"""

    symbol: str
    price: float
    volume_24h: float
    market_cap: float
    price_change_24h: float
    price_change_percentage_24h: float
    last_updated: datetime
    source: str
    confidence: float


# Global session for connection pooling
_enhanced_session: Optional[aiohttp.ClientSession] = None


async def get_enhanced_session() -> aiohttp.ClientSession:
    """Get or create global aiohttp session with connection pooling."""
    global _enhanced_session
    if _enhanced_session is None or _enhanced_session.closed:
        connector = aiohttp.TCPConnector(
            limit=CONNECTION_POOL_SIZE,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        timeout = aiohttp.ClientTimeout(total=TIMEOUT_SECONDS)
        _enhanced_session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "AlphaPredatorBot/1.0"},
        )
    return _enhanced_session


async def get_enhanced_price_data(symbol: str) -> EnhancedPriceData:
    """Get enhanced price data with caching and multiple source fallback."""
    try:
        # Check cache first
        cache_key = f"enhanced_price_{symbol}"
        cached_data = get_cached_data(cache_key)
        if cached_data:
            logger.debug(f"✅ Cache hit for price data: {symbol}")
            return EnhancedPriceData(**cached_data)

        # Format symbol for consistency
        formatted_symbol = format_symbol(symbol)

        # Try multiple sources with fallback
        price_data = await _fetch_price_with_fallback(formatted_symbol)

        if price_data:
            # Cache the result
            set_cached_data(cache_key, price_data.__dict__, CACHE_TTL_PRICES, "high")
            logger.info(f"✅ Enhanced price data for {symbol}: ${price_data.price:.6f}")
            return price_data
        else:
            # Return fallback data
            return _create_fallback_price_data(symbol)

    except Exception as e:
        logger.error(f"❌ Enhanced price fetch failed for {symbol}: {e}")
        return _create_fallback_price_data(symbol)


async def get_batch_price_data(symbols: List[str]) -> List[EnhancedPriceData]:
    """Get price data for multiple symbols in parallel with rate limiting."""
    try:
        logger.info(f"🔄 Fetching price data for {len(symbols)} symbols in parallel...")

        # Create tasks with rate limiting
        tasks = []
        for i, symbol in enumerate(symbols):
            # Add delay to respect rate limits
            delay = i * RATE_LIMIT_DELAY
            task = asyncio.create_task(_delayed_price_fetch(symbol, delay))
            tasks.append(task)

        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter successful results
        price_data_list = [
            result for result in results if isinstance(result, EnhancedPriceData)
        ]

        logger.info(f"✅ Batch price fetch complete: {len(price_data_list)} successful")
        return price_data_list

    except Exception as e:
        logger.error(f"❌ Batch price fetch failed: {e}")
        return [_create_fallback_price_data(symbol) for symbol in symbols]


async def _delayed_price_fetch(symbol: str, delay: float) -> EnhancedPriceData:
    """Fetch price data with delay for rate limiting."""
    if delay > 0:
        await asyncio.sleep(delay)
    return await get_enhanced_price_data(symbol)


async def _fetch_price_with_fallback(symbol: str) -> Optional[EnhancedPriceData]:
    """Fetch price data with multiple source fallback including TokenMetrics."""
    try:
        session = await get_enhanced_session()

        # 🚀 NEW: Try TokenMetrics first (highest quality data)
        tokenmetrics_data = await _fetch_tokenmetrics_price_async(symbol)
        if tokenmetrics_data:
            logger.info(
                f"✅ TokenMetrics data for {symbol}: ${tokenmetrics_data.price:.6f}"
            )
            return tokenmetrics_data

        # Try KuCoin second (primary exchange source)
        kucoin_data = await _fetch_kucoin_price_async(session, symbol)
        if kucoin_data:
            logger.info(f"✅ KuCoin data for {symbol}: ${kucoin_data.price:.6f}")
            return kucoin_data

        # Fallback to CoinGecko
        coingecko_data = await _fetch_coingecko_price_async(session, symbol)
        if coingecko_data:
            logger.info(f"✅ CoinGecko data for {symbol}: ${coingecko_data.price:.6f}")
            return coingecko_data

        # Final fallback to cached data
        logger.warning(f"⚠️ Using cached fallback for {symbol}")
        return _get_cached_fallback_price(symbol)

    except Exception as e:
        logger.error(f"Price fetch with fallback failed for {symbol}: {e}")
        return _create_fallback_price_data(symbol)


async def _fetch_tokenmetrics_price_async(symbol: str) -> Optional[EnhancedPriceData]:
    """Fetch price data from TokenMetrics API asynchronously."""
    try:
        tokenmetrics_data = await get_tokenmetrics_data(symbol)
        if tokenmetrics_data:
            # Convert TokenMetrics data to EnhancedPriceData
            return EnhancedPriceData(
                symbol=symbol,
                price=tokenmetrics_data.price,
                volume_24h=tokenmetrics_data.volume_24h,
                market_cap=tokenmetrics_data.market_cap,
                price_change_24h=tokenmetrics_data.price_change_24h,
                price_change_percentage_24h=tokenmetrics_data.price_change_percentage_24h,
                last_updated=tokenmetrics_data.last_updated,
                source="tokenmetrics",
                confidence=tokenmetrics_data.confidence,
            )
        return None

    except Exception as e:
        logger.error(f"TokenMetrics async price fetch failed for {symbol}: {e}")
        return None


async def _fetch_kucoin_price_async(
    session: aiohttp.ClientSession, symbol: str
) -> Optional[EnhancedPriceData]:
    """Fetch price data from KuCoin API asynchronously."""
    try:
        url = f"{KUCOIN_BASE_URL}/api/v1/market/orderbook/level1"
        params = {"symbol": symbol}

        async with session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                if data.get("code") == "200000":
                    ticker_data = data.get("data", {})

                    return EnhancedPriceData(
                        symbol=symbol,
                        price=float(ticker_data.get("price", 0)),
                        volume_24h=0.0,  # Not available in this endpoint
                        market_cap=0.0,  # Not available
                        price_change_24h=0.0,  # Not available
                        price_change_percentage_24h=0.0,  # Not available
                        last_updated=datetime.now(),
                        source="kucoin",
                        confidence=0.9,
                    )
        return None

    except Exception as e:
        logger.error(f"KuCoin async price fetch failed for {symbol}: {e}")
        return None


def _create_fallback_price_data(symbol: str) -> EnhancedPriceData:
    """Create fallback price data when all sources fail."""
    return EnhancedPriceData(
        symbol=symbol,
        price=1.0,  # Default price
        volume_24h=0.0,
        market_cap=0.0,
        price_change_24h=0.0,
        price_change_percentage_24h=0.0,
        last_updated=datetime.now(),
        source="fallback",
        confidence=0.0,
    )


def _get_cached_fallback_price(symbol: str) -> EnhancedPriceData:
    """Get cached price data as fallback."""
    try:
        cache_key = f"fallback_price_{symbol}"
        cached_data = get_cached_data(cache_key)
        if cached_data:
            return EnhancedPriceData(**cached_data)
        else:
            return _create_fallback_price_data(symbol)
    except Exception:
        return _create_fallback_price_data(symbol)


async def _fetch_coingecko_price_async(
    session: aiohttp.ClientSession, symbol: str
) -> Optional[EnhancedPriceData]:
    """Fetch price data from CoinGecko API asynchronously."""
    try:
        # Get CoinGecko ID for the symbol
        coin_id = _symbol_to_coingecko_id(symbol.replace("-USDT", ""))
        if not coin_id:
            return None

        url = f"https://api.coingecko.com/api/v3/simple/price"
        params = {
            "ids": coin_id,
            "vs_currencies": "usd",
            "include_24hr_change": "true",
            "include_24hr_vol": "true",
            "include_market_cap": "true",
        }

        headers = {"User-Agent": "AlphaPredatorBot/1.0", "Accept": "application/json"}
        if COINGECKO_API_KEY:
            # 🔧 FIX: Use correct header format for CoinGecko API
            if COINGECKO_API_KEY.startswith("CG-"):
                headers["x-cg-pro-api-key"] = COINGECKO_API_KEY  # Pro API key
            else:
                headers["x-cg-demo-api-key"] = COINGECKO_API_KEY  # Demo API key

        async with session.get(url, params=params, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                coin_data = data.get(coin_id, {})

                if coin_data:
                    return EnhancedPriceData(
                        symbol=symbol,
                        price=float(coin_data.get("usd", 0)),
                        volume_24h=float(coin_data.get("usd_24h_vol", 0)),
                        market_cap=float(coin_data.get("usd_market_cap", 0)),
                        price_change_24h=0.0,  # Calculate from percentage
                        price_change_percentage_24h=float(
                            coin_data.get("usd_24h_change", 0)
                        ),
                        last_updated=datetime.now(),
                        source="coingecko",
                        confidence=0.8,
                    )
        return None

    except Exception as e:
        logger.error(f"CoinGecko async price fetch failed for {symbol}: {e}")
        return None


def _symbol_to_coingecko_id(symbol: str) -> str:
    """Convert symbol to CoinGecko ID using enhanced mapping."""
    try:
        # 🔧 FIX: Enhanced symbol to ID mapping
        symbol_clean = symbol.replace("-USDT", "").replace("-USD", "").lower()

        # Common symbol mappings
        symbol_map = {
            "btc": "bitcoin",
            "eth": "ethereum",
            "bnb": "binancecoin",
            "ada": "cardano",
            "sol": "solana",
            "dot": "polkadot",
            "matic": "polygon",
            "avax": "avalanche-2",
            "link": "chainlink",
            "uni": "uniswap",
            "ltc": "litecoin",
            "bch": "bitcoin-cash",
            "xlm": "stellar",
            "vet": "vechain",
            "trx": "tron",
            "etc": "ethereum-classic",
            "ftt": "ftx-token",
            "near": "near",
            "algo": "algorand",
            "mana": "decentraland",
            "sand": "the-sandbox",
            "gala": "gala",
            "ape": "apecoin",
            "lrc": "loopring",
            "cro": "crypto-com-chain",
            "shib": "shiba-inu",
            "doge": "dogecoin",
        }

        # Try direct mapping first
        if symbol_clean in symbol_map:
            return symbol_map[symbol_clean]

        # Try cached mapping
        id_map = _get_coingecko_id_map()
        mapped_id = id_map.get(symbol_clean, "")
        if mapped_id:
            return mapped_id

        # Fallback to symbol itself
        return symbol_clean

    except Exception as e:
        logger.debug(f"Symbol mapping failed for {symbol}: {e}")
        return symbol.replace("-USDT", "").replace("-USD", "").lower()


async def cleanup_enhanced_session():
    """Cleanup the global session."""
    global _enhanced_session
    if _enhanced_session and not _enhanced_session.closed:
        await _enhanced_session.close()
        _enhanced_session = None
