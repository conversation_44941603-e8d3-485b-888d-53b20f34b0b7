"""
API Fallback Manager
Implements intelligent fallback mechanisms between different API providers
to ensure continuous data availability even when primary APIs are rate limited.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import time

from api_optimization_manager import api_optimizer
from tokenmetrics_api import TokenMetricsAPI
from coingecko_enhanced import coingecko_enhanced

logger = logging.getLogger(__name__)

class APIFallbackManager:
    """
    Manages fallback strategies between different API providers
    to ensure continuous data availability
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize API clients
        self.tokenmetrics = TokenMetricsAPI()
        
        # API priority order for different data types
        self.api_priority = {
            'token_data': ['tokenmetrics', 'coingecko'],  # TokenMetrics first due to advanced membership
            'price_data': ['coingecko', 'tokenmetrics'],  # CoinGecko for real-time prices
            'market_data': ['tokenmetrics', 'coingecko'], # TokenMetrics for comprehensive market data
        }
        
        # Track fallback usage for optimization
        self.fallback_stats = {
            'coingecko_failures': 0,
            'tokenmetrics_failures': 0,
            'successful_fallbacks': 0,
            'total_requests': 0
        }
        
        # Enhanced rate limits for TokenMetrics advanced membership
        self.enhanced_limits = {
            'tokenmetrics': {
                'calls_per_minute': 200,  # Advanced membership higher limits
                'calls_per_hour': 12000,
                'delay': 0.3,  # Faster calls with advanced membership
                'burst_limit': 20,
                'reset_window': 60
            }
        }
        
        # Update API optimizer with enhanced TokenMetrics limits
        api_optimizer.rate_limits.update(self.enhanced_limits)
    
    async def get_token_data_with_fallback(self, symbol: str, data_type: str = 'token_data') -> Optional[Dict[str, Any]]:
        """
        Get token data with intelligent fallback between APIs
        
        Args:
            symbol: Token symbol (e.g., 'BTC-USDT')
            data_type: Type of data needed ('token_data', 'price_data', 'market_data')
        
        Returns:
            Token data from the first available API
        """
        self.fallback_stats['total_requests'] += 1
        
        # Get API priority for this data type
        api_list = self.api_priority.get(data_type, ['tokenmetrics', 'coingecko'])
        
        for api_name in api_list:
            try:
                # Check if API is available
                if not api_optimizer.can_make_api_call(api_name, symbol):
                    wait_time = api_optimizer.get_time_until_next_call(api_name)
                    self.logger.info(f"{api_name} rate limited for {symbol}, wait {wait_time:.1f}s, trying next API")
                    continue
                
                # Try to get data from this API
                data = await self._fetch_from_api(api_name, symbol, data_type)
                
                if data:
                    # Record successful API call
                    api_optimizer.record_api_call(api_name)
                    self.logger.info(f"Successfully fetched {data_type} for {symbol} from {api_name}")
                    return data
                else:
                    self.logger.warning(f"No data returned from {api_name} for {symbol}")
                    
            except Exception as e:
                self.logger.error(f"Error fetching {data_type} for {symbol} from {api_name}: {e}")
                self._record_api_failure(api_name)
        
        # All APIs failed
        self.logger.error(f"All APIs failed for {data_type} request: {symbol}")
        return None
    
    async def _fetch_from_api(self, api_name: str, symbol: str, data_type: str) -> Optional[Dict[str, Any]]:
        """
        Fetch data from a specific API
        """
        try:
            if api_name == 'tokenmetrics':
                return await self._fetch_from_tokenmetrics(symbol, data_type)
            elif api_name == 'coingecko':
                return await self._fetch_from_coingecko(symbol, data_type)
            else:
                self.logger.error(f"Unknown API: {api_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error in _fetch_from_api for {api_name}: {e}")
            return None
    
    async def _fetch_from_tokenmetrics(self, symbol: str, data_type: str) -> Optional[Dict[str, Any]]:
        """
        Fetch data from TokenMetrics API using actual available methods
        """
        try:
            # Convert symbol format for TokenMetrics
            base_symbol = symbol.replace("-USDT", "").replace("-USD", "").upper()
            
            if data_type == 'token_data':
                # Get token information from TokenMetrics
                token_data = self.tokenmetrics.get_tokens(base_symbol)
                if token_data.get('success') and token_data.get('data'):
                    token_info = token_data['data'][0]
                    return {
                        'symbol': symbol,
                        'current_price': token_info.get('CURRENT_PRICE'),
                        'market_cap': token_info.get('MARKET_CAP'),
                        'volume_24h': token_info.get('VOLUME_24H'),
                        'price_change_24h': token_info.get('PRICE_CHANGE_24H'),
                        'market_cap_rank': token_info.get('MARKET_CAP_RANK'),
                        'token_name': token_info.get('TOKEN_NAME'),
                        'source': 'tokenmetrics',
                        'timestamp': datetime.now().isoformat()
                    }
            
            elif data_type == 'market_data':
                # Get comprehensive analysis from TokenMetrics
                analysis = self.tokenmetrics.get_comprehensive_analysis(base_symbol)
                if analysis.get('available'):
                    return {
                        'symbol': symbol,
                        'analysis': analysis,
                        'ai_recommendation': analysis.get('ai_analysis', {}),
                        'technical_analysis': analysis.get('technical_analysis', {}),
                        'combined_signal': analysis.get('combined_signal'),
                        'confidence': analysis.get('confidence'),
                        'source': 'tokenmetrics',
                        'timestamp': datetime.now().isoformat()
                    }
            
            elif data_type == 'price_data':
                # Get AI reports which include price analysis
                ai_reports = self.tokenmetrics.get_ai_reports(base_symbol)
                if ai_reports.get('success') and ai_reports.get('data'):
                    # Filter for our symbol
                    for report in ai_reports['data']:
                        if report.get('TOKEN_SYMBOL', '').upper() == base_symbol:
                            return {
                                'symbol': symbol,
                                'price': report.get('CURRENT_PRICE'),
                                'recommendation': report.get('recommendation'),
                                'confidence': report.get('confidence_score'),
                                'price_target': report.get('price_target'),
                                'source': 'tokenmetrics',
                                'timestamp': datetime.now().isoformat()
                            }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching from TokenMetrics for {symbol}: {e}")
            return None
    async def _fetch_from_coingecko(self, symbol: str, data_type: str) -> Optional[Dict[str, Any]]:
        """
        Fetch data from CoinGecko API
        """
        try:
            if data_type in ['token_data', 'price_data', 'market_data']:
                # Use enhanced CoinGecko integration
                data = coingecko_enhanced.get_comprehensive_token_data(symbol)
                if data:
                    return {
                        **data,
                        'source': 'coingecko',
                        'timestamp': datetime.now().isoformat()
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching from CoinGecko for {symbol}: {e}")
            return None
    
    def _record_api_failure(self, api_name: str):
        """
        Record API failure for statistics
        """
        if api_name == 'coingecko':
            self.fallback_stats['coingecko_failures'] += 1
        elif api_name == 'tokenmetrics':
            self.fallback_stats['tokenmetrics_failures'] += 1
    
    async def batch_get_token_data_with_fallback(self, symbols: List[str], data_type: str = 'token_data') -> Dict[str, Dict[str, Any]]:
        """
        Get token data for multiple symbols with intelligent fallback
        """
        results = {}
        
        # Process symbols concurrently with fallback
        tasks = []
        for symbol in symbols:
            task = self.get_token_data_with_fallback(symbol, data_type)
            tasks.append((symbol, task))
        
        # Execute all tasks concurrently
        for symbol, task in tasks:
            try:
                data = await task
                if data:
                    results[symbol] = data
                else:
                    self.logger.warning(f"No data available for {symbol} from any API")
            except Exception as e:
                self.logger.error(f"Error processing {symbol}: {e}")
        
        return results
    
    def get_fallback_stats(self) -> Dict[str, Any]:
        """
        Get statistics about API fallback usage
        """
        total_failures = self.fallback_stats['coingecko_failures'] + self.fallback_stats['tokenmetrics_failures']
        success_rate = 0
        if self.fallback_stats['total_requests'] > 0:
            success_rate = ((self.fallback_stats['total_requests'] - total_failures) / 
                          self.fallback_stats['total_requests']) * 100
        
        return {
            **self.fallback_stats,
            'total_failures': total_failures,
            'success_rate': success_rate,
            'tokenmetrics_enhanced_limits': self.enhanced_limits['tokenmetrics']
        }
    
    def reset_stats(self):
        """Reset fallback statistics"""
        self.fallback_stats = {
            'coingecko_failures': 0,
            'tokenmetrics_failures': 0,
            'successful_fallbacks': 0,
            'total_requests': 0
        }

# Global instance
api_fallback_manager = APIFallbackManager()
