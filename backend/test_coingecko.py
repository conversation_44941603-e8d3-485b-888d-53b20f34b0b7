#!/usr/bin/env python3
"""
Test CoinGecko MCP Integration
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append('.')

def test_imports():
    """Test basic imports"""
    print("🔍 Testing imports...")
    
    try:
        from coingecko_mcp_client import coingecko_client
        print("✅ coingecko_mcp_client imported")
        
        from coingecko_integration import coingecko_integration
        print("✅ coingecko_integration imported")
        
        from coingecko_usage_monitor import coingecko_usage_monitor
        print("✅ coingecko_usage_monitor imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_functionality():
    """Test basic CoinGecko functionality"""
    print("🧪 Testing basic functionality...")
    
    try:
        from coingecko_mcp_client import coingecko_client
        from coingecko_integration import coingecko_integration
        
        # Test usage stats
        usage_stats = coingecko_client.get_usage_stats()
        print(f"📊 Monthly calls: {usage_stats['monthly_calls']}/{usage_stats['monthly_limit']}")
        print(f"📈 Usage: {usage_stats['usage_percentage']:.1f}%")
        
        # Test integration summary
        usage_summary = coingecko_integration.get_usage_summary()
        print(f"📈 Status: {usage_summary['status']}")
        print(f"💰 Cost: ${usage_summary['estimated_monthly_cost']:.2f}")
        
        print("✅ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 CoinGecko MCP Integration Tests")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        print("❌ Import tests failed")
        sys.exit(1)
    
    print("\n" + "=" * 40)
    
    # Test basic functionality
    success = asyncio.run(test_basic_functionality())
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 CoinGecko integration tests passed!")
    else:
        print("❌ Tests failed")
        sys.exit(1)
