import logging
from typing import Dict, List, Optional, Any
from indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
    calculate_volume_oscillator,
    calculate_stochastic_oscillator,
    calculate_ichimoku_cloud,
    calculate_fibonacci_retracement,
    calculate_parabolic_sar,
    calculate_atr
)

logger = logging.getLogger(__name__)

def evaluate_trend_following(prices: list, short_period: int, long_period: int) -> str:
    """
    Evaluates Trend Following strategy based on MA crossover.
    """
    short_ma = calculate_sma(prices, short_period)
    long_ma = calculate_sma(prices, long_period)

    if short_ma > long_ma:
        return "BUY"
    elif short_ma < long_ma:
        return "SELL"
    return "HOLD"

def evaluate_breakout_trading(prices: list, volume_data: list, breakout_period: int) -> str:
    """
    Evaluates Breakout Trading strategy.
    """
    if len(prices) < breakout_period or len(volume_data) < breakout_period:
        return "HOLD"

    current_price = prices[-1]
    highest_high = max(prices[-breakout_period:])
    lowest_low = min(prices[-breakout_period:])
    
    # Simple volume confirmation: check if current volume is higher than average
    avg_volume = sum(volume_data[-breakout_period:]) / breakout_period
    current_volume = volume_data[-1]

    if current_price > highest_high and current_volume > avg_volume * 1.2: # 20% higher volume
        return "BUY"
    elif current_price < lowest_low and current_volume > avg_volume * 1.2:
        return "SELL"
    return "HOLD"

def evaluate_scalping(prices: list, volume_data: list) -> str:
    """
    Evaluates a basic Scalping strategy.
    """
    if len(prices) < 2 or len(volume_data) < 2:
        return "HOLD"

    price_change = (prices[-1] - prices[-2]) / prices[-2]
    volume_change = (volume_data[-1] - volume_data[-2]) / volume_data[-2]

    if price_change > 0.0005 and volume_change > 0.1: # 0.05% price increase with 10% volume increase
        return "BUY"
    elif price_change < -0.0005 and volume_change > 0.1:
        return "SELL"
    return "HOLD"

def evaluate_swing_trading(prices: list, high_prices: list, low_prices: list, rsi_period: int, macd_fast: int, macd_slow: int, macd_signal: int) -> str:
    """
    Evaluates Swing Trading strategy using RSI and MACD.
    """
    rsi = calculate_rsi(prices, rsi_period)
    macd_line, signal_line = calculate_macd(prices, macd_fast, macd_slow, macd_signal)

    rsi_signal = "HOLD"
    if rsi < 30:
        rsi_signal = "BUY"
    elif rsi > 70:
        rsi_signal = "SELL"

    macd_signal_val = "HOLD"
    if macd_line > signal_line:
        macd_signal_val = "BUY"
    elif macd_line < signal_line:
        macd_signal_val = "SELL"

    if rsi_signal == "BUY" and macd_signal_val == "BUY":
        return "BUY"
    elif rsi_signal == "SELL" and macd_signal_val == "SELL":
        return "SELL"
    return "HOLD"

def evaluate_mean_reversion(prices: list, bb_period: int, bb_std_dev: int) -> str:
    """
    Evaluates Mean Reversion strategy using Bollinger Bands.
    """
    middle_band, upper_band, lower_band = calculate_bollinger_bands(prices, bb_period, bb_std_dev)
    current_price = prices[-1]

    if current_price < lower_band:
        return "BUY"
    elif current_price > upper_band:
        return "SELL"
    return "HOLD"

def evaluate_news_based_trading(sentiment_score: float, news_keywords: List[str]) -> str:
    """
    Evaluates News-Based Trading strategy. (Simplified)
    """
    positive_keywords = ["partnership", "listing", "acquisition", "breakthrough"]
    negative_keywords = ["hack", "scam", "regulation", "ban"]

    news_text = " ".join(news_keywords).lower()

    if sentiment_score > 0.6 and any(kw in news_text for kw in positive_keywords):
        return "BUY"
    elif sentiment_score < 0.4 and any(kw in news_text for kw in negative_keywords):
        return "SELL"
    return "HOLD"

def evaluate_volume_spike_trading(volumes: list, prices: list, period: int) -> str:
    """
    Evaluates Volume Spike Trading strategy.
    """
    if len(volumes) < period or len(prices) < period:
        return "HOLD"

    current_volume = volumes[-1]
    avg_volume = sum(volumes[-period:-1]) / (period - 1) if period > 1 else volumes[-1]
    price_change = (prices[-1] - prices[-2]) / prices[-2] if len(prices) > 1 else 0

    if current_volume > avg_volume * 2 and price_change > 0.01: # 2x volume spike with 1% price increase
        return "BUY"
    elif current_volume > avg_volume * 2 and price_change < -0.01:
        return "SELL"
    return "HOLD"

def evaluate_grid_trading(current_price: float, grid_levels: List[float]) -> str:
    """
    Evaluates Grid Trading strategy. (Simplified)
    """
    # This is a very simplified grid. A real grid bot manages multiple orders.
    # Here, we just check if the current price is near a buy or sell grid line.
    for i in range(len(grid_levels)):
        if i % 2 == 0: # Even index for buy lines
            buy_line = grid_levels[i]
            if current_price <= buy_line and current_price > buy_line * 0.995: # Within 0.5% of buy line
                return "BUY"
        else: # Odd index for sell lines
            sell_line = grid_levels[i]
            if current_price >= sell_line and current_price < sell_line * 1.005: # Within 0.5% of sell line
                return "SELL"
    return "HOLD"

def evaluate_event_driven_signals(events: List[Dict[str, Any]], target_symbol: str) -> str:
    """
    Evaluates event-driven signals based on upcoming events.
    """
    for event in events:
        # Assuming event['symbol'] is the token symbol (e.g., 'BTC')
        # and event['date_event'] is the date of the event.
        if event.get('symbol', '').upper() == target_symbol.upper():
            # For simplicity, consider any relevant upcoming event as a potential buy signal
            # This logic can be expanded to consider event type, sentiment around the event, etc.
            return "BUY" # Or a more nuanced signal based on event type
    return "HOLD"