from fastapi.responses import JSONResponse
from fastapi import status

# ✅ Centralized list of known error codes
ERROR_CODES = {
    "TOKEN_NOT_FOUND": "The token symbol was not found in the database.",
    "TRADE_EXECUTION_FAILED": "Trade execution failed due to exchange API error.",
    "INVALID_PARAMETERS": "One or more parameters are invalid.",
    "RATE_LIMIT_EXCEEDED": "Rate limit exceeded. Please try again later.",
    "SERVICE_UNAVAILABLE": "The service is temporarily unavailable.",
    "UNKNOWN_ERROR": "An unknown error has occurred.",
    "NEWS_SAVE_FAILED": "Failed to save news.",
    "NEWS_NOT_FOUND": "News file not found.",
    "INVALID_NEWS_DATA": "Invalid news data format.",
    "NEWS_READ_FAILED": "Failed to read news.",
    "SPIKE_TOKENS_FETCH_FAILED": "Failed to fetch spike tokens.",
    "NEWLY_LISTED_ERROR": "Failed to fetch newly listed tokens.",
    "DISCOVER_TOKEN_ERROR": "Failed to fetch discover tokens.",
    "SOURCE_NOT_FOUND": "News source not found.",
    "INVALID_WEIGHT": "Weight must be between 0 and 1.",
    "NO_LOGIC_DATA": "No AI logic file found.",
    "INVALID_JSON": "Invalid JSON format.",
    "LOGIC_LOAD_FAILED": "Failed to load AI logic.",
    "ANALYTICS_FAILED": "Failed to generate analytics.",
    "REALTIME_ANALYTICS_FAILED": "Failed to generate real-time analytics.",
    "PNL_DATA_FAILED": "Failed to fetch PnL data.",
    "AI_DECISIONS_FETCH_FAILED": "Failed to fetch AI decisions.",
    "SENTIMENT_FEED_FETCH_FAILED": "Failed to fetch sentiment feed.",
    "HEALTH_CHECK_FAILED": "Health check failed.",
    "MICRO_BOT_START_FAILED": "Failed to start micro-bot.",
    "MICRO_BOT_STOP_FAILED": "Failed to stop micro-bot.",
    "MICRO_BOT_STATUS_FAILED": "Failed to get micro-bot status.",
    "ARBITRAGE_SUGGESTIONS_FAILED": "Failed to get arbitrage suggestions.",
    "ALPHA_BOT_START_FAILED": "Failed to start alpha-bot.",
    "ALPHA_BOT_STOP_FAILED": "Failed to stop alpha-bot.",
    "ALPHA_BOT_STATUS_FAILED": "Failed to get alpha-bot status.",
    "AI_DECISION_LOG_FAILED": "Failed to log AI decision.",
    "AI_LOGIC_LOAD_FAILED": "Failed to load AI logic configuration.",
    "AI_LOGIC_WRITE_FAILED": "Failed to write AI logic entry.",
    "ANALYTICS_GENERATION_FAILED": "Failed to generate analytics.",
    "ANALYTICS_FILE_NOT_FOUND": "Analytics file not found.",
    "ANALYTICS_FILE_CORRUPTED": "Analytics file is corrupted.",
    "ANALYTICS_LOAD_FAILED": "Failed to load analytics data.",
    "ARBITRAGE_TOKEN_LIST_FAILED": "Failed to get top tokens for arbitrage.",
    "INSUFFICIENT_CAPITAL": "Insufficient capital for trade.",
    "INSUFFICIENT_ASSETS": "Insufficient assets in portfolio.",
    "UNKNOWN_TRADE_ACTION": "Unknown trade action.",
    "TRADE_ENGINE_RUN_FAILED": "Failed to run trade engine.",
    "KUCOIN_CANDLES_FETCH_FAILED": "Failed to fetch KuCoin candlestick data.",
    "BINANCE_EXCHANGE_INFO_FAILED": "Failed to fetch Binance exchange information.",
    "BINANCE_KLINES_FAILED": "Failed to fetch Binance klines data.",
    "BINANCE_TICKER_FAILED": "Failed to fetch Binance ticker data.",
    "COINGECKO_FETCH_FAILED": "Failed to fetch CoinGecko data.",
    "COINMARKETCAL_API_KEY_MISSING": "CoinMarketCal API key not provided.",
    "COINMARKETCAL_FETCH_FAILED": "Failed to fetch events from CoinMarketCal.",
    "CHART_ANALYSIS_FAILED": "Failed to analyze chart data.",
    "CHART_DATA_NOT_AVAILABLE": "Chart data not available.",
    "UNAUTHORIZED_EMAIL": "Unauthorized email address.",
    "OAUTH_ERROR": "OAuth authentication failed.",
    "NO_TRADE_LOGS": "Trade log file not found.",
    "EMPTY_TRADES": "No trade data found.",
    "INVALID_TRADE_SIDE": "Invalid trade side. Must be 'buy' or 'sell'.",
    "TOP_TOKENS_FILE_NOT_FOUND": "Top tokens file not found.",
    "INVALID_TOP_TOKENS_JSON": "Invalid JSON in top tokens file.",
    "LOAD_TOP_TOKENS_FAILED": "Failed to load top tokens.",
    "STRATEGY_TRADE_EXECUTION_FAILED": "Strategy trade execution failed."
}

from typing import Optional

from typing import Dict, Any, Optional
from fastapi import status

def error_response(code: str, message: Optional[str] = None, status_code: int = status.HTTP_400_BAD_REQUEST) -> Dict[str, Any]:
    """
    Returns a standardized error response dictionary.
    
    Args:
        code (str): A key from ERROR_CODES or a custom error code.
        message (str, optional): A custom error message. Defaults to ERROR_CODES[code] or generic.
        status_code (int, optional): HTTP status code. Defaults to 400.
    
    Returns:
        Dict[str, Any]: A dictionary containing error details.
    """
    default_message = ERROR_CODES.get(code, "An error occurred.")
    return {
        "error_code": code,
        "error_message": message or default_message,
        "status_code": status_code
    }