

from datetime import datetime
from typing import Optional

# Real-time metrics data structure
real_time_metrics = {
    "trade_counts": {
        "total": 0,
        "daily": {},     # date string: count
        "hourly": {}     # datetime hour string: count
    },
    "win_loss": {
        "wins": 0,
        "losses": 0,
        "win_percent": 0.0,
        "loss_percent": 0.0
    },
    "profit_loss": {
        "total_profit": 0.0,
        "total_loss": 0.0,
        "avg_profit": 0.0,
        "avg_loss": 0.0,
        "largest_win": 0.0,
        "largest_loss": 0.0
    },
    "portfolio": {
        "current_value": 0.0,
        "last_value": 0.0,
        "change": 0.0
    },
    "ai_decisions": {
        "buy": 0,
        "sell": 0,
        "hold": 0
    },
    "api_calls": {
        "deepseek": {"success": 0, "failures": 0},
        "openai": {"success": 0, "failures": 0},
        "gemini": {"success": 0, "failures": 0}
    },
    "performance": {
        "avg_latency": 0.0,
        "calls_count": 0
    },
    "per_token": {},  # token_symbol: metrics
    "per_strategy": {},  # strategy_name: metrics
}

def update_trade_counts(metrics: dict, trade_timestamp: Optional[datetime] = None):
    metrics["trade_counts"]["total"] += 1
    now = trade_timestamp or datetime.utcnow()
    date_str = now.strftime("%Y-%m-%d")
    hour_str = now.strftime("%Y-%m-%d %H:00")
    metrics["trade_counts"]["daily"][date_str] = metrics["trade_counts"]["daily"].get(date_str, 0) + 1
    metrics["trade_counts"]["hourly"][hour_str] = metrics["trade_counts"]["hourly"].get(hour_str, 0) + 1

def update_profit_loss(metrics: dict, pnl: float):
    if pnl > 0:
        metrics["win_loss"]["wins"] += 1
        metrics["profit_loss"]["total_profit"] += pnl
        if pnl > metrics["profit_loss"]["largest_win"]:
            metrics["profit_loss"]["largest_win"] = pnl
    else:
        metrics["win_loss"]["losses"] += 1
        loss = abs(pnl)
        metrics["profit_loss"]["total_loss"] += loss
        if loss > metrics["profit_loss"]["largest_loss"]:
            metrics["profit_loss"]["largest_loss"] = loss

    total_trades = metrics["win_loss"]["wins"] + metrics["win_loss"]["losses"]
    if total_trades > 0:
        metrics["win_loss"]["win_percent"] = (metrics["win_loss"]["wins"] / total_trades) * 100
        metrics["win_loss"]["loss_percent"] = (metrics["win_loss"]["losses"] / total_trades) * 100
        metrics["profit_loss"]["avg_profit"] = (
            metrics["profit_loss"]["total_profit"] / metrics["win_loss"]["wins"]
            if metrics["win_loss"]["wins"] > 0 else 0
        )
        metrics["profit_loss"]["avg_loss"] = (
            metrics["profit_loss"]["total_loss"] / metrics["win_loss"]["losses"]
            if metrics["win_loss"]["losses"] > 0 else 0
        )

def update_portfolio_value(metrics: dict, current_value: float):
    last_value = metrics["portfolio"]["current_value"]
    metrics["portfolio"]["last_value"] = last_value
    metrics["portfolio"]["current_value"] = current_value
    metrics["portfolio"]["change"] = current_value - last_value

def update_ai_decisions(metrics: dict, decision: str):
    decision_lower = decision.lower()
    if decision_lower in metrics["ai_decisions"]:
        metrics["ai_decisions"][decision_lower] += 1

def update_api_calls(metrics: dict, api_name: str, success: bool = True):
    if api_name in metrics["api_calls"]:
        if success:
            metrics["api_calls"][api_name]["success"] += 1
        else:
            metrics["api_calls"][api_name]["failures"] += 1

def update_performance(metrics: dict, latency_seconds: float):
    metrics["performance"]["calls_count"] += 1
    count = metrics["performance"]["calls_count"]
    current_avg = metrics["performance"]["avg_latency"]
    metrics["performance"]["avg_latency"] = ((current_avg * (count - 1)) + latency_seconds) / count


# --- Per-token and per-strategy metrics helpers ---
def update_token_metrics(metrics: dict, token_symbol: str, pnl: float, decision: str):
    token = token_symbol.upper()
    if token not in metrics["per_token"]:
        metrics["per_token"][token] = {
            "trades": 0,
            "profit": 0.0,
            "loss": 0.0,
            "wins": 0,
            "losses": 0,
            "buy": 0,
            "sell": 0,
            "hold": 0
        }
    m = metrics["per_token"][token]
    m["trades"] += 1
    if pnl > 0:
        m["profit"] += pnl
        m["wins"] += 1
    else:
        m["loss"] += abs(pnl)
        m["losses"] += 1
    if decision.lower() in m:
        m[decision.lower()] += 1

def update_strategy_metrics(metrics: dict, strategy_name: str, pnl: float):
    strategy = strategy_name.lower()
    if strategy not in metrics["per_strategy"]:
        metrics["per_strategy"][strategy] = {
            "trades": 0,
            "profit": 0.0,
            "loss": 0.0,
            "wins": 0,
            "losses": 0
        }
    s = metrics["per_strategy"][strategy]
    s["trades"] += 1
    if pnl > 0:
        s["profit"] += pnl
        s["wins"] += 1
    else:
        s["loss"] += abs(pnl)
        s["losses"] += 1