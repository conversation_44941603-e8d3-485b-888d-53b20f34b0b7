"""
AlphaPredatorBot — Telegram Callback Listener

Listens for inline button clicks on Telegram messages and handles responses
like 👍 Yes, 👎 No, and 🚀 Trade Now. Optionally connects to trade execution engine.
"""

import os
import logging
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CallbackQueryHandler, ContextTypes
from telegram.error import BadRequest

# ✅ Load environment variables from .env file
load_dotenv()

TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")

# Enable logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)

# Callback function for inline buttons
async def handle_button(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    if query is None:
        logger.warning("[Telegram] No callback_query found in update.")
        return
    try:
        await query.answer()
    except BadRequest as e:
        logger.warning(f"[Telegram] Button expired or invalid: {e}")
        return

    data = query.data
    user = update.effective_user.full_name if update.effective_user else "User"
    logger.info(f"[Telegram] Button pressed: {data} by {user}")

    if data == "yes":
        await query.edit_message_text(f"👍 {user} liked the signal!")
    elif data == "no":
        await query.edit_message_text(f"👎 {user} passed on this signal.")
    elif data == "trade_now":
        await query.edit_message_text(f"🚀 Executing trade... (simulated)")
        # TODO: Call execute_trade() here if live mode is enabled

if __name__ == "__main__":
    if not TELEGRAM_BOT_TOKEN:
        raise EnvironmentError("TELEGRAM_BOT_TOKEN is not set in environment variables.")

    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()

    # Add handler for button callbacks
    app.add_handler(CallbackQueryHandler(handle_button))

    print("✅ Telegram listener started. Waiting for button clicks...")
    app.run_polling()