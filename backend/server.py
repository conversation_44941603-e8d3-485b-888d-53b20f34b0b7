#!/usr/bin/env python3
"""
Simple FastAPI server startup script
"""

import uvicorn
import os
import sys

# Suppress the optimization report during startup
os.environ['SUPPRESS_OPTIMIZATION_REPORT'] = '1'

if __name__ == "__main__":
    # Get port from environment or default to 3005
    port = int(os.getenv("PORT", 3005))
    host = os.getenv("HOST", "0.0.0.0")
    
    print(f"🚀 Starting Alpha Predator Bot API server on {host}:{port}")
    print(f"📡 Frontend should connect to: http://{host}:{port}")
    
    try:
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info",
            access_log=True
        )
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        sys.exit(1)
