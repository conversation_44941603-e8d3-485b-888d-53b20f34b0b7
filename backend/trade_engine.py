import sys
import os
import logging
from typing import Dict, Any, Optional

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

import time
from datetime import datetime

import json

LIVE_TRADE_LOG = "data/live_trades.json"

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def log_live_trade(trade: Dict[str, Any]) -> None:
    os.makedirs("data", exist_ok=True)
    trade["time"] = datetime.utcnow().isoformat()

    # Ensure the file exists and is a valid JSON array
    if not os.path.exists(LIVE_TRADE_LOG) or os.path.getsize(LIVE_TRADE_LOG) == 0:
        logs = []
    else:
        try:
            with open(LIVE_TRADE_LOG, "r") as f:
                logs = json.load(f)
            if not isinstance(logs, list):
                logs = []  # Reset if not a list
        except json.JSONDecodeError:
            logs = []  # Reset if corrupted

    logs.insert(0, trade)
    logs = logs[:100]  # keep only the latest 100 trades

    with open(LIVE_TRADE_LOG, "w") as f:
        json.dump(logs, f, indent=2)


from config import (
    TRADE_INTERVAL_SECONDS,
    MAX_BUY,
    STOP_LOSS,
    TAKE_PROFIT,
    TELEGRAM_BOT_TOKEN,
    KUCOIN_API_KEY,
    KUCOIN_API_SECRET,
    KUCOIN_API_PASSPHRASE,
    DEFAULT_TRADE_USD,
    STOP_LOSS_PCT,
    MAX_TRADE_RISK_PCT,
    TRADING_MODE,
)
from token_selector import generate_top_token_list
from price_fetcher import get_price
from trade_logger import (
    log_trade,
    save_portfolio_json,
    load_portfolio_json,
)
from kucoin_api import KuCoinAPI  # Import KuCoinAPI

from trade_decision_engine import get_trade_decision


def should_buy(symbol: str, price: float) -> bool:
    decision = get_trade_decision(symbol)
    return decision == "BUY"


from telegram_utils import AppType, notify_trade, notify_pnl_summary
from utils.token_utils import format_token_name
import asyncio

# Initialize KuCoin API client
kucoin_api_client = KuCoinAPI()

# === Runtime Validation of Env Config ===
required_keys = [
    TELEGRAM_BOT_TOKEN,
    KUCOIN_API_KEY,
    KUCOIN_API_SECRET,
    KUCOIN_API_PASSPHRASE,
]
if TRADING_MODE == "LIVE" and not all(required_keys):
    raise EnvironmentError(
        "❌ Missing one or more critical environment variables for LIVE trading."
    )

state: Dict[str, Any] = {"portfolio": {}, "balance": 0.0}


# === Initialize tokens and portfolio ===
def refresh_top_tokens() -> list[str]:
    return [t["symbol"] for t in generate_top_token_list()]


state["portfolio"], state["balance"] = load_portfolio_json()
watched_symbols = refresh_top_tokens()
logger.info(f"[WATCHING] Tokens under observation: {watched_symbols}")


def should_sell(symbol: str, current_price: float) -> bool:
    if TRADING_MODE == "LIVE":
        portfolio, _ = load_portfolio_json()
        if symbol not in portfolio:
            return False

        held_token = portfolio[symbol]
        buy_price = held_token.get("avg_price")
        quantity = held_token.get("qty")

        if buy_price is None or quantity is None or quantity <= 0:
            logger.warning(
                f"No valid buy price or quantity found for {symbol} in portfolio."
            )
            return False

        change = (current_price - buy_price) / buy_price

        if change <= -STOP_LOSS:
            logger.info(
                f"[LIVE SELL] {symbol}: STOP LOSS triggered ({change*100:.2f}%)"
            )
            return True
        elif change >= TAKE_PROFIT:
            logger.info(
                f"[LIVE SELL] {symbol}: TAKE PROFIT triggered ({change*100:.2f}%)"
            )
            return True
        return False
    # Default return value for non-LIVE mode or any other code path
    return False


async def send_trade_notification(
    application, symbol: str, price: float, side: str, amount: float, reason: str
) -> None:
    try:
        await notify_trade(
            application=application,  # Pass the application instance
            token=format_token_name(symbol),
            price=price,
            action=side,
            quantity=amount,
            strategy="Default",  # or provide the actual strategy name if available
            reason=reason,
        )
    except Exception as e:
        logger.warning(f"Telegram notification failed: {e}")


def run_trade_engine() -> None:
    """
    Main entry point for the trade engine. This function orchestrates the trading workflow.
    Called by bot_scheduler.py to execute the trading logic.
    """
    try:
        logger.info("🚀 Starting trade engine execution...")

        # Refresh the list of tokens to watch
        global watched_symbols
        watched_symbols = refresh_top_tokens()
        logger.info(f"[WATCHING] Updated token list: {watched_symbols}")

        # Process each watched symbol
        for symbol in watched_symbols:
            try:
                # Get current price
                try:
                    current_price = asyncio.run(get_price(symbol))
                except Exception as e:
                    logger.error(f"Failed to get price for {symbol}: {e}")
                    current_price = None
                if not current_price or current_price <= 0:
                    logger.warning(
                        f"[PRICE] Invalid price for {symbol}: {current_price}"
                    )
                    continue

                # Check if we should buy this token
                if should_buy(symbol, current_price):
                    logger.info(f"[BUY SIGNAL] {symbol} at ${current_price:.6f}")
                    trade_result = trade_token(symbol, "buy", current_price, 0)
                    if trade_result.get("status") in ["executed", "executed_live"]:
                        state["portfolio"][symbol] = {
                            "qty": trade_result.get("quantity", 0),
                            "avg_price": current_price,
                        }
                        save_portfolio_json(state["portfolio"], state["balance"])

                # Check if we should sell tokens we're holding
                elif symbol in state["portfolio"] and should_sell(
                    symbol, current_price
                ):
                    logger.info(f"[SELL SIGNAL] {symbol} at ${current_price:.6f}")
                    trade_result = trade_token(symbol, "sell", current_price, 0)
                    if trade_result.get("status") in ["executed", "executed_live"]:
                        # Remove from portfolio or update quantity
                        if symbol in state["portfolio"]:
                            del state["portfolio"][symbol]
                        save_portfolio_json(state["portfolio"], state["balance"])

            except Exception as e:
                logger.error(f"[ERROR] Processing {symbol}: {e}", exc_info=True)
                continue

        logger.info("✅ Trade engine execution completed")

    except Exception as e:
        logger.error(f"❌ Trade engine execution failed: {e}", exc_info=True)
        raise


def trade_token(
    symbol: str, action: str, price: float, amount: float
) -> Dict[str, Any]:
    """
    Executes a trade (real or simulated) and logs it appropriately.
    """
    if action.lower() not in ["buy", "sell", "hold"]:
        raise ValueError("Invalid action. Must be 'buy', 'sell', or 'hold'.")

    if action.lower() == "hold":
        logger.info(f"[TRADE] HOLD {symbol}")
        return {"symbol": symbol, "action": "HOLD", "status": "executed"}

    trade_usd_amount = DEFAULT_TRADE_USD  # Base trade amount
    if price <= 0:
        logger.warning(
            f"Cannot calculate trade amount for {symbol} due to zero or negative price."
        )
        return {
            "symbol": symbol,
            "action": action.upper(),
            "status": "failed",
            "reason": "Invalid price",
        }

    # Calculate actual amount based on USD value
    amount_to_trade = trade_usd_amount / price

    trade_status = "simulated"
    trade_response = {}
    profit = 0.0

    if TRADING_MODE == "LIVE":
        logger.info(
            f"[LIVE TRADE] Attempting to {action.upper()} {amount_to_trade:.4f} {symbol} at ${price:.6f} on KuCoin..."
        )
        try:
            # KuCoin API place_order expects size in base currency
            # For a BUY, amount_to_trade is the quantity of the token (e.g., BTC for BTC-USDT)
            # For a SELL, amount_to_trade is the quantity of the token you hold
            trade_response = kucoin_api_client.place_order(
                symbol, action.lower(), price, amount_to_trade
            )
            if trade_response and trade_response.get("orderId"):
                trade_status = "executed_live"
                logger.info(
                    f"[LIVE TRADE] Order placed successfully: {trade_response.get('orderId')}"
                )
            else:
                trade_status = "failed_live"
                logger.error(f"[LIVE TRADE] Failed to place order: {trade_response}")
        except Exception as e:
            trade_status = "failed_live"
            logger.error(f"[LIVE TRADE] Exception placing order: {e}", exc_info=True)

    trade_log_data = {
        "symbol": symbol,
        "price": price,
        "quantity": amount_to_trade,
        "action": action.lower(),
        "timestamp": datetime.utcnow().isoformat(),
        "status": trade_status,
        "profit": profit if action.lower() == "sell" else 0.0,
        "trade_usd_amount": trade_usd_amount,
        "kucoin_response": trade_response,  # Log the raw response from KuCoin API
    }

    log_trade(symbol, action.upper(), amount_to_trade, price, reason=trade_status)
    # log_live_trade is now handled by Firestore in trade_logger.py

    logger.info(
        f"[TRADE] {action.upper()} {symbol} @ ${price:.6f} x {amount_to_trade:.4f} (USD: ${trade_usd_amount:.2f}) Status: {trade_status}"
    )
    return trade_log_data


async def execute_trade_with_strategy(
    symbol: str,
    action: str,
    amount: float,
    take_profit: float = 0.0,
    stop_loss: float = 0.0,
    strategy: str = "alpha_predator",
    metadata: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    🎯 ALPHA PREDATOR: Execute trade with comprehensive strategy and TP/SL

    Args:
        symbol: Trading pair symbol (e.g., 'BTC-USDT')
        action: 'BUY' or 'SELL'
        amount: USD amount to trade
        take_profit: Take profit price level
        stop_loss: Stop loss price level
        strategy: Strategy name for tracking
        metadata: Additional trade metadata

    Returns:
        Dict with trade execution results
    """
    try:
        logger.info(f"🎯 Alpha Predator executing {action} for {symbol}: ${amount}")

        # Get current price with fallback
        try:
            current_price = await get_price(symbol)
        except Exception as e:
            logger.warning(f"Price fetch failed for {symbol}: {e}")
            current_price = None

        # Use fallback price if needed (for testing)
        if not current_price or current_price <= 0:
            # Fallback prices for common symbols
            fallback_prices = {
                "BTC-USDT": 45000.0,
                "ETH-USDT": 2800.0,
                "BTC": 45000.0,
                "ETH": 2800.0,
            }
            current_price = fallback_prices.get(symbol, 100.0)  # Default $100
            logger.info(f"Using fallback price for {symbol}: ${current_price}")

        if current_price <= 0:
            return {
                "status": "failed",
                "error": f"Invalid price for {symbol}: {current_price}",
                "symbol": symbol,
                "action": action,
            }

        # Calculate quantity based on USD amount
        quantity = amount / current_price

        # Prepare trade metadata
        trade_metadata = {
            "strategy": strategy,
            "take_profit": take_profit,
            "stop_loss": stop_loss,
            "current_price": current_price,
            "timestamp": datetime.utcnow().isoformat(),
            **(metadata if metadata is not None else {}),
        }

        # Execute the trade
        logger.info(f"Trading mode: {TRADING_MODE}")

        if TRADING_MODE.upper() == "LIVE":
            # Live trading execution
            try:
                trade_response = kucoin_api_client.place_order(
                    symbol=symbol,
                    side=action.lower(),
                    price=current_price,
                    size=quantity,
                )

                if trade_response and trade_response.get("orderId"):
                    trade_id = trade_response.get("orderId")

                    # Log successful trade
                    log_trade(
                        token=symbol,
                        side=action,
                        amount=quantity,
                        price=current_price,
                        reason=f"Alpha Predator {strategy}",
                    )

                    logger.info(
                        f"✅ Live trade executed: {symbol} {action} ${amount} (ID: {trade_id})"
                    )

                    return {
                        "status": "success",
                        "trade_id": trade_id,
                        "symbol": symbol,
                        "action": action,
                        "quantity": quantity,
                        "price": current_price,
                        "amount_usd": amount,
                        "take_profit": take_profit,
                        "stop_loss": stop_loss,
                        "strategy": strategy,
                        "metadata": trade_metadata,
                    }
                else:
                    logger.error(f"❌ Live trade failed: {trade_response}")
                    return {
                        "status": "failed",
                        "error": f"KuCoin order failed: {trade_response}",
                        "symbol": symbol,
                        "action": action,
                    }

            except Exception as e:
                logger.error(f"❌ Live trade execution error: {e}")
                return {
                    "status": "failed",
                    "error": str(e),
                    "symbol": symbol,
                    "action": action,
                }
        else:
            # Simulation mode (fallback when LIVE mode fails)
            trade_id = f"sim_{int(time.time())}_{symbol}"

            # Log simulation trade
            log_trade(
                token=symbol,
                side=action,
                amount=quantity,
                price=current_price,
                reason=f"Alpha Predator {strategy} (Simulation)",
            )

            logger.info(f"🔄 Simulation trade logged: {symbol} {action} ${amount}")

            return {
                "status": "success",
                "trade_id": trade_id,
                "symbol": symbol,
                "action": action,
                "quantity": quantity,
                "price": current_price,
                "amount_usd": amount,
                "take_profit": take_profit,
                "stop_loss": stop_loss,
                "strategy": strategy,
                "metadata": trade_metadata,
                "mode": "simulation",
            }

    except Exception as e:
        logger.error(f"❌ Trade execution failed for {symbol}: {e}")
        return {"status": "failed", "error": str(e), "symbol": symbol, "action": action}
