"""
Optimized Data Pipeline - Production-grade data processing with enhanced performance,
intelligent caching, batch processing, and real-time monitoring.
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import concurrent.futures
import threading
import json
import os

from cache import get_cached_data, set_cached_data
from utils.logger import get_logger
from optimized_ai_core import optimized_ai_core, TokenAnalysis
from optimized_news_sentiment import optimized_news_analyzer, SentimentResult
from token_selector import generate_top_token_list
from kucoin_data import fetch_kucoin_spike_tokens
from utils.api_client import get

logger = get_logger("optimized_data_pipeline")

# Configuration constants
PIPELINE_CACHE_TTL = 180  # 3 minutes
BATCH_SIZE = 15
MAX_CONCURRENT_OPERATIONS = 8
PERFORMANCE_LOG_INTERVAL = 300  # 5 minutes

@dataclass
class PipelineResult:
    """Data class for pipeline processing results"""
    symbol: str
    ai_analysis: Optional[TokenAnalysis]
    sentiment_analysis: Optional[SentimentResult]
    market_data: Dict[str, Any]
    processing_time: float
    timestamp: float
    success: bool
    error_message: Optional[str] = None

@dataclass
class PipelineStats:
    """Data class for pipeline performance statistics"""
    total_processed: int
    successful: int
    failed: int
    avg_processing_time: float
    cache_hit_rate: float
    throughput_per_minute: float
    last_updated: float

class OptimizedDataPipeline:
    """
    Optimized data processing pipeline with:
    - Intelligent caching and batching
    - Concurrent processing with backpressure
    - Real-time performance monitoring
    - Circuit breaker pattern
    - Memory optimization
    - Error recovery
    """
    
    def __init__(self):
        self.cache_lock = threading.Lock()
        self.stats_lock = threading.Lock()
        self.performance_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "avg_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
            "start_time": time.time(),
            "last_performance_log": time.time()
        }
        self.circuit_breaker = {
            "failure_count": 0,
            "last_failure_time": 0,
            "threshold": 10,
            "timeout": 600  # 10 minutes
        }
        self.active_operations = 0
        self.max_operations = MAX_CONCURRENT_OPERATIONS
    
    def _is_circuit_open(self) -> bool:
        """Check if circuit breaker is open"""
        if self.circuit_breaker["failure_count"] >= self.circuit_breaker["threshold"]:
            if time.time() - self.circuit_breaker["last_failure_time"] < self.circuit_breaker["timeout"]:
                return True
            else:
                # Reset circuit breaker after timeout
                self.circuit_breaker["failure_count"] = 0
        return False
    
    def _record_failure(self):
        """Record a failure for circuit breaker"""
        self.circuit_breaker["failure_count"] += 1
        self.circuit_breaker["last_failure_time"] = int(time.time())
    
    def _record_success(self):
        """Record a success for circuit breaker"""
        self.circuit_breaker["failure_count"] = max(0, self.circuit_breaker["failure_count"] - 1)
    
    async def _fetch_market_data_cached(self, symbol: str) -> Dict[str, Any]:
        """Fetch market data with intelligent caching"""
        cache_key = f"market_data_{symbol}"
        
        with self.cache_lock:
            cached_data = get_cached_data(cache_key)
            if cached_data:
                self.performance_stats["cache_hits"] += 1
                return cached_data
            
            self.performance_stats["cache_misses"] += 1
        
        try:
            # Fetch basic market data
            from price_fetcher import get_price
            from volume_ratio import get_volume_ratio_kucoin
            from kucoin_data import get_24h_volume
            
            loop = asyncio.get_event_loop()
            
            # Fetch data concurrently
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                price_task = loop.run_in_executor(executor, get_price, symbol)
                volume_task = loop.run_in_executor(executor, get_24h_volume, symbol)
                
                price, volume = await asyncio.gather(
                    price_task, volume_task, return_exceptions=True
                )
            
            # Handle exceptions
            price = price if not isinstance(price, Exception) else 0.0
            volume = volume if not isinstance(volume, Exception) else 0.0

            # Calculate volume ratio only if volume is a valid float
            try:
                if isinstance(volume, (float, int)):
                    volume_ratio = await loop.run_in_executor(
                        executor, get_volume_ratio_kucoin, symbol, float(volume)
                    )
                else:
                    volume_ratio = 1.0
            except Exception:
                volume_ratio = 1.0
            
            market_data = {
                "symbol": symbol,
                "price": price,
                "volume_24h": volume,
                "volume_ratio": volume_ratio,
                "timestamp": time.time()
            }
            
            # Cache the results
            with self.cache_lock:
                set_cached_data(cache_key, market_data, PIPELINE_CACHE_TTL)
            
            return market_data
            
        except Exception as e:
            logger.error(f"Failed to fetch market data for {symbol}: {e}")
            return {
                "symbol": symbol,
                "price": 0.0,
                "volume_24h": 0.0,
                "volume_ratio": 1.0,
                "timestamp": time.time(),
                "error": str(e)
            }
    
    async def process_single_token(self, symbol: str) -> PipelineResult:
        """
        Process a single token through the complete pipeline
        """
        start_time = time.time()
        
        if self._is_circuit_open():
            logger.warning(f"Circuit breaker open, skipping {symbol}")
            return PipelineResult(
                symbol=symbol,
                ai_analysis=None,
                sentiment_analysis=None,
                market_data={},
                processing_time=0.0,
                timestamp=time.time(),
                success=False,
                error_message="Circuit breaker open"
            )
        
        # Check if we're at capacity
        while self.active_operations >= self.max_operations:
            await asyncio.sleep(0.1)
        
        self.active_operations += 1
        
        try:
            # Check cache first
            cache_key = f"pipeline_result_{symbol}"
            with self.cache_lock:
                cached_result = get_cached_data(cache_key)
                if cached_result:
                    self.performance_stats["cache_hits"] += 1
                    logger.debug(f"Cache hit for pipeline result: {symbol}")
                    return PipelineResult(**cached_result)
                
                self.performance_stats["cache_misses"] += 1
            
            # Process all components concurrently
            tasks = [
                optimized_ai_core.analyze_token_optimized(symbol),
                optimized_news_analyzer.analyze_symbol_sentiment(symbol),
                self._fetch_market_data_cached(symbol)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            ai_analysis = results[0] if not isinstance(results[0], BaseException) else None
            sentiment_analysis = results[1] if not isinstance(results[1], BaseException) else None
            market_data = results[2] if not isinstance(results[2], BaseException) else {}
            
            # Log any exceptions
            for i, result in enumerate(results):
                if isinstance(result, BaseException):
                    component_names = ["AI Analysis", "Sentiment Analysis", "Market Data"]
                    logger.warning(f"{component_names[i]} failed for {symbol}: {result}")
            
            processing_time = time.time() - start_time
            
            # Create result
            pipeline_result = PipelineResult(
                symbol=symbol,
                ai_analysis=ai_analysis,
                sentiment_analysis=sentiment_analysis,
                market_data=market_data,
                processing_time=processing_time,
                timestamp=time.time(),
                success=True
            )
            
            # Cache the result
            with self.cache_lock:
                set_cached_data(cache_key, asdict(pipeline_result), PIPELINE_CACHE_TTL)
            
            # Update stats
            with self.stats_lock:
                self.performance_stats["successful"] += 1
                self.performance_stats["total_processed"] += 1
                self.performance_stats["avg_processing_time"] = (
                    (self.performance_stats["avg_processing_time"] * (self.performance_stats["total_processed"] - 1) + 
                     processing_time) / self.performance_stats["total_processed"]
                )
            
            self._record_success()
            logger.info(f"Successfully processed {symbol} in {processing_time:.2f}s")
            
            return pipeline_result
            
        except Exception as e:
            logger.error(f"Pipeline processing failed for {symbol}: {e}")
            
            with self.stats_lock:
                self.performance_stats["failed"] += 1
                self.performance_stats["total_processed"] += 1
            
            self._record_failure()
            
            return PipelineResult(
                symbol=symbol,
                ai_analysis=None,
                sentiment_analysis=None,
                market_data={},
                processing_time=time.time() - start_time,
                timestamp=time.time(),
                success=False,
                error_message=str(e)
            )
        
        finally:
            self.active_operations -= 1
    
    async def batch_process_tokens(self, symbols: List[str]) -> List[PipelineResult]:
        """
        Batch process multiple tokens through the pipeline
        """
        logger.info(f"Starting batch pipeline processing for {len(symbols)} tokens")
        
        # Process in batches to control memory usage and API limits
        results = []
        
        for i in range(0, len(symbols), BATCH_SIZE):
            batch = symbols[i:i + BATCH_SIZE]
            logger.info(f"Processing pipeline batch {i//BATCH_SIZE + 1}: {batch}")
            
            # Process batch concurrently
            batch_tasks = [self.process_single_token(symbol) for symbol in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Filter out exceptions and add to results
            valid_results = [
                result for result in batch_results 
                if not isinstance(result, Exception)
            ]
            
            results.extend(valid_results)
            
            # Log performance periodically
            await self._log_performance_if_needed()
            
            # Small delay between batches to prevent overwhelming APIs
            await asyncio.sleep(1)
        
        logger.info(f"Batch pipeline processing complete. Processed {len(results)} tokens successfully")
        return results
    
    async def get_top_tokens_processed(self, limit: int = 10) -> List[PipelineResult]:
        """
        Get top tokens and process them through the complete pipeline
        """
        try:
            # Get top tokens
            top_tokens = generate_top_token_list(limit=limit)
            symbols = [token["symbol"] for token in top_tokens]
            
            # Process through pipeline
            results = await self.batch_process_tokens(symbols)
            
            # Sort by AI confidence and sentiment
            def sort_key(result: PipelineResult) -> float:
                ai_score = result.ai_analysis.confidence if result.ai_analysis else 0.0
                sentiment_score = abs(result.sentiment_analysis.score) if result.sentiment_analysis else 0.0
                return ai_score + sentiment_score
            
            results.sort(key=sort_key, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get top tokens processed: {e}")
            return []
    
    async def get_spike_tokens_processed(self, limit: int = 10) -> List[PipelineResult]:
        """
        Get spike tokens and process them through the complete pipeline
        """
        try:
            # Get spike tokens
            spike_tokens = fetch_kucoin_spike_tokens(limit=limit)
            symbols = [token["symbol"] for token in spike_tokens]
            
            # Process through pipeline
            results = await self.batch_process_tokens(symbols)
            
            # Sort by volume ratio and AI decision
            def sort_key(result: PipelineResult) -> float:
                volume_ratio = result.market_data.get("volume_ratio", 1.0)
                ai_confidence = result.ai_analysis.confidence if result.ai_analysis else 0.0
                return volume_ratio + ai_confidence
            
            results.sort(key=sort_key, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get spike tokens processed: {e}")
            return []
    
    async def _log_performance_if_needed(self):
        """Log performance statistics periodically"""
        current_time = time.time()
        
        if current_time - self.performance_stats["last_performance_log"] > PERFORMANCE_LOG_INTERVAL:
            stats = self.get_performance_stats()
            logger.info(f"Pipeline Performance: {json.dumps(stats, indent=2)}")
            self.performance_stats["last_performance_log"] = current_time
    
    def get_performance_stats(self) -> PipelineStats:
        """Get comprehensive performance statistics"""
        with self.stats_lock:
            current_time = time.time()
            elapsed_time = current_time - self.performance_stats["start_time"]
            
            cache_hit_rate = (
                self.performance_stats["cache_hits"] / 
                (self.performance_stats["cache_hits"] + self.performance_stats["cache_misses"])
                if (self.performance_stats["cache_hits"] + self.performance_stats["cache_misses"]) > 0 else 0
            )
            
            throughput_per_minute = (
                self.performance_stats["total_processed"] / (elapsed_time / 60)
                if elapsed_time > 0 else 0
            )
            
            return PipelineStats(
                total_processed=self.performance_stats["total_processed"],
                successful=self.performance_stats["successful"],
                failed=self.performance_stats["failed"],
                avg_processing_time=self.performance_stats["avg_processing_time"],
                cache_hit_rate=cache_hit_rate,
                throughput_per_minute=throughput_per_minute,
                last_updated=current_time
            )
    
    async def save_results_to_files(self, results: List[PipelineResult]):
        """Save pipeline results to data files"""
        try:
            # Prepare data for different endpoints
            ai_logic_data = []
            discover_data = []
            
            for result in results:
                if result.success and result.ai_analysis:
                    # AI Logic data
                    ai_logic_data.append({
                        "symbol": result.symbol,
                        "decision": result.ai_analysis.decision,
                        "confidence": result.ai_analysis.confidence,
                        "reason": result.ai_analysis.reason,
                        "timestamp": result.timestamp
                    })
                    
                    # Discover data
                    discover_data.append({
                        "symbol": result.symbol,
                        "price": result.market_data.get("price", 0.0),
                        "volume": result.market_data.get("volume_24h", 0.0),
                        "sentiment": result.sentiment_analysis.score if result.sentiment_analysis else 0.0,
                        "ai_decision": result.ai_analysis.decision,
                        "confidence": result.ai_analysis.confidence,
                        "score": result.ai_analysis.confidence + abs(result.sentiment_analysis.score if result.sentiment_analysis else 0.0)
                    })
            
            # Save to files
            os.makedirs("backend/data", exist_ok=True)
            
            with open("backend/data/ai_logic.json", "w") as f:
                json.dump(ai_logic_data, f, indent=2)
            
            with open("backend/data/discover_tokens.json", "w") as f:
                json.dump(discover_data, f, indent=2)
            
            logger.info(f"Saved {len(ai_logic_data)} AI logic entries and {len(discover_data)} discover entries")
            
        except Exception as e:
            logger.error(f"Failed to save results to files: {e}")
    
    def reset_stats(self):
        """Reset performance statistics"""
        with self.stats_lock:
            self.performance_stats = {
                "total_processed": 0,
                "successful": 0,
                "failed": 0,
                "avg_processing_time": 0.0,
                "cache_hits": 0,
                "cache_misses": 0,
                "start_time": time.time(),
                "last_performance_log": time.time()
            }

# Global instance
optimized_pipeline = OptimizedDataPipeline()

# Convenience functions
async def process_top_tokens(limit: int = 10) -> List[PipelineResult]:
    """Process top tokens through the optimized pipeline"""
    results = await optimized_pipeline.get_top_tokens_processed(limit)
    await optimized_pipeline.save_results_to_files(results)
    return results

async def process_spike_tokens(limit: int = 10) -> List[PipelineResult]:
    """Process spike tokens through the optimized pipeline"""
    results = await optimized_pipeline.get_spike_tokens_processed(limit)
    await optimized_pipeline.save_results_to_files(results)
    return results

async def process_custom_tokens(symbols: List[str]) -> List[PipelineResult]:
    """Process custom list of tokens through the optimized pipeline"""
    results = await optimized_pipeline.batch_process_tokens(symbols)
    await optimized_pipeline.save_results_to_files(results)
    return results

def get_pipeline_performance_stats() -> Dict[str, Any]:
    """Get pipeline performance statistics"""
    stats = optimized_pipeline.get_performance_stats()
    return asdict(stats)

# Scheduled task function
async def run_scheduled_pipeline():
    """Run the pipeline on a schedule for top tokens"""
    try:
        logger.info("Starting scheduled pipeline run")
        
        # Process top tokens
        top_results = await process_top_tokens(limit=15)
        
        # Process spike tokens
        spike_results = await process_spike_tokens(limit=10)
        
        # Log summary
        total_processed = len(top_results) + len(spike_results)
        successful = sum(1 for r in top_results + spike_results if r.success)
        
        logger.info(f"Scheduled pipeline run complete: {successful}/{total_processed} successful")
        
        return {
            "top_tokens": len(top_results),
            "spike_tokens": len(spike_results),
            "total_processed": total_processed,
            "successful": successful
        }
        
    except Exception as e:
        logger.error(f"Scheduled pipeline run failed: {e}")
        return {"error": str(e)}
