# backend/ai_trade_signal.py

import logging

logger = logging.getLogger("ai_trade_signal")

def score_token_with_ai_signals(indicators, sentiment_score, strategy_name, historical_pnl=0):
    """
    Score a token based on AI-related market signals and strategy configuration.
    Returns an integer score indicating trade potential.
    """
    score = 0

    # RSI scoring
    rsi = indicators.get("rsi", 50)
    if rsi < 35 or rsi > 70:
        score += 2
        logger.debug(f"RSI {rsi} → +2")
    elif 35 <= rsi <= 45 or 60 <= rsi <= 70:
        score += 1
        logger.debug(f"RSI {rsi} → +1")

    # Volume spike
    if indicators.get("volume_spike", False):
        score += 2
        logger.debug("Volume spike → +2")

    # Sentiment contribution
    if sentiment_score >= 2:
        score += 2
        logger.debug("Sentiment score ≥ 2 → +2")
    elif sentiment_score == 1:
        score += 1
        logger.debug("Sentiment score == 1 → +1")

    # Strategy adjustment
    if strategy_name in {"INFINITY_GRID", "SPOT_GRID"}:
        score += 2
        logger.debug(f"Strategy {strategy_name} → +2")
    elif strategy_name == "DCA":
        score += 1
        logger.debug(f"Strategy {strategy_name} → +1")

    # Historical PnL adjustment
    if historical_pnl > 0:
        score += 1
        logger.debug("Historical PnL > 0 → +1")
    elif historical_pnl < -10:
        score -= 1
        logger.debug("Historical PnL < -10 → -1")

    logger.info(f"Final score for strategy '{strategy_name}': {score}")
    return score
