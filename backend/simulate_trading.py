"""
AlphaPredatorBot — Simulate Trading

This script simulates trade execution based on pre-annotated news articles 
with sentiment scores. It is used to test trading logic in a non-live environment.
"""

import json
import logging
from trade_executor import execute_trade
def should_buy_ai(symbol, price_data=None):
    """Fallback function for AI buy decision"""
    try:
        from ai_core import get_final_ai_decision
        result = get_final_ai_decision(symbol)
        return result.get("decision") == "BUY" if isinstance(result, dict) else False
    except ImportError:
        return False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_trading_simulation(articles: list) -> None:
    """
    Simulates trading based on a list of news articles.
    """
    logger.info(f"[SimulateTrading] Starting simulation with {len(articles)} articles.")
    for article in articles:
        symbol = article.get("token")
        sentiment_score = article.get("sentiment_score")
        if symbol and sentiment_score is not None:
            # In a real scenario, you'd fetch current price here.
            # For simulation, we'll use a dummy price or assume it's available.
            dummy_price = 100.0 # Placeholder price
            
            # Decide whether to buy based on AI signal
            if should_buy_ai(symbol):
                logger.info(f"[SimulateTrading] AI recommends BUY for {symbol} (Sentiment: {sentiment_score})")
                # Simulate a buy trade
                execute_trade(token_symbol=symbol, side="BUY", amount_usd=100, price=dummy_price, strategy="AI_SIM", reason="Simulated AI Buy")
            else:
                logger.info(f"[SimulateTrading] AI recommends HOLD/SELL for {symbol} (Sentiment: {sentiment_score})")
        else:
            logger.warning(f"[SimulateTrading] Skipping article due to missing symbol or sentiment: {article}")
    logger.info("[SimulateTrading] Simulation finished.")

def run_simulation() -> None:
    """
    Load news articles with sentiment and pass them to the trading engine 
    for simulated trade decisions.
    """
    try:
        with open("backend/data/news_with_sentiment.json", "r", encoding="utf-8") as f:
            articles = json.load(f)
            logger.info(f"[SimulateTrading] Loaded {len(articles)} articles.")
            run_trading_simulation(articles)  # Call the imported function directly
    except FileNotFoundError:
        logger.error("[SimulateTrading] Input file not found: backend/data/news_with_sentiment.json")
    except json.JSONDecodeError as e:
        logger.error(f"[SimulateTrading] Failed to decode JSON: {e}")
    except Exception as e:
        logger.exception(f"[SimulateTrading] Unexpected error: {e}")
