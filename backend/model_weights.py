import json
import os
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

WEIGHTS_FILE = "data/model_weights.json"

def load_model_weights() -> Dict[str, float]:
    """
    Loads AI model weights from a JSON file. If the file doesn't exist,
    returns default weights.
    """
    if not os.path.exists(WEIGHTS_FILE):
        logger.warning(f"Model weights file not found: {WEIGHTS_FILE}. Using default weights.")
        return {
            "DeepSeek": 0.4,
            "Gemini": 0.2,
            "OpenAI": 0.4,
        }
    try:
        with open(WEIGHTS_FILE, "r") as f:
            weights = json.load(f)
            logger.info(f"Loaded model weights from {WEIGHTS_FILE}")
            return weights
    except Exception as e:
        logger.error(f"Error loading model weights from {WEIGHTS_FILE}: {e}. Using default weights.")
        return {
            "DeepSeek": 0.4,
            "Gemini": 0.2,
            "OpenAI": 0.4,
        }

def save_model_weights(weights: Dict[str, float]):
    """
    Saves AI model weights to a JSON file.
    """
    try:
        os.makedirs(os.path.dirname(WEIGHTS_FILE), exist_ok=True)
        with open(WEIGHTS_FILE, "w") as f:
            json.dump(weights, f, indent=4)
        logger.info(f"Saved model weights to {WEIGHTS_FILE}")
    except Exception as e:
        logger.error(f"Error saving model weights to {WEIGHTS_FILE}: {e}")

# Example usage (for testing)
if __name__ == "__main__":
    # Load weights
    current_weights = load_model_weights()
    print("Current weights:", current_weights)

    # Modify weights (example)
    current_weights["DeepSeek"] = 0.5
    current_weights["Gemini"] = 0.3
    current_weights["OpenAI"] = 0.2

    # Save modified weights
    save_model_weights(current_weights)

    # Load again to verify
    new_weights = load_model_weights()
    print("New weights:", new_weights)
