"""
AlphaPredatorBot — Telegram Integration Tests

Tests the integration between Telegram bot components and trading functionality.
Covers callback handling, notifications, and auto-trading features.
"""

import os
import sys
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from dotenv import load_dotenv

# Load test environment variables
load_dotenv("backend/tests/.env.test")

# Mock config module before importing other modules
mock_config = Mock()
mock_config.KUCOIN_BASE_URL = "https://api.kucoin.com"
mock_config.TELEGRAM_BOT_TOKEN = "test_token"
mock_config.TELEGRAM_CHANNEL_ID = "test_channel"
sys.modules['backend.config'] = mock_config

from telegram import Update, User, CallbackQuery
from telegram.ext import ContextTypes
from telegram_callback_listener import handle_button
from telegram_notifier import (
    notify_trade,
    notify_news_alert,
    notify_new_gem_suggestion,
    notify_pnl_summary,
    handle_callback,
    )

# Test data
TEST_TOKEN = "TEST-USDT"
TEST_USER = "Test User"
TEST_PRICE = 1.234
TEST_QUANTITY = 100.0

@pytest.fixture
def mock_bot():
    return AsyncMock()

@pytest.fixture
def mock_context():
    return Mock(spec=ContextTypes.DEFAULT_TYPE)

@pytest.fixture
def mock_callback_query():
    query = AsyncMock(spec=CallbackQuery)
    query.data = "yes"
    query.from_user = Mock(spec=User, full_name=TEST_USER)
    return query

@pytest.fixture
def mock_update(mock_callback_query):
    update = Mock(spec=Update)
    update.callback_query = mock_callback_query
    update.effective_user = Mock(full_name=TEST_USER)
    return update

@pytest.mark.asyncio
async def test_handle_button_yes(mock_update, mock_context):
    """Test yes button callback handling"""
    mock_update.callback_query.data = "yes"
    await handle_button(mock_update, mock_context)
    mock_update.callback_query.edit_message_text.assert_called_with(
        f"👍 {TEST_USER} liked the signal!"
    )

@pytest.mark.asyncio
async def test_handle_button_no(mock_update, mock_context):
    """Test no button callback handling"""
    mock_update.callback_query.data = "no"
    await handle_button(mock_update, mock_context)
    mock_update.callback_query.edit_message_text.assert_called_with(
        f"👎 {TEST_USER} passed on this signal."
    )

@pytest.mark.asyncio
async def test_notify_trade(mock_bot):
    """Test trade notification with buttons"""
    mock_application = Mock()
    mock_application.bot = mock_bot
    await notify_trade(
        application=mock_application,
        token=TEST_TOKEN,
        action="BUY",
        quantity=TEST_QUANTITY,
        price=TEST_PRICE,
        strategy="Test Strategy",
        reason="Test reason"
    )
    mock_bot.send_message.assert_called_once()
    args = mock_bot.send_message.call_args[1]
    assert TEST_TOKEN in args['text']
    assert "BUY" in args['text']
    assert str(TEST_PRICE) in args['text']
    assert args['parse_mode'] == "HTML"
    assert args['reply_markup'] is not None  # Verify buttons are included

@pytest.mark.asyncio
async def test_notify_news_alert(mock_bot):
    """Test news alert notification"""
    mock_application = Mock()
    mock_application.bot = mock_bot
    await notify_news_alert(
        application=mock_application,
        token=TEST_TOKEN,
        title="Test News",
        sentiment="0.8",
        tags=["positive", "partnership"],
        author="Test Source"
    )
    mock_bot.send_message.assert_called_once()
    args = mock_bot.send_message.call_args[1]
    assert TEST_TOKEN in args['text']
    assert "Test News" in args['text']
    assert "0.8" in args['text']
    assert "positive" in args['text']
    assert "Test Source" in args['text']

@pytest.mark.asyncio
async def test_notify_new_gem_suggestion(mock_bot):
    """Test gem suggestion with approval buttons"""
    mock_application = Mock()
    mock_application.bot = mock_bot
    await notify_new_gem_suggestion(
        application=mock_application,
        token=TEST_TOKEN,
        sentiment_score=0.9,
        summary="Test gem suggestion"
    )
    mock_bot.send_message.assert_called_once()
    args = mock_bot.send_message.call_args[1]
    assert TEST_TOKEN in args['text']
    assert "0.9" in args['text']
    assert args['reply_markup'] is not None  # Verify approval buttons

@pytest.mark.asyncio
async def test_handle_callback_buy(mock_update, mock_context):
    """Test buy callback handling"""
    mock_update.callback_query.data = f"buy_{TEST_TOKEN}"
    with patch('backend.telegram_notifier.logger') as mock_logger:
        await handle_callback(mock_update, mock_context)
        mock_update.callback_query.edit_message_text.assert_called_with(f"✅ Approved. Buying `{TEST_TOKEN}`")
        mock_logger.info.assert_called_once_with(f"[Telegram Callback] User approved BUY for {TEST_TOKEN}")

@pytest.mark.asyncio
async def test_handle_callback_skip(mock_update, mock_context):
    """Test skip callback handling"""
    mock_update.callback_query.data = f"skip_{TEST_TOKEN}"
    await handle_callback(mock_update, mock_context)
    mock_update.callback_query.edit_message_text.assert_called_with(
        f"❌ Skipped `{TEST_TOKEN}`"
    )

@pytest.mark.asyncio
async def test_pnl_summary_notification(mock_bot):
    """Test PnL summary notification"""
    mock_summary = [
        {
            'token': 'PROFIT-USDT',
            'invested': 1000.0,
            'realized': 1000.0,
            'unrealized': 0.0,
            'total_pnl': 1000.0,
            'buy_qty': 5,
            'sell_qty': 3,
            'profit': 1000.0,
            'loss': 0.0,
            'reason': 'Test Profit',
            'status': 'Exited'
        },
        {
            'token': 'LOSS-USDT',
            'invested': 500.0,
            'realized': -200.0,
            'unrealized': 0.0,
            'total_pnl': -200.0,
            'buy_qty': 2,
            'sell_qty': 1,
            'profit': 0.0,
            'loss': 200.0,
            'reason': 'Test Loss',
            'status': 'Exited'
        }
    ]
    mock_application = Mock()
    mock_application.bot = mock_bot
    with patch('backend.telegram_utils.get_pnl_summary', return_value=mock_summary):
        await notify_pnl_summary(application=mock_application)
        mock_bot.send_message.assert_called_once()
        args = mock_bot.send_message.call_args[1]
        assert "Daily PnL Summary" in args['text']
        assert "800.00" in args['text']
        assert "7" in args['text']
        assert "4" in args['text']
        assert "1000.00" in args['text']
        assert "-200.00" in args['text']
        assert args['parse_mode'] == "HTML"
