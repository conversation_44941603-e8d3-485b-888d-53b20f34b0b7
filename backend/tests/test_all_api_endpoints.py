import requests

BASE_URL = "http://127.0.0.1:3005"

endpoints = [
    ("GET", "/", "Health Check"),
    ("POST", "/api/news/fetch", "Fetch News"),
    ("GET", "/api/news", "Get Saved News"),
    ("GET", "/api/spike-tokens", "Spike Tokens"),
    ("GET", "/api/newly-listed", "Newly Listed Tokens"),
    ("GET", "/api/discover", "Discover Tokens"),
    ("GET", "/api/news-sources", "News Source Weights"),
    ("POST", "/api/news-sources/cointelegraph", "Update Cointelegraph Weight", {"weight": 0.7}),
    ("GET", "/api/ai-logic", "Get AI Logic"),
    ("GET", "/api/analytics", "Analytics"),
    ("GET", "/api/analytics/realtime", "Realtime Analytics"),
    ("POST", "/api/login", "Login", {
        "email": "<EMAIL>",
        "password": "**********"
    }),
]

def run_tests():
    print("\n🔍 Testing API Endpoints...\n")
    for method, path, label, *payload in endpoints:
        url = BASE_URL + path
        try:
            if method == "GET":
                response = requests.get(url)
            elif method == "POST":
                data = payload[0] if payload else {}
                response = requests.post(url, json=data)
            else:
                print(f"❓ Skipping unknown method {method}")
                continue

            status = "✅" if response.status_code in [200, 201] else "❌"
            print(f"{status} {label:<30} [{response.status_code}]")

            if status == "❌":
                print(f"   ↳ {response.text[:200]}...")

        except Exception as e:
            print(f"❌ {label:<30} [EXCEPTION]")
            print(f"   ↳ {e}")

if __name__ == "__main__":
    run_tests()