import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def get_auth_token():
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    response = client.post("/api/login", json=login_data)
    assert response.status_code == 200
    token = response.json().get("access_token")
    assert token is not None
    return token

def test_post_live_trade_valid():
    token = get_auth_token()
    headers = {"Authorization": f"Bearer {token}"}
    payload = {
        "symbol": "BTC",
        "side": "buy",
        "quantity": 0.01
    }
    response = client.post("/api/trades/live", json=payload, headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] == "success" or data["status"] == "error"

def test_post_live_trade_invalid_side():
    token = get_auth_token()
    headers = {"Authorization": f"Bearer {token}"}
    payload = {
        "symbol": "BTC",
        "side": "hold",
        "quantity": 0.01
    }
    response = client.post("/api/trades/live", json=payload, headers=headers)
    assert response.status_code == 400
    data = response.json()
    assert "Invalid trade side" in data.get("detail", "")

def test_post_strategy_trade():
    token = get_auth_token()
    headers = {"Authorization": f"Bearer {token}"}
    response = client.post("/api/trades/live/strategy-trade", headers=headers)
    assert response.status_code in (200, 404, 500)
    data = response.json()
    assert "status" in data
    assert "message" in data

# Additional edge case tests can be added here for thorough coverage
