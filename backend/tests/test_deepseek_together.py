import openai
import os
import unittest
from unittest.mock import patch, MagicMock

# Load Together.ai credentials
openai.api_key = os.getenv("TOGETHER_API_KEY") or "your_actual_together_api_key_here"
# openai.api_base = "https://api.together.xyz"  # Commented out because not recognized by openai module

# DeepSeek-V2 model hosted by Together
model_name = "deepseek-ai/DeepSeek-V2"
prompt = "Explain the Ethereum blockchain in simple terms."

class TestDeepSeekModel(unittest.TestCase):

    @patch("openai.ChatCompletion.create")
    def test_deepseek_response_format(self, mock_create):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].text = '{"decision": "BUY", "confidence": 90, "reason": "Strong market signals."}'
        mock_response.usage = {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}
        mock_create.return_value = mock_response

        try:
            # Ensure openai.Completion is available
            if not hasattr(openai, "Completion"):
                self.skipTest("openai.Completion is not available in this openai version.")
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=300,
                temperature=0.7,
            )
            content = response.choices[0].text
            usage = getattr(response, "usage", {})

            print("\n🧠 DeepSeek Response:")
            print(content)
            print("\n📊 Usage Info:", usage)

            self.assertIsInstance(content, str)
            self.assertTrue(content is not None and len(content) > 10)
        except Exception as e:
            self.fail(f"❌ API call failed: {e}")

if __name__ == "__main__":
    unittest.main()
