import unittest
from prompt_builder import build_trade_prompt

class TestPromptBuilder(unittest.TestCase):
    def test_prompt_contains_token(self):
        token = "DOGE"
        prompt = build_trade_prompt(token)

        print("\n=== TEST: Trade Prompt Builder ===")
        print(f"Token: {token}")
        print("\nGenerated Prompt:\n")
        print(prompt)
        print("\n=== END TEST ===")

        self.assertIsInstance(prompt, str)
        self.assertIn(token.upper(), prompt)

if __name__ == "__main__":
    unittest.main()
