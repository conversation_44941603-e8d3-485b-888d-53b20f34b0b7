import unittest
from unittest.mock import Mock
from telegram_notifier import notify_trade

class TestTelegramNotifier(unittest.IsolatedAsyncioTestCase):
    async def test_notify_trade(self):
        mock_application = Mock()
        mock_application.bot = Mock()
        try:
            result = await notify_trade(
                application=mock_application,
                token="TESTCOIN-USDT",
                action="BUY",
                quantity=500,
                price=0.123,
                strategy="Manual Test",
                reason="This is a test trade signal"
            )
            self.assertIsNone(result)
            mock_application.bot.send_message.assert_called_once()
            print("✅ Telegram trade notification sent successfully.")
        except Exception as e:
            self.fail(f"❌ Telegram notification failed: {e}")

if __name__ == "__main__":
    unittest.main()