import os
import json
import time
import unittest

KNOWN_TOKENS_PATH = "backend/data/known_tokens.json"

def load_known_tokens():
    if not os.path.exists(KNOWN_TOKENS_PATH):
        return {}
    try:
        with open(KNOWN_TOKENS_PATH, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        return {}

def save_known_tokens(known_tokens):
    os.makedirs(os.path.dirname(KNOWN_TOKENS_PATH), exist_ok=True)
    with open(KNOWN_TOKENS_PATH, "w") as f:
        json.dump(known_tokens, f, indent=4)

def detect_new_kucoin_tokens(current_symbols):
    known_tokens = load_known_tokens()
    new_tokens = []

    for symbol in current_symbols:
        if symbol not in known_tokens:
            new_tokens.append(symbol)
            known_tokens[symbol] = int(time.time())

    if new_tokens:
        save_known_tokens(known_tokens)

    return new_tokens

class TestTokenDetection(unittest.TestCase):
    def setUp(self):
        self.test_path = "backend/data/test_known_tokens.json"
        global KNOWN_TOKENS_PATH
        self.original_path = KNOWN_TOKENS_PATH
        KNOWN_TOKENS_PATH = self.test_path
        save_known_tokens({"BTC-USDT": int(time.time())})

    def tearDown(self):
        global KNOWN_TOKENS_PATH
        KNOWN_TOKENS_PATH = self.original_path
        if os.path.exists(self.test_path):
            os.remove(self.test_path)

    def test_detect_new_tokens(self):
        test_input = ["ABC-USDT", "BTC-USDT", "NEWCOIN-USDT"]
        new = detect_new_kucoin_tokens(test_input)
        self.assertIn("ABC-USDT", new)
        self.assertIn("NEWCOIN-USDT", new)
        self.assertNotIn("BTC-USDT", new)

    def test_no_new_tokens(self):
        test_input = ["BTC-USDT"]
        new = detect_new_kucoin_tokens(test_input)
        self.assertEqual(new, [])

if __name__ == "__main__":
    unittest.main()
