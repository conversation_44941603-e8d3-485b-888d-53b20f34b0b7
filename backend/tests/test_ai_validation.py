import unittest
from unittest.mock import patch
import sys
import os

# Add backend directory to sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
backend_dir = os.path.join(project_root, "my-app", "backend")
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
backend_dir = os.path.join(project_root, "my-app", "backend")
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
backend_dir = os.path.join(project_root, "my-app", "backend")
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

from ai_validation_engine import get_final_ai_decision
from prompt_builder import build_trade_prompt

class TestAIValidation(unittest.TestCase):

    def test_validate_trade_signal(self):
        token = "DOGE"

        # Step 1: Build prompt with all current signals
        prompt = build_trade_prompt(token)
        self.assertIsInstance(prompt, str)
        self.assertIn(token.upper(), prompt)

        # Step 2: Run through AI validation engine
        with patch('backend.strategy_ai.decide_strategy', return_value='HOLD'):
            result = get_final_ai_decision(prompt, events=[], target_symbol=token, token=token, prices=[], high_prices=[], low_prices=[], volumes=[], sentiment_score=0.0, news_keywords=[])

        # Step 3: Validate response structure
        self.assertIsInstance(result, str)
        self.assertIn(result, ["BUY", "SELL", "HOLD", "UNCERTAIN"])

        print("\n📜 Prompt:")
        print(prompt)
        print("\n🧾 Result:")
        print(result)

if __name__ == "__main__":
    unittest.main()
