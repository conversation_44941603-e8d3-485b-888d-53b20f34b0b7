import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

# Global variable to store auth token
auth_token = None

def test_login_missing_fields():
    response = client.post("/api/login", json={})
    assert response.status_code == 400
    assert response.json() == {"detail": "Invalid login request"}

def test_login_invalid_credentials():
    response = client.post("/api/login", json={"email": "<EMAIL>", "password": "wrong"})
    assert response.status_code == 403
    assert response.json() == {"detail": "Email not allowed"}

def test_login_success():
    global auth_token
    response = client.post("/api/login", json={"email": "<EMAIL>", "password": "password123"})
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "token_type" in data
    assert data["token_type"] == "bearer"
    auth_token = data["access_token"]

def get_auth_headers():
    """Helper function to get authorization headers"""
    if auth_token:
        return {"Authorization": f"Bearer {auth_token}"}
    return {}

def test_fetch_discover_tokens():
    headers = get_auth_headers()
    response = client.get("/api/discover", headers=headers)
    if auth_token:
        assert response.status_code == 200
        assert isinstance(response.json(), list)
    else:
        assert response.status_code == 401

def test_fetch_pnl():
    headers = get_auth_headers()
    response = client.get("/api/pnl", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_fetch_trade_summary():
    headers = get_auth_headers()
    response = client.get("/api/trades/summary", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_fetch_live_trades():
    headers = get_auth_headers()
    response = client.get("/api/trades/live", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_fetch_analytics():
    headers = get_auth_headers()
    response = client.get("/api/analytics", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_fetch_analytics_realtime():
    headers = get_auth_headers()
    response = client.get("/api/analytics/realtime", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_fetch_ai_decisions():
    headers = get_auth_headers()
    response = client.get("/api/ai-decisions", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_fetch_sentiment_feed():
    headers = get_auth_headers()
    response = client.get("/api/sentiment-feed", headers=headers)
    if auth_token:
        assert response.status_code == 200
    else:
        assert response.status_code == 401

def test_health_endpoints():
    """Test health endpoints (no auth required)"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}
    
    response = client.get("/api/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert "version" in data

def test_invalid_route():
    response = client.get("/api/invalid-route")
    assert response.status_code == 404

def test_unauthorized_access():
    """Test that protected endpoints require authentication"""
    response = client.get("/api/discover")
    assert response.status_code == 401
    
    response = client.get("/api/pnl")
    assert response.status_code == 401
