import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load test environment variables first
test_env_path = Path(__file__).resolve().parent / ".env.test"
if test_env_path.exists():
    load_dotenv(dotenv_path=test_env_path, override=True)

# Add the backend directory to sys.path for imports in tests
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.abspath(os.path.join(current_dir, ".."))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

import pytest
from fastapi.testclient import TestClient

# Import after setting up the path and environment
from main import app

@pytest.fixture
def client():
    return TestClient(app)
