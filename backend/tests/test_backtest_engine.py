import json
from prompt_builder import build_trade_prompt  # placeholder for integration

class BacktestEngine:
    def __init__(self, initial_cash=1000):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.portfolio = {}
        self.trade_log = []
        self.pnls = []

    def run(self, events):
        for event in events:
            token = event["token"]
            price = event["price"]
            action = event["decision"]

            if action == "BUY" and self.cash >= price:
                self.portfolio[token] = {"price": price, "qty": 1}
                self.cash -= price
                self.trade_log.append(f"✅ BUY {token} at ${price}")
            elif action == "SELL" and token in self.portfolio:
                bought_price = self.portfolio[token]["price"]
                pnl = price - bought_price
                self.cash += price
                self.pnls.append(pnl)
                del self.portfolio[token]
                self.trade_log.append(f"💰 SELL {token} at ${price} | PnL: ${pnl:.2f}")
            else:
                self.trade_log.append(f"⏸ HOLD {token} at ${price}")

    def summary(self):
        print("\n=== BACKTEST SUMMARY ===")
        for log in self.trade_log:
            print(log)

        total_pnl = sum(self.pnls)
        win_rate = (
            (sum(1 for pnl in self.pnls if pnl > 0) / len(self.pnls)) * 100
            if self.pnls else 0
        )

        print(f"\n💼 Final Cash: ${self.cash:.2f}")
        print(f"📊 Total PnL: ${total_pnl:.2f}")
        print(f"✅ Win Rate: {win_rate:.2f}%")
        print(f"📦 Open Positions: {self.portfolio}")

if __name__ == "__main__":
    SIMULATED_EVENTS = [
        {"token": "DOGE", "price": 0.10, "decision": "BUY"},
        {"token": "DOGE", "price": 0.13, "decision": "SELL"},
        {"token": "SOL", "price": 20.0, "decision": "BUY"},
        {"token": "SOL", "price": 18.0, "decision": "SELL"},
    ]

    engine = BacktestEngine(initial_cash=1000)
    engine.run(SIMULATED_EVENTS)
    engine.summary()