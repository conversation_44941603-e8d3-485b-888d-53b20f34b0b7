# backend/twitter_alpha.py

import os
from utils.api_client import get
import logging
from typing import List, Dict
from dotenv import load_dotenv

load_dotenv()

BEARER_TOKEN = os.getenv("TWITTER_BEARER_TOKEN")

HEADERS = {
    "Authorization": f"Bearer {BEARER_TOKEN}"
}

SEARCH_URL = "https://api.twitter.com/2/tweets/search/recent"
DEFAULT_QUERY_TOKENS = ["dogecoin", "pepe", "kaspa", "$SHIB"]

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def build_query(tokens: List[str]) -> str:
    """
    Construct a Twitter API search query for given token keywords.
    """
    token_query = " OR ".join(tokens)
    return f"({token_query}) lang:en -is:retweet"


def fetch_twitter_alpha_signals(tokens: List[str] = DEFAULT_QUERY_TOKENS, max_results: int = 50) -> List[Dict]:
    """
    Fetch relevant tweets related to crypto tokens to identify early sentiment signals.
    """
    query = build_query(tokens)
    params = {
        "query": query,
        "max_results": min(max_results, 100),
        "tweet.fields": "public_metrics,created_at"
    }

    try:
        logger.info("📡 Querying Twitter API...")
        response = get(SEARCH_URL, headers=HEADERS, params=params, cache_ttl=60) # Cache Twitter search results for 1 minute
        response.raise_for_status()
        tweets = response.json().get("data", [])

        alpha_signals = []
        for tweet in tweets:
            text = tweet["text"].lower()
            for token in tokens:
                token_lower = token.lower().replace("$", "")
                if token_lower in text:
                    alpha_signals.append({
                        "token": token.upper().replace("$", ""),
                        "text": tweet["text"],
                        "retweets": tweet["public_metrics"]["retweet_count"],
                        "likes": tweet["public_metrics"]["like_count"],
                        "created_at": tweet["created_at"]
                    })
        logger.info(f"✅ Retrieved {len(alpha_signals)} alpha tweets.")
        return alpha_signals
    except Exception as e:
        logger.error(f"Twitter API error: {e}")
        return []


if __name__ == "__main__":
    logger.info("🐦 Fetching Twitter Alpha Signals...")
    results = fetch_twitter_alpha_signals()
    for item in results:
        logger.info(f"🚀 {item['token']}: {item['text']} [+{item['likes']}❤ {item['retweets']}🔁]")
