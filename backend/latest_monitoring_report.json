{"timestamp": "2025-07-17T22:52:21.676653", "system_health": {"timestamp": "2025-07-17T22:52:21.677083", "overall_status": "HEALTHY", "components": {"kucoin": {"status": "HEALTHY", "balance_usdt": 0, "last_check": "2025-07-17T22:52:21.677479"}, "ai_system": {"status": "HEALTHY", "working_providers": 2, "total_providers": 4, "last_check": "2025-07-17T22:52:33.816339"}, "token_selection": {"status": "HEALTHY", "tokens_found": 5, "last_check": "2025-07-17T22:53:16.630426"}}, "alerts": []}, "trading_performance": {"timestamp": "2025-07-17T22:53:17.146113", "total_trades_today": 7, "successful_trades": 0, "win_rate": 0.0, "total_pnl": 0, "realized_pnl": 0, "unrealized_pnl": 0, "best_trade": 0.0, "worst_trade": 0.0, "avg_trade_pnl": 0.0, "recent_trades": [{"timestamp": "2025-07-02T23:18:28.478742", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 108646.3, "value": 0.98, "strategy": "AI", "reason": NaN}, {"timestamp": "2025-07-02T23:23:04.394355", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 108648.9, "value": 0.98, "strategy": "AI", "reason": NaN}, {"timestamp": "2025-07-02T23:23:31.226414", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 108649.0, "value": 0.98, "strategy": "AI", "reason": NaN}, {"timestamp": "2025-07-03T11:07:37.070109", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 109364.5, "value": 0.98, "strategy": "AI", "reason": NaN}, {"timestamp": "2025-07-09T23:40:34.334931", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 111046.8, "value": 1.0, "strategy": "AI", "reason": NaN}, {"timestamp": "2025-07-09T23:40:52.567021", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 111026.2, "value": 1.0, "strategy": "AI", "reason": NaN}, {"timestamp": "2025-07-10T12:23:04.879474", "token": "BTC-USDT", "side": "BUY", "amount": 9e-06, "price": 111858.6, "value": 1.01, "strategy": "AI", "reason": NaN}]}, "risk_metrics": {"timestamp": "2025-07-17T22:53:17.146151", "total_balance_usdt": 0, "max_position_size": 100, "current_exposure": 0, "risk_percentage": 0, "max_daily_loss": 50.0, "trades_remaining_today": 300}, "recommendations": ["📉 Low win rate - consider adjusting strategy"]}