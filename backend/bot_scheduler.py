import logging
import datetime
from error_codes import error_response
import trade_engine  # Import the module instead of the function

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("bot_scheduler")

def run_bot():
    logger.info(f"🕒 Running Alpha Predator Bot at {datetime.datetime.now()}")
    try:
        trade_engine.run_trade_engine()  # Call the function from the module
    except Exception as e:
        logger.error("❌ Error running trade engine", exc_info=True)
        return error_response("TRADE_ENGINE_RUN_FAILED", str(e), 500)
