#!/usr/bin/env python3
"""
Test Major Important Files in Alpha Predator System
"""

import sys
import os
import traceback
import asyncio

# Add current directory to path
sys.path.append('.')

def test_import(module_name, description):
    """Test importing a module"""
    try:
        __import__(module_name)
        print(f"✅ {description}: Import successful")
        return True
    except Exception as e:
        print(f"❌ {description}: Import failed - {e}")
        return False

def test_function_call(func, args, description):
    """Test calling a function"""
    try:
        if asyncio.iscoroutinefunction(func):
            result = asyncio.run(func(*args))
        else:
            result = func(*args)
        print(f"✅ {description}: Function call successful")
        return True, result
    except Exception as e:
        print(f"❌ {description}: Function call failed - {e}")
        return False, None

def main():
    print("🧪 TESTING MAJOR IMPORTANT FILES")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Core Trading Components
    print("\n1. 🎯 CORE TRADING COMPONENTS:")
    print("-" * 30)
    
    total_tests += 1
    if test_import("price_fetcher", "Price Fetcher"):
        tests_passed += 1
        try:
            from price_fetcher import get_price
            print("  📊 Function: get_price available")
        except:
            pass
    
    total_tests += 1
    if test_import("tokenmetrics_client", "TokenMetrics Client"):
        tests_passed += 1
        try:
            from tokenmetrics_client import get_tokenmetrics_data, TokenMetricsData
            print("  📈 Functions: get_tokenmetrics_data, TokenMetricsData available")
        except:
            pass
    
    total_tests += 1
    if test_import("token_selector", "Token Selector"):
        tests_passed += 1
        try:
            import token_selector
            print("  🎯 Module: token_selector available")
        except:
            pass
    
    # Test 2: AI Components
    print("\n2. 🤖 AI COMPONENTS:")
    print("-" * 30)
    
    total_tests += 1
    if test_import("ai_clients.openai_client", "OpenAI Client"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("ai_clients.claude_client", "Claude Client"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("ai_clients.gemini_client", "Gemini Client"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("ai_clients.deepseek_client", "DeepSeek Client"):
        tests_passed += 1
    
    # Test 3: CoinGecko Integration
    print("\n3. 🌐 COINGECKO INTEGRATION:")
    print("-" * 30)
    
    total_tests += 1
    if test_import("coingecko_mcp_client", "CoinGecko MCP Client"):
        tests_passed += 1
        try:
            from coingecko_mcp_client import coingecko_client
            usage_stats = coingecko_client.get_usage_stats()
            print(f"  📊 Usage: {usage_stats['monthly_calls']}/{usage_stats['monthly_limit']} calls")
        except Exception as e:
            print(f"  ⚠️ Usage stats error: {e}")
    
    total_tests += 1
    if test_import("coingecko_integration", "CoinGecko Integration"):
        tests_passed += 1
        try:
            from coingecko_integration import coingecko_integration
            usage_summary = coingecko_integration.get_usage_summary()
            print(f"  📈 Status: {usage_summary['status']}")
        except Exception as e:
            print(f"  ⚠️ Integration error: {e}")
    
    # Test 4: Trading Logic
    print("\n4. 💰 TRADING LOGIC:")
    print("-" * 30)
    
    total_tests += 1
    if test_import("trade_executor", "Trade Executor"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("trade_logger", "Trade Logger"):
        tests_passed += 1
        try:
            import trade_logger
            print("  📊 Module: trade_logger available")
        except:
            pass
    
    total_tests += 1
    if test_import("pnl_dashboard", "PnL Dashboard"):
        tests_passed += 1
        try:
            from pnl_dashboard import get_pnl_summary
            print("  📈 Function: get_pnl_summary available")
        except:
            pass
    
    # Test 5: Data Processing
    print("\n5. 📊 DATA PROCESSING:")
    print("-" * 30)
    
    total_tests += 1
    if test_import("advanced_data_collector", "Advanced Data Collector"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("sentiment_analyzer", "Sentiment Analyzer"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("news_fetcher", "News Fetcher"):
        tests_passed += 1
    
    # Test 6: Configuration and Utils
    print("\n6. ⚙️ CONFIGURATION & UTILS:")
    print("-" * 30)
    
    total_tests += 1
    if test_import("config", "Configuration"):
        tests_passed += 1
        try:
            from config import TRADING_MODE, MAX_DAILY_TRADES
            print(f"  🔧 Trading Mode: {TRADING_MODE}")
            print(f"  🎯 Max Daily Trades: {MAX_DAILY_TRADES}")
        except:
            pass
    
    total_tests += 1
    if test_import("auth", "Authentication"):
        tests_passed += 1
    
    total_tests += 1
    if test_import("cache", "Cache System"):
        tests_passed += 1
    
    # Test 7: Main Application
    print("\n7. 🚀 MAIN APPLICATION:")
    print("-" * 30)
    
    total_tests += 1
    try:
        # Test main.py compilation
        import py_compile
        py_compile.compile('main.py', doraise=True)
        print("✅ Main Application: Syntax check passed")
        tests_passed += 1
    except Exception as e:
        print(f"❌ Main Application: Syntax error - {e}")
    
    # Results
    print("\n" + "=" * 40)
    print(f"📊 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL MAJOR FILES WORKING PROPERLY!")
        print("\n🚀 SYSTEM STATUS:")
        print("✅ Core trading components operational")
        print("✅ AI clients loaded successfully")
        print("✅ CoinGecko integration working")
        print("✅ Trading logic components ready")
        print("✅ Data processing systems active")
        print("✅ Configuration and utilities loaded")
        print("✅ Main application syntax valid")
        return True
    else:
        failed = total_tests - tests_passed
        print(f"❌ {failed} components need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
