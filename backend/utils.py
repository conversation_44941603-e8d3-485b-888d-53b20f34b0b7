import requests
import time
import logging
from typing import Optional

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def retry_request(url: str, retries: int = 3, delay: int = 2, timeout: int = 10) -> Optional[requests.Response]:
    """
    Sends a GET request with retry logic.
    
    Args:
        url (str): The URL to send the request to.
        retries (int): Number of retry attempts.
        delay (int): Delay between retries in seconds.
        timeout (int): Timeout for each request in seconds.
    
    Returns:
        requests.Response if successful, else raises an Exception after all retries fail.
    """
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                return response
            else:
                logger.warning(f"HTTP {response.status_code} for {url}")
        except Exception as e:
            logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
        time.sleep(delay)
    
    logger.error(f"All retry attempts failed for {url}")
    raise Exception(f"All retry attempts failed for {url}")
