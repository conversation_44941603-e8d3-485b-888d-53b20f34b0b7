
def get_chart_fallback(symbol):
    """Fallback chart analysis when API fails"""
    return {
        "signal": "NEUTRAL",
        "confidence": 0.6,
        "indicators": {
            "rsi": 50,
            "macd": "NEUTRAL",
            "trend": "SIDEWAYS"
        },
        "note": f"Fallback analysis for {symbol}"
    }

from utils.api_client import get
import numpy as np
import logging
import requests
from error_codes import error_response

logger = logging.getLogger("chart_analyzer")

RSI_PERIOD = 14
EMA_PERIOD = 10
SMA_PERIOD = 20

def fetch_kucoin_candles(symbol, interval="1min", limit=50):
    """
    Fetch recent candlestick data for a symbol from KuCoin with robust error handling.
    """
    try:
        url = f"https://api.kucoin.com/api/v1/market/candles?symbol={symbol}&type={interval}"
        response = get(url, cache_ttl=30) # Cache KuCoin candles for 30 seconds
        
        if not response:
            logger.warning(f"⚠️ No response from KuCoin API for {symbol}")
            return []
            
        data = response.json()
        if data.get("code") != "200000":
            logger.warning(f"⚠️ KuCoin API error for {symbol}: {data.get('msg', 'Unknown error')}")
            return []
            
        candles = data.get("data", [])
        if not candles:
            logger.warning(f"⚠️ No candle data returned for {symbol}")
            return []
            
        return candles[:limit]  # list of [time, open, close, high, low, volume, turnover]
        
    except requests.exceptions.ConnectionError as e:
        logger.warning(f"⚠️ KuCoin connection failed for {symbol}: {e}")
        return []
    except requests.exceptions.Timeout as e:
        logger.warning(f"⚠️ KuCoin request timeout for {symbol}: {e}")
        return []
    except Exception as e:
        logger.warning(f"⚠️ KuCoin candle fetch error for {symbol}: {e}")
        return []

def compute_rsi(closes, period=RSI_PERIOD):
    """
    Compute Relative Strength Index (RSI) from a list of closing prices.
    """
    if len(closes) < period + 1:
        return 50  # neutral default
    deltas = np.diff(closes)
    seed = deltas[:period]
    up = seed[seed > 0].sum() / period
    down = -seed[seed < 0].sum() / period
    rs = up / down if down != 0 else 0
    rsi = 100 - (100 / (1 + rs))
    return round(rsi, 2)

def compute_sma(closes, period=SMA_PERIOD):
    """
    Compute Simple Moving Average (SMA) over a period.
    """
    if len(closes) < period:
        return round(np.mean(closes), 6)
    return round(np.mean(closes[-period:]), 6)

def compute_ema(closes, period=EMA_PERIOD):
    """
    Compute Exponential Moving Average (EMA) over a period.
    """
    if len(closes) < period:
        return round(np.mean(closes), 6)
    ema = closes[0]
    multiplier = 2 / (period + 1)
    for price in closes[1:]:
        ema = (price - ema) * multiplier + ema
    return round(ema, 6)

def analyze_token(token_symbol):
    """
    Analyze a token's chart to determine trend signals like RSI, SMA, EMA, and volume spike.
    """
    # Handle symbols that already have -USDT suffix
    if token_symbol.endswith('-USDT'):
        symbol = token_symbol
    else:
        symbol = f"{token_symbol}-USDT"
    candles = fetch_kucoin_candles(symbol)
    if not candles:
        return get_chart_fallback(symbol)

    try:
        closes = [float(candle[2]) for candle in reversed(candles)]
        volumes = [float(candle[5]) for candle in reversed(candles)]
    except (ValueError, IndexError):
        logger.warning(f"⚠️ Invalid candle data for {symbol}")
        return get_chart_fallback(symbol)

    rsi = compute_rsi(closes)
    sma = compute_sma(closes)
    ema = compute_ema(closes)
    volume_spike = volumes[-1] > np.mean(volumes) * 1.5

    trend_direction = "SIDEWAYS"
    if ema > sma and closes[-1] > ema:
        trend_direction = "UPTREND"
    elif ema < sma and closes[-1] < ema:
        trend_direction = "DOWNTREND"

    return {
        "rsi": rsi,
        "sma_20": sma,
        "ema_10": ema,
        "volume_spike": volume_spike,
        "trend_direction": trend_direction
    }

def analyze_chart_pattern(symbol):
    """
    Analyze chart pattern for a given symbol and return a pattern description.
    This is the missing function that was being imported in tests.
    """
    try:
        analysis = analyze_token(symbol)
        if not analysis:
            logger.error(f"❌ KuCoin API error for {symbol}: Unknown error")
            return "NEUTRAL"
        
        rsi = analysis.get("rsi", 50)
        trend = analysis.get("trend_direction", "SIDEWAYS")
        volume_spike = analysis.get("volume_spike", False)
        
        # Determine pattern based on indicators
        if rsi > 70 and trend == "UPTREND":
            pattern = "OVERBOUGHT_UPTREND"
        elif rsi < 30 and trend == "DOWNTREND":
            pattern = "OVERSOLD_DOWNTREND"
        elif rsi > 50 and trend == "UPTREND" and volume_spike:
            pattern = "BULLISH_BREAKOUT"
        elif rsi < 50 and trend == "DOWNTREND" and volume_spike:
            pattern = "BEARISH_BREAKDOWN"
        elif trend == "SIDEWAYS":
            pattern = "CONSOLIDATION"
        else:
            pattern = "NEUTRAL"
        
        logger.info(f"Chart pattern for {symbol}: {pattern}")
        return pattern
        
    except Exception as e:
        logger.error(f"❌ Error analyzing chart pattern for {symbol}: {e}")
        return "NEUTRAL"

# Alias for backward compatibility
def analyze_chart(symbol):
    """
    Alias for analyze_chart_pattern for backward compatibility.
    """
    return analyze_chart_pattern(symbol)
