#!/usr/bin/env python3
"""
⚡ API POLLING OPTIMIZER
Intelligent API polling with adaptive intervals and caching
"""

import time
import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PriorityLevel(Enum):
    """API endpoint priority levels."""
    CRITICAL = 1    # 5-10 seconds (trading data)
    HIGH = 2        # 15-30 seconds (market data)
    MEDIUM = 3      # 60-120 seconds (news, sentiment)
    LOW = 4         # 300-600 seconds (analytics, reports)

@dataclass
class EndpointConfig:
    """Configuration for API endpoint polling."""
    name: str
    priority: PriorityLevel
    base_interval: int  # Base polling interval in seconds
    max_interval: int   # Maximum interval during errors
    cache_ttl: int      # Cache time-to-live
    error_backoff: float = 2.0  # Backoff multiplier for errors
    max_errors: int = 3  # Max consecutive errors before backing off

class APIPollingOptimizer:
    """Intelligent API polling with adaptive intervals."""
    
    def __init__(self):
        self.endpoint_configs = {
            # Critical endpoints (trading)
            "/api/trades/live": EndpointConfig("Live Trades", PriorityLevel.CRITICAL, 10, 60, 30),
            "/api/portfolio": EndpointConfig("Portfolio", PriorityLevel.CRITICAL, 15, 90, 45),
            
            # High priority endpoints (market data)
            "/api/cost-monitoring": EndpointConfig("Cost Monitoring", PriorityLevel.HIGH, 30, 180, 60),
            "/api/tokens": EndpointConfig("Token Discovery", PriorityLevel.HIGH, 60, 300, 120),
            "/api/discover": EndpointConfig("Discover Tokens", PriorityLevel.HIGH, 45, 240, 90),
            
            # Medium priority endpoints (news, sentiment)
            "/api/news/live": EndpointConfig("Live News", PriorityLevel.MEDIUM, 120, 600, 300),
            "/api/ai-logic": EndpointConfig("AI Logic", PriorityLevel.MEDIUM, 90, 450, 180),
            "/api/summary": EndpointConfig("Trading Summary", PriorityLevel.MEDIUM, 60, 300, 120),
            
            # Low priority endpoints (analytics)
            "/api/analytics": EndpointConfig("Analytics", PriorityLevel.LOW, 300, 1800, 600),
            "/api/news-sources": EndpointConfig("News Sources", PriorityLevel.LOW, 600, 3600, 1200),
        }
        
        # Track endpoint states
        self.endpoint_states = {}
        self.last_requests = {}
        self.error_counts = {}
        self.current_intervals = {}
        
        # Initialize states
        for endpoint, config in self.endpoint_configs.items():
            self.endpoint_states[endpoint] = "ready"
            self.last_requests[endpoint] = 0
            self.error_counts[endpoint] = 0
            self.current_intervals[endpoint] = config.base_interval
    
    def can_poll_endpoint(self, endpoint: str) -> bool:
        """Check if an endpoint can be polled now."""
        if endpoint not in self.endpoint_configs:
            return True  # Unknown endpoints can be polled
        
        current_time = time.time()
        last_request = self.last_requests.get(endpoint, 0)
        current_interval = self.current_intervals.get(endpoint, 60)
        
        return (current_time - last_request) >= current_interval
    
    def get_optimal_interval(self, endpoint: str) -> int:
        """Get optimal polling interval for an endpoint."""
        if endpoint not in self.endpoint_configs:
            return 60  # Default interval
        
        config = self.endpoint_configs[endpoint]
        error_count = self.error_counts.get(endpoint, 0)
        
        if error_count == 0:
            return config.base_interval
        
        # Apply exponential backoff for errors
        backoff_multiplier = config.error_backoff ** min(error_count, 5)
        adjusted_interval = int(config.base_interval * backoff_multiplier)
        
        return min(adjusted_interval, config.max_interval)
    
    def record_request_success(self, endpoint: str):
        """Record successful request for an endpoint."""
        self.last_requests[endpoint] = time.time()
        self.error_counts[endpoint] = 0  # Reset error count on success
        self.current_intervals[endpoint] = self.get_optimal_interval(endpoint)
        self.endpoint_states[endpoint] = "healthy"
        
        logger.debug(f"✅ {endpoint} request successful, next poll in {self.current_intervals[endpoint]}s")
    
    def record_request_error(self, endpoint: str, error: Exception):
        """Record failed request for an endpoint."""
        self.last_requests[endpoint] = time.time()
        self.error_counts[endpoint] = self.error_counts.get(endpoint, 0) + 1
        self.current_intervals[endpoint] = self.get_optimal_interval(endpoint)
        
        config = self.endpoint_configs.get(endpoint)
        if config and self.error_counts[endpoint] >= config.max_errors:
            self.endpoint_states[endpoint] = "degraded"
        else:
            self.endpoint_states[endpoint] = "error"
        
        logger.warning(f"❌ {endpoint} request failed ({self.error_counts[endpoint]} errors), next poll in {self.current_intervals[endpoint]}s: {error}")
    
    def get_endpoint_status(self) -> Dict[str, Any]:
        """Get status of all monitored endpoints."""
        status = {}
        current_time = time.time()
        
        for endpoint, config in self.endpoint_configs.items():
            last_request = self.last_requests.get(endpoint, 0)
            time_since_last = current_time - last_request if last_request > 0 else None
            next_poll_in = max(0, self.current_intervals[endpoint] - (time_since_last or 0))
            
            status[endpoint] = {
                "name": config.name,
                "priority": config.priority.name,
                "state": self.endpoint_states.get(endpoint, "unknown"),
                "current_interval": self.current_intervals.get(endpoint, config.base_interval),
                "base_interval": config.base_interval,
                "error_count": self.error_counts.get(endpoint, 0),
                "time_since_last": time_since_last,
                "next_poll_in": next_poll_in,
                "can_poll_now": self.can_poll_endpoint(endpoint)
            }
        
        return status
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics."""
        status = self.get_endpoint_status()
        
        total_endpoints = len(status)
        healthy_endpoints = sum(1 for s in status.values() if s["state"] == "healthy")
        degraded_endpoints = sum(1 for s in status.values() if s["state"] == "degraded")
        error_endpoints = sum(1 for s in status.values() if s["state"] == "error")
        
        health_score = healthy_endpoints / total_endpoints if total_endpoints > 0 else 0
        
        return {
            "total_endpoints": total_endpoints,
            "healthy": healthy_endpoints,
            "degraded": degraded_endpoints,
            "errors": error_endpoints,
            "health_score": health_score,
            "status": "healthy" if health_score >= 0.8 else "degraded" if health_score >= 0.5 else "critical",
            "endpoints": status
        }
    
    def optimize_polling_for_load(self, current_load: float):
        """Adjust polling intervals based on system load."""
        if current_load > 0.8:  # High load
            multiplier = 2.0
            logger.info("🔥 High system load detected, reducing polling frequency")
        elif current_load > 0.6:  # Medium load
            multiplier = 1.5
            logger.info("⚠️ Medium system load, slightly reducing polling frequency")
        else:  # Normal load
            multiplier = 1.0
        
        for endpoint, config in self.endpoint_configs.items():
            if self.error_counts.get(endpoint, 0) == 0:  # Only adjust healthy endpoints
                adjusted_interval = int(config.base_interval * multiplier)
                self.current_intervals[endpoint] = min(adjusted_interval, config.max_interval)


# Global instance
polling_optimizer = APIPollingOptimizer()

def can_poll_now(endpoint: str) -> bool:
    """Check if an endpoint can be polled now."""
    return polling_optimizer.can_poll_endpoint(endpoint)

def record_success(endpoint: str):
    """Record successful API request."""
    polling_optimizer.record_request_success(endpoint)

def record_error(endpoint: str, error: Exception):
    """Record failed API request."""
    polling_optimizer.record_request_error(endpoint, error)

def get_polling_status() -> Dict[str, Any]:
    """Get current polling status."""
    return polling_optimizer.get_system_health()

def optimize_for_load(load: float):
    """Optimize polling based on system load."""
    polling_optimizer.optimize_polling_for_load(load)

if __name__ == "__main__":
    # Test the polling optimizer
    print("⚡ TESTING API POLLING OPTIMIZER")
    print("=" * 50)
    
    # Simulate some requests
    test_endpoints = ["/api/cost-monitoring", "/api/news/live", "/api/tokens"]
    
    for endpoint in test_endpoints:
        print(f"\n📊 Testing {endpoint}:")
        print(f"   Can poll now: {can_poll_now(endpoint)}")
        
        # Simulate success
        record_success(endpoint)
        print(f"   After success: {can_poll_now(endpoint)}")
        
        # Simulate error
        record_error(endpoint, Exception("Test error"))
        print(f"   After error: interval = {polling_optimizer.current_intervals[endpoint]}s")
    
    # Show system health
    health = get_polling_status()
    print(f"\n🏥 System Health: {health['status']} ({health['health_score']:.1%})")
    print(f"   Healthy: {health['healthy']}/{health['total_endpoints']}")
    
    print("\n✅ API polling optimizer working correctly!")
