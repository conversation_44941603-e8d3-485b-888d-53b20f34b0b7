from utils.api_client import get
import logging
from typing import Optional, Dict, Any, Set
import asyncio
import requests
from cache import get_cached_data, set_cached_data
from coingecko_enhanced import coingecko_enhanced

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Cache for storing which tokens are available on which exchanges
EXCHANGE_AVAILABILITY_CACHE: Dict[str, Set[str]] = {}

def check_token_on_binance_via_coingecko(symbol: str) -> bool:
    """
    Check if a token is listed on Binance by querying CoinGecko.
    Uses the enhanced CoinGecko integration with improved caching and rate limiting.
    
    Args:
        symbol: Token symbol in KuCoin format (e.g., "BTC-USDT")
        
    Returns:
        True if token is available on Binance, False otherwise
    """
    try:
        # Use the enhanced CoinGecko client
        return coingecko_enhanced.is_token_available_on_binance(symbol)
    except Exception as e:
        logger.warning(f"Failed to check Binance availability for {symbol} via enhanced CoinGecko: {e}")
        return False

def is_token_available_on_exchange(symbol: str, exchange: str) -> bool:
    """
    Check if a token is available on a specific exchange.
    
    Args:
        symbol: Token symbol in KuCoin format (e.g., "BTC-USDT")
        exchange: Exchange name ("Binance", "KuCoin", etc.)
        
    Returns:
        True if token is available on the exchange, False otherwise
    """
    if exchange.lower() == "kucoin":
        # Assume all tokens from our system are available on KuCoin
        return True
    elif exchange.lower() == "binance":
        return check_token_on_binance_via_coingecko(symbol)
    else:
        logger.warning(f"Unknown exchange: {exchange}")
        return False

# --- KuCoin API --- 
KUCOIN_API_BASE = "https://api.kucoin.com"

def fetch_kucoin_price(symbol: str) -> Optional[float]:
    try:
        url = f"{KUCOIN_API_BASE}/api/v1/market/orderbook/level1?symbol={symbol}"
        response = get(url, timeout=5, cache_ttl=10) # Cache KuCoin price for 10 seconds
        response.raise_for_status()
        data = response.json()
        if data.get("code") == "200000" and data.get("data"):
            return float(data["data"]["price"])
    except Exception as e:
        logger.warning(f"Failed to fetch KuCoin price for {symbol}: {e}")
    return None

# --- Binance API --- 
BINANCE_API_BASE = "https://api.binance.com"

def fetch_binance_price(symbol: str) -> Optional[float]:
    """
    Fetch price from Binance, but only if the token is confirmed to be available there.
    This prevents unnecessary API calls and 400 errors for tokens not listed on Binance.
    """
    # First check if token is available on Binance via CoinGecko
    if not is_token_available_on_exchange(symbol, "Binance"):
        logger.debug(f"Token {symbol} not available on Binance, skipping price fetch")
        return None
    
    # Binance uses different symbol format (e.g., BTCUSDT)
    binance_symbol = symbol.replace("-", "")
    try:
        url = f"{BINANCE_API_BASE}/api/v3/ticker/price?symbol={binance_symbol}"
        response = get(url, timeout=5, cache_ttl=10) # Cache Binance price for 10 seconds
        response.raise_for_status()
        data = response.json()
        if data.get("price"):
            logger.debug(f"Successfully fetched Binance price for {symbol}: {data['price']}")
            return float(data["price"])
    except requests.exceptions.HTTPError as http_err:
        if http_err.response.status_code == 400:
            logger.warning(f"Invalid symbol {binance_symbol} for Binance, skipping.")
            # Cache this as unavailable to avoid future attempts
            cache_key = f"binance_availability_{symbol}"
            set_cached_data(cache_key, False, ttl=3600)
        else:
            logger.warning(f"HTTP error fetching Binance price for {symbol}: {http_err}")
    except Exception as e:
        logger.warning(f"Failed to fetch Binance price for {symbol}: {e}")
    return None

# --- Unified Fetcher ---
EXCHANGES = {
    "KuCoin": fetch_kucoin_price,
    "Binance": fetch_binance_price,
}

async def get_prices_from_all_exchanges(symbol: str) -> Dict[str, float]:
    """
    Fetch prices from all available exchanges for a given symbol.
    Only queries exchanges where the token is confirmed to be available.
    """
    prices = {}
    for exchange_name, fetch_func in EXCHANGES.items():
        # Check if token is available on this exchange before attempting to fetch
        if is_token_available_on_exchange(symbol, exchange_name):
            price = await asyncio.to_thread(fetch_func, symbol) # Use to_thread for blocking calls
            if price is not None:
                prices[exchange_name] = price
                logger.debug(f"Fetched price for {symbol} from {exchange_name}: {price}")
        else:
            logger.debug(f"Skipping {exchange_name} for {symbol} - token not available")
    return prices
