# Multi-stage production Dockerfile for Alpha Predator Backend
FROM --platform=linux/amd64 python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1     PYTHONUNBUFFERED=1     PIP_NO_CACHE_DIR=1     PIP_DISABLE_PIP_VERSION_CHECK=1

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip &&     pip install -r requirements.txt

# Copy local NLTK data
COPY nltk_data /usr/share/nltk_data

# Production stage
FROM --platform=linux/amd64 python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1     PYTHONUNBUFFERED=1     ENV=production     LOG_LEVEL=INFO     NLTK_DATA=/usr/share/nltk_data     PORT=3005     HOST=0.0.0.0

# Install system dependencies including curl for health checks
RUN apt-get update && apt-get install -y     curl     && rm -rf /var/lib/apt/lists/*     && apt-get clean

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy NLTK data from builder stage
COPY --from=builder /usr/share/nltk_data /usr/share/nltk_data

# Set work directory
WORKDIR /app

# Copy application code
COPY --chown=appuser:appuser . .

# Create necessary directories and fix permissions
RUN mkdir -p /app/data /app/logs /app/config &&     chown -R appuser:appuser /app &&     chmod -R 755 /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3005

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "3005", "--workers", "1"]