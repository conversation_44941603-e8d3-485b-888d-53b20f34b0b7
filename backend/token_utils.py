import re
import logging
from typing import Optional

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

KNOWN_TOKENS = {
    "BTC", "ETH", "SOL", "DOGE", "SHIB", "PEPE",
    "AVAX", "MATIC", "ADA", "XRP", "TON", "TRX", "AAVE", "LTC", "DOT", "UNI"
}
"""
A set of popular crypto token symbols to match against user or news text.
This list can be dynamically expanded or sourced from a live market API.
"""

def format_token_name(symbol):
    if not isinstance(symbol, str) or not symbol:
        return ""
    return symbol.replace("USDT", "").replace("-", "").upper()


def extract_token_from_text(text: Optional[str]) -> str:
    """
    Extract the first known token symbol mentioned in the given text.

    Args:
        text (str): Input string to search for known tokens.

    Returns:
        str: Uppercase token symbol if found, else "UNKNOWN".
    """
    if not text:
        return "UNKNOWN"

    normalized_text = text.strip().upper()
    for token in sorted(KNOWN_TOKENS):
        if re.search(rf"\b{re.escape(token)}\b", normalized_text, re.IGNORECASE):
            logger.info(f"🪙 Token matched: {token}")
            return token.upper()

    logger.info("⚠️ No known token found in text.")
    return "UNKNOWN"