"""
Optimized News Sentiment Analysis - Production-grade sentiment processing
with caching, batch processing, and enhanced performance.
"""

import json
import os
import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import concurrent.futures
import threading
import textblob
from textblob import TextBlob
import aiohttp
import aiofiles

from cache import get_cached_data, set_cached_data
from utils.logger import get_logger
from config import USE_REAL_NEWS

logger = get_logger("optimized_news_sentiment")

# Configuration constants
SENTIMENT_CACHE_TTL = 300  # 5 minutes
NEWS_CACHE_TTL = 600  # 10 minutes
BATCH_SIZE = 20
MAX_CONCURRENT_REQUESTS = 10


@dataclass
class SentimentResult:
    """Data class for sentiment analysis results"""

    symbol: str
    score: float
    confidence: float
    sources: Dict[str, float]
    news_count: int
    timestamp: float


class OptimizedNewsAnalyzer:
    """
    Optimized news sentiment analyzer with:
    - Intelligent caching
    - Batch processing
    - Concurrent execution
    - Source weighting
    - Performance monitoring
    """

    def __init__(self):
        self.cache_lock = threading.Lock()
        self.source_weights = {
            "discord": 0.4,
            "cmc": 0.2,
            "reddit": 0.3,
            "cryptopanic": 0.2,
            "blog": 0.2,
            "mock": 0.1,
        }
        self.performance_stats = {
            "total_analyzed": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_processing_time": 0.0,
            "source_success_rates": {},
        }

    async def _load_news_data_cached(self, source: str) -> List[Dict[str, Any]]:
        """Load news data with caching"""
        cache_key = f"news_data_{source}"

        with self.cache_lock:
            cached_data = get_cached_data(cache_key)
            if cached_data:
                return cached_data

        try:
            file_path = os.path.join("backend", "data", f"{source}.json")

            if not os.path.exists(file_path):
                # Create empty file if it doesn't exist
                async with aiofiles.open(file_path, "w") as f:
                    await f.write("[]")
                return []

            async with aiofiles.open(file_path, "r") as f:
                content = await f.read()
                data = json.loads(content)

                if not isinstance(data, list):
                    logger.warning(f"Expected list for {source}, got {type(data)}")
                    return []

                # Cache the data
                with self.cache_lock:
                    set_cached_data(cache_key, data, NEWS_CACHE_TTL)

                return data

        except Exception as e:
            logger.error(f"Failed to load {source} news data: {e}")
            return []

    async def _analyze_sentiment_batch(self, texts: List[str]) -> List[float]:
        """Analyze sentiment for multiple texts in batch"""
        if not texts:
            return []

        loop = asyncio.get_event_loop()

        # Use thread pool for CPU-intensive sentiment analysis
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            tasks = [
                loop.run_in_executor(executor, self._analyze_single_sentiment, text)
                for text in texts
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle exceptions
            sentiment_scores = []
            for result in results:
                if isinstance(result, Exception):
                    logger.warning(f"Sentiment analysis failed: {result}")
                    sentiment_scores.append(0.0)
                else:
                    sentiment_scores.append(result)

            return sentiment_scores

    def _analyze_single_sentiment(self, text: str) -> float:
        """Analyze sentiment for a single text"""
        try:
            # Ensure TextBlob is used from the correct import
            blob = TextBlob(text)
            # Use getattr to safely access polarity and avoid Pylance cached_property issues
            return float(getattr(blob.sentiment, 'polarity', 0.0))
        except Exception as e:
            logger.warning(f"TextBlob analysis failed: {e}")
            return 0.0

    async def _get_source_sentiment(
        self, symbol: str, source: str
    ) -> Tuple[float, int]:
        """Get sentiment score from a specific source"""
        try:
            news_data = await self._load_news_data_cached(source)

            # Filter relevant news
            relevant_news = [
                item
                for item in news_data
                if isinstance(item, dict)
                and symbol.upper() in item.get("content", "").upper()
            ]

            if not relevant_news:
                return 0.0, 0

            # Extract content for sentiment analysis
            contents = [item.get("content", "") for item in relevant_news]

            # Analyze sentiment in batch
            sentiment_scores = await self._analyze_sentiment_batch(contents)

            # Calculate average sentiment
            if sentiment_scores:
                avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                return avg_sentiment, len(sentiment_scores)

            return 0.0, 0

        except Exception as e:
            logger.error(f"Failed to get {source} sentiment for {symbol}: {e}")
            return 0.0, 0

    async def _get_reddit_sentiment(self, symbol: str) -> Tuple[float, int]:
        """Get Reddit sentiment with fallback"""
        try:
            from reddit_github_alpha import fetch_signal_sentiment

            loop = asyncio.get_event_loop()
            score = await loop.run_in_executor(None, fetch_signal_sentiment, symbol)

            return score if score is not None else 0.0, 1 if score is not None else 0

        except Exception as e:
            logger.warning(f"Reddit sentiment fetch failed for {symbol}: {e}")
            return 0.0, 0

    async def _get_blog_sentiment(self, symbol: str) -> Tuple[float, int]:
        """Get blog sentiment with fallback"""
        if not USE_REAL_NEWS:
            return 0.0, 0

        try:
            # Try to import analyze_blog_sentiment safely
            try:
                import kryptomerch_scraper
                analyze_blog_sentiment = getattr(kryptomerch_scraper, "analyze_blog_sentiment", None)
                if analyze_blog_sentiment is None:
                    logger.warning(f"analyze_blog_sentiment not available for {symbol}")
                    return 0.0, 0
            except ImportError:
                logger.warning(f"kryptomerch_scraper module not available for {symbol}")
                return 0.0, 0

            loop = asyncio.get_event_loop()
            score = await loop.run_in_executor(
                None, analyze_blog_sentiment, symbol
            )

            return score if score is not None else 0.0, 1 if score is not None else 0

        except Exception as e:
            logger.warning(f"Blog sentiment fetch failed for {symbol}: {e}")
            return 0.0, 0

    def _generate_mock_sentiment(self, symbol: str) -> Tuple[float, int]:
        """Generate mock sentiment for testing"""
        import random

        mock_headlines = [
            f"{symbol.upper()} price surges as whales accumulate more tokens",
            f"Mixed outlook for {symbol.upper()} amid market volatility",
            f"{symbol.upper()} crashes after negative sentiment spreads on Twitter",
            f"Investors bullish on {symbol.upper()} ahead of upcoming update",
            f"Crypto experts warn of rug pull risks in {symbol.upper()}",
        ]

        selected_headlines = random.sample(
            mock_headlines, k=min(3, len(mock_headlines))
        )
        sentiments = [
            self._analyze_single_sentiment(headline) for headline in selected_headlines
        ]

        avg_sentiment = sum(sentiments) / len(sentiments) if sentiments else 0.0
        return avg_sentiment, len(sentiments)

    async def analyze_symbol_sentiment(self, symbol: str) -> SentimentResult:
        """
        Analyze sentiment for a single symbol from all sources
        """
        start_time = time.time()

        # Check cache first
        cache_key = f"sentiment_analysis_{symbol}"
        with self.cache_lock:
            cached_result = get_cached_data(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                return SentimentResult(**cached_result)

            self.performance_stats["cache_misses"] += 1

        try:
            # Fetch sentiment from all sources concurrently
            tasks = [
                self._get_source_sentiment(symbol, "discord_news"),
                self._get_source_sentiment(symbol, "cmc_news"),
                self._get_source_sentiment(symbol, "cryptopanic"),
                self._get_reddit_sentiment(symbol),
                self._get_blog_sentiment(symbol),
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            source_scores = {}
            source_counts = {}
            total_weighted_score = 0.0
            total_weight = 0.0
            total_news_count = 0

            source_names = ["discord", "cmc", "cryptopanic", "reddit", "blog"]

            for i, result in enumerate(results):
                source_name = source_names[i]

                if isinstance(result, Exception):
                    logger.warning(
                        f"Source {source_name} failed for {symbol}: {result}"
                    )
                    score, count = 0.0, 0
                else:
                    if isinstance(result, tuple) and len(result) == 2:
                        score, count = result
                    else:
                        logger.warning(
                            f"Unexpected result format from {source_name} for {symbol}: {result}"
                        )
                        score, count = 0.0, 0

                source_scores[source_name] = score
                source_counts[source_name] = count

                if count > 0:
                    weight = self.source_weights.get(source_name, 0.1)
                    total_weighted_score += score * weight
                    total_weight += weight
                    total_news_count += count

            # Add mock sentiment if no real data
            if total_news_count == 0:
                mock_score, mock_count = self._generate_mock_sentiment(symbol)
                source_scores["mock"] = mock_score
                source_counts["mock"] = mock_count
                total_weighted_score = mock_score * self.source_weights["mock"]
                total_weight = self.source_weights["mock"]
                total_news_count = mock_count

            # Calculate final sentiment score
            final_score = (
                total_weighted_score / total_weight if total_weight > 0 else 0.0
            )

            # Calculate confidence based on news count and source diversity
            confidence = min(
                1.0, total_news_count / 10.0
            )  # Max confidence at 10+ news items
            source_diversity = len([s for s in source_counts.values() if s > 0])
            confidence *= min(
                1.0, source_diversity / 3.0
            )  # Boost confidence with source diversity

            # Create result
            result = SentimentResult(
                symbol=symbol,
                score=round(final_score, 3),
                confidence=round(confidence, 3),
                sources=source_scores,
                news_count=total_news_count,
                timestamp=time.time(),
            )

            # Cache the result
            with self.cache_lock:
                set_cached_data(cache_key, result.__dict__, SENTIMENT_CACHE_TTL)

            # Update performance stats
            processing_time = time.time() - start_time
            self.performance_stats["total_analyzed"] += 1
            self.performance_stats["avg_processing_time"] = (
                self.performance_stats["avg_processing_time"]
                * (self.performance_stats["total_analyzed"] - 1)
                + processing_time
            ) / self.performance_stats["total_analyzed"]

            logger.info(
                f"Analyzed sentiment for {symbol}: {final_score:.3f} (confidence: {confidence:.3f}) in {processing_time:.2f}s"
            )

            return result

        except Exception as e:
            logger.error(f"Sentiment analysis failed for {symbol}: {e}")
            return SentimentResult(
                symbol=symbol,
                score=0.0,
                confidence=0.0,
                sources={},
                news_count=0,
                timestamp=time.time(),
            )

    async def batch_analyze_sentiment(
        self, symbols: List[str]
    ) -> List[SentimentResult]:
        """
        Batch analyze sentiment for multiple symbols
        """
        logger.info(f"Starting batch sentiment analysis for {len(symbols)} symbols")

        # Process in batches to control memory usage
        results = []

        for i in range(0, len(symbols), BATCH_SIZE):
            batch = symbols[i : i + BATCH_SIZE]
            logger.info(f"Processing sentiment batch {i//BATCH_SIZE + 1}: {batch}")

            # Limit concurrent executions
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

            async def analyze_with_semaphore(symbol):
                async with semaphore:
                    return await self.analyze_symbol_sentiment(symbol)

            # Process batch concurrently
            batch_tasks = [analyze_with_semaphore(symbol) for symbol in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Filter out exceptions
            valid_results = [
                result for result in batch_results if not isinstance(result, Exception)
            ]

            results.extend(valid_results)

            # Small delay between batches
            await asyncio.sleep(0.5)

        logger.info(
            f"Batch sentiment analysis complete. Processed {len(results)} symbols successfully"
        )
        return results

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            **self.performance_stats,
            "cache_hit_rate": (
                self.performance_stats["cache_hits"]
                / (
                    self.performance_stats["cache_hits"]
                    + self.performance_stats["cache_misses"]
                )
                if (
                    self.performance_stats["cache_hits"]
                    + self.performance_stats["cache_misses"]
                )
                > 0
                else 0
            ),
        }

    async def save_sentiment_log(self, result: SentimentResult):
        """Save sentiment result to log file"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "symbol": result.symbol,
                "score": result.score,
                "confidence": result.confidence,
                "sources": result.sources,
                "news_count": result.news_count,
            }

            log_file = os.path.join("backend", "data", "news_sentiment.jsonl")
            async with aiofiles.open(log_file, "a") as f:
                await f.write(json.dumps(log_entry) + "\n")

        except Exception as e:
            logger.error(f"Failed to save sentiment log: {e}")


# Global instance
optimized_news_analyzer = OptimizedNewsAnalyzer()


# Backward compatibility functions
async def get_combined_sentiment_score_optimized(symbol: str) -> Dict[str, Any]:
    """Optimized version of get_combined_sentiment_score"""
    result = await optimized_news_analyzer.analyze_symbol_sentiment(symbol)

    # Save to log
    await optimized_news_analyzer.save_sentiment_log(result)

    return {
        "score": result.score,
        "confidence": result.confidence,
        "news_snippets": [],  # For backward compatibility
        "sources": result.sources,
        "news_count": result.news_count,
    }


async def batch_get_sentiment_scores(symbols: List[str]) -> Dict[str, Dict[str, Any]]:
    """Batch get sentiment scores for multiple symbols"""
    results = await optimized_news_analyzer.batch_analyze_sentiment(symbols)

    # Save all to log
    for result in results:
        await optimized_news_analyzer.save_sentiment_log(result)

    return {
        result.symbol: {
            "score": result.score,
            "confidence": result.confidence,
            "sources": result.sources,
            "news_count": result.news_count,
        }
        for result in results
    }


def get_sentiment_performance_stats() -> Dict[str, Any]:
    """Get sentiment analysis performance statistics"""
    return optimized_news_analyzer.get_performance_stats()
