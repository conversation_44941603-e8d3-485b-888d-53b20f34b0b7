# ✅ strategy_ai.py
#
# AlphaPredatorBot — Strategy AI Module
#
# This module decides the trading strategy based on market trend, RSI,
# sentiment analysis, and volume signals.
#

import logging
from typing import Dict, List, Optional, Any
from strategy_evaluator import (
    evaluate_trend_following,
    evaluate_breakout_trading,
    evaluate_scalping,
    evaluate_swing_trading,
    evaluate_mean_reversion,
    evaluate_news_based_trading,
    evaluate_volume_spike_trading,
    evaluate_grid_trading,
    evaluate_event_driven_signals
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def decide_strategy(
    token: str,
    prices: list,
    high_prices: list,
    low_prices: list,
    volumes: list,
    sentiment_score: float,
    news_keywords: List[str],
    events: List[Dict[str, Any]],
    target_symbol: str,
    short_ma: Optional[float] = None,
    long_ma: Optional[float] = None,
    rsi: Optional[float] = None,
    macd: Optional[float] = None,
    macd_signal: Optional[float] = None,
    upper_band: Optional[float] = None,
    lower_band: Optional[float] = None,
    breakout_level: Optional[float] = None,
    volume_oscillator: Optional[float] = None,
    stochastic_k: Optional[float] = None,
    stochastic_d: Optional[float] = None,
    ichimoku_tenkan: Optional[float] = None,
    ichimoku_kijun: Optional[float] = None,
    ichimoku_senkou_a: Optional[float] = None,
    ichimoku_senkou_b: Optional[float] = None,
    ichimoku_chikou: Optional[float] = None,
    fib_levels: Optional[dict] = None,
    parabolic_sar: Optional[float] = None,
    atr: Optional[float] = None,
    current_price: Optional[float] = None
) -> str:
    """
    Decides which trading strategy to use based on various indicators and market conditions.
    """

    logger.info(f"[StrategyAI] Deciding strategy for {token}...")

    # Initialize a dictionary to hold strategy recommendations and their confidence/priority
    recommendations = {}

    # Evaluate Trend Following
    if short_ma is not None and long_ma is not None:
        trend_signal = evaluate_trend_following(prices, 10, 50) # Using 10 and 50 period SMAs for trend
        recommendations["TREND_FOLLOWING"] = trend_signal

    # Evaluate Breakout Trading
    if prices and volumes:
        breakout_signal = evaluate_breakout_trading(prices, volumes, 20)
        recommendations["BREAKOUT_TRADING"] = breakout_signal

    # Evaluate Scalping (requires very recent data, simplified here)
    if prices and volumes:
        scalping_signal = evaluate_scalping(prices, volumes)
        recommendations["SCALPING"] = scalping_signal

    # Evaluate Swing Trading
    if rsi is not None and macd is not None and macd_signal is not None and fib_levels is not None and current_price is not None:
        swing_signal = evaluate_swing_trading(prices, high_prices, low_prices, 14, 12, 26, 9)
        recommendations["SWING_TRADING"] = swing_signal

    # Evaluate Mean Reversion
    if prices and upper_band is not None and lower_band is not None:
        mean_reversion_signal = evaluate_mean_reversion(prices, 20, 2)
        recommendations["MEAN_REVERSION"] = mean_reversion_signal

    # Evaluate News-Based Trading
    news_signal = evaluate_news_based_trading(sentiment_score, news_keywords)
    recommendations["NEWS_BASED_TRADING"] = news_signal

    # Evaluate Volume Spike Trading
    if volumes and prices:
        volume_spike_signal = evaluate_volume_spike_trading(volumes, prices, 20)
        recommendations["VOLUME_SPIKE_TRADING"] = volume_spike_signal

    # Evaluate Grid Trading (simplified, assumes grid_levels are passed or derived)
    # For a real implementation, grid_levels would need to be dynamically set or configured
    if current_price is not None:
        # Example static grid levels for testing
        grid_levels = [current_price * 0.9, current_price * 0.95, current_price, current_price * 1.05, current_price * 1.1]
        grid_signal = evaluate_grid_trading(current_price, grid_levels)
        recommendations["GRID_TRADING"] = grid_signal

    # Evaluate Event-Driven Signals
    event_signal = evaluate_event_driven_signals(events, target_symbol)
    recommendations["EVENT_DRIVEN_TRADING"] = event_signal

    logger.info(f"[StrategyAI] Recommendations: {recommendations}")

    # --- Decision Hierarchy/Prioritization ---
    # Prioritize event-driven BUY signals
    if recommendations.get("EVENT_DRIVEN_TRADING") == "BUY":
        return "BUY_EVENT"

    logger.info(f"[StrategyAI] Recommendations: {recommendations}")

    # --- Decision Hierarchy/Prioritization ---
    # This is a simplified example. A more advanced AI would use machine learning
    # or a more complex rule engine to weigh these signals.

    # Prioritize strong signals
    if recommendations.get("BREAKOUT_TRADING") == "BUY":
        return "BUY_BREAKOUT"
    if recommendations.get("BREAKOUT_TRADING") == "SELL":
        return "SELL_BREAKOUT"

    if recommendations.get("VOLUME_SPIKE_TRADING") == "BUY":
        return "BUY_VOLUME_SPIKE"
    if recommendations.get("VOLUME_SPIKE_TRADING") == "SELL":
        return "SELL_VOLUME_SPIKE"

    if recommendations.get("NEWS_BASED_TRADING") == "BUY":
        return "BUY_NEWS"
    if recommendations.get("NEWS_BASED_TRADING") == "SELL":
        return "SELL_NEWS"

    # Then consider trend and mean reversion
    if recommendations.get("TREND_FOLLOWING") == "BUY":
        return "BUY_TREND"
    if recommendations.get("TREND_FOLLOWING") == "SELL":
        return "SELL_TREND"

    if recommendations.get("MEAN_REVERSION") == "BUY":
        return "BUY_MEAN_REVERSION"
    if recommendations.get("MEAN_REVERSION") == "SELL":
        return "SELL_MEAN_REVERSION"

    # Default to HOLD if no strong signals or conflicting signals
    return "HOLD"
