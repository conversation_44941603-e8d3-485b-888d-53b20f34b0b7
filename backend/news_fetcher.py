"""
News Fetcher - Alias for real_news_fetcher.py
"""

# Import all functions from real_news_fetcher
try:
    from real_news_fetcher import *
    from real_news_fetcher import fetch_real_news, get_latest_crypto_news

    # Create aliases for compatibility
    def fetch_news_real():
        """Fetch news - alias for fetch_real_news"""
        return fetch_real_news()

    def get_crypto_news_alias():
        """Get crypto news - alias for get_latest_crypto_news"""
        return get_latest_crypto_news()

    print("✅ News fetcher loaded from real_news_fetcher")
    # For backward compatibility, keep the original name
    fetch_news = fetch_news_real
    get_crypto_news = get_crypto_news_alias
except ImportError:
    # Fallback to basic news fetching
    import time

    def fetch_news():
        """Basic news fetching fallback"""
        return [
            {
                "title": "Bitcoin reaches new highs",
                "content": "Bitcoin continues its upward trend",
                "timestamp": time.time(),
                "source": "fallback",
            }
        ]

    def get_crypto_news():
        """Get crypto news fallback"""
        return fetch_news()

    print("⚠️ Using basic news fetching fallback")
