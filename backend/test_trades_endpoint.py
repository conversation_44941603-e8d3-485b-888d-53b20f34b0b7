#!/usr/bin/env python3
"""
Test script to verify the recent trades endpoint is working correctly
"""

import requests
import json
import sys
import os

# Add backend to path
sys.path.append(os.path.dirname(__file__))

def test_trades_endpoint():
    print('🧪 TESTING RECENT TRADES ENDPOINT')
    print('=' * 50)
    
    try:
        # Test the API endpoint
        print('\n1. 📊 TESTING API ENDPOINT...')
        response = requests.get('http://localhost:3005/api/trades/live', timeout=10)
        print(f'   Status Code: {response.status_code}')
        
        if response.status_code == 200:
            trades = response.json()
            print(f'   ✅ Response Type: {type(trades)}')
            print(f'   📊 Number of trades: {len(trades)}')
            
            if trades and len(trades) > 0:
                print('\n📋 RECENT TRADES:')
                for i, trade in enumerate(trades[:3], 1):
                    print(f'   {i}. {trade.get("token", "N/A")} {trade.get("type", "N/A").upper()} '
                          f'{trade.get("quantity", 0)} @ ${trade.get("price", 0):,.2f} '
                          f'(${trade.get("value", 0):,.2f}) - {trade.get("time", "N/A")}')
                
                # Check for BTC trades
                btc_trades = [t for t in trades if t.get('token', '').upper() == 'BTC']
                print(f'\n🔍 BTC TRADES FOUND: {len(btc_trades)}')
                
                if btc_trades:
                    btc = btc_trades[0]
                    print(f'   💰 Latest BTC: {btc.get("type", "N/A").upper()} '
                          f'{btc.get("quantity", 0)} BTC @ ${btc.get("price", 0):,.2f} '
                          f'(${btc.get("value", 0):,.2f})')
                    print('   ✅ BTC TRADE WILL SHOW IN DASHBOARD!')
                    return True
                else:
                    print('   ⚠️ No BTC trades found')
                    return False
            else:
                print('   ⚠️ No trades returned')
                return False
        else:
            print(f'   ❌ Endpoint failed: Status {response.status_code}')
            print(f'   Response: {response.text}')
            return False
            
    except Exception as e:
        print(f'   ❌ Test failed: {e}')
        return False

def test_function_directly():
    print('\n2. 📁 TESTING FUNCTION DIRECTLY...')
    try:
        from trade_logger import load_live_trades
        
        trades_data = load_live_trades()
        print(f'   ✅ Function returns: {type(trades_data)}')
        
        if isinstance(trades_data, dict) and 'trades' in trades_data:
            trades = trades_data['trades']
            print(f'   📊 Number of trades: {len(trades)}')
            
            if trades:
                # Check BTC trades
                btc_trades = [t for t in trades if t.get('token', '').upper() == 'BTC']
                print(f'   🔍 BTC trades in function: {len(btc_trades)}')
                
                if btc_trades:
                    btc = btc_trades[0]
                    print(f'   💰 BTC trade: {btc}')
                    return True
                else:
                    print('   ⚠️ No BTC trades in function')
                    return False
            else:
                print('   ⚠️ No trades in function')
                return False
        else:
            print(f'   ⚠️ Unexpected function response: {trades_data}')
            return False
            
    except Exception as e:
        print(f'   ❌ Function test failed: {e}')
        return False

if __name__ == "__main__":
    print('🎯 RECENT TRADES DIAGNOSTIC')
    print('=' * 60)
    
    # Test function first
    function_works = test_function_directly()
    
    # Test API endpoint
    api_works = test_trades_endpoint()
    
    print('\n🎯 DIAGNOSTIC RESULTS:')
    print('=' * 50)
    print(f'Function works: {"✅" if function_works else "❌"}')
    print(f'API endpoint works: {"✅" if api_works else "❌"}')
    
    if function_works and api_works:
        print('\n🎉 DASHBOARD SHOULD NOW SHOW BTC TRADES!')
        print('✅ Recent trades section will display:')
        print('   • BTC BUY transactions with correct amounts')
        print('   • Proper timestamps and formatting')
        print('   • Real trade data from CSV file')
    elif function_works and not api_works:
        print('\n⚠️ FUNCTION WORKS BUT API ENDPOINT FAILS')
        print('   • Data is available but API has issues')
        print('   • May need server restart')
    else:
        print('\n❌ BOTH FUNCTION AND API HAVE ISSUES')
        print('   • Need to investigate data loading')
