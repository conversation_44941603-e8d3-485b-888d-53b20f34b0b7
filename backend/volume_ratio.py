from utils.api_client import get
import logging

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def get_volume_ratio_kucoin(
    symbol: str, avg_volume: float, fallback: float = 1.0
) -> float:
    """
    Calculate volume ratio for a KuCoin token based on its current and average volume.

    Args:
        symbol (str): KuCoin trading pair symbol (e.g., "DOGE-USDT").
        avg_volume (float): Average historical volume to compare against.
        fallback (float): Default value to return if calculation fails.

    Returns:
        float: Volume ratio or fallback value.
    """
    try:
        url = f"https://api.kucoin.com/api/v1/market/stats?symbol={symbol}"
        response = get(url, timeout=10, cache_ttl=60)  # Cache volume ratio for 1 minute
        response.raise_for_status()
        try:
            json_data = response.json()
        except Exception:
            raise ValueError("Response content is not valid JSON")

        data = json_data.get("data", {})
        if not isinstance(data, dict):
            raise ValueError(f"Expected dict for volume data, got {type(data)}: {data}")

        vol_value = data.get("volValue")
        if not isinstance(vol_value, (int, float, str)):
            raise ValueError("Missing or invalid 'volValue' in response data")

        try:
            token_volume = float(vol_value)
        except ValueError:
            raise ValueError(f"Cannot convert volValue to float: {vol_value}")

        if avg_volume == 0:
            return fallback
        ratio = token_volume / avg_volume
        logger.info(f"📊 Volume ratio for {symbol}: {ratio:.2f}x")
        return round(ratio, 2)
    except Exception as e:
        logger.error(f"❌ Error calculating volume ratio for {symbol}: {e}")
        return fallback

def get_volume_ratio(symbol: str, avg_volume: float = 1000000.0) -> float:
    """
    Get volume ratio for a symbol - this is the function expected by tests.
    """
    try:
        logger.info(f"Getting volume ratio for {symbol}")
        return get_volume_ratio_kucoin(symbol, avg_volume)
    except Exception as e:
        logger.error(f"Error getting volume ratio for {symbol}: {e}")
        return 1.0  # Return default fallback

# Alias for backward compatibility
def calculate_volume_ratio(symbol: str, avg_volume: float = 1000000.0) -> float:
    """
    Alias for get_volume_ratio_kucoin for backward compatibility.
    """
    return get_volume_ratio_kucoin(symbol, avg_volume)
