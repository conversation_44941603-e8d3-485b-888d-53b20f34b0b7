#!/usr/bin/env python3
"""
🧪 MASTER TEST SUITE - COMPREHENSIVE SYSTEM TESTING
Tests ALL components: Bot, News, AI, Trading, Frontend Integration
"""

import sys
import os
import asyncio
import requests
import json
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class MasterTestSuite:
    def __init__(self):
        self.results = {}
        self.total_tests = 0
        self.passed_tests = 0
        
    def log_test(self, category, test_name, status, details=""):
        """Log test results"""
        if category not in self.results:
            self.results[category] = {}
        
        self.results[category][test_name] = {
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        self.total_tests += 1
        if status == "PASS":
            self.passed_tests += 1
            
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {category} - {test_name}: {details}")

    def test_core_imports(self):
        """Test all core module imports"""
        print("\n📦 TESTING CORE IMPORTS")
        print("-" * 50)
        
        modules = [
            "config", "tokenmetrics_client", "reddit_github_alpha", 
            "sentiment_engine", "discord_news_bot", "price_fetcher",
            "ai_core", "trade_engine", "live_runner", "main"
        ]
        
        for module in modules:
            try:
                __import__(module)
                self.log_test("IMPORTS", module, "PASS", "Module imported successfully")
            except Exception as e:
                self.log_test("IMPORTS", module, "FAIL", str(e))

    def test_tokenmetrics_integration(self):
        """Test TokenMetrics API and fallback"""
        print("\n🔧 TESTING TOKENMETRICS INTEGRATION")
        print("-" * 50)
        
        try:
            from tokenmetrics_client import TokenMetricsClient
            
            async def test_tokenmetrics():
                client = TokenMetricsClient()
                
                # Test BTC data
                btc_data = await client.get_token_data("BTC-USDT")
                if btc_data:
                    self.log_test("TOKENMETRICS", "BTC Data", "PASS", 
                                f"Source: {btc_data.source}, Confidence: {btc_data.confidence}")
                    return True
                else:
                    self.log_test("TOKENMETRICS", "BTC Data", "FAIL", "No data returned")
                    return False
            
            result = asyncio.run(test_tokenmetrics())
            
        except Exception as e:
            self.log_test("TOKENMETRICS", "Integration", "FAIL", str(e))

    def test_news_sources(self):
        """Test all news sources"""
        print("\n📰 TESTING NEWS SOURCES")
        print("-" * 50)
        
        # Test Reddit/GitHub
        try:
            from reddit_github_alpha import fetch_reddit_signals, fetch_github_signals
            
            reddit_signals = fetch_reddit_signals()
            github_signals = fetch_github_signals()
            
            self.log_test("NEWS", "Reddit Signals", "PASS", f"{len(reddit_signals)} signals")
            self.log_test("NEWS", "GitHub Signals", "PASS", f"{len(github_signals)} signals")
            
        except Exception as e:
            self.log_test("NEWS", "Reddit/GitHub", "FAIL", str(e))
        
        # Test Discord News
        try:
            from discord_news_bot import get_latest_discord_news, simulate_discord_news
            
            simulate_discord_news()
            discord_news = get_latest_discord_news(limit=5)
            
            self.log_test("NEWS", "Discord News", "PASS", f"{len(discord_news)} items")
            
        except Exception as e:
            self.log_test("NEWS", "Discord News", "FAIL", str(e))

    def test_sentiment_analysis(self):
        """Test sentiment analysis"""
        print("\n🧠 TESTING SENTIMENT ANALYSIS")
        print("-" * 50)
        
        try:
            from sentiment_engine import get_sentiment_score, get_combined_sentiment
            
            # Test basic sentiment
            btc_sentiment = get_sentiment_score("BTC")
            self.log_test("SENTIMENT", "Basic Sentiment", "PASS", f"BTC: {btc_sentiment:.3f}")
            
            # Test combined sentiment
            combined = get_combined_sentiment("BTC")
            sentiment_label = combined.get('combined_sentiment', 'unknown')
            confidence = combined.get('confidence', 0)
            
            self.log_test("SENTIMENT", "Combined Analysis", "PASS", 
                         f"BTC: {sentiment_label} (confidence: {confidence:.3f})")
            
        except Exception as e:
            self.log_test("SENTIMENT", "Analysis", "FAIL", str(e))

    def test_ai_decision_engine(self):
        """Test AI decision making"""
        print("\n🤖 TESTING AI DECISION ENGINE")
        print("-" * 50)
        
        try:
            from ai_core import get_ai_engine, analyze_token_comprehensive
            
            # Test AI engine initialization
            ai_engine = get_ai_engine()
            self.log_test("AI", "Engine Init", "PASS", f"Engine: {type(ai_engine).__name__}")
            
            # Test comprehensive analysis
            async def test_ai_analysis():
                analysis = await analyze_token_comprehensive("BTC", {
                    "price": 45000,
                    "volume": 1000000,
                    "sentiment": 0.6
                })
                return analysis
            
            result = asyncio.run(test_ai_analysis())
            decision = result.get('decision', 'UNKNOWN')
            confidence = result.get('confidence', 0)
            
            self.log_test("AI", "Token Analysis", "PASS", 
                         f"BTC: {decision} (confidence: {confidence:.2f})")
            
        except Exception as e:
            self.log_test("AI", "Decision Engine", "FAIL", str(e))

    def test_trading_engine(self):
        """Test trading functionality"""
        print("\n💰 TESTING TRADING ENGINE")
        print("-" * 50)
        
        try:
            from trade_engine import execute_trade_with_strategy
            from config import TRADING_MODE
            
            # Test trade execution (paper mode)
            async def test_trade():
                result = await execute_trade_with_strategy(
                    symbol="BTC-USDT",
                    action="BUY",
                    amount=100.0,
                    take_profit=46000.0,
                    stop_loss=44000.0,
                    strategy="test_strategy"
                )
                return result
            
            trade_result = asyncio.run(test_trade())
            status = trade_result.get('status', 'unknown')
            
            self.log_test("TRADING", "Trade Execution", "PASS" if status == "success" else "FAIL",
                         f"Mode: {TRADING_MODE}, Status: {status}")
            
        except Exception as e:
            self.log_test("TRADING", "Engine", "FAIL", str(e))

    def test_live_runner(self):
        """Test live trading runner"""
        print("\n⚡ TESTING LIVE RUNNER")
        print("-" * 50)
        
        try:
            from live_runner import run_live_cycle
            
            # Test live cycle (with timeout)
            async def test_live():
                try:
                    await asyncio.wait_for(run_live_cycle(), timeout=30.0)
                    return True
                except asyncio.TimeoutError:
                    return True  # Timeout is acceptable for this test
                except Exception as e:
                    raise e
            
            result = asyncio.run(test_live())
            self.log_test("LIVE_RUNNER", "Cycle Execution", "PASS", "Live cycle completed")
            
        except Exception as e:
            self.log_test("LIVE_RUNNER", "Execution", "FAIL", str(e))

    def test_frontend_apis(self):
        """Test frontend API endpoints"""
        print("\n🌐 TESTING FRONTEND API INTEGRATION")
        print("-" * 50)
        
        base_url = "http://localhost:8000"
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                self.log_test("API", "Health Endpoint", "PASS", "Server responding")
                server_running = True
            else:
                self.log_test("API", "Health Endpoint", "FAIL", f"Status: {response.status_code}")
                server_running = False
        except Exception as e:
            self.log_test("API", "Health Endpoint", "FAIL", str(e))
            server_running = False
        
        if not server_running:
            self.log_test("API", "Server Status", "FAIL", "Server not running - skipping API tests")
            return
        
        # Test API endpoints (without auth for basic connectivity)
        endpoints_to_test = [
            ("/api/tokenmetrics/BTC", "TokenMetrics API"),
            ("/api/news/live", "Live News API"),
            ("/api/news/token/BTC", "Token News API"),
            ("/api/tokens", "Tokens API"),
            ("/api/pnl", "PnL API")
        ]
        
        for endpoint, name in endpoints_to_test:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                # Accept 401 (auth required) as valid endpoint existence
                if response.status_code in [200, 401]:
                    self.log_test("API", name, "PASS", f"Endpoint exists (status: {response.status_code})")
                else:
                    self.log_test("API", name, "FAIL", f"Status: {response.status_code}")
            except Exception as e:
                self.log_test("API", name, "FAIL", str(e))

    def test_data_integration(self):
        """Test data flow integration"""
        print("\n🔗 TESTING DATA INTEGRATION")
        print("-" * 50)
        
        try:
            # Test token selection pipeline
            from token_selector import get_top_tokens_for_trading
            
            tokens = get_top_tokens_for_trading(10)
            self.log_test("INTEGRATION", "Token Selection", "PASS", f"{len(tokens)} tokens selected")
            
            # Test Alpha Predator flow
            from token_selector import alpha_predator_trading_flow
            
            async def test_flow():
                try:
                    result = await asyncio.wait_for(alpha_predator_trading_flow(), timeout=60.0)
                    return result
                except asyncio.TimeoutError:
                    return {"status": "timeout", "message": "Flow timeout (acceptable for test)"}
            
            flow_result = asyncio.run(test_flow())
            status = flow_result.get('status', 'unknown')
            
            self.log_test("INTEGRATION", "Alpha Predator Flow", 
                         "PASS" if status in ["success", "timeout"] else "FAIL",
                         f"Status: {status}")
            
        except Exception as e:
            self.log_test("INTEGRATION", "Data Flow", "FAIL", str(e))

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE SYSTEM TEST REPORT")
        print("=" * 80)
        
        # Category summary
        for category, tests in self.results.items():
            passed = sum(1 for test in tests.values() if test['status'] == 'PASS')
            total = len(tests)
            percentage = (passed / total * 100) if total > 0 else 0
            
            print(f"\n{category}: {passed}/{total} ({percentage:.1f}%)")
            
            for test_name, result in tests.items():
                status_icon = "✅" if result['status'] == "PASS" else "❌"
                print(f"  {status_icon} {test_name}: {result['details']}")
        
        # Overall summary
        overall_percentage = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"\n🎯 OVERALL SYSTEM STATUS")
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {overall_percentage:.1f}%")
        
        if overall_percentage >= 90:
            print("🎉 SYSTEM STATUS: EXCELLENT - Ready for production!")
        elif overall_percentage >= 75:
            print("✅ SYSTEM STATUS: GOOD - Minor issues to address")
        elif overall_percentage >= 50:
            print("⚠️ SYSTEM STATUS: NEEDS WORK - Several issues to fix")
        else:
            print("❌ SYSTEM STATUS: CRITICAL - Major issues require attention")
        
        return overall_percentage

    def run_all_tests(self):
        """Run all test categories"""
        print("🚀 STARTING COMPREHENSIVE SYSTEM TEST")
        print(f"⏰ Started at: {datetime.now()}")
        
        test_categories = [
            ("Core Imports", self.test_core_imports),
            ("TokenMetrics Integration", self.test_tokenmetrics_integration),
            ("News Sources", self.test_news_sources),
            ("Sentiment Analysis", self.test_sentiment_analysis),
            ("AI Decision Engine", self.test_ai_decision_engine),
            ("Trading Engine", self.test_trading_engine),
            ("Live Runner", self.test_live_runner),
            ("Frontend APIs", self.test_frontend_apis),
            ("Data Integration", self.test_data_integration)
        ]
        
        for category_name, test_func in test_categories:
            try:
                test_func()
            except Exception as e:
                print(f"❌ {category_name} test suite crashed: {e}")
        
        # Generate final report
        overall_score = self.generate_report()
        
        print(f"\n⏰ Completed at: {datetime.now()}")
        return overall_score

def main():
    """Main test execution"""
    suite = MasterTestSuite()
    score = suite.run_all_tests()
    
    # Exit with appropriate code
    exit(0 if score >= 90 else 1)

if __name__ == "__main__":
    main()
