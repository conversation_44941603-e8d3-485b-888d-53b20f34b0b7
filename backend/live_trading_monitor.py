#!/usr/bin/env python3
"""
Live Trading Monitoring System
Comprehensive monitoring for Alpha Predator live trading operations
"""

import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Import core systems
from enhanced_token_selector import get_enhanced_discover_tokens
from ai_clients.ai_request_manager import get_ai_decision_with_consensus
from kucoin_sdk_migration import kucoin_sdk
from pnl_dashboard import get_pnl_summary
import config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LiveTradingMonitor:
    def __init__(self):
        # Use config module directly
        self.monitoring_active = False
        self.last_check = None
        self.performance_metrics = {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "avg_trade_time": 0.0,
            "api_calls_today": 0,
            "errors_today": 0,
        }

    def _extract_pnl_value(self, pnl_data, key: str) -> float:
        """Extract PnL value from either list or dict format"""
        if isinstance(pnl_data, list):
            return sum([item.get(key, 0) for item in pnl_data])
        elif isinstance(pnl_data, dict):
            return pnl_data.get(key, 0)
        else:
            return 0.0

    def get_system_health(self) -> Dict[str, Any]:
        """Check overall system health"""
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "UNKNOWN",
            "components": {},
            "alerts": [],
        }

        # Check KuCoin connection
        try:
            balance = kucoin_sdk.get_account_balance()
            health_status["components"]["kucoin"] = {
                "status": "HEALTHY",
                "balance_usdt": balance.get("USDT", 0),
                "last_check": datetime.now().isoformat(),
            }
        except Exception as e:
            health_status["components"]["kucoin"] = {
                "status": "ERROR",
                "error": str(e),
                "last_check": datetime.now().isoformat(),
            }
            health_status["alerts"].append(f"KuCoin connection error: {e}")

        # Check AI systems
        try:
            # Quick AI test
            test_result = get_ai_decision_with_consensus(
                "Quick system health check for BTC", "BTC-USDT"
            )
            working_providers = len(
                [p for p in test_result["provider_results"] if p["confidence"] > 0.5]
            )

            health_status["components"]["ai_system"] = {
                "status": "HEALTHY" if working_providers >= 2 else "DEGRADED",
                "working_providers": working_providers,
                "total_providers": len(test_result["provider_results"]),
                "last_check": datetime.now().isoformat(),
            }

            if working_providers < 2:
                health_status["alerts"].append(
                    f"Only {working_providers}/4 AI providers working"
                )

        except Exception as e:
            health_status["components"]["ai_system"] = {
                "status": "ERROR",
                "error": str(e),
                "last_check": datetime.now().isoformat(),
            }
            health_status["alerts"].append(f"AI system error: {e}")

        # Check token selection
        try:
            tokens = get_enhanced_discover_tokens(limit=5)
            health_status["components"]["token_selection"] = {
                "status": "HEALTHY" if len(tokens) > 0 else "ERROR",
                "tokens_found": len(tokens),
                "last_check": datetime.now().isoformat(),
            }

            if len(tokens) == 0:
                health_status["alerts"].append("Token selection returning 0 tokens")

        except Exception as e:
            health_status["components"]["token_selection"] = {
                "status": "ERROR",
                "error": str(e),
                "last_check": datetime.now().isoformat(),
            }
            health_status["alerts"].append(f"Token selection error: {e}")

        # Determine overall status
        component_statuses = [
            comp["status"] for comp in health_status["components"].values()
        ]
        if all(status == "HEALTHY" for status in component_statuses):
            health_status["overall_status"] = "HEALTHY"
        elif any(status == "ERROR" for status in component_statuses):
            health_status["overall_status"] = "ERROR"
        else:
            health_status["overall_status"] = "DEGRADED"

        return health_status

    def get_trading_performance(self) -> Dict[str, Any]:
        """Get current trading performance metrics"""
        try:
            # Get PnL summary
            pnl_data = get_pnl_summary()

            # Load trade logs if available
            recent_trades = []
            try:
                import pandas as pd
                import os

                trade_log_path = "backend/data/trade_logs.csv"
                if os.path.exists(trade_log_path):
                    df = pd.read_csv(trade_log_path)
                    recent_trades = df.tail(50).to_dict("records")
            except Exception as e:
                logger.warning(f"Could not load trade logs: {e}")

            # Calculate performance metrics
            total_trades = len(recent_trades)
            successful_trades = len(
                [t for t in recent_trades if float(t.get("pnl", 0)) > 0]
            )

            performance = {
                "timestamp": datetime.now().isoformat(),
                "total_trades_today": total_trades,
                "successful_trades": successful_trades,
                "win_rate": (
                    (successful_trades / total_trades * 100) if total_trades > 0 else 0
                ),
                "total_pnl": self._extract_pnl_value(pnl_data, "total_pnl"),
                "realized_pnl": self._extract_pnl_value(pnl_data, "realized_pnl"),
                "unrealized_pnl": self._extract_pnl_value(pnl_data, "unrealized_pnl"),
                "best_trade": max(
                    [float(t.get("pnl", 0)) for t in recent_trades], default=0
                ),
                "worst_trade": min(
                    [float(t.get("pnl", 0)) for t in recent_trades], default=0
                ),
                "avg_trade_pnl": sum([float(t.get("pnl", 0)) for t in recent_trades])
                / max(total_trades, 1),
                "recent_trades": recent_trades[:10],  # Last 10 trades
            }

            return performance

        except Exception as e:
            logger.error(f"Error getting trading performance: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "total_trades_today": 0,
                "win_rate": 0,
                "total_pnl": 0,
            }

    def get_risk_metrics(self) -> Dict[str, Any]:
        """Calculate current risk exposure"""
        try:
            # Get current portfolio
            balance = kucoin_sdk.get_account_balance()

            # Get open positions (if any)
            # This would need to be implemented based on your position tracking

            risk_metrics = {
                "timestamp": datetime.now().isoformat(),
                "total_balance_usdt": balance.get("USDT", 0),
                "max_position_size": getattr(config, "MAX_BUY_AMOUNT", 100),
                "current_exposure": 0,  # Would calculate from open positions
                "risk_percentage": 0,  # Current risk as % of total balance
                "max_daily_loss": getattr(config, "MAX_BUY_AMOUNT", 100)
                * 0.1
                * 5,  # 10% loss on 5 trades
                "trades_remaining_today": max(
                    0,
                    getattr(config, "MAX_DAILY_TRADES", 300)
                    - self.performance_metrics["total_trades"],
                ),
            }

            return risk_metrics

        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "total_balance_usdt": 0,
                "risk_percentage": 0,
            }

    def generate_monitoring_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        logger.info("🔍 Generating live trading monitoring report...")

        report = {
            "timestamp": datetime.now().isoformat(),
            "system_health": self.get_system_health(),
            "trading_performance": self.get_trading_performance(),
            "risk_metrics": self.get_risk_metrics(),
            "recommendations": [],
        }

        # Generate recommendations based on status
        health = report["system_health"]
        performance = report["trading_performance"]

        if health["overall_status"] == "ERROR":
            report["recommendations"].append(
                "🚨 CRITICAL: System errors detected - trading should be paused"
            )
        elif health["overall_status"] == "DEGRADED":
            report["recommendations"].append(
                "⚠️ WARNING: System degraded - monitor closely"
            )

        if performance["win_rate"] < 40:
            report["recommendations"].append(
                "📉 Low win rate - consider adjusting strategy"
            )
        elif performance["win_rate"] > 70:
            report["recommendations"].append(
                "📈 High win rate - system performing well"
            )

        if performance["total_pnl"] < -100:
            report["recommendations"].append(
                "💰 Significant losses - consider reducing position sizes"
            )

        return report

    def start_monitoring(self, interval_minutes: int = 5):
        """Start continuous monitoring"""
        logger.info(
            f"🚀 Starting live trading monitoring (interval: {interval_minutes}min)"
        )
        self.monitoring_active = True

        while self.monitoring_active:
            try:
                report = self.generate_monitoring_report()

                # Save report
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                with open(f"monitoring_report_{timestamp}.json", "w") as f:
                    json.dump(report, f, indent=2)

                # Log key metrics
                health = report["system_health"]["overall_status"]
                pnl = report["trading_performance"]["total_pnl"]
                trades = report["trading_performance"]["total_trades_today"]
                win_rate = report["trading_performance"]["win_rate"]

                logger.info(
                    f"📊 Status: {health} | PnL: ${pnl:.2f} | Trades: {trades} | Win Rate: {win_rate:.1f}%"
                )

                # Check for alerts
                alerts = report["system_health"]["alerts"]
                if alerts:
                    for alert in alerts:
                        logger.warning(f"🚨 ALERT: {alert}")

                self.last_check = datetime.now()
                time.sleep(interval_minutes * 60)

            except KeyboardInterrupt:
                logger.info("🛑 Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
                time.sleep(30)  # Wait 30s before retrying

        self.monitoring_active = False


def main():
    """Run monitoring system"""
    monitor = LiveTradingMonitor()

    # Generate single report
    print("🔍 Alpha Predator Live Trading Monitor")
    print("=" * 50)

    report = monitor.generate_monitoring_report()

    # Display key information
    health = report["system_health"]
    performance = report["trading_performance"]
    risk = report["risk_metrics"]

    print(f"\n🏥 SYSTEM HEALTH: {health['overall_status']}")
    for component, status in health["components"].items():
        print(f"   • {component}: {status['status']}")

    print(f"\n📊 TRADING PERFORMANCE:")
    print(f"   • Total PnL: ${performance['total_pnl']:.2f}")
    print(f"   • Trades Today: {performance['total_trades_today']}")
    print(f"   • Win Rate: {performance['win_rate']:.1f}%")

    print(f"\n⚖️ RISK METRICS:")
    print(f"   • Balance: ${risk['total_balance_usdt']:.2f}")
    print(f"   • Max Position: ${risk['max_position_size']:.2f}")

    if report["recommendations"]:
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in report["recommendations"]:
            print(f"   {rec}")

    # Save report
    with open("latest_monitoring_report.json", "w") as f:
        json.dump(report, f, indent=2)

    print(f"\n💾 Report saved to latest_monitoring_report.json")

    return report


if __name__ == "__main__":
    main()
