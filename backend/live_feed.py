# backend/live_feed.py
import json
from fastapi import APIRouter

router = APIRouter()

@router.get("/dashboard/live_feed")
def get_live_feed():
    try:
        with open("backend/data/ai_logic.json", "r") as f:
            trade_logs = [json.loads(line.strip()) for line in f if line.strip()]
        return {
            "status": "success",
            "data": trade_logs[-10:]  # return last 10 trades
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }