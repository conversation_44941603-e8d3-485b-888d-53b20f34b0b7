import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import coingecko_data
from coingecko_data import fetch_coingecko_volumes

from utils.api_client import get
import requests # Keep requests for now, as aiohttp is used in the async function
import time
import logging
import aiohttp
from typing import List, Dict, Optional, Any

KUCOIN_API_BASE = "https://api.kucoin.com"

# Setup logger
logger = logging.getLogger("kucoin_data")
logging.basicConfig(level=logging.INFO)

def get_token_volume_ratio(token_data: Dict[str, Any]) -> float:
    """
    Calculates the volume-to-market-cap ratio for scoring token interest.
    """
    try:
        volume = token_data.get("volume") or token_data.get("vol") or 0
        market_cap = token_data.get("cg_market_cap") or token_data.get("market_cap") or 0
        if volume > 0 and market_cap > 0:
            return volume / market_cap
    except Exception as e:
        logger.warning(f"⚠️ Error calculating volume ratio: {e}")
    return 0.0

def fetch_kucoin_listed_tokens(limit: int = 100, coingecko_volumes: Optional[Dict[str, Dict]] = None) -> List[Dict[str, Any]]:
    """
    Fetches USDT pairs listed on KuCoin with volume and market info.
    """
    logger.info("🟡 Fetching KuCoin tokens from public API")
    try:
        url = f"{KUCOIN_API_BASE}/api/v1/market/allTickers"
        resp = get(url, timeout=10, cache_ttl=300) # Cache KuCoin listed tokens for 5 minutes
        data = resp.json()

        if data.get("code") != "200000":
            raise ValueError("Invalid response code from KuCoin")

        tickers = data["data"]["ticker"]
        filtered = []

        for t in tickers:
            if not isinstance(t, dict):
                continue
            symbol = t.get("symbol", "")
            if not symbol.endswith("USDT"):
                continue

            token_name = symbol.replace("-USDT", "")
            vol_raw = t.get("volValue")
            cg_volume, cg_market_cap, cg_change = None, None, None

            if vol_raw is None or float(vol_raw) == 0:
                if coingecko_volumes:
                    cg_data = coingecko_volumes.get(token_name.lower())
                    if cg_data:
                        vol_raw = cg_data.get("volume")
                        cg_volume = cg_data.get("volume")
                        cg_market_cap = cg_data.get("market_cap")
                        cg_change = cg_data.get("change")

            if vol_raw is None or float(vol_raw) == 0:
                logger.debug(f"⚠️ Token missing volume fields: {symbol}")
                continue

            try:
                vol = float(vol_raw)
                change_ratio = float(t.get("changeRate", 0))
                last = float(t.get("last", 0))
            except Exception as e:
                logger.warning(f"⚠️ Skipping token {symbol} due to parse error: {e}")
                continue

            filtered.append({
                "token": token_name,
                "symbol": symbol,
                "price": last,
                "volume": vol,
                "volume_ratio": change_ratio,
                "is_spike": abs(change_ratio) > 0.2,
                "cg_volume": cg_volume,
                "cg_market_cap": cg_market_cap,
                "cg_change": cg_change
            })

        filtered.sort(key=lambda x: x["volume"], reverse=True)
        top_tokens = filtered[:limit]
        logger.info(f"✅ Top {len(top_tokens)} tokens selected.")
        return top_tokens

    except Exception as e:
        logger.error(f"🔴 KuCoin fetch error: {e}")
        return []

async def fetch_all_kucoin_tokens() -> List[Dict[str, Any]]:
    logger.info("🚀 Starting fetch_all_kucoin_tokens()")
    url = f"{KUCOIN_API_BASE}/api/v1/market/allTickers"

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    raise ValueError(f"KuCoin API returned {response.status}")
                data = await response.json()
    except Exception as e:
        logger.error(f"🔴 KuCoin async fetch error: {e}")
        return []

    tickers = data.get("data", {}).get("ticker", [])
    top_100 = tickers[:100]
    logger.info(f"✅ Retrieved {len(top_100)} tokens")

    simplified = []
    for t in top_100:
        if not isinstance(t, dict):
            continue
        symbol = t.get("symbol", "")
        if not symbol.endswith("USDT"):
            continue

        token_name = symbol.replace("-USDT", "")
        last = t.get("last")
        vol = t.get("volValue")

        if last is None or vol is None:
            cg_data = await coingecko_data.fetch_coingecko_data(token_name)
            if cg_data:
                simplified.append({
                    "symbol": symbol,
                    "token_name": token_name,
                    "price": cg_data.get("price", 0),
                    "vol": cg_data.get("volume", 0),
                    "market_cap": cg_data.get("market_cap", 0)
                })
            continue

        simplified.append({
            "symbol": symbol,
            "token_name": token_name,
            "price": float(last),
            "vol": float(vol),
            "market_cap": 0
        })

    logger.info(f"✅ Final simplified token count: {len(simplified)}")
    return simplified

async def fetch_kucoin_candlestick_data(symbol: str, interval: str = "1hour", limit: int = 90) -> List[List[Any]]:
    """
    Fetches historical candlestick data for a given symbol and interval from KuCoin.
    Intervals: 1min, 3min, 5min, 15min, 30min, 1hour, 2hour, 4hour, 6hour, 8hour, 12hour, 1day, 1week.
    Returns a list of lists, where each inner list is [timestamp, open, close, high, low, volume, turnover].
    """
    logger.info(f"Fetching {limit} {interval} candlesticks for {symbol} from KuCoin...")
    url = f"{KUCOIN_API_BASE}/api/v1/market/candles?symbol={symbol}&type={interval}&limit={limit}"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
                data = await response.json()
                if data.get("code") == "200000":
                    # KuCoin returns [timestamp, open, close, high, low, volume, turnover]
                    # Convert string numbers to floats
                    candlesticks = []
                    for entry in data["data"]:
                        candlesticks.append([
                            int(entry[0]),  # timestamp
                            float(entry[1]),  # open
                            float(entry[2]),  # close
                            float(entry[3]),  # high
                            float(entry[4]),  # low
                            float(entry[5]),  # volume
                            float(entry[6])   # turnover
                        ])
                    logger.info(f"Successfully fetched {len(candlesticks)} candlesticks for {symbol}.")
                    return candlesticks
                else:
                    logger.error(f"KuCoin API error for {symbol}: {data.get('msg', 'Unknown error')}")
                    return []
    except aiohttp.ClientError as e:
        logger.error(f"Network or HTTP error fetching candlesticks for {symbol}: {e}")
        return []
    except Exception as e:
        logger.error(f"An unexpected error occurred fetching candlesticks for {symbol}: {e}", exc_info=True)
        return []

def get_24h_volume(symbol: str) -> float:
    """
    Fetches the 24-hour volume for a specific symbol from KuCoin.
    """
    logger.info(f"Fetching 24h volume for {symbol}")
    try:
        url = f"{KUCOIN_API_BASE}/api/v1/market/allTickers"
        resp = get(url, timeout=10, cache_ttl=300) # Cache KuCoin listed tokens for 5 minutes
        data = resp.json()

        if data.get("code") != "200000":
            raise ValueError("Invalid response code from KuCoin")

        tickers = data["data"]["ticker"]
        for t in tickers:
            if t.get("symbol") == symbol:
                return float(t.get("volValue", 0))
        return 0.0
    except Exception as e:
        logger.error(f"🔴 KuCoin fetch error for {symbol}: {e}")
        return 0.0

def fetch_kucoin_spike_tokens(threshold: float = 0.2, limit: int = 20) -> List[Dict[str, Any]]:
    """
    Detects spike tokens using change rate above the given threshold.
    """
    logger.info("📈 Detecting spike tokens from KuCoin")
    try:
        all_tokens = fetch_kucoin_listed_tokens(limit=500)
        spike_tokens = [t for t in all_tokens if abs(t["volume_ratio"]) > threshold]
        spike_tokens.sort(key=lambda x: abs(x["volume_ratio"]), reverse=True)
        top_spikes = spike_tokens[:limit]
        logger.info(f"✅ Found {len(top_spikes)} spike tokens")
        return top_spikes
    except Exception as e:
        logger.error(f"❌ Error in fetch_kucoin_spike_tokens: {e}")
        return []

def fetch_kucoin_data(symbol: str, interval: str = "1hour", limit: int = 100) -> List[Dict[str, Any]]:
    """
    Synchronous wrapper for fetching KuCoin candlestick data.
    Returns data in a format compatible with the rest of the system.
    """
    logger.info(f"Fetching {limit} {interval} candles for {symbol} from KuCoin...")
    
    try:
        url = f"{KUCOIN_API_BASE}/api/v1/market/candles?symbol={symbol}&type={interval}&limit={limit}"
        resp = get(url, timeout=10, cache_ttl=60)
        data = resp.json()
        
        if data.get("code") != "200000":
            logger.error(f"❌ KuCoin API error for {symbol}: {data.get('msg', 'Unknown error')}")
            return []
        
        # Convert KuCoin format to standard format
        candlesticks = []
        for entry in data["data"]:
            candlesticks.append({
                "timestamp": int(entry[0]),
                "open": float(entry[1]),
                "close": float(entry[2]),
                "high": float(entry[3]),
                "low": float(entry[4]),
                "volume": float(entry[5]),
                "turnover": float(entry[6])
            })
        
        logger.info(f"Successfully fetched {len(candlesticks)} candlesticks for {symbol}.")
        return candlesticks
        
    except Exception as e:
        logger.error(f"❌ KuCoin API error for {symbol}: {e}")
        return []
