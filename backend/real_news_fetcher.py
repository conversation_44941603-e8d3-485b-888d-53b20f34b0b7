"""
Real News Fetcher for AlphaPredator <PERSON><PERSON>
Fetches real-time crypto news from multiple sources with fallback mechanisms
"""

import requests
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import os
from pathlib import Path
import xml.etree.ElementTree as ET

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealNewsFetcher:
    def __init__(self):
        self.data_dir = Path("backend/data")
        self.data_dir.mkdir(exist_ok=True)
        self.news_file = self.data_dir / "real_news.json"
        self.log_file = Path("backend/logs/real_news_fetch.log")
        self.log_file.parent.mkdir(exist_ok=True)
        
        # News sources configuration
        self.sources = {
            "cryptopanic": {
                "url": "https://cryptopanic.com/api/v1/posts/",
                "params": {"public": "true", "kind": "news", "filter": "hot"},
                "enabled": True
            },
            "coindesk": {
                "url": "https://www.coindesk.com/arc/outboundfeeds/rss/",
                "enabled": True
            },
            "cointelegraph": {
                "url": "https://cointelegraph.com/rss",
                "enabled": True
            }
        }

    def log_message(self, message: str):
        """Log message to file and console"""
        timestamp = datetime.utcnow().isoformat()
        log_entry = f"[{timestamp}] {message}\n"
        
        with open(self.log_file, "a") as f:
            f.write(log_entry)
        logger.info(message)

    def calculate_basic_sentiment(self, text: str) -> float:
        """Calculate basic sentiment score from text"""
        positive_words = ['bullish', 'surge', 'pump', 'moon', 'rocket', 'buy', 'long', 'profit', 
                         'gain', 'rise', 'up', 'high', 'strong', 'positive', 'optimistic', 'rally']
        negative_words = ['bearish', 'crash', 'dump', 'rug', 'scam', 'fail', 'sell', 'short', 
                         'loss', 'drop', 'fall', 'down', 'low', 'weak', 'negative', 'pessimistic']

        text_lower = text.lower()
        pos_count = sum(1 for word in positive_words if word in text_lower)
        neg_count = sum(1 for word in negative_words if word in text_lower)

        if pos_count + neg_count == 0:
            return 0.0
        return (pos_count - neg_count) / (pos_count + neg_count)

    def fetch_cryptopanic_news(self) -> List[Dict]:
        """Fetch news from CryptoPanic API"""
        try:
            url = self.sources["cryptopanic"]["url"]
            params = self.sources["cryptopanic"]["params"]
            headers = {"User-Agent": "Mozilla/5.0 (compatible; AlphaPredatorBot/1.0)"}
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            articles = data.get("results", [])
            
            processed_articles = []
            for article in articles:
                processed_article = {
                    "title": article.get("title", ""),
                    "url": article.get("url", ""),
                    "published_at": article.get("published_at", datetime.utcnow().isoformat()),
                    "source": "CryptoPanic",
                    "content": article.get("title", ""),
                    "sentiment_score": self.calculate_basic_sentiment(article.get("title", "")),
                    "fetched_at": datetime.utcnow().isoformat()
                }
                processed_articles.append(processed_article)
            
            self.log_message(f"✅ Fetched {len(processed_articles)} articles from CryptoPanic")
            return processed_articles
            
        except Exception as e:
            self.log_message(f"❌ CryptoPanic fetch failed: {e}")
            return []

    def fetch_rss_news(self, source_name: str, rss_url: str) -> List[Dict]:
        """Fetch news from RSS feeds"""
        try:
            headers = {"User-Agent": "Mozilla/5.0 (compatible; AlphaPredatorBot/1.0)"}
            response = requests.get(rss_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            articles = []
            
            # Handle different RSS formats
            items = root.findall('.//item') or root.findall('.//{http://www.w3.org/2005/Atom}entry')
            
            for item in items[:20]:  # Limit to 20 articles
                title = ""
                link = ""
                pub_date = datetime.utcnow().isoformat()
                description = ""
                
                # Extract title
                title_elem = item.find('title') or item.find('{http://www.w3.org/2005/Atom}title')
                if title_elem is not None:
                    title = title_elem.text or ""
                
                # Extract link
                link_elem = item.find('link') or item.find('{http://www.w3.org/2005/Atom}link')
                if link_elem is not None:
                    if hasattr(link_elem, 'get') and link_elem.get('href'):
                        link = link_elem.get('href')
                    else:
                        link = link_elem.text or ""
                
                # Extract description
                desc_elem = item.find('description') or item.find('{http://www.w3.org/2005/Atom}summary')
                if desc_elem is not None:
                    description = desc_elem.text or ""
                
                # Extract publication date
                date_elem = item.find('pubDate') or item.find('{http://www.w3.org/2005/Atom}published')
                if date_elem is not None:
                    pub_date = date_elem.text or datetime.utcnow().isoformat()
                
                if title:  # Only add if we have a title
                    article = {
                        "title": title,
                        "url": link,
                        "published_at": pub_date,
                        "source": source_name,
                        "content": f"{title} {description}",
                        "sentiment_score": self.calculate_basic_sentiment(f"{title} {description}"),
                        "fetched_at": datetime.utcnow().isoformat()
                    }
                    articles.append(article)
            
            self.log_message(f"✅ Fetched {len(articles)} articles from {source_name}")
            return articles
            
        except Exception as e:
            self.log_message(f"❌ {source_name} RSS fetch failed: {e}")
            return []

    def fetch_all_news(self) -> List[Dict]:
        """Fetch news from all enabled sources"""
        all_articles = []
        
        # Fetch from CryptoPanic
        if self.sources["cryptopanic"]["enabled"]:
            cryptopanic_articles = self.fetch_cryptopanic_news()
            all_articles.extend(cryptopanic_articles)
        
        # Fetch from RSS sources
        for source_name, config in self.sources.items():
            if source_name != "cryptopanic" and config["enabled"]:
                rss_articles = self.fetch_rss_news(source_name.title(), config["url"])
                all_articles.extend(rss_articles)
        
        # Remove duplicates based on title
        seen_titles = set()
        unique_articles = []
        for article in all_articles:
            title = article.get("title", "").lower()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_articles.append(article)
        
        self.log_message(f"✅ Total unique articles fetched: {len(unique_articles)}")
        return unique_articles

    def save_news_to_file(self) -> bool:
        """Save fetched news to file"""
        try:
            articles = self.fetch_all_news()
            
            # Load existing articles to avoid duplicates
            existing_articles = []
            if self.news_file.exists():
                try:
                    with open(self.news_file, 'r') as f:
                        existing_articles = json.load(f)
                except json.JSONDecodeError:
                    self.log_message("⚠️ Error reading existing news file, starting fresh")
                    existing_articles = []
            
            # Combine and deduplicate
            existing_titles = {article.get("title", "").lower() for article in existing_articles}
            new_articles = [article for article in articles 
                          if article.get("title", "").lower() not in existing_titles]
            
            # Keep only recent articles (last 7 days) plus new ones
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            recent_articles = []
            
            for article in existing_articles:
                try:
                    article_date = datetime.fromisoformat(article.get("fetched_at", "").replace('Z', '+00:00'))
                    if article_date > cutoff_date:
                        recent_articles.append(article)
                except:
                    # Keep articles with invalid dates
                    recent_articles.append(article)
            
            # Combine recent + new articles
            all_articles_final = recent_articles + new_articles
            
            # Save to file
            with open(self.news_file, 'w') as f:
                json.dump(all_articles_final, f, indent=2)
            
            self.log_message(f"✅ Saved {len(new_articles)} new articles, total: {len(all_articles_final)}")
            return True
            
        except Exception as e:
            self.log_message(f"❌ Error saving news to file: {e}")
            return False

    def get_latest_news(self, limit: int = 50) -> List[Dict]:
        """Get latest news articles"""
        try:
            if self.news_file.exists():
                with open(self.news_file, 'r') as f:
                    articles = json.load(f)
                
                # Sort by published date (newest first)
                sorted_articles = sorted(articles, 
                                       key=lambda x: x.get("published_at", ""), 
                                       reverse=True)
                return sorted_articles[:limit]
            else:
                self.log_message("⚠️ No news file found, fetching fresh news")
                return self.fetch_all_news()[:limit]
                
        except Exception as e:
            self.log_message(f"❌ Error getting latest news: {e}")
            return []

# Convenience functions for easy import
def fetch_real_news() -> List[Dict]:
    """Fetch real news from all sources"""
    fetcher = RealNewsFetcher()
    return fetcher.fetch_all_news()

def get_latest_crypto_news(limit: int = 50) -> List[Dict]:
    """Get latest crypto news"""
    fetcher = RealNewsFetcher()
    return fetcher.get_latest_news(limit)

def update_news_database() -> bool:
    """Update the news database with latest articles"""
    fetcher = RealNewsFetcher()
    return fetcher.save_news_to_file()

if __name__ == "__main__":
    # Test the news fetcher
    fetcher = RealNewsFetcher()
    print("🔍 Testing Real News Fetcher...")
    
    # Fetch and save news
    success = fetcher.save_news_to_file()
    if success:
        print("✅ News fetching and saving successful")
        
        # Get latest news
        latest = fetcher.get_latest_news(10)
        print(f"📰 Latest {len(latest)} articles:")
        for i, article in enumerate(latest[:5], 1):
            print(f"  {i}. {article.get('title', 'No title')[:60]}...")
    else:
        print("❌ News fetching failed")
