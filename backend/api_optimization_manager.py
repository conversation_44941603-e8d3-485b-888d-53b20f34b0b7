"""
API Optimization Manager
Handles intelligent API call optimization, caching, and fallback mechanisms
to prevent rate limiting and improve system efficiency.
"""

import logging
import time
import asyncio
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from collections import defaultdict
import json

from cache import get_cached_data, set_cached_data
from config import API_CLIENT_MIN_DELAY

logger = logging.getLogger(__name__)

class APIOptimizationManager:
    """
    Manages API calls across the system to prevent rate limiting and optimize performance
    """
    
    def __init__(self):
        self.api_call_history: Dict[str, List[float]] = defaultdict(list)
        self.failed_tokens: Set[str] = set()
        # Enhanced rate limits with proper API specifications
        self.rate_limits = {
            'coingecko': {
                'calls_per_minute': 10,  # Free tier: 10-50 calls/minute (conservative)
                'calls_per_hour': 1000,  # Hourly limit
                'delay': 6.5,  # 60/10 = 6 seconds + buffer
                'burst_limit': 5,  # Max burst calls
                'reset_window': 60  # Reset window in seconds
            },
            'kucoin': {
                'calls_per_minute': 100,
                'calls_per_hour': 6000,
                'delay': 0.6,
                'burst_limit': 10,
                'reset_window': 60
            },
            'tokenmetrics': {
                'calls_per_minute': 60,
                'calls_per_hour': 3600,
                'delay': 1.0,
                'burst_limit': 8,
                'reset_window': 60
            }
        }
        
        # Track API call windows for precise rate limiting
        self.api_windows = {}
        self.next_allowed_call = {}
        
        # Enhanced token mapping for CoinGecko
        self.coingecko_token_map = {
            # Major tokens
            "btc": "bitcoin",
            "eth": "ethereum", 
            "bnb": "binancecoin",
            "ada": "cardano",
            "dot": "polkadot",
            "link": "chainlink",
            "ltc": "litecoin",
            "bch": "bitcoin-cash",
            "xlm": "stellar",
            "vet": "vechain",
            "trx": "tron",
            "eos": "eos",
            "xmr": "monero",
            "xtz": "tezos",
            "atom": "cosmos",
            "neo": "neo",
            "mkr": "maker",
            "dash": "dash",
            "etc": "ethereum-classic",
            "zec": "zcash",
            "uni": "uniswap",
            "aave": "aave",
            "comp": "compound-governance-token",
            "sushi": "sushi",
            "1inch": "1inch",
            "snx": "havven",
            "crv": "curve-dao-token",
            "yfi": "yearn-finance",
            "bal": "balancer",
            "ren": "republic-protocol",
            
            # Additional popular tokens
            "sol": "solana",
            "avax": "avalanche-2",
            "matic": "matic-network",
            "ftm": "fantom",
            "algo": "algorand",
            "icp": "internet-computer",
            "theta": "theta-token",
            "fil": "filecoin",
            "xrp": "ripple",
            "doge": "dogecoin",
            "shib": "shiba-inu",
            "luna": "terra-luna",
            "near": "near",
            "flow": "flow",
            "sand": "the-sandbox",
            "mana": "decentraland",
            "grt": "the-graph",
            "lrc": "loopring",
            "ens": "ethereum-name-service",
            "imx": "immutable-x",
            "apt": "aptos",
            "op": "optimism",
            "arb": "arbitrum",
            
            # DeFi tokens
            "cake": "pancakeswap-token",
            "rune": "thorchain",
            "alpha": "alpha-finance",
            "bnt": "bancor",
            "kcs": "kucoin-shares",
            "ht": "huobi-token",
            "okb": "okb",
            "cro": "crypto-com-chain",
            "ftt": "ftx-token",
            
            # Meme/Community tokens
            "pepe": "pepe",
            "floki": "floki",
            "bonk": "bonk",
            
            # Layer 2 and scaling
            "ldo": "lido-dao",
            "rpl": "rocket-pool",
            "steth": "staked-ether",
            
            # Gaming/NFT
            "axs": "axie-infinity",
            "slp": "smooth-love-potion",
            "chr": "chromaway",
            "enj": "enjincoin",
            "wax": "wax",
            
            # AI/Data tokens
            "fet": "fetch-ai",
            "ocean": "ocean-protocol",
            "rndr": "render-token",
            
            # Infrastructure
            "ar": "arweave",
            "storj": "storj",
            "bat": "basic-attention-token",
            "zrx": "0x",
            
            # Stablecoins and wrapped tokens
            "usdc": "usd-coin",
            "usdt": "tether",
            "dai": "dai",
            "busd": "binance-usd",
            "wbtc": "wrapped-bitcoin",
            "weth": "weth",
            
            # Additional tokens that might be traded
            "ray": "raydium",
            "node": "dappnode",
            "s": None,  # Invalid token - will be filtered out
        }
        
        # Cache for failed token lookups to avoid repeated failures
        self.failed_token_cache_ttl = 3600  # 1 hour
    
    def is_token_supported(self, symbol: str) -> bool:
        """Check if a token is supported by CoinGecko"""
        base_token = symbol.replace("-USDT", "").replace("-USD", "").lower()
        
        # Check if token is in our known failed list
        if base_token in self.failed_tokens:
            return False
        
        # Check if we have a mapping for this token
        coin_id = self.coingecko_token_map.get(base_token)
        return coin_id is not None
    
    def get_coingecko_id(self, symbol: str) -> Optional[str]:
        """Get CoinGecko ID for a token symbol"""
        base_token = symbol.replace("-USDT", "").replace("-USD", "").lower()
        return self.coingecko_token_map.get(base_token)
    
    def mark_token_as_failed(self, symbol: str):
        """Mark a token as failed to avoid future API calls"""
        base_token = symbol.replace("-USDT", "").replace("-USD", "").lower()
        self.failed_tokens.add(base_token)
        
        # Cache the failure
        cache_key = f"failed_token_{base_token}"
        set_cached_data(cache_key, True, ttl=self.failed_token_cache_ttl)
        
        logger.info(f"Marked {base_token} as failed token - will skip future API calls")
    
    def is_token_failed(self, symbol: str) -> bool:
        """Check if a token has been marked as failed"""
        base_token = symbol.replace("-USDT", "").replace("-USD", "").lower()
        
        # Check in-memory cache first
        if base_token in self.failed_tokens:
            return True
        
        # Check persistent cache
        cache_key = f"failed_token_{base_token}"
        cached_failure = get_cached_data(cache_key)
        if cached_failure:
            self.failed_tokens.add(base_token)
            return True
        
        return False
    
    def should_skip_api_call(self, api_name: str, symbol: str) -> bool:
        """Determine if an API call should be skipped with enhanced rate limiting"""
        
        # Skip if token is known to fail
        if api_name == 'coingecko' and self.is_token_failed(symbol):
            return True
        
        # Skip if token is not supported
        if api_name == 'coingecko' and not self.is_token_supported(symbol):
            self.mark_token_as_failed(symbol)
            return True
        
        # Enhanced rate limiting check
        return self._is_rate_limited(api_name)
    
    def _is_rate_limited(self, api_name: str) -> bool:
        """Check if API is currently rate limited with precise tracking"""
        current_time = time.time()
        rate_config = self.rate_limits.get(api_name, {
            'calls_per_minute': 60, 
            'delay': 1.0,
            'reset_window': 60
        })
        
        # Check if we need to wait for next allowed call
        if api_name in self.next_allowed_call:
            if current_time < self.next_allowed_call[api_name]:
                wait_time = self.next_allowed_call[api_name] - current_time
                logger.warning(f"Rate limited for {api_name}, need to wait {wait_time:.1f}s")
                return True
        
        # Clean old entries from the sliding window
        reset_window = rate_config['reset_window']
        cutoff_time = current_time - reset_window
        
        self.api_call_history[api_name] = [
            call_time for call_time in self.api_call_history[api_name] 
            if call_time > cutoff_time
        ]
        
        # Check if we're at the rate limit
        calls_in_window = len(self.api_call_history[api_name])
        max_calls = rate_config['calls_per_minute']
        
        if calls_in_window >= max_calls:
            # Calculate when the oldest call will expire
            if self.api_call_history[api_name]:
                oldest_call = min(self.api_call_history[api_name])
                next_available = oldest_call + reset_window
                self.next_allowed_call[api_name] = next_available
                
                wait_time = next_available - current_time
                logger.warning(f"Rate limit reached for {api_name} ({calls_in_window}/{max_calls}), "
                             f"next call available in {wait_time:.1f}s")
            else:
                # Fallback if no call history
                self.next_allowed_call[api_name] = current_time + reset_window
                logger.warning(f"Rate limit reached for {api_name}, waiting {reset_window}s")
            return True
        
        return False
    
    def get_time_until_next_call(self, api_name: str) -> float:
        """Get time in seconds until next API call is allowed"""
        if api_name not in self.next_allowed_call:
            return 0.0
        
        current_time = time.time()
        wait_time = max(0, self.next_allowed_call[api_name] - current_time)
        return wait_time
    
    def can_make_api_call(self, api_name: str, symbol: Optional[str] = None) -> bool:
        """Check if an API call can be made right now"""
        if symbol and api_name == 'coingecko':
            if self.is_token_failed(symbol) or not self.is_token_supported(symbol):
                return False
        
        return not self._is_rate_limited(api_name)
    
    async def wait_for_api_availability(self, api_name: str, max_wait: float = 300) -> bool:
        """Wait until API is available, up to max_wait seconds"""
        wait_time = self.get_time_until_next_call(api_name)
        
        if wait_time <= 0:
            return True
        
        if wait_time > max_wait:
            logger.error(f"API {api_name} wait time ({wait_time:.1f}s) exceeds max wait ({max_wait}s)")
            return False
        
        logger.info(f"Waiting {wait_time:.1f}s for {api_name} API availability")
        await asyncio.sleep(wait_time)
        return True
    
    def record_api_call(self, api_name: str):
        """Record an API call for rate limiting purposes"""
        current_time = time.time()
        self.api_call_history[api_name].append(current_time)
    
    def get_optimal_delay(self, api_name: str) -> float:
        """Get optimal delay for an API call"""
        rate_limit = self.rate_limits.get(api_name, {'delay': 1.0})
        return rate_limit['delay']
    
    def filter_supported_tokens(self, symbols: List[str]) -> List[str]:
        """Filter out unsupported tokens to avoid unnecessary API calls"""
        supported = []
        for symbol in symbols:
            if not self.is_token_failed(symbol) and self.is_token_supported(symbol):
                supported.append(symbol)
            else:
                logger.debug(f"Filtering out unsupported/failed token: {symbol}")
        
        logger.info(f"Filtered {len(symbols)} tokens down to {len(supported)} supported tokens")
        return supported
    
    def get_batch_size(self, api_name: str) -> int:
        """Get optimal batch size for API calls"""
        if api_name == 'coingecko':
            return 3  # Small batches for CoinGecko to avoid rate limits
        elif api_name == 'kucoin':
            return 5
        elif api_name == 'tokenmetrics':
            return 4
        else:
            return 5

# Global instance
api_optimizer = APIOptimizationManager()
