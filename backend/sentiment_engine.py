import logging
import time
import asyncio
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON><PERSON>Executor, as_completed
from collections import defaultdict
from dataclasses import dataclass

from cache import get_cached_data, set_cached_data

logger = logging.getLogger(__name__)

# Enhanced sentiment configuration for LIVE DATA
SENTIMENT_CACHE_TTL = 30  # 🔴 REDUCED: 30 seconds for live sentiment
BATCH_SIZE = 25  # 🚀 INCREASED: Process more sentiment in batches
MAX_WORKERS = 12  # 🚀 INCREASED: More parallel workers for live data
NEWS_SOURCES_WEIGHTS = {
    "cointelegraph": 0.9,
    "coindesk": 0.9,
    "cryptopanic": 0.7,
    "reddit": 0.6,
    "discord": 0.5,
    "twitter": 0.4,
}


@dataclass
class EnhancedSentimentResult:
    """Enhanced sentiment analysis result"""

    symbol: str
    score: float
    confidence: float
    source_breakdown: Dict[str, float]
    keyword_matches: Dict[str, int]
    news_count: int
    timestamp: float
    reasoning: str


# Enhanced keyword sets with weights
SENTIMENT_KEYWORDS = {
    "bullish": {
        "strong": [
            "moon",
            "pump",
            "rocket",
            "breakout",
            "surge",
            "rally",
            "explosion",
            "skyrocket",
        ],
        "moderate": [
            "bullish",
            "up",
            "rise",
            "gain",
            "profit",
            "good",
            "great",
            "positive",
            "buy",
        ],
        "weak": ["stable", "steady", "holding", "support", "recovery"],
    },
    "bearish": {
        "strong": [
            "crash",
            "dump",
            "collapse",
            "plummet",
            "disaster",
            "panic",
            "liquidation",
        ],
        "moderate": [
            "bearish",
            "down",
            "fall",
            "loss",
            "bad",
            "negative",
            "sell",
            "drop",
        ],
        "weak": ["correction", "dip", "pullback", "resistance", "consolidation"],
    },
}

# Sentiment weights
SENTIMENT_WEIGHTS = {"strong": 3.0, "moderate": 1.0, "weak": 0.3}

# Global sentiment cache
_sentiment_cache = {}
_news_cache = {}


class EnhancedSentimentAnalyzer:
    """Enhanced sentiment analyzer optimized for high-frequency trading."""

    def __init__(self):
        self.cache = {}
        self.last_cleanup = time.time()
        self.processing_queue = []

    def simple_sentiment_fallback(self, text: str) -> float:
        """Enhanced simple sentiment analysis with weighted keywords."""
        if not text:
            return 0.5

        text_lower = str(text).lower()
        bullish_score = 0.0
        bearish_score = 0.0

        # Calculate weighted scores
        for sentiment_type, categories in SENTIMENT_KEYWORDS.items():
            for strength, keywords in categories.items():
                weight = SENTIMENT_WEIGHTS[strength]
                count = sum(1 for word in keywords if word in text_lower)

                if sentiment_type == "bullish":
                    bullish_score += count * weight
                else:
                    bearish_score += count * weight

        # Normalize score
        total_score = bullish_score + bearish_score
        if total_score == 0:
            return 0.5  # Neutral

        # Return score between 0 and 1
        return bullish_score / total_score

    def analyze_sentiment_batch(self, texts: List[str]) -> List[float]:
        """Analyze sentiment for multiple texts in parallel."""
        if not texts:
            return []

        # Use ThreadPoolExecutor for CPU-bound sentiment analysis
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = [
                executor.submit(self.simple_sentiment_fallback, text) for text in texts
            ]
            results = []

            for future in as_completed(futures, timeout=10):
                try:
                    result = future.result(timeout=2)
                    results.append(result)
                except Exception as e:
                    logger.debug(f"Sentiment analysis failed: {e}")
                    results.append(0.5)  # Default to neutral

        return results

    def cleanup_cache(self):
        """Clean expired cache entries."""
        current_time = time.time()
        if current_time - self.last_cleanup < 300:  # Cleanup every 5 minutes
            return

        expired_keys = [
            key
            for key, data in self.cache.items()
            if current_time - data.get("timestamp", 0) > SENTIMENT_CACHE_TTL
        ]

        for key in expired_keys:
            del self.cache[key]

        self.last_cleanup = current_time
        logger.debug(f"Cleaned {len(expired_keys)} expired sentiment cache entries")


# Global sentiment analyzer instance
sentiment_analyzer = EnhancedSentimentAnalyzer()


def get_sentiment_feed() -> List[Dict[str, Any]]:
    """Enhanced sentiment feed with real-time data and caching."""
    cache_key = "sentiment_feed"
    current_time = time.time()

    # Check cache first
    if cache_key in _sentiment_cache:
        cached_data = _sentiment_cache[cache_key]
        if current_time - cached_data["timestamp"] < SENTIMENT_CACHE_TTL:
            return cached_data["data"]

    try:
        # Load real sentiment data from multiple sources
        sentiment_data = []

        # Try to load from news sentiment files
        try:
            news_files = [
                "backend/data/news_sentiment.jsonl",
                "backend/data/cryptopanic.json",
                "backend/data/cmc_news.json",
            ]

            for file_path in news_files:
                if os.path.exists(file_path):
                    with open(file_path, "r") as f:
                        if file_path.endswith(".jsonl"):
                            # Read JSONL format
                            for line in f:
                                try:
                                    data = json.loads(line.strip())
                                    if isinstance(data, dict) and "symbol" in data:
                                        sentiment_data.append(
                                            {
                                                "symbol": data["symbol"],
                                                "sentiment": (
                                                    "positive"
                                                    if data.get("score", 0) > 0.1
                                                    else (
                                                        "negative"
                                                        if data.get("score", 0) < -0.1
                                                        else "neutral"
                                                    )
                                                ),
                                                "score": data.get("score", 0),
                                                "headline": (
                                                    data.get("news_snippets", [""])[0]
                                                    if data.get("news_snippets")
                                                    else ""
                                                ),
                                                "source": "news_sentiment",
                                                "timestamp": data.get(
                                                    "timestamp",
                                                    datetime.now().isoformat(),
                                                ),
                                            }
                                        )
                                except json.JSONDecodeError:
                                    continue
                        else:
                            # Read JSON format
                            data = json.load(f)
                            if isinstance(data, list):
                                for item in data[:10]:  # Limit to recent items
                                    if isinstance(item, dict):
                                        symbol = item.get("tickers", [])
                                        if symbol and isinstance(symbol, list):
                                            symbol = symbol[0] + "-USDT"
                                        elif not symbol:
                                            continue

                                        sentiment_score = item.get("sentiment_score", 0)
                                        sentiment_data.append(
                                            {
                                                "symbol": symbol,
                                                "sentiment": (
                                                    "positive"
                                                    if sentiment_score > 0
                                                    else (
                                                        "negative"
                                                        if sentiment_score < 0
                                                        else "neutral"
                                                    )
                                                ),
                                                "score": sentiment_score,
                                                "headline": item.get(
                                                    "title", item.get("content", "")
                                                ),
                                                "source": item.get("source", "unknown"),
                                                "timestamp": item.get(
                                                    "published",
                                                    item.get(
                                                        "fetched_at",
                                                        datetime.now().isoformat(),
                                                    ),
                                                ),
                                            }
                                        )
        except Exception as e:
            logger.debug(f"Error loading sentiment files: {e}")

        # Fallback to default data if no real data found
        if not sentiment_data:
            sentiment_data = [
                {
                    "symbol": "BTC-USDT",
                    "sentiment": "positive",
                    "score": 0.6,
                    "headline": "Bitcoin shows strong momentum",
                    "source": "fallback",
                    "timestamp": datetime.now().isoformat(),
                },
                {
                    "symbol": "ETH-USDT",
                    "sentiment": "positive",
                    "score": 0.5,
                    "headline": "Ethereum network stable",
                    "source": "fallback",
                    "timestamp": datetime.now().isoformat(),
                },
            ]

        # Cache the result
        _sentiment_cache[cache_key] = {
            "data": sentiment_data,
            "timestamp": current_time,
        }

        return sentiment_data

    except Exception as e:
        logger.error(f"Enhanced sentiment feed error: {e}")
        return []


def analyze_sentiment(text: str) -> Dict[str, Any]:
    """Enhanced sentiment analysis with caching and improved accuracy."""
    if not text:
        return {"sentiment": "neutral", "score": 0.0}

    # Check cache first
    cache_key = f"sentiment_{hash(text)}"
    current_time = time.time()

    if cache_key in _sentiment_cache:
        cached_data = _sentiment_cache[cache_key]
        if current_time - cached_data["timestamp"] < SENTIMENT_CACHE_TTL:
            return cached_data["data"]

    try:
        # Use enhanced sentiment analyzer
        score = sentiment_analyzer.simple_sentiment_fallback(text)

        # Convert to sentiment classification
        if score > 0.6:
            sentiment = "positive"
            final_score = (score - 0.5) * 2  # Scale to -1 to 1
        elif score < 0.4:
            sentiment = "negative"
            final_score = (score - 0.5) * 2  # Scale to -1 to 1
        else:
            sentiment = "neutral"
            final_score = 0.0

        result = {
            "sentiment": sentiment,
            "score": round(final_score, 3),
            "confidence": abs(final_score),
            "raw_score": score,
        }

        # Cache the result
        _sentiment_cache[cache_key] = {"data": result, "timestamp": current_time}

        return result

    except Exception as e:
        logger.error(f"Enhanced sentiment analysis error: {e}")
        return {"sentiment": "neutral", "score": 0.0}


def analyze_sentiment_batch(texts: List[str]) -> List[Dict[str, Any]]:
    """Analyze sentiment for multiple texts in parallel for high-frequency processing."""
    if not texts:
        return []

    try:
        # Use the enhanced sentiment analyzer's batch processing
        scores = sentiment_analyzer.analyze_sentiment_batch(texts)

        results = []
        for i, (text, score) in enumerate(zip(texts, scores)):
            # Convert to sentiment classification
            if score > 0.6:
                sentiment = "positive"
                final_score = (score - 0.5) * 2
            elif score < 0.4:
                sentiment = "negative"
                final_score = (score - 0.5) * 2
            else:
                sentiment = "neutral"
                final_score = 0.0

            result = {
                "sentiment": sentiment,
                "score": round(final_score, 3),
                "confidence": abs(final_score),
                "raw_score": score,
                "text_index": i,
            }
            results.append(result)

        return results

    except Exception as e:
        logger.error(f"Batch sentiment analysis error: {e}")
        return [{"sentiment": "neutral", "score": 0.0} for _ in texts]


def get_sentiment_score(symbol: str) -> float:
    """Get comprehensive sentiment score for a symbol using multiple sources"""
    try:
        # 🔧 FIX: Use actual sentiment analysis instead of hardcoded values
        logger.debug(f"🧠 Analyzing sentiment for {symbol}")

        # Get combined sentiment from multiple sources
        combined_result = get_combined_sentiment(symbol)
        sentiment_score = combined_result.get("combined_score", 0.5)

        # If we have a valid score, return it
        if isinstance(sentiment_score, (int, float)) and 0.0 <= sentiment_score <= 1.0:
            logger.debug(f"✅ Sentiment for {symbol}: {sentiment_score:.3f}")
            return float(sentiment_score)

        # Fallback to enhanced analysis
        sentiment_analyzer = EnhancedSentimentAnalyzer()
        fallback_score = sentiment_analyzer.simple_sentiment_fallback(symbol)
        logger.debug(f"⚠️ Using fallback sentiment for {symbol}: {fallback_score:.3f}")
        return fallback_score

    except Exception as e:
        logger.error(f"Sentiment score error for {symbol}: {e}")
        # Last resort fallback
        return 0.5


def get_combined_sentiment(symbol: str) -> Dict[str, Any]:
    """Get combined sentiment analysis for a symbol using real data sources"""
    try:
        logger.debug(f"🔍 Getting combined sentiment for {symbol}")

        # 🔧 FIX: Use actual news sentiment analysis
        try:
            from news_sentiment import get_combined_sentiment_score

            news_result = get_combined_sentiment_score(symbol, [])
            news_score = (
                news_result.get("score", 0.5) if isinstance(news_result, dict) else 0.5
            )
        except Exception as e:
            logger.warning(f"News sentiment failed for {symbol}: {e}")
            news_score = 0.5

        # 🔧 FIX: Use social media sentiment
        try:
            from reddit_github_alpha import fetch_signal_sentiment

            social_score = fetch_signal_sentiment(symbol)
            # Normalize to 0-1 range (fetch_signal_sentiment returns -1 to 1)
            social_score = (social_score + 1) / 2
        except Exception as e:
            logger.warning(f"Social sentiment failed for {symbol}: {e}")
            social_score = 0.5

        # 🔧 FIX: Use enhanced sentiment analyzer for keyword analysis
        sentiment_analyzer = EnhancedSentimentAnalyzer()
        keyword_score = sentiment_analyzer.simple_sentiment_fallback(symbol)

        # Combine scores with weights
        weights = {"news": 0.4, "social": 0.3, "keyword": 0.3}
        combined_score = (
            news_score * weights["news"]
            + social_score * weights["social"]
            + keyword_score * weights["keyword"]
        )

        # Determine sentiment labels
        news_sentiment = (
            "bullish"
            if news_score > 0.6
            else "bearish" if news_score < 0.4 else "neutral"
        )
        social_sentiment = (
            "bullish"
            if social_score > 0.6
            else "bearish" if social_score < 0.4 else "neutral"
        )

        # Calculate confidence based on score consistency
        score_variance = abs(news_score - social_score) + abs(
            social_score - keyword_score
        )
        confidence = max(0.1, 1.0 - (score_variance / 2))

        result = {
            "sentiment_score": combined_score,
            "news_sentiment": news_sentiment,
            "social_sentiment": social_sentiment,
            "combined_score": combined_score,
            "confidence": confidence,
            "breakdown": {
                "news_score": news_score,
                "social_score": social_score,
                "keyword_score": keyword_score,
            },
        }

        logger.debug(
            f"✅ Combined sentiment for {symbol}: {combined_score:.3f} (confidence: {confidence:.2f})"
        )
        return result

    except Exception as e:
        logger.error(f"Combined sentiment error for {symbol}: {e}")
        return {
            "sentiment_score": 0.5,
            "news_sentiment": "neutral",
            "social_sentiment": "neutral",
            "combined_score": 0.5,
            "confidence": 0.1,
        }


# Enhanced sentiment analysis functions


async def get_enhanced_sentiment_analysis(symbol: str) -> EnhancedSentimentResult:
    """Get comprehensive sentiment analysis with caching and multi-source aggregation."""
    try:
        # Check cache first
        cache_key = f"enhanced_sentiment_{symbol}"
        cached_result = get_cached_data(cache_key)
        if cached_result:
            logger.debug(f"✅ Cache hit for sentiment: {symbol}")
            return EnhancedSentimentResult(**cached_result)

        # Gather sentiment from multiple sources
        source_results = await _gather_multi_source_sentiment(symbol)

        # Calculate weighted sentiment score
        weighted_score = _calculate_weighted_sentiment(source_results)

        # Analyze keyword matches
        keyword_matches = _analyze_keyword_matches(source_results)

        # Calculate confidence based on source agreement
        confidence = _calculate_sentiment_confidence(source_results)

        # Generate reasoning
        reasoning = _generate_sentiment_reasoning(
            source_results, weighted_score, confidence
        )

        # Create enhanced result
        enhanced_result = EnhancedSentimentResult(
            symbol=symbol,
            score=weighted_score,
            confidence=confidence,
            source_breakdown={
                source: data.get("score", 0.5)
                for source, data in source_results.items()
            },
            keyword_matches=keyword_matches,
            news_count=sum(
                data.get("news_count", 0) for data in source_results.values()
            ),
            timestamp=time.time(),
            reasoning=reasoning,
        )

        # Cache the result
        set_cached_data(
            cache_key, enhanced_result.__dict__, SENTIMENT_CACHE_TTL, "high"
        )

        logger.info(
            f"✅ Enhanced sentiment for {symbol}: {weighted_score:.2f} (confidence: {confidence:.2f})"
        )
        return enhanced_result

    except Exception as e:
        logger.error(f"❌ Enhanced sentiment analysis failed for {symbol}: {e}")
        return _create_fallback_sentiment_result(symbol)


async def get_batch_sentiment_analysis(
    symbols: List[str],
) -> List[EnhancedSentimentResult]:
    """Get sentiment analysis for multiple symbols in parallel."""
    try:
        logger.info(f"🔄 Analyzing sentiment for {len(symbols)} symbols in parallel...")

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            loop = asyncio.get_event_loop()
            tasks = [
                loop.run_in_executor(
                    executor,
                    lambda s=symbol: asyncio.run(get_enhanced_sentiment_analysis(s)),
                )
                for symbol in symbols
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter successful results
        sentiment_results = [
            result for result in results if isinstance(result, EnhancedSentimentResult)
        ]

        logger.info(
            f"✅ Batch sentiment analysis complete: {len(sentiment_results)} results"
        )
        return sentiment_results

    except Exception as e:
        logger.error(f"❌ Batch sentiment analysis failed: {e}")
        return [_create_fallback_sentiment_result(symbol) for symbol in symbols]


async def _gather_multi_source_sentiment(symbol: str) -> Dict[str, Dict[str, Any]]:
    """Gather sentiment from multiple news sources."""
    try:
        source_results = {}

        # News sources to check
        sources = [
            ("cointelegraph", _get_cointelegraph_sentiment),
            ("coindesk", _get_coindesk_sentiment),
            ("cryptopanic", _get_cryptopanic_sentiment),
            ("reddit", _get_reddit_sentiment),
            ("discord", _get_discord_sentiment),
        ]

        # Gather sentiment from all sources in parallel
        tasks = [source_func(symbol) for _, source_func in sources]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Combine results
        for (source_name, _), result in zip(sources, results):
            if isinstance(result, dict) and not isinstance(result, Exception):
                source_results[source_name] = result
            else:
                source_results[source_name] = {
                    "score": 0.5,
                    "news_count": 0,
                    "keywords": [],
                }

        return source_results

    except Exception as e:
        logger.error(f"Error gathering multi-source sentiment for {symbol}: {e}")
        return {}


def _calculate_weighted_sentiment(source_results: Dict[str, Dict[str, Any]]) -> float:
    """Calculate weighted sentiment score from multiple sources."""
    try:
        total_weight = 0.0
        weighted_sum = 0.0

        for source, data in source_results.items():
            weight = NEWS_SOURCES_WEIGHTS.get(source, 0.5)
            score = data.get("score", 0.5)
            news_count = data.get("news_count", 0)

            # Boost weight based on news volume
            adjusted_weight = weight * (1 + min(news_count / 10, 0.5))

            weighted_sum += score * adjusted_weight
            total_weight += adjusted_weight

        if total_weight > 0:
            return max(0.0, min(1.0, weighted_sum / total_weight))
        else:
            return 0.5

    except Exception:
        return 0.5


def _calculate_sentiment_confidence(source_results: Dict[str, Dict[str, Any]]) -> float:
    """Calculate confidence based on source agreement."""
    try:
        scores = [data.get("score", 0.5) for data in source_results.values()]
        if len(scores) < 2:
            return 0.5

        # Calculate standard deviation
        mean_score = sum(scores) / len(scores)
        variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
        std_dev = variance**0.5

        # Higher agreement = higher confidence
        confidence = max(0.1, 1.0 - (std_dev * 2))
        return min(0.95, confidence)

    except Exception:
        return 0.5


def _analyze_keyword_matches(
    source_results: Dict[str, Dict[str, Any]],
) -> Dict[str, int]:
    """Analyze keyword matches across sources."""
    try:
        keyword_counts = defaultdict(int)

        for data in source_results.values():
            keywords = data.get("keywords", [])
            for keyword in keywords:
                keyword_counts[keyword] += 1

        return dict(keyword_counts)

    except Exception:
        return {}


def _generate_sentiment_reasoning(
    source_results: Dict[str, Dict[str, Any]], score: float, confidence: float
) -> str:
    """Generate human-readable reasoning for sentiment analysis."""
    try:
        reasons = []

        # Score-based reasoning
        if score > 0.7:
            reasons.append("Strong positive sentiment")
        elif score > 0.6:
            reasons.append("Moderate positive sentiment")
        elif score < 0.3:
            reasons.append("Strong negative sentiment")
        elif score < 0.4:
            reasons.append("Moderate negative sentiment")
        else:
            reasons.append("Neutral sentiment")

        # Confidence reasoning
        if confidence > 0.8:
            reasons.append("High source agreement")
        elif confidence < 0.4:
            reasons.append("Low source agreement")

        # Source-specific insights
        active_sources = [
            source
            for source, data in source_results.items()
            if data.get("news_count", 0) > 0
        ]
        if len(active_sources) > 3:
            reasons.append(f"Multiple sources active ({len(active_sources)})")
        elif len(active_sources) == 0:
            reasons.append("Limited news coverage")

        return "; ".join(reasons) if reasons else "Standard sentiment analysis"

    except Exception:
        return "Sentiment analysis incomplete"


def _create_fallback_sentiment_result(symbol: str) -> EnhancedSentimentResult:
    """Create fallback sentiment result when analysis fails."""
    return EnhancedSentimentResult(
        symbol=symbol,
        score=0.5,
        confidence=0.0,
        source_breakdown={},
        keyword_matches={},
        news_count=0,
        timestamp=time.time(),
        reasoning="Sentiment analysis failed - using neutral fallback",
    )


# Source-specific sentiment functions (simplified implementations)


async def _get_cointelegraph_sentiment(symbol: str) -> Dict[str, Any]:
    """Get sentiment from CoinTelegraph news using actual news analysis."""
    try:
        # Load actual CoinTelegraph news
        import json
        from pathlib import Path

        news_file = Path(__file__).resolve().parent / "data" / "cointelegraph.json"
        if news_file.exists():
            with open(news_file, "r") as f:
                news_data = json.load(f)

            # Filter news for the symbol
            relevant_news = []
            for article in news_data.get("articles", []):
                title = article.get("title", "").lower()
                description = article.get("description", "").lower()
                if symbol.lower() in title or symbol.lower() in description:
                    relevant_news.append(article)

            if relevant_news:
                # Analyze sentiment of relevant news
                scores = []
                keywords = []

                for article in relevant_news:
                    text = (
                        f"{article.get('title', '')} {article.get('description', '')}"
                    )
                    score = sentiment_analyzer.simple_sentiment_fallback(text)
                    scores.append(score)

                    # Extract keywords
                    text_lower = text.lower()
                    for sentiment_type, categories in SENTIMENT_KEYWORDS.items():
                        for strength, words in categories.items():
                            for word in words:
                                if word in text_lower and word not in keywords:
                                    keywords.append(word)

                avg_score = sum(scores) / len(scores) if scores else 0.5
                return {
                    "score": avg_score,
                    "news_count": len(relevant_news),
                    "keywords": keywords[:5],
                }

        # Fallback to neutral if no relevant news
        return {"score": 0.5, "news_count": 0, "keywords": []}
    except Exception as e:
        logger.debug(f"CoinTelegraph sentiment error for {symbol}: {e}")
        return {"score": 0.5, "news_count": 0, "keywords": []}


async def _get_coindesk_sentiment(symbol: str) -> Dict[str, Any]:
    """Get sentiment from CoinDesk news using actual news analysis."""
    try:
        # Load actual CoinDesk news
        import json
        from pathlib import Path

        news_file = Path(__file__).resolve().parent / "data" / "coindesk.json"
        if news_file.exists():
            with open(news_file, "r") as f:
                news_data = json.load(f)

            # Filter and analyze news for the symbol
            relevant_news = []
            for article in news_data.get("articles", []):
                title = article.get("title", "").lower()
                description = article.get("description", "").lower()
                if symbol.lower() in title or symbol.lower() in description:
                    relevant_news.append(article)

            if relevant_news:
                scores = []
                keywords = []

                for article in relevant_news:
                    text = (
                        f"{article.get('title', '')} {article.get('description', '')}"
                    )
                    score = sentiment_analyzer.simple_sentiment_fallback(text)
                    scores.append(score)

                    # Extract keywords
                    text_lower = text.lower()
                    for sentiment_type, categories in SENTIMENT_KEYWORDS.items():
                        for strength, words in categories.items():
                            for word in words:
                                if word in text_lower and word not in keywords:
                                    keywords.append(word)

                avg_score = sum(scores) / len(scores) if scores else 0.5
                return {
                    "score": avg_score,
                    "news_count": len(relevant_news),
                    "keywords": keywords[:5],
                }

        return {"score": 0.5, "news_count": 0, "keywords": []}
    except Exception as e:
        logger.debug(f"CoinDesk sentiment error for {symbol}: {e}")
        return {"score": 0.5, "news_count": 0, "keywords": []}


async def _get_cryptopanic_sentiment(symbol: str) -> Dict[str, Any]:
    """Get sentiment from CryptoPanic using actual news analysis."""
    try:
        # Load actual CryptoPanic news
        import json
        from pathlib import Path

        news_file = Path(__file__).resolve().parent / "data" / "cryptopanic.json"
        if news_file.exists():
            with open(news_file, "r") as f:
                news_data = json.load(f)

            # Filter and analyze news for the symbol
            relevant_news = []
            for article in news_data.get("results", []):
                title = article.get("title", "").lower()
                if symbol.lower() in title:
                    relevant_news.append(article)

            if relevant_news:
                scores = []
                keywords = []

                for article in relevant_news:
                    text = article.get("title", "")
                    score = sentiment_analyzer.simple_sentiment_fallback(text)
                    scores.append(score)

                    # Extract keywords
                    text_lower = text.lower()
                    for sentiment_type, categories in SENTIMENT_KEYWORDS.items():
                        for strength, words in categories.items():
                            for word in words:
                                if word in text_lower and word not in keywords:
                                    keywords.append(word)

                avg_score = sum(scores) / len(scores) if scores else 0.5
                return {
                    "score": avg_score,
                    "news_count": len(relevant_news),
                    "keywords": keywords[:5],
                }

        return {"score": 0.5, "news_count": 0, "keywords": []}
    except Exception as e:
        logger.debug(f"CryptoPanic sentiment error for {symbol}: {e}")
        return {"score": 0.5, "news_count": 0, "keywords": []}


async def _get_reddit_sentiment(symbol: str) -> Dict[str, Any]:
    """Get sentiment from Reddit discussions using actual data."""
    try:
        # Use the actual Reddit sentiment function
        score = get_reddit_sentiment(symbol)

        # Determine keywords based on score
        if score > 0.6:
            keywords = ["bullish", "moon", "pump"]
        elif score < 0.4:
            keywords = ["bearish", "dump", "crash"]
        else:
            keywords = ["neutral", "stable", "hold"]

        return {"score": score, "news_count": 1, "keywords": keywords}
    except Exception as e:
        logger.debug(f"Reddit sentiment error for {symbol}: {e}")
        return {"score": 0.5, "news_count": 0, "keywords": []}


async def _get_discord_sentiment(symbol: str) -> Dict[str, Any]:
    """Get sentiment from Discord channels using actual data."""
    try:
        # Use the actual Discord sentiment function
        score = get_discord_sentiment(symbol)

        # Determine keywords based on score
        if score > 0.6:
            keywords = ["bullish", "buy", "moon"]
        elif score < 0.4:
            keywords = ["bearish", "sell", "dump"]
        else:
            keywords = ["neutral", "hold", "wait"]

        return {"score": score, "news_count": 1, "keywords": keywords}
    except Exception as e:
        logger.debug(f"Discord sentiment error for {symbol}: {e}")
        return {"score": 0.5, "news_count": 0, "keywords": []}


# ————————————————————————————————————————————————————————————————
# NEW ENHANCED SENTIMENT FUNCTIONS FOR ALPHA PREDATOR
# ————————————————————————————————————————————————————————————————


def get_discord_sentiment(token: str) -> float:
    """Get sentiment from Discord news"""
    try:
        from discord_news_bot import get_latest_discord_news

        discord_news = get_latest_discord_news(limit=10, token=token)
        if not discord_news:
            return 0.5

        # Calculate average sentiment from Discord news
        sentiment_scores = [news.get("sentiment_score", 0.5) for news in discord_news]
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)

        logger.debug(
            f"Discord sentiment for {token}: {avg_sentiment:.3f} from {len(discord_news)} items"
        )
        return avg_sentiment

    except Exception as e:
        logger.debug(f"Discord sentiment failed for {token}: {e}")
        return 0.5


def get_reddit_sentiment(token: str) -> float:
    """Get sentiment from Reddit signals"""
    try:
        from reddit_github_alpha import fetch_reddit_signals_for_token

        reddit_signals = fetch_reddit_signals_for_token(token)
        if not reddit_signals:
            return 0.5

        # Calculate sentiment based on credibility and relevance
        total_score = 0.0
        total_weight = 0.0

        for signal in reddit_signals:
            credibility = signal.get("credibility", 0.5)
            relevance = signal.get("relevance_score", 0.5)
            weight = credibility * relevance

            # Simple sentiment based on title keywords
            title = signal.get("title", "").lower()
            if any(word in title for word in ["pump", "moon", "bullish", "up", "gain"]):
                sentiment = 0.7
            elif any(
                word in title for word in ["dump", "crash", "bearish", "down", "loss"]
            ):
                sentiment = 0.3
            else:
                sentiment = 0.5

            total_score += sentiment * weight
            total_weight += weight

        avg_sentiment = total_score / total_weight if total_weight > 0 else 0.5
        logger.debug(
            f"Reddit sentiment for {token}: {avg_sentiment:.3f} from {len(reddit_signals)} signals"
        )
        return avg_sentiment

    except Exception as e:
        logger.debug(f"Reddit sentiment failed for {token}: {e}")
        return 0.5


def get_github_sentiment(token: str) -> float:
    """Get sentiment from GitHub signals"""
    try:
        from reddit_github_alpha import fetch_github_signals_for_token

        github_signals = fetch_github_signals_for_token(token)
        if not github_signals:
            return 0.5

        # GitHub sentiment based on activity and relevance
        total_score = 0.0
        total_weight = 0.0

        for signal in github_signals:
            credibility = signal.get("credibility", 0.5)
            relevance = signal.get("relevance_score", 0.5)
            weight = credibility * relevance

            # GitHub activity is generally positive for development
            sentiment = 0.6  # Slightly positive bias for active development

            total_score += sentiment * weight
            total_weight += weight

        avg_sentiment = total_score / total_weight if total_weight > 0 else 0.5
        logger.debug(
            f"GitHub sentiment for {token}: {avg_sentiment:.3f} from {len(github_signals)} repos"
        )
        return avg_sentiment

    except Exception as e:
        logger.debug(f"GitHub sentiment failed for {token}: {e}")
        return 0.5
