import gspread
from gspread.client import Client
import logging
from google.oauth2.service_account import Credentials
import os

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("google_sheets_logger")

SCOPE = [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/drive"
]

# Load configuration from environment variables
CREDENTIALS_FILE = os.getenv("GOOGLE_SHEETS_CREDENTIALS_PATH", "backend/credentials/alpha-predator-bot-d5303889d863.json")
SHEET_NAME = os.getenv("GOOGLE_SHEETS_SHEET_NAME", "AlphaPredator Alerts")
WORKSHEET_NAME = os.getenv("GOOGLE_SHEETS_WORKSHEET_NAME", "NewsAlerts")

# Global variables for gspread client and sheet (initialized once)
from typing import Optional

gs_client: Optional[Client] = None
gs_sheet = None

def _initialize_gspread():
    """
    Initializes the gspread client and sheet object.
    """
    global gs_client, gs_sheet
    if gs_client is None or gs_sheet is None:
        try:
            if not os.path.exists(CREDENTIALS_FILE):
                logger.error(f"Google Sheets credentials file not found at: {CREDENTIALS_FILE}")
                return False
            creds = Credentials.from_service_account_file(CREDENTIALS_FILE, scopes=SCOPE)
            gs_client = Client(auth=creds)
            gs_sheet = gs_client.open(SHEET_NAME).worksheet(WORKSHEET_NAME)
            logger.info("✅ Google Sheets client and sheet initialized successfully.")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google Sheets: {e}")
            return False
    return True

def log_news_alert(token: str, title: str, sentiment: str, tags: list, author: str) -> None:
    """
    Logs a news alert into a Google Sheet with columns:
    [token, title, sentiment, tags, author].

    Args:
        token (str): Token symbol (e.g., BTC-USDT)
        title (str): News headline/title
        sentiment (str): Sentiment label (e.g., 'Positive', 'Negative', 'Neutral')
        tags (list): List of string tags
        author (str): Source or author of the news
    """
    if not _initialize_gspread():
        logger.error("Skipping Google Sheets logging due to initialization failure.")
        return

    try:
        formatted_tags = ", ".join(tags) if tags else "None"
        row = [token, title, sentiment, formatted_tags, author]
        if gs_sheet is not None:
            gs_sheet.append_row(row)
            logger.info(f"✅ Logged news alert to Google Sheets: {token} | {sentiment}")
        else:
            logger.error("❌ gs_sheet is None. Cannot append row.")
    except Exception as e:
        logger.error(f"❌ Failed to log to Google Sheets: {e}")