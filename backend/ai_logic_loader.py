from pathlib import Path
import json
import logging
import time
from typing import Dict, Any, Optional
from functools import lru_cache

logger = logging.getLogger("ai_logic_loader")

# Cache for AI logic with TTL
_ai_logic_cache: Optional[Dict[str, Any]] = None
_cache_timestamp: float = 0
_cache_ttl: float = 300  # 5 minutes

def _validate_ai_logic_entry(entry: Dict[str, Any]) -> bool:
    """Validate AI logic entry structure"""
    required_fields = ['symbol', 'decision', 'confidence']
    return all(field in entry for field in required_fields)

def _create_default_ai_logic() -> Dict[str, Any]:
    """Create default AI logic structure"""
    return {
        "entries": [],
        "metadata": {
            "created_at": time.time(),
            "version": "1.0",
            "total_entries": 0
        }
    }

@lru_cache(maxsize=1)
def _get_file_path() -> Path:
    """Get cached file path"""
    return Path(__file__).parent / 'data' / 'ai_logic.json'

def load_ai_logic(force_reload: bool = False) -> Dict[str, Any]:
    """
    Load AI logic configuration from ai_logic.json with intelligent caching
    
    Args:
        force_reload: Force reload from disk, bypassing cache
        
    Returns:
        Dict containing AI logic data
    """
    global _ai_logic_cache, _cache_timestamp
    
    current_time = time.time()
    
    # Check if we can use cached data
    if (not force_reload and 
        _ai_logic_cache is not None and 
        current_time - _cache_timestamp < _cache_ttl):
        logger.debug("Using cached AI logic data")
        return _ai_logic_cache
    
    file_path = _get_file_path()
    
    try:
        logger.info(f"Loading ai_logic.json from: {file_path}")
        
        if not file_path.exists():
            logger.warning(f"ai_logic.json not found at {file_path}, creating default")
            default_data = _create_default_ai_logic()
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create default file
            with file_path.open('w') as f:
                json.dump(default_data, f, indent=2)
            
            _ai_logic_cache = default_data
            _cache_timestamp = current_time
            return default_data
        
        with file_path.open('r') as f:
            data = json.load(f)
        
        # Validate structure
        if not isinstance(data, dict):
            logger.warning("AI logic data is not a dictionary, using default structure")
            data = _create_default_ai_logic()
        
        # Ensure required keys exist
        if 'entries' not in data:
            data['entries'] = []
        if 'metadata' not in data:
            data['metadata'] = {
                "created_at": time.time(),
                "version": "1.0",
                "total_entries": len(data.get('entries', []))
            }
        
        # Validate entries
        valid_entries = []
        for entry in data.get('entries', []):
            if _validate_ai_logic_entry(entry):
                valid_entries.append(entry)
            else:
                logger.warning(f"Invalid AI logic entry found: {entry}")
        
        data['entries'] = valid_entries
        data['metadata']['total_entries'] = len(valid_entries)
        
        # Update cache
        _ai_logic_cache = data
        _cache_timestamp = current_time
        
        logger.info(f"Successfully loaded {len(valid_entries)} AI logic entries")
        return data
        
    except json.JSONDecodeError as e:
        logger.error(f"❌ Error parsing ai_logic.json: {e}")
        # Try to create backup and use default
        try:
            backup_path = file_path.with_suffix('.json.backup')
            file_path.rename(backup_path)
            logger.info(f"Corrupted file backed up to {backup_path}")
        except Exception as backup_error:
            logger.error(f"Failed to create backup: {backup_error}")
        
        default_data = _create_default_ai_logic()
        _ai_logic_cache = default_data
        _cache_timestamp = current_time
        return default_data
        
    except Exception as e:
        logger.error(f"❌ Unexpected error loading ai_logic.json: {e}")
        # Return cached data if available, otherwise default
        if _ai_logic_cache is not None:
            logger.info("Returning cached AI logic data due to error")
            return _ai_logic_cache
        
        default_data = _create_default_ai_logic()
        _ai_logic_cache = default_data
        _cache_timestamp = current_time
        return default_data

def clear_ai_logic_cache():
    """Clear the AI logic cache to force reload on next access"""
    global _ai_logic_cache, _cache_timestamp
    _ai_logic_cache = None
    _cache_timestamp = 0
    logger.info("AI logic cache cleared")

def get_ai_logic_stats() -> Dict[str, Any]:
    """Get statistics about the AI logic data"""
    data = load_ai_logic()
    entries = data.get('entries', [])
    
    if not entries:
        return {
            "total_entries": 0,
            "decisions": {},
            "avg_confidence": 0.0,
            "cache_status": "active" if _ai_logic_cache is not None else "empty"
        }
    
    # Calculate decision distribution
    decisions = {}
    total_confidence = 0.0
    
    for entry in entries:
        decision = entry.get('decision', 'UNKNOWN')
        confidence = entry.get('confidence', 0.0)
        
        decisions[decision] = decisions.get(decision, 0) + 1
        total_confidence += confidence
    
    avg_confidence = total_confidence / len(entries) if entries else 0.0
    
    return {
        "total_entries": len(entries),
        "decisions": decisions,
        "avg_confidence": round(avg_confidence, 3),
        "cache_status": "active" if _ai_logic_cache is not None else "empty",
        "cache_age_seconds": time.time() - _cache_timestamp if _cache_timestamp > 0 else 0
    }
