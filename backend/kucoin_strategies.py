import logging
from typing import Dict, List, Tuple
from indicators import calculate_sma, calculate_ema, calculate_rsi, calculate_macd, calculate_bollinger_bands, calculate_volume_oscillator, calculate_stochastic_oscillator, calculate_ichimoku_cloud, calculate_fibonacci_retracement, calculate_parabolic_sar, calculate_atr

logger = logging.getLogger("kucoin_strategies")

def trend_following_strategy(short_ma: float, long_ma: float) -> str:
    """
    Trend Following strategy using Moving Average Crossover.
    """
    if short_ma > long_ma:
        return "BUY"
    elif short_ma < long_ma:
        return "SELL"
    return "HOLD"

def scalping_strategy(price_data: List[float], volume_data: List[float]) -> str:
    """
    Basic Scalping strategy based on rapid price and volume changes.
    """
    if len(price_data) < 3 or len(volume_data) < 3:
        return "HOLD"

    # Check for rapid price movement
    price_diff = price_data[-1] - price_data[-2]
    volume_diff = volume_data[-1] - volume_data[-2]

    if price_diff > 0.001 and volume_diff > 0:
        return "BUY"
    elif price_diff < -0.001 and volume_diff > 0:
        return "SELL"
    return "HOLD"

def swing_trading_strategy(rsi: float, macd_line: float, macd_signal: float, fib_levels: Dict[str, float], current_price: float) -> str:
    """
    Swing Trading strategy using RSI, MACD, and Fibonacci levels.
    """
    rsi_signal = rsi_strategy(rsi)
    macd_signal_val = macd_strategy(macd_line, macd_signal)

    # Simplified Fibonacci logic: buy near support, sell near resistance
    fib_buy_level = fib_levels.get("38.2%")
    fib_sell_level = fib_levels.get("61.8%")

    if (
        rsi_signal == "BUY"
        and macd_signal_val == "BUY"
        and fib_buy_level is not None
        and current_price < fib_buy_level
    ):
        return "BUY"
    elif (
        rsi_signal == "SELL"
        and macd_signal_val == "SELL"
        and fib_sell_level is not None
        and current_price > fib_sell_level
    ):
        return "SELL"
    return "HOLD"

def mean_reversion_strategy(price: float, middle_band: float, upper_band: float, lower_band: float) -> str:
    """
    Mean Reversion strategy using Bollinger Bands.
    """
    if price <= lower_band:
        return "BUY"
    elif price >= upper_band:
        return "SELL"
    return "HOLD"

def news_based_trading_strategy(news_sentiment_score: float, news_keywords: List[str]) -> str:
    """
    News-Based Trading strategy. Simplified: triggers based on sentiment and specific keywords.
    """
    positive_keywords = ["partnership", "listing", "innovation", "breakthrough"]
    negative_keywords = ["hack", "scam", "regulation", "ban"]

    if news_sentiment_score > 0.7 and any(keyword in ' '.join(news_keywords).lower() for keyword in positive_keywords):
        return "BUY"
    elif news_sentiment_score < 0.3 and any(keyword in ' '.join(news_keywords).lower() for keyword in negative_keywords):
        return "SELL"
    return "HOLD"

def volume_spike_trading_strategy(volume_ratio: float, price_change: float) -> str:
    """
    Volume Spike Trading strategy. Buys on significant volume increase with positive price action.
    """
    if volume_ratio > 2.0 and price_change > 0.01: # 2x average volume and 1% price increase
        return "BUY"
    elif volume_ratio > 2.0 and price_change < -0.01: # 2x average volume and 1% price decrease
        return "SELL"
    return "HOLD"

def grid_trading_strategy(current_price: float, grid_levels: List[float]) -> str:
    """
    Grid Trading strategy. Places buy/sell orders at predefined price levels.
    """
    # This is a simplified representation. A real grid trading bot would manage multiple orders.
    for i in range(len(grid_levels) - 1):
        lower = grid_levels[i]
        upper = grid_levels[i+1]
        if current_price >= lower and current_price < upper:
            # If price is in a grid, decide based on proximity to edges
            if current_price - lower < upper - current_price: # Closer to lower bound
                return "BUY"
            else:
                return "SELL"
    return "HOLD"

def spot_grid_strategy(price_data: Dict[str, float]) -> Tuple[str, str]:
    """
    Buys low, sells high. Works well in sideways markets.
    Returns a tuple: (decision, reason)
    """
    low = price_data.get('low')
    high = price_data.get('high')
    current = price_data.get('price')

    if None in (low, high, current):
        logger.warning("Missing price data for spot_grid_strategy")
        return "HOLD", "Missing price data"

    if low is None or high is None:
        logger.warning("Missing low or high price for spot_grid_strategy")
        return "HOLD", "Missing low or high price"
    midpoint = (high + low) / 2
    logger.debug(f"Spot Grid: current={current}, midpoint={midpoint}")

    # Adjusted thresholds for more sensitivity
    buy_threshold = midpoint * 1.01
    sell_threshold = midpoint * 0.99

    if current is not None and current < buy_threshold:
        return "BUY", f"Current price {current} is below buy threshold {buy_threshold}"
    elif current is not None and current > sell_threshold:
        return "SELL", f"Current price {current} is above sell threshold {sell_threshold}"
    return "HOLD", "Price near midpoint"

def value_investing_strategy(fundamentals: Dict[str, float]) -> str:
    """
    Inspired by Warren Buffett - buy undervalued stocks.
    Returns one of: "BUY", "HOLD", "SELL"
    """
    pe_ratio = fundamentals.get("pe_ratio")
    debt_to_equity = fundamentals.get("debt_to_equity")
    if pe_ratio is None or debt_to_equity is None:
        return "HOLD"
    if pe_ratio < 15 and debt_to_equity < 0.5:
        return "BUY"
    elif pe_ratio > 25 or debt_to_equity > 1.0:
        return "SELL"
    return "HOLD"

def momentum_strategy(price_history: List[float]) -> str:
    """
    Inspired by Richard Dennis - buy assets with upward momentum.
    Returns one of: "BUY", "HOLD", "SELL"
    """
    if len(price_history) < 2:
        return "HOLD"
    if price_history[-1] > price_history[-2]:
        return "BUY"
    elif price_history[-1] < price_history[-2]:
        return "SELL"
    return "HOLD"

def contrarian_strategy(sentiment_score: float) -> str:
    """
    Inspired by David Dreman - buy when sentiment is low (fear).
    Returns one of: "BUY", "HOLD", "SELL"
    """
    if sentiment_score < -0.5:
        return "BUY"
    elif sentiment_score > 0.5:
        return "SELL"
    return "HOLD"

def growth_investing_strategy(earnings_growth: float) -> str:
    """
    Inspired by Peter Lynch - buy stocks with strong earnings growth.
    Returns one of: "BUY", "HOLD", "SELL"
    """
    if earnings_growth > 0.15:
        return "BUY"
    elif earnings_growth < 0.05:
        return "SELL"
    return "HOLD"

def futures_grid_strategy(trend: str) -> str:
    """
    Uses trend analysis to decide whether to LONG or SHORT.
    Returns one of: "LONG", "SHORT", "HOLD"
    """
    logger.debug(f"Futures Grid: trend={trend}")
    if trend == "uptrend":
        return "LONG"
    elif trend == "downtrend":
        return "SHORT"
    return "HOLD"

def martingale_strategy(price_data: Dict[str, float], previous_loss: bool) -> str:
    """
    Buy in stages, double down on losses.
    Returns one of: "BUY_MORE", "BUY"
    """
    logger.debug(f"Martingale: previous_loss={previous_loss}")
    return "BUY_MORE" if previous_loss else "BUY"

def smart_rebalance_strategy(
    portfolio: Dict[str, float], ideal_allocations: Dict[str, float]
) -> List[Tuple[str, str]]:
    """
    Rebalances portfolio weights based on long-term targets.
    Returns list of tuples like [("TOKEN", "BUY"), ...]
    """
    actions: List[Tuple[str, str]] = []
    for token, target_weight in ideal_allocations.items():
        current_weight = portfolio.get(token, 0.0)
        lower_bound = target_weight * 0.95
        upper_bound = target_weight * 1.05

        if current_weight < lower_bound:
            actions.append((token, "BUY"))
        elif current_weight > upper_bound:
            actions.append((token, "SELL"))
        else:
            logger.debug(f"{token} within range ({lower_bound:.2f}-{upper_bound:.2f})")

    return actions or [("HOLD", "NO_ACTION")]

def dca_strategy(step: int, dip_detected: bool) -> str:
    """
    Buys on every time step, adds extra if dip is detected.
    Returns one of: "BUY_EXTRA", "BUY_REGULAR"
    """
    logger.debug(f"DCA: step={step}, dip_detected={dip_detected}")
    return "BUY_EXTRA" if dip_detected else "BUY_REGULAR"

def infinity_grid_strategy(price_data: Dict[str, float]) -> str:
    """
    Bullish grid strategy — never sells completely.
    Returns one of: "BUY", "PARTIAL_SELL", "HOLD"
    """
    price = price_data.get("price")
    ema = price_data.get("ema")

    if price is None or ema is None:
        logger.warning("Missing price/ema data in infinity_grid_strategy")
        return "HOLD"

    logger.debug(f"Infinity Grid: price={price}, ema={ema}")
    if price < ema:
        return "BUY"
    elif price > ema:
        return "PARTIAL_SELL"
    return "HOLD"

def turtle_trading_strategy(price_history: List[float], breakout_period: int = 20) -> str:
    """
    Turtle Trading strategy - buy on breakout above highest high of last N days,
    sell on breakdown below lowest low of last N days.
    Returns one of: "BUY", "SELL", "HOLD"
    """
    if len(price_history) < breakout_period:
        return "HOLD"
    highest_high = max(price_history[-breakout_period:])
    lowest_low = min(price_history[-breakout_period:])
    current_price = price_history[-1]

    if current_price > highest_high:
        return "BUY"
    elif current_price < lowest_low:
        return "SELL"
    return "HOLD"

def moving_average_crossover_strategy(short_ma: float, long_ma: float) -> str:
    """
    Moving Average Crossover strategy - buy when short MA crosses above long MA,
    sell when short MA crosses below long MA.
    Returns one of: "BUY", "SELL", "HOLD"
    """
    if short_ma > long_ma:
        return "BUY"
    elif short_ma < long_ma:
        return "SELL"
    return "HOLD"

def bollinger_bands_strategy(price: float, upper_band: float, lower_band: float) -> str:
    """
    Bollinger Bands strategy - buy when price touches lower band,
    sell when price touches upper band.
    Returns one of: "BUY", "SELL", "HOLD"
    """
    if price <= lower_band:
        return "BUY"
    elif price >= upper_band:
        return "SELL"
    return "HOLD"

def rsi_strategy(rsi: float, oversold_threshold: float = 30, overbought_threshold: float = 70) -> str:
    """
    RSI strategy - buy when RSI is below oversold threshold,
    sell when RSI is above overbought threshold.
    Returns one of: "BUY", "SELL", "HOLD"
    """
    if rsi < oversold_threshold:
        return "BUY"
    elif rsi > overbought_threshold:
        return "SELL"
    return "HOLD"

def macd_strategy(macd: float, signal: float) -> str:
    """
    MACD strategy - buy when MACD crosses above signal line,
    sell when MACD crosses below signal line.
    Returns one of: "BUY", "SELL", "HOLD"
    """
    if macd > signal:
        return "BUY"
    elif macd < signal:
        return "SELL"
    return "HOLD"

def breakout_strategy(price: float, breakout_level: float) -> str:
    """
    Breakout strategy - buy when price breaks above breakout level,
    sell when price falls below breakout level.
    Returns one of: "BUY", "SELL", "HOLD"
    """
    if price > breakout_level:
        return "BUY"
    elif price < breakout_level:
        return "SELL"
    return "HOLD"

