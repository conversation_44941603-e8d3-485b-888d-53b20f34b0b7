"""
AlphaPredatorBot — Telegram News Listener

Fetches CoinTelegraph headlines using RSS and returns a list of articles.
"""

import feedparser
import logging
from typing import List, Dict

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def _fetch_cointelegraph_headlines(limit: int = 5) -> List[Dict[str, str]]:
    logger.info("📰 Fetching CoinTelegraph headlines via RSS...")
    articles = []
    try:
        feed = feedparser.parse("https://cointelegraph.com/rss")
        for entry in feed.entries[:limit]:
            articles.append({
                "title": entry.get("title", "No Title"),
                "content": entry.get("summary", ""),
                "source": entry.get("link", "")
            })
            logger.info(f"[CoinTelegraph] {entry.get('title')}")
    except Exception as e:
        logger.exception("⚠️ Error fetching CoinTelegraph RSS")
    return articles


def _fetch_coindesk_headlines(limit: int = 5) -> List[Dict[str, str]]:
    logger.info("📰 Fetching Coindesk headlines...")
    # Stub implementation
    return []


def _fetch_cryptopanic_headlines(limit: int = 5) -> List[Dict[str, str]]:
    logger.info("📰 Fetching CryptoPanic headlines...")
    # Stub implementation
    return []


def _fetch_reddit_trending(limit: int = 5) -> List[Dict[str, str]]:
    logger.info("📰 Fetching Reddit trending posts...")
    # Stub implementation
    return []


def fetch_news_from_sources(limit_per_source: int = 5) -> List[Dict[str, str]]:
    """
    Aggregates crypto news from all sources into a single list.

    Args:
        limit_per_source (int): Number of articles to fetch per source.

    Returns:
        List[Dict[str, str]]: Combined list of news articles.
    """
    news = []
    news += _fetch_cointelegraph_headlines(limit_per_source)
    news += _fetch_coindesk_headlines(limit_per_source)
    news += _fetch_cryptopanic_headlines(limit_per_source)
    news += _fetch_reddit_trending(limit_per_source)
    logger.info(f"✅ Total articles fetched from all sources: {len(news)}")
    return news
