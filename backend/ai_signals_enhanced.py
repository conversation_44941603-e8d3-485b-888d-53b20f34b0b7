"""
Enhanced AI Signals Implementation for Alpha Predator
Uses cached TokenMetrics data for fast, efficient AI signal generation
"""

import json
import time
import logging
import os

logger = logging.getLogger(__name__)

def generate_ai_signals_from_tokenmetrics():
    """
    Generate AI signals using cached TokenMetrics data from discover tokens
    This is much faster than making live API calls
    """
    try:
        # Load discover tokens (already contains TokenMetrics data)
        discover_file = "data/discover_tokens.json"
        if not os.path.exists(discover_file):
            logger.warning("Discover tokens file not found")
            return []
        
        with open(discover_file, "r") as f:
            tokens = json.load(f)
        
        signals = []
        
        for token in tokens[:5]:  # Top 5 tokens
            try:
                symbol = token.get("symbol", "")
                if not symbol:
                    continue

                # Extract TokenMetrics data from discover tokens
                tm_grade = token.get("tm_grade", "N/A")
                tm_score = token.get("tm_score", 50)
                score = token.get("score", 0)
                price = token.get("price", 0)
                volume = token.get("volume", 0)
                
                # Generate AI signal based on TokenMetrics grade and score
                signal_data = generate_signal_from_tokenmetrics_grade(
                    tm_grade, tm_score, score, volume
                )
                
                signal = {
                    "symbol": symbol,
                    "signal": signal_data["signal"],
                    "confidence": signal_data["confidence"],
                    "reason": signal_data["reason"],
                    "source": "TokenMetrics AI Analysis",
                    "timestamp": time.time(),
                    "tm_grade": tm_grade,
                    "tm_score": tm_score,
                    "price": price,
                    "volume": volume,
                    "score": score,
                }
                signals.append(signal)

            except Exception as e:
                logger.warning(f"Failed to generate AI signal for token: {e}")
                continue
        
        return signals
        
    except Exception as e:
        logger.error(f"Failed to generate AI signals: {e}")
        return []

def generate_signal_from_tokenmetrics_grade(tm_grade, tm_score, score, volume):
    """
    Generate trading signal based on TokenMetrics grade and additional factors
    """
    signal_type = "HOLD"
    confidence = 50
    reason = f"TokenMetrics Grade: {tm_grade}, Score: {score:.2f}"
    
    # TokenMetrics grade-based AI signals
    if tm_grade in ["A+", "A", "A-"]:
        signal_type = "BUY"
        confidence = 80 + min((tm_score - 70) * 0.5, 5)  # 80-85%
        reason = f"Excellent TokenMetrics grade {tm_grade} (Score: {score:.2f})"
        
    elif tm_grade in ["B+", "B"]:
        if score > 1.5 and tm_score > 65:
            signal_type = "BUY"
            confidence = 70 + min((tm_score - 60) * 0.3, 5)  # 70-75%
            reason = f"Good TokenMetrics grade {tm_grade} with strong score {score:.2f}"
        else:
            signal_type = "HOLD"
            confidence = 60
            reason = f"Good TokenMetrics grade {tm_grade} but moderate score {score:.2f}"
            
    elif tm_grade in ["B-", "C+", "C"]:
        if score > 2.0 and tm_score > 70:
            signal_type = "BUY"
            confidence = 65
            reason = f"Average grade {tm_grade} but high score {score:.2f}"
        elif score < 1.0 or tm_score < 40:
            signal_type = "SELL"
            confidence = 60
            reason = f"Average grade {tm_grade} with low score {score:.2f}"
        else:
            signal_type = "HOLD"
            confidence = 50
            reason = f"Average TokenMetrics grade {tm_grade}, score {score:.2f}"
            
    elif tm_grade in ["C-", "D+", "D", "D-", "F"]:
        signal_type = "SELL"
        confidence = 70
        reason = f"Poor TokenMetrics grade {tm_grade} indicates sell signal"
        
    else:
        # Fallback to score-based logic for unknown grades
        if score > 2.0 and tm_score > 70:
            signal_type = "BUY"
            confidence = 65
            reason = f"High score {score:.2f} despite unknown grade"
        elif score > 1.5 and tm_score > 60:
            signal_type = "HOLD"
            confidence = 55
            reason = f"Moderate score {score:.2f}, holding position"
        else:
            signal_type = "HOLD"
            confidence = 45
            reason = f"Low score {score:.2f}, neutral position"

    # Volume-based confidence adjustments
    if volume > 50000000:  # Very high volume
        confidence += 8
        reason += " (Very high volume)"
    elif volume > 10000000:  # High volume
        confidence += 5
        reason += " (High volume)"
    elif volume > 1000000:  # Medium volume
        confidence += 2
        reason += " (Good volume)"
    
    # Score-based confidence adjustments
    if score > 2.5:
        confidence += 3
    elif score > 2.0:
        confidence += 2
    elif score < 0.5:
        confidence -= 5
    
    # Cap confidence between 30-85%
    confidence = min(max(confidence, 30), 85)
    
    return {
        "signal": signal_type,
        "confidence": int(confidence),
        "reason": reason
    }

def get_cached_ai_signals():
    """
    Get AI signals with caching for performance
    """
    cache_file = "data/ai_signals_cache.json"
    
    # Check cache first
    if os.path.exists(cache_file):
        try:
            with open(cache_file, "r") as f:
                cached_data = json.load(f)
                if time.time() - cached_data.get("timestamp", 0) < 300:  # 5 min cache
                    return cached_data
        except Exception as e:
            logger.warning(f"Failed to load AI signals cache: {e}")
    
    # Generate fresh signals
    signals = generate_ai_signals_from_tokenmetrics()
    
    result = {
        "signals": signals,
        "source": "tokenmetrics_ai_analysis",
        "timestamp": time.time(),
        "total_signals": len(signals),
    }
    
    # Cache the result
    try:
        os.makedirs("data", exist_ok=True)
        with open(cache_file, "w") as f:
            json.dump(result, f, indent=2)
    except Exception as e:
        logger.warning(f"Failed to cache AI signals: {e}")
    
    return result

def get_ai_signals_summary():
    """
    Get a summary of current AI signals
    """
    try:
        signals_data = get_cached_ai_signals()
        signals = signals_data.get("signals", [])
        
        if not signals:
            return {
                "total_signals": 0,
                "buy_signals": 0,
                "sell_signals": 0,
                "hold_signals": 0,
                "avg_confidence": 0,
                "top_signal": None
            }
        
        buy_count = len([s for s in signals if s.get("signal") == "BUY"])
        sell_count = len([s for s in signals if s.get("signal") == "SELL"])
        hold_count = len([s for s in signals if s.get("signal") == "HOLD"])
        
        avg_confidence = sum(s.get("confidence", 0) for s in signals) / len(signals)
        
        # Get highest confidence signal
        top_signal = max(signals, key=lambda x: x.get("confidence", 0))
        
        return {
            "total_signals": len(signals),
            "buy_signals": buy_count,
            "sell_signals": sell_count,
            "hold_signals": hold_count,
            "avg_confidence": round(avg_confidence, 1),
            "top_signal": {
                "symbol": top_signal.get("symbol"),
                "signal": top_signal.get("signal"),
                "confidence": top_signal.get("confidence"),
                "tm_grade": top_signal.get("tm_grade")
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get AI signals summary: {e}")
        return {
            "total_signals": 0,
            "buy_signals": 0,
            "sell_signals": 0,
            "hold_signals": 0,
            "avg_confidence": 0,
            "top_signal": None,
            "error": str(e)
        }
