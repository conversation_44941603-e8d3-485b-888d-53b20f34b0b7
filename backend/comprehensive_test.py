#!/usr/bin/env python3
"""
Comprehensive Alpha Predator System Test
"""

import requests
import json
import sys
import time

def test_api_endpoint(url, name):
    """Test an API endpoint"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {name}: Status {response.status_code}")
            return True, data
        else:
            print(f"❌ {name}: Status {response.status_code}")
            return False, None
    except Exception as e:
        print(f"❌ {name}: Error - {e}")
        return False, None

def main():
    print("🧪 COMPREHENSIVE ALPHA PREDATOR SYSTEM TESTS")
    print("=" * 50)
    
    base_url = "http://localhost:3005"
    tests_passed = 0
    total_tests = 0
    
    # Test endpoints
    endpoints = [
        ("/", "Server Health Check"),
        ("/api/dashboard/summary-fast", "Dashboard Summary (Fast)"),
        ("/api/dashboard/trades-fast", "Dashboard Trades (Fast)"),
        ("/api/dashboard/pnl-fast", "Dashboard PnL (Fast)"),
        ("/api/dashboard/discover-fast", "Dashboard Discover (Fast)"),
        ("/api/dashboard/all-fast", "Dashboard All Data (Fast)"),
        ("/api/coingecko/usage", "CoinGecko Usage Stats"),
        ("/api/coingecko/enhanced-tokens?limit=5", "CoinGecko Enhanced Tokens"),
        ("/api/coingecko/trending", "CoinGecko Trending"),
    ]
    
    print(f"\n📊 TESTING {len(endpoints)} API ENDPOINTS:")
    print("-" * 50)
    
    for endpoint, name in endpoints:
        total_tests += 1
        url = f"{base_url}{endpoint}"
        success, data = test_api_endpoint(url, name)
        
        if success:
            tests_passed += 1
            
            # Show specific data for key endpoints
            if "all-fast" in endpoint and data:
                cache_info = data.get("cache_info", {})
                data_sources = cache_info.get("data_sources", [])
                cached_sections = cache_info.get("cached_sections", [])
                print(f"  📈 Data Sources: {', '.join(data_sources)}")
                print(f"  💾 Cached Sections: {len(cached_sections)}")
                
            elif "coingecko/usage" in endpoint and data:
                provider = data.get("provider", "Unknown")
                plan = data.get("plan", "Unknown")
                monthly_calls = data.get("monthly_calls", 0)
                monthly_limit = data.get("monthly_limit", 0)
                status = data.get("status", "Unknown")
                print(f"  🌐 {provider} ({plan}): {monthly_calls}/{monthly_limit} calls, Status: {status}")
                
            elif "enhanced-tokens" in endpoint and data:
                tokens = data.get("tokens", [])
                count = data.get("count", 0)
                source = data.get("source", "Unknown")
                print(f"  🪙 Enhanced Tokens: {count} tokens from {source}")
                
            elif "discover-fast" in endpoint and data:
                tokens = data.get("tokens", [])
                source = data.get("source", "Unknown")
                print(f"  🔍 Discover Tokens: {len(tokens)} tokens from {source}")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! System is fully operational.")
        
        print(f"\n🚀 SYSTEM STATUS:")
        print(f"✅ Server: Running on {base_url}")
        print(f"✅ Fast Cache: Operational")
        print(f"✅ CoinGecko MCP: Integrated")
        print(f"✅ Dashboard: All endpoints working")
        print(f"✅ Data Sources: KuCoin, TokenMetrics, CoinGecko MCP, File Cache")
        
        return True
    else:
        print(f"❌ {total_tests - tests_passed} tests failed. System needs attention.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
