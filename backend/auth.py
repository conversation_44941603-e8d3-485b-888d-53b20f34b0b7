from datetime import datetime, timedelta
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status, Depends
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer
from passlib.context import Crypt<PERSON>ontext
import os

# Import ALLOWED_EMAILS from config to ensure consistency
from config import ALLOWED_EMAILS

SECRET_KEY = os.getenv("JWT_SECRET_KEY", "super-secret")
ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/login")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=30) # Default expiry
    to_encode.update({"exp": expire})
    assert SECRET_KEY is not None, "SECRET_KEY cannot be None"
    assert ALGORITHM is not None, "ALGORITHM cannot be None"
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str, credentials_exception):
    try:
        assert SECRET_KEY is not None, "SECRET_KEY cannot be None"
        assert ALGORITHM is not None, "ALGORITHM cannot be None"
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
        return email
    except JWTError:
        raise credentials_exception

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    email = verify_token(token, credentials_exception)
    if email not in ALLOWED_EMAILS:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized")
    return email
