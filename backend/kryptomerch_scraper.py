from utils.api_client import get
from bs4 import BeautifulSoup
from textblob import TextBlob
import logging
import time
from typing import List, Dict
import requests

BLOG_URL: str = "https://kryptomerch.io/blog"
USER_AGENT: str = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36"
)
REQUEST_TIMEOUT: int = 10  # seconds
CACHE_TTL: int = 300  # Cache time-to-live in seconds (5 minutes)

_cache: Dict[str, Dict[str, object]] = {}

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s"
)
logger = logging.getLogger("kryptomerch_scraper")


def fetch_kryptomerch_articles(token: str) -> List[Dict[str, str]]:
    """
    DISABLED: Kryptomerch scraper disabled due to 403 errors.
    Returns empty list to avoid blocking the system.
    Use Discord news from kryptonewsdaily instead.
    """
    logger.debug(f"Kryptomerch scraper disabled for {token}, returning empty results")
    return []


def get_sentiment_for_token(token: str) -> float:
    timestamp = cached.get("timestamp")
    if (
        isinstance(timestamp, (int, float))
        and time.time() - float(timestamp) < CACHE_TTL
    ):
        logger.info(f"[CACHE HIT] Returning cached articles for token '{token}'")
        return list(cached["articles"])  # type: ignore

    # Fix potential indentation issues
    headers: Dict[str, str] = {"User-Agent": USER_AGENT}
    params: Dict[str, str] = {"search": token}

    try:
        logger.info(f"[FETCH] Scraping Kryptomerch blog for token '{token}'")
        response = get(
            BLOG_URL,
            headers=headers,
            params=params,
            timeout=REQUEST_TIMEOUT,
            cache_ttl=CACHE_TTL,
        )
        response.raise_for_status()
        soup = BeautifulSoup(response.text, "html.parser")

        articles: List[Dict[str, str]] = []
        for article in soup.find_all("article"):
            title_tag = article.find("h2") or article.find("h1")
            summary_tag = article.find("p")
            title: str = title_tag.text.strip() if title_tag else "No Title"
            summary: str = summary_tag.text.strip() if summary_tag else ""
            if token_lower in title.lower() or token_lower in summary.lower():
                articles.append({"title": title, "summary": summary})

        logger.info(
            f"[INFO] Found {len(articles)} articles containing token '{token}'."
        )
        _cache[token_lower] = {"timestamp": time.time(), "articles": articles}
        return articles

    except requests.RequestException as e:
        logger.error(f"[ERROR] HTTP error while fetching blog articles: {e}")
    except Exception as e:
        logger.error(f"[ERROR] Unexpected error while scraping blog: {e}")

    return []


def analyze_sentiment_from_blog(token: str) -> float:
    """
    Analyze sentiment polarity of fetched blog articles for a given token.
    Returns average polarity rounded to 3 decimals.
    Returns 0.0 if no articles or on failure.
    """
    try:
        articles: List[Dict[str, str]] = fetch_kryptomerch_articles(token)
        if not articles:
            logger.warning(
                f"[WARN] No articles found for token '{token}', returning neutral sentiment 0.0"
            )
            return 0.0

        total_score: float = 0.0
        for article in articles:
            content: str = f"{article['title']} {article['summary']}"
            try:
                blob = TextBlob(content)
                sentiment: float = getattr(blob.sentiment, "polarity", 0.0)
                total_score += sentiment
            except Exception as e:
                logger.warning(
                    f"[WARN] Sentiment analysis failed for '{article['title']}': {e}"
                )

        avg_score: float = round(total_score / len(articles), 3)
        logger.info(f"[INFO] Average sentiment for token '{token}': {avg_score}")
        return avg_score

    except Exception as e:
        logger.error(f"[ERROR] Sentiment analysis failed: {e}")
        return 0.0


if __name__ == "__main__":
    sample_token: str = "bitcoin"
    score: float = analyze_sentiment_from_blog(sample_token)
    logger.info(f"🧠 Kryptomerch Blog Sentiment Score for '{sample_token}': {score}")
