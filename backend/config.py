import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Enhanced configuration management with security and validation
logger = logging.getLogger(__name__)

# Load environment variables from .env files with priority
load_dotenv(".env.production")  # Production overrides
load_dotenv(".env")  # Development defaults


# Configuration validation and security
class ConfigValidator:
    """Enhanced configuration validator with security checks."""

    @staticmethod
    def validate_api_key(key: Optional[str], service_name: str) -> bool:
        """Validate API key format and security."""
        if not key or key.startswith("YOUR_") or len(key) < 10:
            logger.warning(f"⚠️ Invalid or missing {service_name} API key")
            return False
        return True

    @staticmethod
    def validate_numeric_config(
        value: str, default: float, min_val: float = 0, max_val: float = float("inf")
    ) -> float:
        """Validate numeric configuration with bounds."""
        try:
            num_val = float(value)
            if min_val <= num_val <= max_val:
                return num_val
            else:
                logger.warning(
                    f"⚠️ Config value {num_val} out of bounds [{min_val}, {max_val}], using default {default}"
                )
                return default
        except (ValueError, TypeError):
            logger.warning(
                f"⚠️ Invalid numeric config '{value}', using default {default}"
            )
            return default

    @staticmethod
    def secure_log_key(key: Optional[str]) -> str:
        """Safely log API key for debugging."""
        if not key:
            return "❌ Not set"
        elif len(key) < 10:
            return "❌ Too short"
        else:
            return f"✅ {key[:8]}...{key[-4:]}"


config_validator = ConfigValidator()

# AI API Keys with validation and fallbacks
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY")
TOKENMETRICS_API_KEY = os.getenv("TOKENMETRICS_API_KEY")

# API Key validation
validator = ConfigValidator()

# Validate critical API keys
OPENAI_VALID = validator.validate_api_key(OPENAI_API_KEY, "OpenAI")
GEMINI_VALID = validator.validate_api_key(GEMINI_API_KEY, "Gemini")
TOGETHER_VALID = validator.validate_api_key(TOGETHER_API_KEY, "Together")
TOKENMETRICS_VALID = validator.validate_api_key(TOKENMETRICS_API_KEY, "TokenMetrics")

# Fallback configurations for missing API keys
AI_FALLBACK_MODE = not (OPENAI_VALID or GEMINI_VALID or TOGETHER_VALID)
TOKENMETRICS_FALLBACK_MODE = not TOKENMETRICS_VALID

# Telegram with validation
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
TELEGRAM_CHANNEL_ID = os.getenv("TELEGRAM_CHANNEL_ID")
TELEGRAM_VALID = validator.validate_api_key(TELEGRAM_BOT_TOKEN, "Telegram")

# Trading Configuration
TRADE_INTERVAL_SECONDS = int(os.getenv("TRADE_INTERVAL_SECONDS", "300"))
MAX_BUY = int(
    os.getenv("MAX_BUY", "20")
)  # 🚀 INCREASED: Allow up to 20 simultaneous buy orders
STOP_LOSS = float(os.getenv("STOP_LOSS", "0.10"))
TAKE_PROFIT = float(os.getenv("TAKE_PROFIT", "0.20"))

# Trading Parameters
DEFAULT_TRADE_USD = float(os.getenv("DEFAULT_TRADE_USD", "2.0"))
STOP_LOSS_PCT = float(os.getenv("STOP_LOSS_PCT", "0.05"))
MAX_TRADE_RISK_PCT = float(os.getenv("MAX_TRADE_RISK_PCT", "0.02"))
TRADING_MODE = os.getenv("TRADING_MODE", "LIVE")
VOLATILITY_MULTIPLIER = float(os.getenv("VOLATILITY_MULTIPLIER", "1.5"))
TAKE_PROFIT_USD = float(os.getenv("TAKE_PROFIT_USD", "20.0"))
BASE_ORDER_SIZE_USDT = float(os.getenv("BASE_ORDER_SIZE_USDT", "2.0"))
MIN_ORDER_SIZE_USDT = float(os.getenv("MIN_ORDER_SIZE_USDT", "1.0"))
MAX_ORDER_SIZE_USDT = float(os.getenv("MAX_ORDER_SIZE_USDT", "2.0"))

# Micro Bot Configuration - High Frequency $1 Profit Trades
MAX_DAILY_TRADES = int(os.getenv("MAX_DAILY_TRADES", "300"))  # High frequency trading
MICRO_STOP_LOSS_PCT = float(
    os.getenv("MICRO_STOP_LOSS_PCT", "0.02")
)  # Tighter stop loss
MICRO_RISK_PER_TRADE_PCT = float(os.getenv("MICRO_RISK_PER_TRADE_PCT", "0.01"))
MICRO_PROFIT_TARGET_USD = float(
    os.getenv("MICRO_PROFIT_TARGET_USD", "1.0")
)  # $1 profit per trade
MICRO_TRADE_SIZE_USD = float(
    os.getenv("MICRO_TRADE_SIZE_USD", "10.0")
)  # Small trade size for quick profits

# API Keys for External Services with validation
# CoinGecko API Configuration with fallback
COINGECKO_API_KEY = os.getenv("COINGECKO_API_KEY", "")
COINGECKO_USE_FREE_API = (
    not COINGECKO_API_KEY or COINGECKO_API_KEY == "your_coingecko_api_key_here"
)

if COINGECKO_USE_FREE_API:
    COINGECKO_BASE_URL = "https://api.coingecko.com/api/v3"
    COINGECKO_RATE_LIMIT = 50  # Free API limit per minute
else:
    COINGECKO_BASE_URL = "https://pro-api.coingecko.com/api/v3"
    COINGECKO_RATE_LIMIT = 500  # Pro API limit per minute
COINMARKETCAL_API_KEY = os.getenv("COINMARKETCAL_API_KEY", "")
GITHUB_OAUTH_TOKEN = os.getenv("GITHUB_OAUTH_TOKEN", "")

# Validate external service API keys
COINGECKO_VALID = (
    validator.validate_api_key(COINGECKO_API_KEY, "CoinGecko")
    if COINGECKO_API_KEY
    else False
)
GITHUB_VALID = (
    validator.validate_api_key(GITHUB_OAUTH_TOKEN, "GitHub")
    if GITHUB_OAUTH_TOKEN
    else False
)

# News and Data Configuration
USE_REAL_NEWS = os.getenv("USE_REAL_NEWS", "true").lower() == "true"

# Default crypto news RSS feeds if not specified in environment
# Removed problematic CoinMarketCap feed that causes parsing errors
DEFAULT_CRYPTO_RSS_FEEDS = [
    "https://cointelegraph.com/rss",
    "https://coindesk.com/arc/outboundfeeds/rss/",
    "https://decrypt.co/feed",
    "https://cryptonews.com/news/feed/",
    "https://www.newsbtc.com/feed/",
    "https://cryptopotato.com/feed/",
    "https://bitcoinist.com/feed/",
    "https://cryptopanic.com/news/rss/",  # Added CryptoPanic as replacement
]

RSS_FEEDS = (
    os.getenv("RSS_FEEDS", "").split(",")
    if os.getenv("RSS_FEEDS")
    else DEFAULT_CRYPTO_RSS_FEEDS
)

# KuCoin Configuration
# KuCoin Sandbox Configuration
KUCOIN_SANDBOX = os.getenv("KUCOIN_SANDBOX", "false").lower() == "true"
KUCOIN_SANDBOX_BASE_URL = "https://openapi-sandbox.kucoin.com"
KUCOIN_PRODUCTION_BASE_URL = "https://api.kucoin.com"

# Use appropriate base URL based on sandbox setting
KUCOIN_BASE_URL = (
    KUCOIN_SANDBOX_BASE_URL if KUCOIN_SANDBOX else KUCOIN_PRODUCTION_BASE_URL
)

# Firebase/Firestore
FIRESTORE_CREDENTIALS_PATH = os.getenv("FIRESTORE_CREDENTIALS_PATH", "")

# API Client Configuration
API_CLIENT_MIN_DELAY = float(os.getenv("API_CLIENT_MIN_DELAY", "1.0"))
API_CLIENT_RETRIES = int(os.getenv("API_CLIENT_RETRIES", "3"))
API_CLIENT_BACKOFF_FACTOR = float(os.getenv("API_CLIENT_BACKOFF_FACTOR", "2.0"))
API_CLIENT_DEFAULT_CACHE_TTL = int(os.getenv("API_CLIENT_DEFAULT_CACHE_TTL", "300"))

# KuCoin with validation
KUCOIN_API_KEY = os.getenv("KUCOIN_API_KEY")
KUCOIN_API_SECRET = os.getenv("KUCOIN_API_SECRET")
KUCOIN_API_PASSPHRASE = os.getenv("KUCOIN_API_PASSPHRASE")

# Validate KuCoin credentials
KUCOIN_VALID = all(
    [
        validator.validate_api_key(KUCOIN_API_KEY, "KuCoin API Key"),
        validator.validate_api_key(KUCOIN_API_SECRET, "KuCoin API Secret"),
        validator.validate_api_key(KUCOIN_API_PASSPHRASE, "KuCoin API Passphrase"),
    ]
)

# Authentication
ALLOWED_EMAILS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]

# Load additional allowed emails from environment
env_emails = os.getenv("ALLOWED_EMAILS", "")
if env_emails:
    additional_emails = [email.strip().lower() for email in env_emails.split(",")]
    ALLOWED_EMAILS.extend(additional_emails)

# Convert to lowercase for case-insensitive comparison and remove duplicates
ALLOWED_EMAILS = list(set([email.lower() for email in ALLOWED_EMAILS]))


# Enhanced configuration validation and security checks
def validate_configuration() -> Dict[str, Any]:
    """Comprehensive configuration validation with security checks."""
    validation_results = {
        "valid": True,
        "warnings": [],
        "errors": [],
        "api_keys": {},
        "trading_config": {},
        "security_status": "unknown",
    }

    # Validate API keys
    api_keys = {
        "OpenAI": OPENAI_API_KEY,
        "Gemini": GEMINI_API_KEY,
        "Together": TOGETHER_API_KEY,
        "TokenMetrics": TOKENMETRICS_API_KEY,
        "Telegram": TELEGRAM_BOT_TOKEN,
        "CoinGecko": COINGECKO_API_KEY,
    }

    for service, key in api_keys.items():
        is_valid = config_validator.validate_api_key(key, service)
        validation_results["api_keys"][service] = {
            "valid": is_valid,
            "status": config_validator.secure_log_key(key),
        }
        if not is_valid and service in ["OpenAI", "Telegram"]:
            validation_results["errors"].append(
                f"Critical {service} API key missing or invalid"
            )
            validation_results["valid"] = False
        elif not is_valid:
            validation_results["warnings"].append(
                f"{service} API key missing or invalid"
            )

    # Validate trading configuration
    trading_configs = {
        "TRADE_INTERVAL_SECONDS": (TRADE_INTERVAL_SECONDS, 60, 3600),  # 1 min to 1 hour
        "MAX_BUY": (MAX_BUY, 1, 20),  # 1 to 20 trades
        "STOP_LOSS": (STOP_LOSS, 0.01, 0.5),  # 1% to 50%
        "TAKE_PROFIT": (TAKE_PROFIT, 0.05, 2.0),  # 5% to 200%
        "MAX_DAILY_TRADES": (MAX_DAILY_TRADES, 1, 500),  # 1 to 500 trades
    }

    for config_name, (value, min_val, max_val) in trading_configs.items():
        if min_val <= value <= max_val:
            validation_results["trading_config"][config_name] = {
                "valid": True,
                "value": value,
            }
        else:
            validation_results["trading_config"][config_name] = {
                "valid": False,
                "value": value,
            }
            validation_results["warnings"].append(
                f"{config_name} value {value} outside recommended range [{min_val}, {max_val}]"
            )

    # Security assessment
    if TRADING_MODE == "LIVE":
        if not all([KUCOIN_API_KEY, KUCOIN_API_SECRET, KUCOIN_API_PASSPHRASE]):
            validation_results["errors"].append(
                "LIVE trading mode requires all KuCoin API credentials"
            )
            validation_results["valid"] = False
        validation_results["security_status"] = "live_trading"
    else:
        validation_results["security_status"] = "paper_trading"

    # Email validation
    if len(ALLOWED_EMAILS) == 0:
        validation_results["warnings"].append("No allowed emails configured")

    return validation_results


# Run validation
validation_result = validate_configuration()

# Enhanced logging with security awareness (only if not suppressed)
if not os.getenv("SUPPRESS_OPTIMIZATION_REPORT"):
    logger.info("🔧 Alpha Predator Bot Configuration Validation")
    logger.info("=" * 60)

    # Log API key status securely
    for service, status in validation_result["api_keys"].items():
        logger.info(f"🔑 {service} API Key: {status['status']}")

    # Log trading configuration
    logger.info(f"📊 Trading Mode: {TRADING_MODE}")
    logger.info(f"⏱️ Trade Interval: {TRADE_INTERVAL_SECONDS}s")
    logger.info(f"💰 Max Buy Orders: {MAX_BUY}")
    logger.info(f"📉 Stop Loss: {STOP_LOSS*100:.1f}%")
    logger.info(f"📈 Take Profit: {TAKE_PROFIT*100:.1f}%")
    logger.info(f"🎯 Max Daily Trades: {MAX_DAILY_TRADES}")

    # Log security status
    logger.info(f"🔒 Security Status: {validation_result['security_status'].upper()}")
    logger.info(f"📧 Allowed Emails: {len(ALLOWED_EMAILS)} configured")

    # Log warnings and errors
    if validation_result["warnings"]:
        logger.warning("⚠️ Configuration Warnings:")
        for warning in validation_result["warnings"]:
            logger.warning(f"   • {warning}")

    if validation_result["errors"]:
        logger.error("❌ Configuration Errors:")
        for error in validation_result["errors"]:
            logger.error(f"   • {error}")

    # Final validation status
    if validation_result["valid"]:
        logger.info("✅ Configuration validation PASSED - System ready for operation")
    else:
        logger.error(
            "❌ Configuration validation FAILED - Please fix errors before proceeding"
        )

# Export validation result for other modules
CONFIG_VALIDATION = validation_result


# Enhanced configuration validation with fallback modes
def get_system_status() -> Dict[str, Any]:
    """Get comprehensive system status including API availability."""
    return {
        "ai_services": {
            "openai": OPENAI_VALID,
            "gemini": GEMINI_VALID,
            "together": TOGETHER_VALID,
            "fallback_mode": AI_FALLBACK_MODE,
        },
        "external_apis": {
            "tokenmetrics": TOKENMETRICS_VALID,
            "coingecko": COINGECKO_VALID,
            "github": GITHUB_VALID,
            "telegram": TELEGRAM_VALID,
        },
        "trading": {
            "kucoin": KUCOIN_VALID,
            "mode": TRADING_MODE,
            "live_trading_ready": KUCOIN_VALID and TRADING_MODE == "LIVE",
        },
        "fallback_modes": {
            "ai_fallback": AI_FALLBACK_MODE,
            "tokenmetrics_fallback": TOKENMETRICS_FALLBACK_MODE,
        },
    }
