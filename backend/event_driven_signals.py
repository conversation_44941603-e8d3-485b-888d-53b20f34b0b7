
from coinmarketcal_api import CoinMarketCalAPI
from datetime import datetime, timedelta

class EventDrivenSignals:
    def __init__(self):
        self.api = CoinMarketCalAPI()

    def get_upcoming_events(self, days=7):
        """
        Gets upcoming events in the next `days`.
        """
        # Note: The CoinMarketCal API requires a 'max' parameter, which we'll set to a high number
        # to get all events in the date range.
        return self.api.get_events(dateRangeStart=datetime.now().strftime('%d/%m/%Y'), 
                                   dateRangeEnd=(datetime.now() + timedelta(days=days)).strftime('%d/%m/%Y'),
                                   max=1000)

    def generate_signals(self, events):
        """
        Generates trading signals from a list of events.
        For now, we'll generate a 'buy' signal for any event.
        """
        signals = []
        for event in events:
            signals.append({
                'symbol': event['symbol'],
                'signal': 'buy',
                'reason': f"Upcoming event: {event['title']}",
                'date': event['date_event']
            })
        return signals
