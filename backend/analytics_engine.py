import json
import logging
from pathlib import Path
from copy import deepcopy
import os

from constants import TRADE_LOG_PATH

from real_time_metrics import real_time_metrics

def error_response(code, message, status):
    return {
        "status": "error",
        "error_code": code,
        "message": message,
        "http_status": status
    }

logger = logging.getLogger(__name__)

analytics_file = Path("backend/data/analytics.json")

def generate_analytics(real_time=False):
    try:
        if real_time:
            logger.info("[Realtime Mode] Generating analytics...")
            # Placeholder for actual real-time analytics logic
            return {
                "status": "success",
                "mode": "real-time",
                "summary": {
                    "active_tokens": 5,
                    "top_gainer": "XYZ",
                    "top_loser": "ABC"
                }
            }
        else:
            logger.info("[Batch Mode] Generating analytics...")
            # Placeholder for actual batch analytics logic
            return {
                "status": "success",
                "mode": "batch",
                "summary": {
                    "total_tokens": 50,
                    "average_gain": "3.5%",
                    "average_loss": "2.1%"
                }
            }
    except Exception as e:
        logger.error(f"Error generating analytics: {e}")
        return error_response("ANALYTICS_GENERATION_FAILED", str(e), 500)

def get_analytics_summary():
    """
    Load analytics summary from analytics.json file.
    If file missing or corrupted, return a default message.
    """
    if not analytics_file.exists():
        logger.warning("Analytics file not found.")
        return error_response("ANALYTICS_FILE_NOT_FOUND", "No analytics data found.", 404)

    try:
        with open(analytics_file, "r") as f:
            data = json.load(f)
        return data
    except json.JSONDecodeError:
        logger.error("Analytics file is corrupted.")
        return error_response("ANALYTICS_FILE_CORRUPTED", "Analytics file is corrupted.", 500)
    except Exception as e:
        logger.error(f"Error loading analytics file: {e}")
        return error_response("ANALYTICS_LOAD_FAILED", f"Failed to load analytics data: {e}", 500)

