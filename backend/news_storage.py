import json
import os
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta

NEWS_STORAGE_FILE = "backend/data/news_storage.jsonl"
TRADE_STORAGE_FILE = "backend/data/trade_storage.jsonl"

# Ensure storage directories exist
os.makedirs(os.path.dirname(NEWS_STORAGE_FILE), exist_ok=True)
os.makedirs(os.path.dirname(TRADE_STORAGE_FILE), exist_ok=True)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def save_news_message(news_message: Dict) -> None:
    """
    Append a news message dictionary to the JSONL storage file after checking for duplicates.
    Adds default values for missing fields.
    """
    if is_duplicate(news_message):
        logging.info(f"Duplicate news message skipped: {news_message.get('message_id') or news_message.get('content', '')[:30]}...")
        return
    try:
        news_message.setdefault("source", "unknown")
        news_message.setdefault("type", "general")
        news_message.setdefault("credibility", 0.5)

        with open(NEWS_STORAGE_FILE, "a", encoding="utf-8") as f:
            json.dump(news_message, f)
            f.write("\n")
        logging.info(
            f"✅ News saved: {news_message.get('source')} | "
            f"{news_message.get('type')} | "
            f"Cred: {news_message.get('credibility')} | "
            f"{news_message.get('message_id') or news_message.get('content', '')[:30]}..."
        )
    except Exception as e:
        logging.error(f"❌ Failed to save news message: {e}")

def read_all_news() -> List[Dict]:
    """
    Read all news messages from the JSONL storage file.
    Returns:
        List[Dict]: List of news message dictionaries.
    """
    if not os.path.exists(NEWS_STORAGE_FILE):
        return []
    news_list = []
    with open(NEWS_STORAGE_FILE, "r", encoding="utf-8") as f:
        for line in f:
            try:
                news_list.append(json.loads(line))
            except json.JSONDecodeError:
                continue
    return news_list

def is_duplicate(news_message: Dict) -> bool:
    """
    Check if a news message is a duplicate based on message_id, exact content, and timestamp proximity.

    Args:
        news_message (Dict): News message dictionary.

    Returns:
        bool: True if the message is a duplicate, False otherwise.
    """
    existing_news = read_all_news()
    message_id = news_message.get("message_id")
    content = news_message.get("content", "").strip()
    timestamp_str = news_message.get("timestamp")

    try:
        timestamp = datetime.fromisoformat(timestamp_str) if timestamp_str else None
    except Exception:
        timestamp = None

    for news in existing_news:
        if message_id and news.get("message_id") == message_id:
            return True
        if content and news.get("content", "").strip() == content:
            existing_timestamp_str = news.get("timestamp")
            try:
                existing_timestamp = datetime.fromisoformat(existing_timestamp_str) if existing_timestamp_str else None
            except Exception:
                existing_timestamp = None
            if timestamp and existing_timestamp:
                if abs(timestamp - existing_timestamp) < timedelta(minutes=5):
                    return True
            else:
                return True
    return False

def save_trade_record(trade_record: Dict) -> None:
    """
    Append a trade record dictionary to the JSONL trade storage file.

    Args:
        trade_record (Dict): Trade record dictionary.
    """
    try:
        with open(TRADE_STORAGE_FILE, "a", encoding="utf-8") as f:
            json.dump(trade_record, f)
            f.write("\n")
        logging.info(f"✅ Trade record saved: {trade_record.get('trade_id') or trade_record.get('symbol', '')}")
    except Exception as e:
        logging.error(f"❌ Failed to save trade record: {e}")

def read_all_trades() -> List[Dict]:
    """
    Read all trade records from the JSONL trade storage file.

    Returns:
        List[Dict]: List of trade record dictionaries.
    """
    if not os.path.exists(TRADE_STORAGE_FILE):
        return []
    trades = []
    with open(TRADE_STORAGE_FILE, "r", encoding="utf-8") as f:
        for line in f:
            try:
                trades.append(json.loads(line))
            except json.JSONDecodeError:
                continue
    return trades