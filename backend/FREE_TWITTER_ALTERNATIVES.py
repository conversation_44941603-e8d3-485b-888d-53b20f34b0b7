#!/usr/bin/env python3
"""
Free Twitter Alternatives for Crypto Sentiment Analysis
Replaces expensive Twitter API with free alternatives
"""

import requests
import json
import time
from datetime import datetime, timedelta
import praw
import feedparser
from bs4 import BeautifulSoup
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class RedditCryptoSentiment:
    """Free Reddit API for crypto sentiment analysis"""
    
    def __init__(self):
        # Reddit API is FREE with generous rate limits
        self.reddit = praw.Reddit(
            client_id="your_reddit_client_id",  # Get from reddit.com/prefs/apps
            client_secret="your_reddit_client_secret",
            user_agent="AlphaPredator:v1.0 (by /u/yourusername)"
        )
        
        self.crypto_subreddits = [
            'CryptoCurrency',
            'Bitcoin', 
            'ethereum',
            'altcoin',
            'CryptoMarkets',
            'CryptoMoonShots',
            'SatoshiStreetBets'
        ]
    
    def get_token_sentiment(self, token_symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get sentiment for a specific token from Reddit"""
        try:
            all_posts = []
            
            for subreddit_name in self.crypto_subreddits:
                try:
                    subreddit = self.reddit.subreddit(subreddit_name)
                    
                    # Search for token mentions
                    for post in subreddit.search(token_symbol, limit=limit//len(self.crypto_subreddits)):
                        all_posts.append({
                            'title': post.title,
                            'text': post.selftext,
                            'score': post.score,
                            'upvote_ratio': post.upvote_ratio,
                            'num_comments': post.num_comments,
                            'created_utc': post.created_utc,
                            'subreddit': subreddit_name,
                            'url': post.url
                        })
                        
                except Exception as e:
                    logger.warning(f"Error accessing r/{subreddit_name}: {e}")
                    continue
            
            # Analyze sentiment
            sentiment_score = self._calculate_reddit_sentiment(all_posts)
            
            return {
                'source': 'reddit',
                'token': token_symbol,
                'sentiment_score': sentiment_score,
                'total_posts': len(all_posts),
                'average_score': sum(p['score'] for p in all_posts) / len(all_posts) if all_posts else 0,
                'posts': all_posts[:10],  # Return top 10 posts
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Reddit sentiment error for {token_symbol}: {e}")
            return {'error': str(e), 'sentiment_score': 50}
    
    def _calculate_reddit_sentiment(self, posts: List[Dict]) -> float:
        """Calculate sentiment score from Reddit posts"""
        if not posts:
            return 50.0
        
        # Weight by score and upvote ratio
        total_weighted_score = 0
        total_weight = 0
        
        for post in posts:
            # Positive indicators
            weight = max(1, post['score']) * post['upvote_ratio']
            
            # Simple sentiment based on score and engagement
            if post['score'] > 10 and post['upvote_ratio'] > 0.7:
                sentiment = 75  # Positive
            elif post['score'] > 0 and post['upvote_ratio'] > 0.5:
                sentiment = 60  # Slightly positive
            elif post['score'] < -5 or post['upvote_ratio'] < 0.3:
                sentiment = 25  # Negative
            else:
                sentiment = 50  # Neutral
            
            total_weighted_score += sentiment * weight
            total_weight += weight
        
        return total_weighted_score / total_weight if total_weight > 0 else 50.0


class CryptoNewsSentiment:
    """Free crypto news sentiment analysis"""
    
    def __init__(self):
        self.news_sources = {
            'cointelegraph': 'https://cointelegraph.com/rss',
            'coindesk': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'cryptonews': 'https://cryptonews.com/news/feed/',
            'decrypt': 'https://decrypt.co/feed',
            'bitcoinist': 'https://bitcoinist.com/feed/',
            'newsbtc': 'https://www.newsbtc.com/feed/'
        }
    
    def get_token_news_sentiment(self, token_symbol: str, hours_back: int = 24) -> Dict[str, Any]:
        """Get news sentiment for a token"""
        try:
            all_articles = []
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            for source_name, rss_url in self.news_sources.items():
                try:
                    feed = feedparser.parse(rss_url)
                    
                    for entry in feed.entries:
                        # Check if article mentions the token
                        title_text = entry.title.lower()
                        summary_text = getattr(entry, 'summary', '').lower()
                        
                        if (token_symbol.lower() in title_text or 
                            token_symbol.lower() in summary_text):
                            
                            # Parse publication date
                            pub_date = datetime.fromtimestamp(time.mktime(entry.published_parsed))
                            
                            if pub_date > cutoff_time:
                                all_articles.append({
                                    'title': entry.title,
                                    'summary': getattr(entry, 'summary', ''),
                                    'published': pub_date.isoformat(),
                                    'source': source_name,
                                    'link': entry.link
                                })
                                
                except Exception as e:
                    logger.warning(f"Error parsing {source_name}: {e}")
                    continue
            
            # Analyze sentiment
            sentiment_score = self._calculate_news_sentiment(all_articles, token_symbol)
            
            return {
                'source': 'news',
                'token': token_symbol,
                'sentiment_score': sentiment_score,
                'total_articles': len(all_articles),
                'articles': all_articles[:10],  # Return top 10 articles
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"News sentiment error for {token_symbol}: {e}")
            return {'error': str(e), 'sentiment_score': 50}
    
    def _calculate_news_sentiment(self, articles: List[Dict], token_symbol: str) -> float:
        """Calculate sentiment from news articles"""
        if not articles:
            return 50.0
        
        # Simple keyword-based sentiment
        positive_keywords = ['surge', 'rally', 'bullish', 'gains', 'up', 'rise', 'breakthrough', 'adoption']
        negative_keywords = ['crash', 'drop', 'bearish', 'down', 'fall', 'decline', 'sell-off', 'dump']
        
        total_sentiment = 0
        
        for article in articles:
            text = (article['title'] + ' ' + article['summary']).lower()
            
            positive_count = sum(1 for word in positive_keywords if word in text)
            negative_count = sum(1 for word in negative_keywords if word in text)
            
            if positive_count > negative_count:
                sentiment = 70 + min(positive_count * 5, 25)  # 70-95
            elif negative_count > positive_count:
                sentiment = 30 - min(negative_count * 5, 25)  # 5-30
            else:
                sentiment = 50  # Neutral
            
            total_sentiment += sentiment
        
        return total_sentiment / len(articles)


class SocialSentimentAggregator:
    """Combines multiple free sources for comprehensive sentiment"""
    
    def __init__(self):
        self.reddit_client = RedditCryptoSentiment()
        self.news_client = CryptoNewsSentiment()
    
    def get_comprehensive_sentiment(self, token_symbol: str) -> Dict[str, Any]:
        """Get sentiment from all free sources"""
        try:
            # Get Reddit sentiment
            reddit_data = self.reddit_client.get_token_sentiment(token_symbol)
            
            # Get news sentiment  
            news_data = self.news_client.get_token_news_sentiment(token_symbol)
            
            # Calculate weighted average
            reddit_score = reddit_data.get('sentiment_score', 50)
            news_score = news_data.get('sentiment_score', 50)
            
            # Weight: Reddit 40%, News 60% (news is more reliable)
            combined_score = (reddit_score * 0.4) + (news_score * 0.6)
            
            return {
                'token': token_symbol,
                'combined_sentiment_score': round(combined_score, 1),
                'reddit_sentiment': reddit_score,
                'news_sentiment': news_score,
                'reddit_posts': reddit_data.get('total_posts', 0),
                'news_articles': news_data.get('total_articles', 0),
                'confidence': self._calculate_confidence(reddit_data, news_data),
                'timestamp': datetime.now().isoformat(),
                'sources': {
                    'reddit': reddit_data,
                    'news': news_data
                }
            }
            
        except Exception as e:
            logger.error(f"Comprehensive sentiment error for {token_symbol}: {e}")
            return {
                'token': token_symbol,
                'combined_sentiment_score': 50.0,
                'error': str(e)
            }
    
    def _calculate_confidence(self, reddit_data: Dict, news_data: Dict) -> float:
        """Calculate confidence based on data availability"""
        reddit_posts = reddit_data.get('total_posts', 0)
        news_articles = news_data.get('total_articles', 0)
        
        # More data = higher confidence
        confidence = min(100, (reddit_posts * 2) + (news_articles * 10))
        return round(confidence, 1)


# Usage example and API integration
def get_token_sentiment_api(token_symbol: str) -> Dict[str, Any]:
    """API endpoint function for getting token sentiment"""
    aggregator = SocialSentimentAggregator()
    return aggregator.get_comprehensive_sentiment(token_symbol)


if __name__ == "__main__":
    # Test the system
    aggregator = SocialSentimentAggregator()
    result = aggregator.get_comprehensive_sentiment("BTC")
    print(json.dumps(result, indent=2))
