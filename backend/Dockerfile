FROM python:3.10-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

WORKDIR /app

# Set environment variables to match Flux specs
# REMOVED HARDCODED ADMIN_EMAIL AND ADMIN_PASSWORD FOR SECURITY

# Install system dependencies
RUN apt-get update && apt-get install -y build-essential

# Copy requirements first for better caching
COPY requirements.txt /app/requirements.txt

# Install pip and core NLTK/TextBlob dependencies first
RUN pip install --upgrade pip && \
    pip install --no-cache-dir nltk textblob

# Set NLTK data path and download necessary corpora
ENV NLTK_DATA /usr/local/nltk_data
RUN python -c "import nltk; import os; nltk.data.path.append(os.environ.get('NLTK_DATA')); nltk.download('punkt'); nltk.download('averaged_perceptron_tagger'); nltk.download('wordnet')"

# Install remaining Python dependencies
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copy the backend code only
COPY . /app

# Create required directories and files
RUN mkdir -p /app/data && \
    touch /app/data/news.json \
         /app/data/discord_news.json \
         /app/data/cmc_news.json \
         /app/data/cryptopanic.json \
         /app/data/news_signals.json \
         /app/data/ai_logic.json

EXPOSE 3005

CMD ["python3", "main.py"]
