"""
CoinGecko MCP (Model Context Protocol) Client
Integrates CoinGecko's free API with 30 calls/min, 10k calls/month
"""

import aiohttp
import asyncio
import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import json
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CoinGeckoData:
    """Data structure for CoinGecko API responses"""
    symbol: str
    name: str
    current_price: float
    market_cap: int
    market_cap_rank: int
    volume_24h: float
    price_change_24h: float
    price_change_percentage_24h: float
    circulating_supply: float
    total_supply: float
    max_supply: Optional[float]
    ath: float
    ath_change_percentage: float
    atl: float
    atl_change_percentage: float
    last_updated: str

class CoinGeckoMCPClient:
    """
    CoinGecko MCP Client for Alpha Predator Bot
    Uses free tier: 30 calls/min, 10k calls/month
    """
    
    def __init__(self):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.session = None
        self.rate_limit_calls = 0
        self.rate_limit_window_start = time.time()
        self.monthly_calls = 0
        self.monthly_limit = 10000  # Free tier limit
        self.rate_limit_per_minute = 30  # Free tier limit
        
        # Load usage stats
        self.usage_file = "data/coingecko_usage.json"
        self._load_usage_stats()
        
    def _load_usage_stats(self):
        """Load monthly usage statistics"""
        try:
            if os.path.exists(self.usage_file):
                with open(self.usage_file, 'r') as f:
                    data = json.load(f)
                    current_month = time.strftime("%Y-%m")
                    if data.get("month") == current_month:
                        self.monthly_calls = data.get("calls", 0)
                    else:
                        self.monthly_calls = 0
                        self._save_usage_stats()
        except Exception as e:
            logger.warning(f"Failed to load usage stats: {e}")
            self.monthly_calls = 0
    
    def _save_usage_stats(self):
        """Save monthly usage statistics"""
        try:
            os.makedirs("data", exist_ok=True)
            usage_data = {
                "month": time.strftime("%Y-%m"),
                "calls": self.monthly_calls,
                "last_updated": time.time()
            }
            with open(self.usage_file, 'w') as f:
                json.dump(usage_data, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save usage stats: {e}")
    
    def _check_rate_limits(self) -> bool:
        """Check if we can make an API call within rate limits"""
        current_time = time.time()
        
        # Check monthly limit
        if self.monthly_calls >= self.monthly_limit:
            logger.warning(f"Monthly limit reached: {self.monthly_calls}/{self.monthly_limit}")
            return False
        
        # Reset rate limit window if needed (1 minute window)
        if current_time - self.rate_limit_window_start >= 60:
            self.rate_limit_calls = 0
            self.rate_limit_window_start = current_time
        
        # Check per-minute limit
        if self.rate_limit_calls >= self.rate_limit_per_minute:
            logger.warning(f"Rate limit reached: {self.rate_limit_calls}/{self.rate_limit_per_minute} per minute")
            return False
        
        return True
    
    def _increment_usage(self):
        """Increment usage counters"""
        self.rate_limit_calls += 1
        self.monthly_calls += 1
        self._save_usage_stats()
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                enable_cleanup_closed=True
            )
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                connector_owner=True
            )
        return self.session
    
    async def get_coins_markets(self, vs_currency: str = "usd", limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get coins market data (price, market cap, volume, etc.)
        """
        if not self._check_rate_limits():
            return []
        
        try:
            session = await self.get_session()
            url = f"{self.base_url}/coins/markets"
            params = {
                "vs_currency": vs_currency,
                "order": "market_cap_desc",
                "per_page": min(limit, 250),  # API limit is 250
                "page": 1,
                "sparkline": False,
                "price_change_percentage": "24h"
            }
            
            async with session.get(url, params=params) as response:
                self._increment_usage()
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ CoinGecko: Fetched {len(data)} coins market data")
                    return data
                elif response.status == 429:
                    logger.warning("CoinGecko rate limited")
                    await asyncio.sleep(60)  # Wait 1 minute
                    return []
                else:
                    logger.error(f"CoinGecko API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to fetch coins markets: {e}")
            return []
    
    async def get_simple_price(self, coin_ids: List[str], vs_currencies: List[str] = ["usd"]) -> Dict[str, Any]:
        """
        Get simple price data for specific coins
        """
        if not self._check_rate_limits():
            return {}
        
        try:
            session = await self.get_session()
            url = f"{self.base_url}/simple/price"
            params = {
                "ids": ",".join(coin_ids),
                "vs_currencies": ",".join(vs_currencies),
                "include_market_cap": True,
                "include_24hr_vol": True,
                "include_24hr_change": True,
                "include_last_updated_at": True
            }
            
            async with session.get(url, params=params) as response:
                self._increment_usage()
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ CoinGecko: Fetched prices for {len(data)} coins")
                    return data
                elif response.status == 429:
                    logger.warning("CoinGecko rate limited")
                    await asyncio.sleep(60)
                    return {}
                else:
                    logger.error(f"CoinGecko API error: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Failed to fetch simple prices: {e}")
            return {}
    
    async def get_trending_coins(self) -> Dict[str, Any]:
        """
        Get trending coins (free endpoint)
        """
        if not self._check_rate_limits():
            return {}
        
        try:
            session = await self.get_session()
            url = f"{self.base_url}/search/trending"
            
            async with session.get(url) as response:
                self._increment_usage()
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ CoinGecko: Fetched trending data")
                    return data
                elif response.status == 429:
                    logger.warning("CoinGecko rate limited")
                    await asyncio.sleep(60)
                    return {}
                else:
                    logger.error(f"CoinGecko API error: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Failed to fetch trending coins: {e}")
            return {}
    
    async def get_global_data(self) -> Dict[str, Any]:
        """
        Get global cryptocurrency market data
        """
        if not self._check_rate_limits():
            return {}
        
        try:
            session = await self.get_session()
            url = f"{self.base_url}/global"
            
            async with session.get(url) as response:
                self._increment_usage()
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ CoinGecko: Fetched global market data")
                    return data
                elif response.status == 429:
                    logger.warning("CoinGecko rate limited")
                    await asyncio.sleep(60)
                    return {}
                else:
                    logger.error(f"CoinGecko API error: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Failed to fetch global data: {e}")
            return {}
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics"""
        return {
            "monthly_calls": self.monthly_calls,
            "monthly_limit": self.monthly_limit,
            "monthly_remaining": self.monthly_limit - self.monthly_calls,
            "rate_limit_calls": self.rate_limit_calls,
            "rate_limit_per_minute": self.rate_limit_per_minute,
            "rate_limit_remaining": self.rate_limit_per_minute - self.rate_limit_calls,
            "usage_percentage": (self.monthly_calls / self.monthly_limit) * 100
        }
    
    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()

# Global client instance
coingecko_client = CoinGeckoMCPClient()
