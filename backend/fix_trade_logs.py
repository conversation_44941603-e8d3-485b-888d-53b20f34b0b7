import csv
import logging
import os

INPUT_FILE = "backend/data/trade_logs.csv"
OUTPUT_FILE = "backend/data/trade_logs.csv"
EXPECTED_COLUMNS = 6
FALLBACK_STRATEGY = "DCA"
HEADER = ["timestamp", "token", "side", "amount", "price", "value", "strategy"]

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fix_trade_logs")

def fix_trade_log(input_path, output_path):
    if not os.path.exists(input_path):
        logger.error(f"❌ Input file does not exist: {input_path}")
        return

    try:
        with open(input_path, "r", newline="") as infile:
            reader = list(csv.reader(infile))
            header = reader[0]
            rows = reader[1:]
    except Exception as e:
        logger.error(f"❌ Failed to read file: {e}")
        return

    fixed_rows = []
    for row in rows:
        if len(row) == EXPECTED_COLUMNS:
            row.append(FALLBACK_STRATEGY)
        elif len(row) == EXPECTED_COLUMNS + 1:
            pass  # Already complete
        else:
            logger.warning(f"❌ Skipping broken row: {row}")
            continue
        fixed_rows.append(row)

    try:
        with open(output_path, "w", newline="") as outfile:
            writer = csv.writer(outfile)
            writer.writerow(HEADER)
            writer.writerows(fixed_rows)
        logger.info("✅ Trade log fixed and saved.")
    except Exception as e:
        logger.error(f"❌ Failed to write output file: {e}")

if __name__ == "__main__":
    fix_trade_log(INPUT_FILE, OUTPUT_FILE)
