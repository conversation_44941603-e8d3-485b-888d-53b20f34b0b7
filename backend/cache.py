import time
import logging
import threading
import hashlib
import json
from typing import Any, Dict, Optional, List, Tuple
from datetime import datetime
from collections import defaultdict, OrderedDict

logger = logging.getLogger(__name__)

# Enhanced cache with thread safety and intelligent management
_cache: Dict[str, Dict[str, Any]] = {}
_cache_lock = threading.RLock()
_cache_stats = {
    "hits": 0,
    "misses": 0,
    "evictions": 0,
    "total_requests": 0,
    "memory_usage": 0,
}

# Enhanced configuration for high-frequency trading
MAX_CACHE_SIZE = 10000  # Maximum number of cache entries
CACHE_CLEANUP_INTERVAL = 300  # Cleanup every 5 minutes
DEFAULT_TTL = 300  # Default 5 minutes
PRIORITY_LEVELS = {
    "critical": 1800,  # 30 minutes for critical data
    "high": 900,  # 15 minutes for high priority
    "normal": 300,  # 5 minutes for normal data
    "low": 60,  # 1 minute for low priority
}

# LRU cache for frequently accessed data
_lru_cache = OrderedDict()
_access_patterns = defaultdict(int)
_last_cleanup = time.time()


def get_cached_data(key: str) -> Any:
    """Enhanced cache retrieval with thread safety and LRU management."""
    global _last_cleanup

    with _cache_lock:
        _cache_stats["total_requests"] += 1

        # Periodic cleanup
        current_time = time.time()
        if current_time - _last_cleanup > CACHE_CLEANUP_INTERVAL:
            _cleanup_expired_entries()
            _last_cleanup = current_time

        if key in _cache:
            entry = _cache[key]
            data, timestamp, ttl = entry["data"], entry["timestamp"], entry["ttl"]

            if (current_time - timestamp) < ttl:
                # Cache hit - update access patterns and LRU
                _cache_stats["hits"] += 1
                _access_patterns[key] += 1

                # Update LRU order
                if key in _lru_cache:
                    _lru_cache.move_to_end(key)
                else:
                    _lru_cache[key] = current_time

                logger.debug(f"✅ Cache hit for key: {key[:20]}...")
                return data
            else:
                # Expired entry
                logger.debug(f"⏰ Cache expired for key: {key[:20]}...")
                del _cache[key]
                _lru_cache.pop(key, None)
                _cache_stats["evictions"] += 1

        # Cache miss
        _cache_stats["misses"] += 1
        logger.debug(f"❌ Cache miss for key: {key[:20]}...")
        return None


def set_cached_data(
    key: str, data: Any, ttl: int = DEFAULT_TTL, priority: str = "normal"
) -> None:
    """Enhanced cache storage with intelligent management and priority levels."""
    with _cache_lock:
        current_time = time.time()

        # Use priority-based TTL if specified
        if priority in PRIORITY_LEVELS:
            ttl = PRIORITY_LEVELS[priority]

        # Check if cache is full and needs cleanup
        if len(_cache) >= MAX_CACHE_SIZE:
            _evict_least_used_entries()

        # Store data with enhanced metadata
        _cache[key] = {
            "data": data,
            "timestamp": current_time,
            "ttl": ttl,
            "priority": priority,
            "access_count": 0,
            "size": _estimate_size(data),
        }

        # Update LRU cache
        _lru_cache[key] = current_time

        # Update memory usage stats
        _cache_stats["memory_usage"] += _estimate_size(data)

        logger.debug(
            f"📦 Data cached for key: {key[:20]}... (TTL: {ttl}s, Priority: {priority})"
        )


def _cleanup_expired_entries() -> None:
    """Clean up expired cache entries."""
    current_time = time.time()
    expired_keys = []

    for key, entry in _cache.items():
        if (current_time - entry["timestamp"]) >= entry["ttl"]:
            expired_keys.append(key)

    for key in expired_keys:
        entry = _cache.pop(key, {})
        _lru_cache.pop(key, None)
        _cache_stats["evictions"] += 1
        _cache_stats["memory_usage"] -= entry.get("size", 0)

    if expired_keys:
        logger.debug(f"🧹 Cleaned up {len(expired_keys)} expired cache entries")


def _evict_least_used_entries() -> None:
    """Evict least recently used entries when cache is full."""
    eviction_count = max(1, MAX_CACHE_SIZE // 10)  # Evict 10% of cache

    # Sort by access patterns and LRU
    sorted_keys = sorted(
        _lru_cache.keys(), key=lambda k: (_access_patterns[k], _lru_cache[k])
    )

    for key in sorted_keys[:eviction_count]:
        if key in _cache:
            entry = _cache.pop(key)
            _lru_cache.pop(key, None)
            _access_patterns.pop(key, None)
            _cache_stats["evictions"] += 1
            _cache_stats["memory_usage"] -= entry.get("size", 0)

    logger.debug(f"🗑️ Evicted {eviction_count} least used cache entries")


def _estimate_size(data: Any) -> int:
    """Estimate memory size of cached data."""
    try:
        if isinstance(data, (str, int, float, bool)):
            return len(str(data))
        elif isinstance(data, (list, tuple)):
            return sum(_estimate_size(item) for item in data)
        elif isinstance(data, dict):
            return sum(_estimate_size(k) + _estimate_size(v) for k, v in data.items())
        else:
            return len(str(data))
    except:
        return 100  # Default estimate


# Advanced caching functions for high-frequency trading


def get_cached_data_batch(keys: List[str]) -> Dict[str, Any]:
    """Batch retrieve multiple cache entries efficiently."""
    results = {}
    with _cache_lock:
        for key in keys:
            data = get_cached_data(key)
            if data is not None:
                results[key] = data
    return results


def set_cached_data_batch(data_dict: Dict[str, Tuple[Any, int, str]]) -> None:
    """Batch set multiple cache entries efficiently."""
    with _cache_lock:
        for key, (data, ttl, priority) in data_dict.items():
            set_cached_data(key, data, ttl, priority)


def invalidate_cache_pattern(pattern: str) -> int:
    """Invalidate cache entries matching a pattern."""
    invalidated = 0
    with _cache_lock:
        keys_to_remove = [key for key in _cache.keys() if pattern in key]
        for key in keys_to_remove:
            entry = _cache.pop(key, {})
            _lru_cache.pop(key, None)
            _access_patterns.pop(key, None)
            _cache_stats["memory_usage"] -= entry.get("size", 0)
            invalidated += 1

    if invalidated > 0:
        logger.info(
            f"🗑️ Invalidated {invalidated} cache entries matching pattern: {pattern}"
        )
    return invalidated


def get_cache_stats() -> Dict[str, Any]:
    """Get comprehensive cache statistics."""
    with _cache_lock:
        hit_rate = (_cache_stats["hits"] / max(_cache_stats["total_requests"], 1)) * 100

        return {
            "total_entries": len(_cache),
            "memory_usage_bytes": _cache_stats["memory_usage"],
            "hit_rate_percent": round(hit_rate, 2),
            "total_requests": _cache_stats["total_requests"],
            "cache_hits": _cache_stats["hits"],
            "cache_misses": _cache_stats["misses"],
            "evictions": _cache_stats["evictions"],
            "most_accessed_keys": dict(
                sorted(_access_patterns.items(), key=lambda x: x[1], reverse=True)[:10]
            ),
        }


def warm_cache_for_symbols(
    symbols: List[str], data_fetcher, ttl: int = DEFAULT_TTL
) -> None:
    """Pre-warm cache for frequently accessed symbols."""
    logger.info(f"🔥 Warming cache for {len(symbols)} symbols...")

    for symbol in symbols:
        try:
            cache_key = f"market_data_{symbol}"
            if get_cached_data(cache_key) is None:
                data = data_fetcher(symbol)
                if data:
                    set_cached_data(cache_key, data, ttl, "high")
        except Exception as e:
            logger.error(f"Failed to warm cache for {symbol}: {e}")


def optimize_cache_for_trading() -> None:
    """Optimize cache settings for high-frequency trading."""
    global MAX_CACHE_SIZE, CACHE_CLEANUP_INTERVAL

    # Increase cache size for trading
    MAX_CACHE_SIZE = 20000
    CACHE_CLEANUP_INTERVAL = 180  # More frequent cleanup

    # Adjust priority levels for trading data
    PRIORITY_LEVELS.update(
        {
            "price_data": 30,  # Very short TTL for price data
            "market_data": 60,  # Short TTL for market data
            "ai_decisions": 120,  # Medium TTL for AI decisions
            "sentiment": 300,  # Longer TTL for sentiment data
        }
    )

    logger.info("⚡ Cache optimized for high-frequency trading")


def clear_cache() -> None:
    """Clear all cache entries."""
    with _cache_lock:
        cleared_count = len(_cache)
        _cache.clear()
        _lru_cache.clear()
        _access_patterns.clear()
        _cache_stats.update(
            {
                "hits": 0,
                "misses": 0,
                "evictions": 0,
                "total_requests": 0,
                "memory_usage": 0,
            }
        )

    logger.info(f"🧹 Cleared {cleared_count} cache entries")


def get_cache_key_hash(data: str) -> str:
    """Generate consistent hash for cache keys."""
    return hashlib.md5(data.encode()).hexdigest()[:16]


# Initialize optimizations for trading
optimize_cache_for_trading()
