import os
import logging
from utils.api_client import get, post
import hmac
import hashlib
import base64
import time
import json
import requests
from typing import Dict, Any, Optional, List

from config import (
    KUCOIN_API_KEY,
    KUCOIN_API_SECRET,
    KUCOIN_API_PASSPHRASE,
    KUCOIN_BASE_URL,
)

logger = logging.getLogger(__name__)


class KuCoinAPI:
    def __init__(self):
        self.api_key = KUCOIN_API_KEY
        self.api_secret = KUCOIN_API_SECRET
        self.api_passphrase = KUCOIN_API_PASSPHRASE
        self.base_url = KUCOIN_BASE_URL

        if not all([self.api_key, self.api_secret, self.api_passphrase]):
            logger.error(
                "KuCoin API credentials are not fully set. Live trading will not work."
            )
            self.authenticated = False
        else:
            self.authenticated = True

    def _get_headers(
        self, endpoint: str, body: str = "", method: str = "GET"
    ) -> Dict[str, str]:
        if not self.authenticated:
            return {}

        timestamp = str(int(time.time() * 1000))

        # KuCoin signature format: timestamp + method + endpoint + body
        str_to_sign = timestamp + method.upper() + endpoint + body

        if not self.api_secret:
            logger.error("KuCoin API secret is not set.")
            return {}

        # Create signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode("utf-8"),
                str_to_sign.encode("utf-8"),
                hashlib.sha256,
            ).digest()
        )

        # Create passphrase signature (encrypt passphrase with API secret)
        passphrase_signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode("utf-8"),
                (self.api_passphrase or "").encode("utf-8"),
                hashlib.sha256,
            ).digest()
        )

        return {
            "KC-API-KEY": self.api_key or "",
            "KC-API-SIGN": signature.decode("utf-8"),
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-PASSPHRASE": passphrase_signature.decode("utf-8"),
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json",
        }

    def get_account_list(self, currency: Optional[str] = None) -> List[Dict[str, Any]]:
        endpoint = "/api/v1/accounts"
        if currency:
            endpoint += f"?currency={currency}"
        headers = self._get_headers(endpoint, method="GET")
        try:
            response = get(
                f"{self.base_url}{endpoint}", headers=headers, cache_ttl=60
            )  # Cache klines for 1 minute
            response.raise_for_status()
            data = response.json()
            if data.get("code") == "200000":
                return data.get("data", [])
            else:
                logger.error(
                    f"KuCoin API error getting account list: {data.get('msg', 'Unknown error')}"
                )
                return []
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get KuCoin account list: {e}", exc_info=True)
            return []

    def get_account_balance(self, currency: str = "USDT") -> float:
        accounts = self.get_account_list(currency=currency)
        for account in accounts:
            if account.get("currency") == currency:
                return float(account.get("available", 0))
        return 0.0

    def place_order(
        self, symbol: str, side: str, price: float, size: float
    ) -> Optional[Dict[str, Any]]:
        endpoint = "/api/v1/orders"
        order_params = {
            "clientOid": str(int(time.time() * 1000)),  # Unique order ID
            "symbol": symbol,
            "type": "limit",
            "side": side,
            "price": f"{price:.8f}",
            "size": f"{size:.8f}",
        }
        body = json.dumps(order_params)
        headers = self._get_headers(endpoint, body=body, method="POST")
        try:
            response = post(f"{self.base_url}{endpoint}", headers=headers, data=body)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == "200000":
                return data.get("data")
            else:
                logger.error(
                    f"KuCoin API error placing order: {data.get('msg', 'Unknown error')}"
                )
                return None
        except requests.exceptions.RequestException as e:
            logger.error(
                f"Failed to place KuCoin order for {symbol} {side} at {price} size {size}: {e}",
                exc_info=True,
            )
            return None

    def place_market_order(
        self,
        symbol: str,
        side: str,
        size: Optional[float] = None,
        funds: Optional[float] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Place a market order on KuCoin

        Args:
            symbol: Trading pair (e.g., "BTC-USDT")
            side: "buy" or "sell"
            size: Amount of base currency to buy/sell (for sell orders)
            funds: Amount of quote currency to spend (for buy orders)
        """
        if not self.authenticated:
            logger.error("KuCoin API not authenticated")
            return None

        endpoint = "/api/v1/orders"
        order_params = {
            "clientOid": str(int(time.time() * 1000)),  # Unique order ID
            "symbol": symbol,
            "type": "market",
            "side": side,
        }

        # For market buy orders, use funds (USDT amount)
        # For market sell orders, use size (token amount)
        if side == "buy" and funds:
            order_params["funds"] = str(funds)
        elif side == "sell" and size:
            order_params["size"] = str(size)
        else:
            logger.error(
                f"Invalid market order parameters: side={side}, size={size}, funds={funds}"
            )
            return None

        body = json.dumps(order_params)
        headers = self._get_headers(endpoint, body=body, method="POST")
        try:
            response = post(f"{self.base_url}{endpoint}", headers=headers, data=body)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == "200000":
                logger.info(
                    f"Market order placed successfully: {data.get('data', {}).get('orderId')}"
                )
                return data.get("data")
            else:
                logger.error(
                    f"KuCoin API error placing market order: {data.get('msg', 'Unknown error')}"
                )
                return None
        except Exception as e:
            logger.error(f"KuCoin API error placing market order: {e}")
            return None

    def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        endpoint = f"/api/v1/market/orderbook/level1?symbol={symbol}"
        headers = self._get_headers(
            endpoint, method="GET"
        )  # Public endpoint, but good to include headers
        try:
            response = get(
                f"{self.base_url}{endpoint}", headers=headers, cache_ttl=5
            )  # Cache ticker for 5 seconds
            response.raise_for_status()
            data = response.json()
            if data.get("code") == "200000":
                return data.get("data")
            else:
                logger.error(
                    f"KuCoin API error getting ticker: {data.get('msg', 'Unknown error')}"
                )
                return None
        except requests.exceptions.RequestException as e:
            logger.error(
                f"Failed to get KuCoin ticker for {symbol}: {e}", exc_info=True
            )
            return None

    def get_klines(
        self,
        symbol: str,
        kline_type: str = "1min",
        start_at: Optional[int] = None,
        end_at: Optional[int] = None,
    ) -> Optional[List[List[str]]]:
        endpoint = "/api/v1/market/candles"
        params = {
            "symbol": symbol,
            "type": kline_type,
            "startAt": start_at,
            "endAt": end_at,
        }
        # Filter out None values from params
        params = {k: v for k, v in params.items() if v is not None}

        # Build query string for signature
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        endpoint_with_params = f"{endpoint}?{query_string}"

        headers = self._get_headers(
            endpoint_with_params, method="GET"
        )  # Public endpoint
        try:
            response = get(
                f"{self.base_url}{endpoint}",
                headers=headers,
                params=params,
                cache_ttl=60,
            )  # Cache klines for 1 minute
            response.raise_for_status()
            data = response.json()
            if data.get("code") == "200000":
                return data.get("data")
            else:
                logger.error(
                    f"KuCoin API error getting klines: {data.get('msg', 'Unknown error')}"
                )
                return None
        except requests.exceptions.RequestException as e:
            logger.error(
                f"Failed to get KuCoin klines for {symbol} ({kline_type}): {e}",
                exc_info=True,
            )
            return None
