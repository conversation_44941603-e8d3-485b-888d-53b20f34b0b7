import os
import csv
import json
import logging
from collections import defaultdict
from typing import Callable, Dict, List, Any, Optional
from binance_data import BinanceData

binance_data_client = BinanceData()

def fetch_binance_price(symbol: str) -> Optional[float]:
    try:
        ticker = binance_data_client.get_ticker_24hr(symbol=symbol.replace("-USDT", "") + "USDT")
        if isinstance(ticker, dict) and "lastPrice" in ticker:
            return float(ticker["lastPrice"])
        return None
    except Exception as e:
        logging.error(f"Error fetching Binance price for {symbol}: {e}")
        return None
# from price_fetcher import get_price_from_kucoin
def get_price_from_kucoin(token):
    # Dummy fallback implementation or raise an error
    raise NotImplementedError("get_price_from_kucoin is not implemented in backend.price_fetcher")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
ENABLE_COLOR_OUTPUT = True

def fetch_latest_prices(tokens: List[str]) -> Dict[str, float]:
    """Fetch latest token prices using Binance, fallback to <PERSON><PERSON>oin."""
    prices = {}
    for token in tokens:
        try:
            price = fetch_binance_price(token)
            if price and price > 0:
                prices[token] = price
            else:
                price = get_price_from_kucoin(token)
                if price and price > 0:
                    prices[token] = price
                else:
                    logger.error(f"Price fetch failed for {token}: No valid price from Binance or KuCoin")
                    prices[token] = 0
        except Exception as e:
            logger.error(f"Price fetch failed for {token}: {e}")
            prices[token] = 0
    return prices

def calculate_unrealized_pnl(portfolio: Dict[str, Dict[str, Any]], prices: Dict[str, float]) -> Dict[str, Dict[str, float]]:
    """Calculate unrealized profit/loss for each token in the portfolio."""
    unrealized_data = {}
    try:
        for token, data in portfolio.items():
            amount = data.get("qty", 0)
            avg_price = data.get("avg_price", 0)
            current_price = prices.get(token, 0)
            if current_price > 0 and amount > 0:
                current_value = current_price * amount
                invested_value = avg_price * amount
                pnl_pct = ((current_value - invested_value) / invested_value) * 100 if invested_value > 0 else 0
                unrealized_data[token] = {
                    "current": current_value,
                    "invested": invested_value,
                    "pnl_pct": pnl_pct
                }
    except Exception as e:
        logger.error(f"Error calculating unrealized PnL: {e}")
    return unrealized_data

def print_pnl_dashboard(portfolio: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """Print and save the PnL dashboard with realized and unrealized data."""
    trade_log_path = "backend/data/trade_logs.csv"
    if not os.path.exists(trade_log_path):
        logger.info("💹 No trade log available yet.")
        return {}

    token_data = defaultdict(lambda: {"invested": 0.0, "realized": 0.0, "buy_qty": 0.0, "sell_qty": 0.0, "reason": ""})

    try:
        with open(trade_log_path, mode='r', encoding="utf-8") as file:
            reader = csv.DictReader(file)
            for row in reader:
                try:
                    token = row["token"]
                    side = row["side"]
                    value = float(row["value"])
                    qty = float(row["amount"])

                    if side == "BUY":
                        token_data[token]["invested"] = float(token_data[token]["invested"]) + float(value)
                        token_data[token]["buy_qty"] = float(token_data[token]["buy_qty"]) + float(qty)
                    elif side == "SELL":
                        token_data[token]["realized"] = float(token_data[token]["realized"]) + float(value)
                        token_data[token]["sell_qty"] += qty  # type: ignore

                    token_data[token]["reason"] = row.get("strategy", "")
                except Exception as e:
                    logger.error(f"Error processing trade log row: {e}")
    except Exception as e:
        logger.error(f"Failed to read trade log: {e}")
        return {}

    live_prices = fetch_latest_prices(list(portfolio.keys()))
    unrealized = calculate_unrealized_pnl(portfolio, live_prices)

    logger.info("\n💹 P&L DASHBOARD (Realized & Unrealized)")
    logger.info("--------------------------------------------------------------------------")
    header = f"{'Token':<12}{'Invested':<12}{'Realized':<12}{'Unrealized':<12}{'Total P/L %':<12}{'Reason':<15}{'Status'}"
    logger.info(header)
    logger.info("--------------------------------------------------------------------------")

    output_json = []
    output_csv_path = "backend/data/pnl_report.csv"
    output_json_path = "backend/data/pnl_report.json"

    os.makedirs("backend/data", exist_ok=True)
    try:
        logger.info(f"Saving CSV to {output_csv_path}")
        with open(output_csv_path, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = ["token", "invested", "realized", "unrealized", "total_pnl", "reason", "status"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for token in token_data:
                try:
                    inv = token_data[token]["invested"]
                    realized = token_data[token]["realized"]
                    unreal = unrealized.get(token, {}).get("current", 0)
                    total_value = float(realized) + float(unreal)
                    inv = float(inv)  # Ensure 'inv' is a float
                    total_pnl = ((total_value - inv) / inv) * 100 if inv > 0 else 0
                    reason = token_data[token]["reason"]

                    status = "Held" if token in portfolio else "Exited"
                    if total_pnl == 0 or (unreal == 0 and realized == 0):
                        status = "Inactive"

                    if ENABLE_COLOR_OUTPUT:
                        color = "\033[91m" if total_pnl < 0 else "\033[92m"
                        reset = "\033[0m"
                    else:
                        color = ""
                        reset = ""

                    marker = "🔄 " if token in portfolio else ""
                    line = f"{marker}{token:<12}{inv:<12.2f}{realized:<12.2f}{unreal:<12.2f}{color}{total_pnl:<12.2f}{reset}{reason:<15}{status}"
                    logger.info(line)

                    data = {
                        "token": token,
                        "invested": round(float(inv), 2),
                        "realized": round(float(realized), 2),
                        "unrealized": round(unreal, 2),
                        "total_pnl": round(total_pnl, 2),
                        "reason": reason,
                        "status": status
                    }
                    output_json.append(data)
                    writer.writerow(data)
                except Exception as e:
                    logger.error(f"Error processing token {token}: {e}")

        logger.info(f"Saving JSON to {output_json_path}")
        with open(output_json_path, "w", encoding="utf-8") as jsonfile:
            json.dump(output_json, jsonfile, indent=4)

    except Exception as e:
        logger.error(f"Failed to save PnL reports: {e}")

    logger.info("--------------------------------------------------------------------------\n")

    backtest_path = "backend/data/backtest_summary.json"
    if os.path.exists(backtest_path):
        try:
            with open(backtest_path, encoding="utf-8") as f:
                backtest = json.load(f)
                logger.info("\n📈 Backtest Summary:")
                logger.info(f"  Final Simulated Balance: ${backtest.get('final_balance', 0):,.2f}")
                logger.info(f"  Total Trades Simulated: {len(backtest.get('results', []))}")
                logger.info(f"  Held Positions at End: {len(backtest.get('open_positions', {}))}")
        except Exception as e:
            logger.error(f"Failed to load backtest results: {e}")

    return {item["token"]: item for item in output_json}

def get_pnl_summary() -> List[Dict[str, Any]]:
    """Load latest PnL report JSON from disk."""
    path = "backend/data/pnl_report.json"
    if not os.path.exists(path):
        return []

    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to read PnL summary: {e}")
        return []

def update_dashboard_after_trade(portfolio: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """Update the dashboard after a trade is executed."""
    logger.info("[INFO] Updating PnL dashboard after trade...")
    return print_pnl_dashboard(portfolio)

def send_daily_pnl_summary(telegram_send_func: Callable[[str], None], portfolio: Dict[str, Dict[str, Any]]) -> None:
    """Send daily PnL summary to Telegram using the provided sender."""
    summary_data = get_pnl_summary()
    if not summary_data:
        message = "No PnL data available yet."
    else:
        message = "📊 Daily PnL Summary:\n"
        for item in summary_data:
            token = item["token"]
            pnl = item["total_pnl"]
            color = "🔴" if pnl < 0 else "🟢"
            message += f"{color} {token}: {pnl}%\n"

    telegram_send_func(message)