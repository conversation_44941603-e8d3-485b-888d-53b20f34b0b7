#!/usr/bin/env python3
"""
🌙 TOKENMETRICS MOONSHOTS FEATURE
Identifies high-potential tokens using TokenMetrics AI analysis
Combines trading signals, AI reports, and grades for moonshot detection
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

from tokenmetrics_api import TokenMetricsAPI
from cache import get_cached_data, set_cached_data

logger = logging.getLogger(__name__)


@dataclass
class MoonshotToken:
    """Data class for moonshot token analysis"""

    symbol: str
    name: str
    current_price: float
    market_cap: float
    volume_24h: float

    # TokenMetrics Analysis
    tm_grade: str  # A, B, C, D, F
    tm_score: float  # 0-100
    investor_grade: str
    trader_grade: str

    # AI Analysis
    ai_report_summary: str
    ai_confidence: float
    breakout_potential: float

    # Trading Signals
    signal_type: str  # BUY, SELL, HOLD
    signal_strength: float  # 0-100
    entry_price: float
    target_price: float
    stop_loss: float

    # Moonshot Metrics
    moonshot_score: float  # 0-100 (our calculated score)
    moonshot_category: str  # "High Potential", "Moderate", "Speculative"
    risk_level: str  # "Low", "Medium", "High"
    potential_return: float  # Expected % return

    # Metadata
    last_updated: datetime
    data_sources: List[str]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "symbol": self.symbol,
            "name": self.name,
            "current_price": self.current_price,
            "market_cap": self.market_cap,
            "volume_24h": self.volume_24h,
            "tm_grade": self.tm_grade,
            "tm_score": self.tm_score,
            "investor_grade": self.investor_grade,
            "trader_grade": self.trader_grade,
            "ai_report_summary": self.ai_report_summary,
            "ai_confidence": self.ai_confidence,
            "breakout_potential": self.breakout_potential,
            "signal_type": self.signal_type,
            "signal_strength": self.signal_strength,
            "entry_price": self.entry_price,
            "target_price": self.target_price,
            "stop_loss": self.stop_loss,
            "moonshot_score": self.moonshot_score,
            "moonshot_category": self.moonshot_category,
            "risk_level": self.risk_level,
            "potential_return": self.potential_return,
            "last_updated": self.last_updated.isoformat(),
            "data_sources": self.data_sources,
        }


class TokenMetricsMoonshots:
    """TokenMetrics Moonshots analyzer for high-potential tokens"""

    def __init__(self):
        self.tm_api = TokenMetricsAPI()
        self.cache_ttl = 1800  # 30 minutes cache for moonshots
        self.moonshot_threshold = 70.0  # Minimum score for moonshot classification

    async def get_moonshot_tokens(self, limit: int = 20) -> List[MoonshotToken]:
        """Get top moonshot tokens with high potential"""
        cache_key = f"moonshots_top_{limit}"
        cached_data = get_cached_data(cache_key)

        if cached_data:
            logger.info("✅ Using cached moonshot data")
            return [MoonshotToken(**token) for token in cached_data]

        logger.info("🌙 Analyzing tokens for moonshot potential...")

        try:
            # Step 1: Get top tokens by market cap and trading signals
            moonshot_candidates = await self._get_moonshot_candidates()

            # Step 2: Analyze each candidate for moonshot potential
            moonshot_tokens = []
            for candidate in moonshot_candidates[:50]:  # Analyze top 50 candidates
                moonshot_analysis = await self._analyze_moonshot_potential(candidate)
                if (
                    moonshot_analysis
                    and moonshot_analysis.moonshot_score >= self.moonshot_threshold
                ):
                    moonshot_tokens.append(moonshot_analysis)

            # Step 3: Sort by moonshot score and return top results
            moonshot_tokens.sort(key=lambda x: x.moonshot_score, reverse=True)
            top_moonshots = moonshot_tokens[:limit]

            # Cache the results
            cache_data = [token.to_dict() for token in top_moonshots]
            set_cached_data(cache_key, cache_data, ttl=self.cache_ttl)

            logger.info(f"🎯 Found {len(top_moonshots)} moonshot tokens")
            return top_moonshots

        except Exception as e:
            logger.error(f"❌ Failed to get moonshot tokens: {e}")
            return []

    async def _get_moonshot_candidates(self) -> List[Dict[str, Any]]:
        """Get candidate tokens for moonshot analysis"""
        candidates = []

        try:
            # Get tokens with strong trading signals
            signals_response = self.tm_api._make_request(
                "trading-signals", {"limit": "100"}
            )
            if signals_response.get("success") and signals_response.get("data"):
                for signal in signals_response["data"]:
                    if signal.get("signal") == "BUY" and signal.get("strength", 0) > 60:
                        candidates.append(
                            {"symbol": signal.get("symbol"), "signal_data": signal}
                        )

            # Get top tokens by market cap for additional candidates
            tokens_response = self.tm_api._make_request("tokens", {"limit": "100"})
            if tokens_response.get("success") and tokens_response.get("data"):
                for token in tokens_response["data"]:
                    if token.get("symbol") not in [c["symbol"] for c in candidates]:
                        candidates.append(
                            {"symbol": token.get("symbol"), "token_data": token}
                        )

            logger.info(f"📊 Found {len(candidates)} moonshot candidates")
            return candidates

        except Exception as e:
            logger.error(f"❌ Failed to get moonshot candidates: {e}")
            return []

    async def _analyze_moonshot_potential(
        self, candidate: Dict[str, Any]
    ) -> Optional[MoonshotToken]:
        """Analyze a token's moonshot potential"""
        symbol = candidate.get("symbol")
        if not symbol:
            return None

        try:
            logger.info(f"🔍 Analyzing moonshot potential for {symbol}")

            # Gather comprehensive data
            token_data = await self._gather_token_data(symbol)
            if not token_data:
                return None

            # Calculate moonshot score
            moonshot_score = self._calculate_moonshot_score(token_data)

            # Determine category and risk level
            category, risk_level = self._categorize_moonshot(moonshot_score, token_data)

            # Calculate potential return
            potential_return = self._calculate_potential_return(token_data)

            # Create moonshot token object
            moonshot_token = MoonshotToken(
                symbol=symbol,
                name=token_data.get("name", symbol),
                current_price=token_data.get("current_price", 0.0),
                market_cap=token_data.get("market_cap", 0.0),
                volume_24h=token_data.get("volume_24h", 0.0),
                tm_grade=token_data.get("tm_grade", "N/A"),
                tm_score=token_data.get("tm_score", 0.0),
                investor_grade=token_data.get("investor_grade", "N/A"),
                trader_grade=token_data.get("trader_grade", "N/A"),
                ai_report_summary=token_data.get(
                    "ai_summary", "No AI report available"
                ),
                ai_confidence=token_data.get("ai_confidence", 0.0),
                breakout_potential=token_data.get("breakout_potential", 0.0),
                signal_type=token_data.get("signal_type", "HOLD"),
                signal_strength=token_data.get("signal_strength", 0.0),
                entry_price=token_data.get(
                    "entry_price", token_data.get("current_price", 0.0)
                ),
                target_price=token_data.get("target_price", 0.0),
                stop_loss=token_data.get("stop_loss", 0.0),
                moonshot_score=moonshot_score,
                moonshot_category=category,
                risk_level=risk_level,
                potential_return=potential_return,
                last_updated=datetime.now(),
                data_sources=["tokenmetrics_api", "trading_signals", "ai_reports"],
            )

            return moonshot_token

        except Exception as e:
            logger.error(f"❌ Failed to analyze moonshot potential for {symbol}: {e}")
            return None

    async def _gather_token_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Gather comprehensive token data from multiple endpoints"""
        token_data: Dict[str, Any] = {"symbol": symbol}

        try:
            # Get basic token info
            token_info = self.tm_api.get_token_info(symbol)
            if token_info.get("success") and token_info.get("data"):
                data = token_info["data"][0] if token_info["data"] else {}
                token_data.update(
                    {
                        "name": data.get("name", symbol),
                        "current_price": float(data.get("price", 0)),
                        "market_cap": float(data.get("market_cap", 0)),
                        "volume_24h": float(data.get("volume_24h", 0)),
                    }
                )

            # Get trading signals
            signals = self.tm_api._make_request(
                "trading-signals", {"symbol": symbol, "limit": "1"}
            )
            if signals.get("success") and signals.get("data"):
                signal_data = signals["data"][0] if signals["data"] else {}
                token_data.update(
                    {
                        "signal_type": signal_data.get("signal", "HOLD"),
                        "signal_strength": float(signal_data.get("strength", 0)),
                        "entry_price": float(
                            signal_data.get(
                                "entry_price", token_data.get("current_price", 0)
                            )
                        ),
                        "target_price": float(signal_data.get("target_price", 0)),
                        "stop_loss": float(signal_data.get("stop_loss", 0)),
                    }
                )

            # Get AI reports
            ai_reports = self.tm_api.get_ai_reports(symbol)
            if ai_reports.get("success") and ai_reports.get("data"):
                ai_data = ai_reports["data"][0] if ai_reports["data"] else {}
                token_data.update(
                    {
                        "ai_summary": ai_data.get("summary", "No summary available")[
                            :200
                        ],
                        "ai_confidence": float(ai_data.get("confidence", 0)),
                        "breakout_potential": float(ai_data.get("breakout_score", 0)),
                    }
                )

            # Get grades
            grades = self.tm_api._make_request(
                "trader-grades", {"symbol": symbol, "limit": "1"}
            )
            if grades.get("success") and grades.get("data"):
                grade_data = grades["data"][0] if grades["data"] else {}
                token_data.update(
                    {
                        "tm_grade": grade_data.get("grade", "N/A"),
                        "tm_score": float(grade_data.get("score", 0)),
                        "trader_grade": grade_data.get("trader_grade", "N/A"),
                    }
                )

            return token_data

        except Exception as e:
            logger.error(f"❌ Failed to gather data for {symbol}: {e}")
            return None

    def _calculate_moonshot_score(self, token_data: Dict[str, Any]) -> float:
        """Calculate moonshot score based on multiple factors"""
        score = 0.0

        # Trading signal strength (30% weight)
        signal_strength = token_data.get("signal_strength", 0)
        if token_data.get("signal_type") == "BUY":
            score += (signal_strength / 100) * 30

        # TokenMetrics grade (25% weight)
        tm_score = token_data.get("tm_score", 0)
        score += (tm_score / 100) * 25

        # AI confidence (20% weight)
        ai_confidence = token_data.get("ai_confidence", 0)
        score += (ai_confidence / 100) * 20

        # Breakout potential (15% weight)
        breakout_potential = token_data.get("breakout_potential", 0)
        score += (breakout_potential / 100) * 15

        # Volume and market cap factors (10% weight)
        volume_score = min(
            100, token_data.get("volume_24h", 0) / 1000000
        )  # Normalize volume
        market_cap = token_data.get("market_cap", 0)

        # Prefer tokens with good volume but not too high market cap (more room to grow)
        if 10000000 < market_cap < 1000000000:  # $10M - $1B sweet spot
            score += 5

        if volume_score > 10:  # Good volume
            score += 5

        return min(100.0, score)

    def _categorize_moonshot(self, score: float, _token_data: Dict[str, Any]) -> tuple:
        """Categorize moonshot and determine risk level"""
        if score >= 85:
            return "High Potential", "Medium"
        elif score >= 75:
            return "Moderate Potential", "Medium"
        elif score >= 70:
            return "Speculative", "High"
        else:
            return "Low Potential", "High"

    def _calculate_potential_return(self, token_data: Dict[str, Any]) -> float:
        """Calculate potential return percentage"""
        current_price = token_data.get("current_price", 0)
        target_price = token_data.get("target_price", 0)

        if current_price > 0 and target_price > current_price:
            return ((target_price - current_price) / current_price) * 100

        # Fallback calculation based on moonshot score
        tm_score = token_data.get("tm_score", 0)
        signal_strength = token_data.get("signal_strength", 0)

        # Estimate potential return based on scores
        base_return = (tm_score + signal_strength) / 2
        return min(500.0, base_return * 2)  # Cap at 500% potential return


# Global moonshots analyzer instance
moonshots_analyzer = TokenMetricsMoonshots()


# Convenience functions
async def get_moonshot_tokens(limit: int = 20) -> List[MoonshotToken]:
    """Get top moonshot tokens"""
    return await moonshots_analyzer.get_moonshot_tokens(limit)


async def analyze_token_moonshot_potential(symbol: str) -> Optional[MoonshotToken]:
    """Analyze a specific token's moonshot potential"""
    candidate = {"symbol": symbol}
    return await moonshots_analyzer._analyze_moonshot_potential(candidate)


if __name__ == "__main__":
    # Test the moonshots analyzer
    async def test_moonshots():
        print("🌙 Testing TokenMetrics Moonshots Analyzer")
        print("=" * 50)

        moonshots = await get_moonshot_tokens(10)

        if moonshots:
            print(f"✅ Found {len(moonshots)} moonshot tokens:")
            for i, token in enumerate(moonshots, 1):
                print(
                    f"{i}. {token.symbol} - Score: {token.moonshot_score:.1f} - Category: {token.moonshot_category}"
                )
        else:
            print("❌ No moonshot tokens found")

    asyncio.run(test_moonshots())
