"""
Fallback news sentiment analysis without TextBlob dependency.
Uses simple keyword-based sentiment analysis.
"""
import re
from typing import Dict, List, Optional

# Simple sentiment word lists
POSITIVE_WORDS = {
    'bullish', 'bull', 'up', 'rise', 'rising', 'gain', 'gains', 'profit', 'profits',
    'growth', 'increase', 'surge', 'rally', 'boom', 'positive', 'good', 'great',
    'excellent', 'strong', 'strength', 'breakthrough', 'success', 'win', 'winning',
    'moon', 'pump', 'pumping', 'green', 'buy', 'buying', 'long', 'hodl', 'hold'
}

NEGATIVE_WORDS = {
    'bearish', 'bear', 'down', 'fall', 'falling', 'drop', 'drops', 'loss', 'losses',
    'decline', 'decrease', 'crash', 'dump', 'dumping', 'correction', 'negative',
    'bad', 'terrible', 'weak', 'weakness', 'sell', 'selling', 'short', 'red',
    'fear', 'panic', 'concern', 'worried', 'risk', 'risks', 'problem', 'problems'
}

def simple_sentiment_score(text: str) -> float:
    """
    Calculate sentiment score using simple keyword matching.
    Returns a score between -1 (very negative) and 1 (very positive).
    """
    if not text:
        return 0.0
    
    # Convert to lowercase and split into words
    words = re.findall(r'\b\w+\b', text.lower())
    
    positive_count = sum(1 for word in words if word in POSITIVE_WORDS)
    negative_count = sum(1 for word in words if word in NEGATIVE_WORDS)
    
    total_sentiment_words = positive_count + negative_count
    
    if total_sentiment_words == 0:
        return 0.0
    
    # Calculate normalized sentiment score
    sentiment_score = (positive_count - negative_count) / len(words)
    
    # Clamp between -1 and 1
    return max(-1.0, min(1.0, sentiment_score * 10))

def get_combined_sentiment_score(news_items: List[Dict]) -> float:
    """
    Get combined sentiment score from multiple news items.
    """
    if not news_items:
        return 0.0
    
    total_score = 0.0
    total_weight = 0.0
    
    for item in news_items:
        # Extract text from news item
        text_parts = []
        
        if isinstance(item, dict):
            if 'title' in item:
                text_parts.append(item['title'])
            if 'description' in item:
                text_parts.append(item['description'])
            if 'content' in item:
                text_parts.append(item['content'])
        elif isinstance(item, str):
            text_parts.append(item)
        
        combined_text = ' '.join(text_parts)
        
        if combined_text:
            score = simple_sentiment_score(combined_text)
            weight = len(combined_text.split())  # Weight by word count
            
            total_score += score * weight
            total_weight += weight
    
    if total_weight == 0:
        return 0.0
    
    return total_score / total_weight

def analyze_sentiment(text: str) -> Dict[str, float]:
    """
    Analyze sentiment and return detailed breakdown.
    """
    score = simple_sentiment_score(text)
    
    # Convert to polarity and subjectivity-like metrics
    polarity = score
    subjectivity = min(1.0, abs(score) * 2)  # Higher absolute score = more subjective
    
    return {
        'polarity': polarity,
        'subjectivity': subjectivity,
        'compound': score
    }

def get_sentiment_label(score: float) -> str:
    """
    Get sentiment label from score.
    """
    if score > 0.1:
        return 'positive'
    elif score < -0.1:
        return 'negative'
    else:
        return 'neutral'

# Compatibility functions for existing code
class MockTextBlob:
    """Mock TextBlob class for compatibility."""
    
    def __init__(self, text: str):
        self.text = text
        self._sentiment = None
    
    @property
    def sentiment(self):
        if self._sentiment is None:
            analysis = analyze_sentiment(self.text)
            self._sentiment = MockSentiment(analysis['polarity'], analysis['subjectivity'])
        return self._sentiment

class MockSentiment:
    """Mock sentiment object for compatibility."""
    
    def __init__(self, polarity: float, subjectivity: float):
        self.polarity = polarity
        self.subjectivity = subjectivity

# For compatibility with existing imports
TextBlob = MockTextBlob
