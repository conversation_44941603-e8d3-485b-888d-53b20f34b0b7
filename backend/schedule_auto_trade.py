"""
AlphaPredatorBot — Auto Trading Scheduler

This module runs an infinite loop that periodically:
- Selects top tokens using multi-factor scoring
- Uses AI to determine trading decisions
- Executes trades in LIVE or PAPER mode
- Reassesses held positions periodically
- Updates a PnL dashboard
"""

import time
import traceback
import logging
import asyncio
from typing import List, Callable, Any, Dict
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# Enhanced imports with error handling
try:
    from config import TRADE_INTERVAL_SECONDS, TRADING_MODE
except ImportError:
    TRADE_INTERVAL_SECONDS = 60  # Default 1 minute
    TRADING_MODE = "PAPER"

try:
    from token_selector import get_top_tokens_for_trading
except ImportError:
    from token_selector import generate_top_token_list as get_top_tokens_for_trading

try:
    from ai_clients.ai_request_manager import get_ai_decisions_parallel
except ImportError:
    from trade_decision_engine import get_trade_decision as get_ai_decisions_parallel

try:
    from trade_executor import (
        execute_trade,
        execute_batch_trades,
        get_trading_performance_metrics,
    )
except ImportError:
    from trade_executor import execute_trade

    def execute_batch_trades(trade_requests):
        return [execute_trade(**trade) for trade in trade_requests]

    def get_trading_performance_metrics():
        return {}


try:
    from pnl_dashboard import print_pnl_dashboard
except ImportError:

    def print_pnl_dashboard(portfolio: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        return portfolio


try:
    from trade_logger import load_portfolio_json
except ImportError:

    def load_portfolio_json():
        return {}, 1000.0


# Enhanced logging setup
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("backend/logs/schedule_auto_trade.log"),
        logging.StreamHandler(),
    ],
)

# Enhanced configuration for high-frequency trading
MAX_RETRIES = 3
BATCH_SIZE = 10  # Process tokens in batches
MAX_CONCURRENT_DECISIONS = 5  # Parallel AI decisions
PERFORMANCE_LOG_INTERVAL = 10  # Log performance every 10 cycles
FAST_MODE_INTERVAL = 30  # Fast mode for high-frequency (30 seconds)

# Performance tracking
cycle_count = 0
total_trades = 0
successful_trades = 0


def retry(
    func: Callable, retries: int = MAX_RETRIES, delay: int = 2, *args, **kwargs
) -> Any:
    """Retry wrapper for fault tolerance."""
    for attempt in range(1, retries + 1):
        try:
            logging.info(
                f"[Retry] Attempt {attempt} — {func.__name__} args={args}, kwargs={kwargs}"
            )
            return func(*args, **kwargs)
        except Exception as e:
            logging.warning(
                f"[Retry] Failure on attempt {attempt} — {func.__name__}: {e}"
            )
            if attempt == retries:
                logging.error(f"[Retry] Max retries reached — {func.__name__}")
                raise
            time.sleep(delay)


def make_enhanced_trade_decision(token: Dict[str, Any]) -> Dict[str, Any]:
    """Make enhanced trade decision for a single token."""
    try:
        symbol = token.get("symbol", "")
        if not symbol:
            return {"symbol": "", "side": "HOLD", "price": 0, "amount_usd": 0, "strategy": "No Symbol", "reason": "No symbol provided"}

        # Use enhanced AI decision making
        prompt = f"""
        Analyze {symbol} for high-frequency trading:
        Price: ${token.get('price', 0):.6f}
        Volume: ${token.get('volume', 0):,.0f}
        24h Change: {token.get('change_rate', 0):.2%}
        Score: {token.get('score', 0):.2f}
        Volatility: {token.get('volatility', 0):.3f}

        Should we BUY, SELL, or HOLD? Consider momentum and risk.
        """

        decision_result = get_ai_decisions_parallel(prompt)
        
        # Handle both string and dict responses
        if isinstance(decision_result, str):
            decision = decision_result.upper()
            confidence = 0.5  # Default confidence for string responses
        else:
            decision = decision_result.get("decision", "HOLD").upper()
            confidence = decision_result.get("confidence", 0.0)

        # Only proceed with high-confidence decisions
        if decision in ["BUY", "SELL"] and confidence > 0.6:
            return {
                "symbol": symbol,
                "side": decision,
                "price": token.get("price", 0),
                "amount_usd": 100,  # Default trade amount
                "strategy": "Enhanced AI",
                "reason": f"AI decision with {confidence:.2f} confidence",
            }

        return {"symbol": symbol, "side": "HOLD", "price": token.get("price", 0), "amount_usd": 0, "strategy": "Enhanced AI", "reason": "Low confidence decision"}

    except Exception as e:
        logging.error(f"Enhanced decision failed for {token}: {e}")
        return {"symbol": token.get("symbol", ""), "side": "HOLD", "price": 0, "amount_usd": 0, "strategy": "Error", "reason": f"Error: {str(e)}"}


def schedule_auto_trade():
    """Enhanced auto trading scheduler with parallel processing and improved performance."""
    global cycle_count, total_trades, successful_trades

    logging.info("[START] Enhanced auto trade loop initiated...")

    held_tokens = set()
    last_performance_log = time.time()

    try:
        while True:
            cycle_start = time.time()
            cycle_count += 1

            logging.info(
                f"[CYCLE {cycle_count}] Starting enhanced trading cycle at {datetime.now()}"
            )

            # --- Enhanced Token Fetching ---
            try:
                tokens = retry(
                    get_top_tokens_for_trading, args=(20,)
                )  # Get more tokens
                if not tokens:
                    logging.warning("[Token Fetch] Empty list received. Retrying.")
                    time.sleep(TRADE_INTERVAL_SECONDS)
                    continue

                # Filter and prepare tokens for analysis
                top_tokens = []
                for token in tokens[:BATCH_SIZE]:
                    if isinstance(token, dict):
                        symbol = token.get("symbol", "")
                        if (
                            symbol and symbol not in held_tokens
                        ):  # Avoid duplicate positions
                            top_tokens.append(token)

                logging.info(f"[Fetch] Selected {len(top_tokens)} tokens for analysis")

            except Exception as e:
                logging.error(f"[Token Fetch] Failed: {e}")
                logging.error(traceback.format_exc())
                time.sleep(TRADE_INTERVAL_SECONDS)
                continue

            # --- Enhanced Parallel Trade Decision Processing ---
            trade_requests = []

            if top_tokens:
                try:
                    # Process tokens in parallel for faster decision making
                    with ThreadPoolExecutor(
                        max_workers=MAX_CONCURRENT_DECISIONS
                    ) as executor:
                        # Submit decision tasks
                        future_to_token = {
                            executor.submit(make_enhanced_trade_decision, token): token
                            for token in top_tokens
                        }

                        # Collect results
                        for future in as_completed(future_to_token, timeout=30):
                            token = future_to_token[future]
                            try:
                                decision_result = future.result(timeout=5)
                                if decision_result:
                                    trade_requests.append(decision_result)
                            except Exception as e:
                                symbol = token.get("symbol", "UNKNOWN")
                                logging.error(f"[Decision Error] {symbol}: {e}")

                except Exception as e:
                    logging.error(f"[Parallel Processing] Failed: {e}")
                    # Fallback to sequential processing
                    for token in top_tokens:
                        try:
                            decision_result = make_enhanced_trade_decision(token)
                            if decision_result:
                                trade_requests.append(decision_result)
                        except Exception as e:
                            logging.error(
                                f"[Sequential Decision] {token.get('symbol', 'UNKNOWN')}: {e}"
                            )

            # --- Enhanced Trade Execution ---
            if trade_requests:
                logging.info(
                    f"[Execute] Processing {len(trade_requests)} trade requests"
                )

                try:
                    if len(trade_requests) > 1:
                        # Batch execution for multiple trades
                        trade_results = execute_batch_trades(trade_requests)
                    else:
                        # Single trade execution
                        req = trade_requests[0]
                        result = execute_trade(
                            req["symbol"],
                            req["side"].lower(),
                            req.get("amount_usd"),
                            req.get("price"),
                            req.get("strategy", "Enhanced AI"),
                            req.get("reason", ""),
                        )
                        trade_results = [result]

                    # Process results and update held tokens
                    for result in trade_results:
                        total_trades += 1
                        if result.get("success", False):
                            successful_trades += 1
                            symbol = result.get("symbol", "")
                            side = result.get("side", "").upper()

                            if side == "BUY":
                                held_tokens.add(symbol)
                                logging.info(f"[BUY] Added {symbol} to held tokens")
                            elif side == "SELL":
                                held_tokens.discard(symbol)
                                logging.info(
                                    f"[SELL] Removed {symbol} from held tokens"
                                )
                        else:
                            logging.warning(
                                f"[Trade Failed] {result.get('message', 'Unknown error')}"
                            )

                except Exception as e:
                    logging.error(f"[Execution Error] {e}")
                    logging.error(traceback.format_exc())
            else:
                logging.info("[Execute] No trading opportunities found")

            # --- Enhanced Post-Trade Monitoring ---
            if held_tokens:
                logging.info(
                    f"[Monitor] Monitoring {len(held_tokens)} held tokens: {held_tokens}"
                )
                tokens_to_remove = set()

                # Monitor held positions in parallel
                with ThreadPoolExecutor(max_workers=3) as executor:
                    future_to_symbol = {
                        executor.submit(check_exit_signal, symbol): symbol
                        for symbol in held_tokens
                    }

                    for future in as_completed(future_to_symbol, timeout=20):
                        symbol = future_to_symbol[future]
                        try:
                            should_exit = future.result(timeout=5)
                            if should_exit:
                                if TRADING_MODE == "LIVE":
                                    result = retry(execute_trade, args=(symbol, "sell"))
                                    if result.get("success", False):
                                        tokens_to_remove.add(symbol)
                                        logging.info(
                                            f"[Exit] Successfully sold {symbol}"
                                        )
                                else:
                                    tokens_to_remove.add(symbol)
                                    logging.info(f"[Paper Exit] {symbol}")
                        except Exception as e:
                            logging.error(f"[Monitor Error] {symbol}: {e}")

                held_tokens -= tokens_to_remove

            # --- Performance Logging ---
            cycle_time = time.time() - cycle_start
            if (
                time.time() - last_performance_log
                > PERFORMANCE_LOG_INTERVAL * TRADE_INTERVAL_SECONDS
            ):
                success_rate = (
                    (successful_trades / total_trades * 100) if total_trades > 0 else 0
                )
                logging.info(
                    f"[PERFORMANCE] Cycle {cycle_count} | "
                    f"Time: {cycle_time:.2f}s | "
                    f"Trades: {total_trades} | "
                    f"Success Rate: {success_rate:.1f}% | "
                    f"Held: {len(held_tokens)}"
                )
                last_performance_log = time.time()

            # --- Enhanced Dashboard Update ---
            try:
                portfolio, balance = load_portfolio_json()
                retry(print_pnl_dashboard, args=(portfolio,))

                # Log trading metrics
                metrics = get_trading_performance_metrics()
                if metrics:
                    logging.debug(f"[Metrics] {metrics}")

            except Exception as e:
                logging.error(f"[Dashboard Error] {e}")

            # --- Dynamic Sleep Interval ---
            sleep_time = TRADE_INTERVAL_SECONDS
            if len(trade_requests) > 3:  # High activity - reduce interval
                sleep_time = max(FAST_MODE_INTERVAL, sleep_time // 2)
            elif not trade_requests:  # No activity - increase interval slightly
                sleep_time = min(sleep_time * 1.5, 120)

            logging.info(
                f"[Sleep] Waiting {sleep_time:.0f} seconds until next cycle..."
            )
            time.sleep(sleep_time)

    except KeyboardInterrupt:
        logging.info("[EXIT] Enhanced auto trade loop interrupted.")
    except Exception as e:
        logging.error(f"[Fatal Error] {e}")
        logging.error(traceback.format_exc())


def check_exit_signal(symbol: str) -> bool:
    """Check if we should exit a position for the given symbol."""
    try:
        # Use enhanced AI decision for exit signals
        prompt = f"Should we SELL our position in {symbol}? Consider current market conditions and profit-taking."
        decision_result = get_ai_decisions_parallel(prompt)
        
        # Handle both string and dict responses
        if isinstance(decision_result, str):
            decision = decision_result.upper()
            confidence = 0.5  # Default confidence for string responses
        else:
            decision = decision_result.get("decision", "HOLD").upper()
            confidence = decision_result.get("confidence", 0.0)

        # Exit if AI suggests SELL with high confidence
        if decision == "SELL" and confidence > 0.7:
            logging.info(
                f"[Exit Signal] {symbol} - {decision} with {confidence:.2f} confidence"
            )
            return True

        return False

    except Exception as e:
        logging.error(f"Exit signal check failed for {symbol}: {e}")
        return False


if __name__ == "__main__":
    schedule_auto_trade()
