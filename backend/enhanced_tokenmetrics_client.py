#!/usr/bin/env python3
"""
🚀 Enhanced TokenMetrics Client - Advanced Membership Features
Maximizes $100/month membership value with all premium features
"""

import logging
import time
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime
import os

# Import SDKs for price validation
from kucoin_sdk_migration import kucoin_sdk
from coingecko_sdk_migration import coingecko_sdk

logger = logging.getLogger(__name__)


class EnhancedTokenMetricsClient:
    """
    Advanced TokenMetrics client that maximizes membership value
    Uses all premium features: AI reports, sentiment, trading signals, etc.
    """

    def __init__(self):
        self.base_url = "https://api.tokenmetrics.com/v2"
        self.api_key = os.getenv("TOKENMETRICS_API_KEY")
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes cache for live trading

        # Advanced membership endpoints
        self.premium_endpoints = {
            "ai_reports": "/ai-reports",
            "trading_signals": "/trading-signals",
            "sentiment": "/sentiments",
            "quantmetrics": "/quantmetrics",
            "scenario_analysis": "/scenario-analysis",
            "resistance_support": "/resistance-support",
            "correlation": "/correlation",
            "investor_grades": "/investor-grades",
            "market_metrics": "/market-metrics",
            "tokens": "/tokens",
            "prices": "/prices",
        }

        if self.api_key:
            logger.info(f"✅ Enhanced TokenMetrics client initialized with API key")
        else:
            logger.warning("⚠️ TokenMetrics API key not found - using fallback mode")

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "AlphaPredator-Advanced/1.0",
        }
        if self.api_key:
            headers["x-api-key"] = self.api_key
        return headers

    def _make_request(
        self, endpoint: str, params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Make API request with error handling"""
        if not self.api_key:
            return {"success": False, "error": "API key not configured"}

        try:
            url = f"{self.base_url}{endpoint}"
            response = requests.get(
                url, headers=self._get_headers(), params=params, timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                return {"success": True, "data": data}
            else:
                logger.warning(
                    f"TokenMetrics API error {response.status_code}: {response.text}"
                )
                return {"success": False, "error": f"API error {response.status_code}"}

        except Exception as e:
            logger.error(f"TokenMetrics request failed: {e}")
            return {"success": False, "error": str(e)}

    def get_live_prices_with_validation(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get live prices with cross-validation from multiple sources
        Uses TokenMetrics + KuCoin + CoinGecko for accuracy
        """
        result = {"prices": {}, "sources": {}, "validation": {}}

        # Get TokenMetrics prices
        tm_prices = {}
        if self.api_key:
            tm_response = self._make_request(
                self.premium_endpoints["prices"],
                {"symbols": ",".join(symbols), "limit": "50"},
            )
            if tm_response.get("success"):
                for item in tm_response.get("data", []):
                    symbol = item.get("symbol", "").upper()
                    price = item.get("price", 0)
                    if symbol and price > 0:
                        tm_prices[symbol] = float(price)

        # Get KuCoin prices for validation
        kucoin_prices = {}
        try:
            tickers = kucoin_sdk.get_all_tickers()
            for ticker in tickers:
                symbol = ticker.get("symbol", "").replace("-USDT", "").upper()
                if symbol in symbols:
                    price = float(ticker.get("last", 0))
                    if price > 0:
                        kucoin_prices[symbol] = price
        except Exception as e:
            logger.warning(f"KuCoin price validation failed: {e}")

        # Get CoinGecko prices for validation
        coingecko_prices = {}
        try:
            cg_symbols = [s.lower() for s in symbols]
            cg_data = coingecko_sdk.get_price(cg_symbols, ["usd"])
            for symbol in symbols:
                if symbol.lower() in cg_data:
                    price = cg_data[symbol.lower()].get("usd", 0)
                    if price > 0:
                        coingecko_prices[symbol] = float(price)
        except Exception as e:
            logger.warning(f"CoinGecko price validation failed: {e}")

        # Combine and validate prices
        for symbol in symbols:
            prices = []
            sources = []

            if symbol in tm_prices:
                prices.append(tm_prices[symbol])
                sources.append("TokenMetrics")
            if symbol in kucoin_prices:
                prices.append(kucoin_prices[symbol])
                sources.append("KuCoin")
            if symbol in coingecko_prices:
                prices.append(coingecko_prices[symbol])
                sources.append("CoinGecko")

            if prices:
                # Use TokenMetrics price if available, otherwise use average
                if symbol in tm_prices:
                    final_price = tm_prices[symbol]
                    primary_source = "TokenMetrics"
                else:
                    final_price = sum(prices) / len(prices)
                    primary_source = "Average"

                result["prices"][symbol] = final_price
                result["sources"][symbol] = primary_source
                result["validation"][symbol] = {
                    "price_count": len(prices),
                    "price_range": (
                        [min(prices), max(prices)] if len(prices) > 1 else [prices[0]]
                    ),
                    "sources": sources,
                }

        return result

    def get_comprehensive_token_analysis(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive analysis using all advanced membership features
        """
        analysis = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "premium_features": {},
        }

        if not self.api_key:
            return {"error": "API key required for comprehensive analysis"}

        # 1. AI Reports (Premium Feature)
        ai_reports = self._make_request(
            self.premium_endpoints["ai_reports"], {"symbol": symbol, "limit": "1"}
        )
        if ai_reports.get("success"):
            analysis["premium_features"]["ai_reports"] = ai_reports["data"]

        # 2. Trading Signals (Premium Feature)
        trading_signals = self._make_request(
            self.premium_endpoints["trading_signals"], {"symbol": symbol, "limit": "1"}
        )
        if trading_signals.get("success"):
            analysis["premium_features"]["trading_signals"] = trading_signals["data"]

        # 3. Sentiment Analysis (Premium Feature)
        sentiment = self._make_request(
            self.premium_endpoints["sentiment"], {"symbol": symbol, "limit": "1"}
        )
        if sentiment.get("success"):
            analysis["premium_features"]["sentiment"] = sentiment["data"]

        # 4. Quantmetrics (Premium Feature)
        quantmetrics = self._make_request(
            self.premium_endpoints["quantmetrics"], {"symbol": symbol, "limit": "1"}
        )
        if quantmetrics.get("success"):
            analysis["premium_features"]["quantmetrics"] = quantmetrics["data"]

        # 5. Investor Grades (Premium Feature)
        grades = self._make_request(
            self.premium_endpoints["investor_grades"], {"symbol": symbol, "limit": "1"}
        )
        if grades.get("success"):
            analysis["premium_features"]["investor_grades"] = grades["data"]

        return analysis


# Global instance
enhanced_tm_client = EnhancedTokenMetricsClient()


async def get_enhanced_tokenmetrics_data(symbols: List[str]) -> Dict[str, Any]:
    """
    Get enhanced TokenMetrics data with all premium features
    """
    try:
        # Get live prices with validation
        price_data = enhanced_tm_client.get_live_prices_with_validation(symbols)

        # Get comprehensive analysis for top symbols
        analyses = {}
        for symbol in symbols[:10]:  # Limit to top 10 to manage API costs
            analysis = enhanced_tm_client.get_comprehensive_token_analysis(symbol)
            if "error" not in analysis:
                analyses[symbol] = analysis

        return {
            "success": True,
            "prices": price_data,
            "analyses": analyses,
            "timestamp": datetime.now().isoformat(),
            "premium_features_used": len(enhanced_tm_client.premium_endpoints),
        }

    except Exception as e:
        logger.error(f"Enhanced TokenMetrics data failed: {e}")
        return {"success": False, "error": str(e)}


def get_fallback_tokenmetrics_data() -> Dict[str, Any]:
    """
    Fallback data when TokenMetrics API is not available
    Uses live prices from KuCoin/CoinGecko
    """
    try:
        # Get live prices from KuCoin
        tickers = kucoin_sdk.get_all_tickers()
        tokens = []

        for ticker in tickers[:50]:  # Top 50 tokens
            symbol = ticker.get("symbol", "")
            if "-USDT" in symbol:
                clean_symbol = symbol.replace("-USDT", "")
                price = float(ticker.get("last", 0))
                volume = float(ticker.get("volValue", 0))
                change = float(ticker.get("changeRate", 0)) * 100

                if price > 0 and volume > 100000:  # Filter for liquid tokens
                    tokens.append(
                        {
                            "symbol": symbol,
                            "clean_symbol": clean_symbol,
                            "price": price,
                            "volume_24h": volume,
                            "change_24h": change,
                            "tm_grade": "N/A",
                            "tm_score": 50,
                            "confidence": 30,
                            "tokenmetrics_available": False,
                            "trading_signal": "HOLD",
                            "source": "KuCoin Live",
                        }
                    )

        # Sort by volume
        tokens.sort(key=lambda x: x["volume_24h"], reverse=True)

        return {
            "tokens": tokens[:50],
            "total_tokens": len(tokens),
            "timestamp": time.time(),
            "source": "fallback_live_data",
        }

    except Exception as e:
        logger.error(f"Fallback data generation failed: {e}")
        return {"tokens": [], "total_tokens": 0, "error": str(e)}
