import logging
from typing import List
from token_selector import get_top_tokens
from ai_validation_engine import get_final_ai_decision
from price_fetcher import get_price  # Update to the correct function name if it's 'get_price'
from telegram_notifier import notify_new_gem_suggestion

GEM_CREDIBILITY_THRESHOLD = 0.6
TOKEN_LIMIT = 5

import asyncio

async def suggest_gems() -> None:
    """
    Suggests promising tokens (gems) based on AI decision and credibility.
    Sends a notification via Telegram if the decision is 'BUY' and credibility is above threshold.
    """
    logging.info("🧠 Running Gem Suggester...")
    
    try:
        tokens = get_top_tokens(limit=TOKEN_LIMIT)
    except Exception as e:
        logging.error(f"❌ Failed to fetch top tokens: {e}")
        return

    for token in tokens:
        try:
            # If token is a dict, extract the symbol or name; adjust the key as needed
            token_str = token["symbol"] if isinstance(token, dict) and "symbol" in token else str(token)
            # Prepare prompt string for the AI decision engine
            prompt = f"Analyze token {token_str} for investment potential."
            # Expect get_final_ai_decision to return a tuple or dict with decision and credibility
            result = get_final_ai_decision(prompt, events=[], target_symbol=token_str)
            if isinstance(result, tuple) and len(result) == 2:
                decision, credibility = result
            elif isinstance(result, dict):
                decision = result.get("decision")
                credibility = result.get("credibility")
            else:
                logging.warning(f"⚠️ Unexpected result format from AI decision engine for {token_str}: {result}")
                continue

            logging.debug(f"🔍 {token_str} → Decision: {decision}, Score: {credibility:.2f}")
            
            try:
                if credibility is None:
                    raise ValueError("Credibility is None")
                credibility_score = float(credibility)
            except (TypeError, ValueError):
                logging.warning(f"⚠️ Invalid credibility score for {token_str}: {credibility}")
                continue

            if decision == "BUY" and credibility_score > GEM_CREDIBILITY_THRESHOLD:
                price = await get_price(token_str)
                message = (
                    f"💎 <b>New Gem Detected!</b>\n\n"
                    f"📌 Token: <code>{token_str}</code>\n"
                    f"💰 Price: ${price:.6f}\n"
                    f"🧠 AI Decision: <b>{decision}</b>\n"
                    f"📊 Credibility Score: {credibility * 100:.2f}%"
                )
                # Assuming 'credibility_score' as sentiment_score and a placeholder summary
                summary = f"AI suggests {decision} for {token_str} with credibility {credibility_score:.2f}"
                # You must provide a valid Application instance here
                # Ensure 'application' is available in this scope; import or pass as needed
                # from telegram_notifier import application  # Remove this line if 'application' is not exported
                # You must pass 'application' as an argument to this function or set it globally
                # Example: await notify_new_gem_suggestion(token=token_str, sentiment_score=credibility_score, summary=summary, application=application)
                logging.error("❌ 'application' is not defined. Please provide a valid Application instance to notify_new_gem_suggestion.")
                continue
        except Exception as e:
            logging.warning(f"⚠️ Gem suggestion failed for {token}: {e}")