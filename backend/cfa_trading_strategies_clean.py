#!/usr/bin/env python3
"""
📈 CFA-Level Trading Strategies - Clean Version
Professional-grade trading strategies based on CFA Institute curriculum
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class CFASignal:
    """CFA trading signal with professional metrics"""

    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0.0 to 1.0
    reasoning: str
    strategy_name: str
    timestamp: datetime
    profit_potential: float = 0.0
    risk_score: float = 0.5


class CFATradingStrategies:
    """
    Professional CFA-level trading strategies
    Based on quantitative finance and portfolio theory
    """

    def __init__(self):
        self.risk_free_rate = 0.02  # 2% risk-free rate
        self.logger = logging.getLogger(__name__)

    def momentum_factor_strategy(
        self, price_data: List[float], volume_data: List[float]
    ) -> CFASignal:
        """
        Momentum Factor Strategy (CFA Level II)
        Based on Fama-French factor models
        """
        try:
            if len(price_data) < 252:  # Need 1 year of data
                return self._create_signal(
                    "HOLD", 0.5, "Insufficient data for momentum analysis"
                )

            # Calculate momentum over multiple periods
            returns_1m = (
                (price_data[-1] / price_data[-21] - 1) if len(price_data) >= 21 else 0
            )
            returns_3m = (
                (price_data[-1] / price_data[-63] - 1) if len(price_data) >= 63 else 0
            )
            returns_6m = (
                (price_data[-1] / price_data[-126] - 1) if len(price_data) >= 126 else 0
            )
            returns_12m = (
                (price_data[-1] / price_data[-252] - 1) if len(price_data) >= 252 else 0
            )

            # Weighted momentum score
            momentum_score = (
                returns_1m * 0.1
                + returns_3m * 0.2
                + returns_6m * 0.3
                + returns_12m * 0.4
            )

            # Volume confirmation
            recent_volume = np.mean(volume_data[-21:]) if len(volume_data) >= 21 else 0
            avg_volume = np.mean(volume_data[-252:]) if len(volume_data) >= 252 else 1
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1

            # Risk-adjusted momentum
            volatility = np.std(
                [
                    price_data[i] / price_data[i - 1] - 1
                    for i in range(1, min(len(price_data), 63))
                ]
            )
            sharpe_momentum = momentum_score / volatility if volatility > 0 else 0

            # Generate signal
            if momentum_score > 0.15 and volume_ratio > 1.2 and sharpe_momentum > 0.5:
                confidence = float(min(0.8, momentum_score * 2 + volume_ratio * 0.1))
                return self._create_signal(
                    "BUY",
                    confidence,
                    f"Strong momentum: {momentum_score:.2%}, Volume: {volume_ratio:.1f}x, Sharpe: {sharpe_momentum:.2f}",
                )
            elif momentum_score < -0.15 and sharpe_momentum < -0.3:
                confidence = float(min(0.7, abs(momentum_score) * 2))
                return self._create_signal(
                    "SELL",
                    confidence,
                    f"Negative momentum: {momentum_score:.2%}, Sharpe: {sharpe_momentum:.2f}",
                )

            return self._create_signal(
                "HOLD", 0.5, f"Neutral momentum: {momentum_score:.2%}"
            )

        except Exception as e:
            self.logger.error(f"Momentum strategy error: {e}")
            return self._create_signal("HOLD", 0.5, f"Momentum calculation error: {e}")

    def mean_reversion_strategy(
        self, price_data: List[float], fundamental_data: Dict[str, Any]
    ) -> CFASignal:
        """
        Mean Reversion Strategy (CFA Level II)
        Statistical arbitrage with fundamental analysis
        """
        try:
            if len(price_data) < 50:
                return self._create_signal(
                    "HOLD", 0.5, "Insufficient data for mean reversion"
                )

            # Calculate Z-score
            current_price = price_data[-1]
            mean_price = np.mean(price_data[-50:])
            std_price = np.std(price_data[-50:])
            z_score = (current_price - mean_price) / std_price if std_price > 0 else 0

            # Bollinger Bands
            upper_band = mean_price + (2 * std_price)
            lower_band = mean_price - (2 * std_price)

            # RSI calculation
            gains = []
            losses = []
            for i in range(1, min(len(price_data), 15)):
                change = price_data[-i] - price_data[-i - 1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            avg_gain = np.mean(gains) if gains else 0
            avg_loss = np.mean(losses) if losses else 1
            rs = avg_gain / avg_loss if avg_loss > 0 else 0
            rsi = 100 - (100 / (1 + rs))

            # Fundamental value assessment
            fair_value_ratio = fundamental_data.get("fair_value_ratio", 1.0)

            # Generate signal
            if (
                z_score < -2
                and rsi < 30
                and current_price < lower_band
                and fair_value_ratio > 1.1
            ):
                confidence = float(min(0.8, abs(z_score) * 0.3 + (100 - rsi) * 0.01))
                return self._create_signal(
                    "BUY",
                    confidence,
                    f"Oversold: Z-score {z_score:.2f}, RSI {rsi:.1f}, Fair value {fair_value_ratio:.2f}",
                )
            elif z_score > 2 and rsi > 70 and current_price > upper_band:
                confidence = float(min(0.8, z_score * 0.3 + (rsi - 50) * 0.01))
                return self._create_signal(
                    "SELL",
                    confidence,
                    f"Overbought: Z-score {z_score:.2f}, RSI {rsi:.1f}",
                )

            return self._create_signal(
                "HOLD", 0.5, f"Neutral: Z-score {z_score:.2f}, RSI {rsi:.1f}"
            )

        except Exception as e:
            self.logger.error(f"Mean reversion strategy error: {e}")
            return self._create_signal("HOLD", 0.5, f"Mean reversion error: {e}")

    def pairs_trading_strategy(
        self, price_data_a: List[float], price_data_b: List[float]
    ) -> CFASignal:
        """
        Pairs Trading Strategy (CFA Level III)
        Statistical arbitrage between correlated assets
        """
        try:
            if len(price_data_a) < 50 or len(price_data_b) < 50:
                return self._create_signal(
                    "HOLD", 0.5, "Insufficient data for pairs trading"
                )

            # Calculate correlation
            min_length = min(len(price_data_a), len(price_data_b))
            returns_a = (
                np.diff(price_data_a[-min_length:]) / price_data_a[-min_length:-1]
            )
            returns_b = (
                np.diff(price_data_b[-min_length:]) / price_data_b[-min_length:-1]
            )

            correlation = np.corrcoef(returns_a, returns_b)[0, 1]

            if abs(correlation) < 0.7:
                return self._create_signal(
                    "HOLD", 0.5, f"Low correlation: {correlation:.2f}"
                )

            # Calculate spread
            ratio = np.array(price_data_a[-min_length:]) / np.array(
                price_data_b[-min_length:]
            )
            mean_ratio = np.mean(ratio)
            std_ratio = np.std(ratio)
            current_ratio = price_data_a[-1] / price_data_b[-1]

            z_score = (current_ratio - mean_ratio) / std_ratio if std_ratio > 0 else 0

            # Generate signal
            if z_score > 2:
                confidence = float(min(0.8, abs(z_score) * 0.3))
                return self._create_signal(
                    "SELL",
                    confidence,
                    f"Pairs divergence: Z-score {z_score:.2f}, Correlation {correlation:.2f}",
                )
            elif z_score < -2:
                confidence = float(min(0.8, abs(z_score) * 0.3))
                return self._create_signal(
                    "BUY",
                    confidence,
                    f"Pairs convergence: Z-score {z_score:.2f}, Correlation {correlation:.2f}",
                )

            return self._create_signal(
                "HOLD", 0.5, f"Pairs neutral: Z-score {z_score:.2f}"
            )

        except Exception as e:
            self.logger.error(f"Pairs trading strategy error: {e}")
            return self._create_signal("HOLD", 0.5, f"Pairs trading error: {e}")

    def _create_signal(
        self, action: str, confidence: float, reasoning: str
    ) -> CFASignal:
        """Create a standardized CFA signal"""
        return CFASignal(
            symbol="",
            action=action,
            confidence=max(0.0, min(1.0, confidence)),
            reasoning=reasoning,
            strategy_name="CFA Strategy",
            timestamp=datetime.now(),
            profit_potential=confidence * 2.0 if action == "BUY" else 0.0,
            risk_score=1.0 - confidence,
        )


# Global instance
cfa_strategies = CFATradingStrategies()


def get_cfa_trading_signal(
    symbol: str,
    price_data: List[float],
    volume_data: List[float],
    strategy_type: str = "momentum",
) -> CFASignal:
    """Get professional trading signal using CFA-level strategies"""
    try:
        if strategy_type == "momentum":
            signal = cfa_strategies.momentum_factor_strategy(price_data, volume_data)
        elif strategy_type == "mean_reversion":
            fundamental_data = {"fair_value_ratio": 1.0}
            signal = cfa_strategies.mean_reversion_strategy(
                price_data, fundamental_data
            )
        elif strategy_type == "pairs_trading":
            signal = cfa_strategies.pairs_trading_strategy(price_data, price_data)
        elif strategy_type == "volatility_breakout":
            # Simple volatility strategy
            if len(price_data) >= 20:
                returns = np.diff(price_data) / price_data[:-1]
                volatility = np.std(returns[-20:])
                if volatility > np.std(returns) * 1.5:
                    signal = cfa_strategies._create_signal(
                        "BUY", 0.7, f"High volatility: {volatility:.3f}"
                    )
                else:
                    signal = cfa_strategies._create_signal(
                        "HOLD", 0.5, f"Normal volatility: {volatility:.3f}"
                    )
            else:
                signal = cfa_strategies._create_signal("HOLD", 0.5, "Insufficient data")
        elif strategy_type == "risk_parity":
            # Simple risk parity strategy
            if len(price_data) >= 50:
                returns = np.diff(price_data) / price_data[:-1]
                sharpe = (
                    np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
                )
                if sharpe > 0.5:
                    signal = cfa_strategies._create_signal(
                        "BUY", 0.6, f"Good Sharpe ratio: {sharpe:.2f}"
                    )
                elif sharpe < -0.3:
                    signal = cfa_strategies._create_signal(
                        "SELL", 0.6, f"Poor Sharpe ratio: {sharpe:.2f}"
                    )
                else:
                    signal = cfa_strategies._create_signal(
                        "HOLD", 0.5, f"Neutral Sharpe: {sharpe:.2f}"
                    )
            else:
                signal = cfa_strategies._create_signal("HOLD", 0.5, "Insufficient data")
        else:
            signal = cfa_strategies._create_signal(
                "HOLD", 0.5, "Strategy type not implemented"
            )

        signal.symbol = symbol
        return signal

    except Exception as e:
        logger.error(f"CFA strategy error for {symbol}: {e}")
        return cfa_strategies._create_signal("HOLD", 0.5, "Strategy execution error")
