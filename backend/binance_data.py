
from utils.api_client import get
import logging
from typing import Dict, Any, List, Optional
import requests
from error_codes import error_response

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class BinanceData:
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"

    def get_exchange_info(self) -> Dict[str, Any]:
        """
        Fetches exchange information, including symbol trading rules and status.
        """
        endpoint = f"{self.base_url}/exchangeInfo"
        try:
            response = get(endpoint, cache_ttl=3600) # Cache exchange info for 1 hour
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching Binance exchange info: {e}")
            return error_response("BINANCE_EXCHANGE_INFO_FAILED", str(e), 500)

    def get_klines(self, symbol: str, interval: str, limit: int = 500) -> List[List[Any]] | Dict[str, Any]:
        """
        Fetches kline (candlestick) data for a given symbol and interval.

        Args:
            symbol (str): Trading pair symbol (e.g., 'BTCUSDT').
            interval (str): Candlestick interval (e.g., '1h', '1d').
            limit (int): Number of candlesticks to retrieve (max 1000).

        Returns:
            List[List[Any]] | Dict[str, Any]: List of kline data or error response.
        """
        endpoint = f"{self.base_url}/klines"
        params = {
            "symbol": symbol.upper(),
            "interval": interval,
            "limit": limit
        }
        try:
            response = get(endpoint, params=params, cache_ttl=60) # Cache klines for 1 minute
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching Binance klines for {symbol}: {e}")
            return error_response("BINANCE_KLINES_FAILED", str(e), 500)

    def get_ticker_24hr(self, symbol: Optional[str] = None) -> Dict[str, Any] | List[Dict[str, Any]] | Dict[str, Any]:
        """
        Fetches 24hr ticker price change statistics.

        Args:
            symbol (str, optional): Trading pair symbol (e.g., 'BTCUSDT'). If None, returns all symbols.

        Returns:
            Dict[str, Any] | List[Dict[str, Any]]: Ticker data.
        """
        endpoint = f"{self.base_url}/ticker/24hr"
        params = {"symbol": symbol.upper()} if symbol else {}
        try:
            response = get(endpoint, params=params, cache_ttl=60) # Cache klines for 1 minute
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching Binance 24hr ticker for {symbol if symbol else 'all symbols'}: {e}")
            return error_response("BINANCE_TICKER_FAILED", str(e), 500) if symbol else error_response("BINANCE_TICKER_FAILED", str(e), 500)

