"""
Enhanced CoinGecko integration for comprehensive token data including:
- Price data and market metrics
- News and social sentiment
- Exchange availability
- Market trends and signals
"""

import logging
from typing import Optional, Dict, Any, List
from utils.api_client import get
from cache import get_cached_data, set_cached_data
from api_optimization_manager import api_optimizer
import time

logger = logging.getLogger(__name__)

class CoinGeckoEnhanced:
    """Enhanced CoinGecko API client with comprehensive data fetching capabilities."""
    
    BASE_URL = "https://api.coingecko.com/api/v3"
    
    # Common token symbol to CoinGecko ID mapping
    TOKEN_ID_MAP = {
        "btc": "bitcoin",
        "eth": "ethereum", 
        "bnb": "binancecoin",
        "ada": "cardano",
        "dot": "polkadot",
        "link": "chainlink",
        "ltc": "litecoin",
        "bch": "bitcoin-cash",
        "xlm": "stellar",
        "vet": "vechain",
        "trx": "tron",
        "eos": "eos",
        "xmr": "monero",
        "xtz": "tezos",
        "atom": "cosmos",
        "neo": "neo",
        "mkr": "maker",
        "dash": "dash",
        "etc": "ethereum-classic",
        "zec": "zcash",
        "uni": "uniswap",
        "aave": "aave",
        "comp": "compound-governance-token",
        "sushi": "sushi",
        "1inch": "1inch",
        "snx": "havven",
        "crv": "curve-dao-token",
        "yfi": "yearn-finance",
        "bal": "balancer",
        "ren": "republic-protocol"
    }
    
    def __init__(self):
        self.rate_limit_delay = 1.0  # 1 second between requests to avoid rate limits
        self.last_request_time = 0
    
    def _rate_limit(self):
        """Implement rate limiting to avoid CoinGecko API limits."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            time.sleep(self.rate_limit_delay - time_since_last)
        self.last_request_time = time.time()
    
    def _get_coin_id(self, symbol: str) -> str:
        """Convert token symbol to CoinGecko coin ID."""
        # Use the API optimizer's enhanced mapping
        return api_optimizer.get_coingecko_id(symbol) or symbol.replace("-USDT", "").replace("-USD", "").lower()
    
    def get_comprehensive_token_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive token data including price, market data, and social metrics.
        
        Args:
            symbol: Token symbol (e.g., "BTC-USDT")
            
        Returns:
            Dictionary with comprehensive token data
        """
        cache_key = f"coingecko_comprehensive_{symbol}"
        cached_data = get_cached_data(cache_key)
        if cached_data is not None:
            return cached_data
        
        coin_id = self._get_coin_id(symbol)
        
        try:
            self._rate_limit()
            url = f"{self.BASE_URL}/coins/{coin_id}"
            params = {
                "localization": "false",
                "tickers": "true",
                "market_data": "true",
                "community_data": "true",
                "developer_data": "true",
                "sparkline": "true"
            }
            
            response = get(url, params=params, timeout=15, cache_ttl=300)  # Cache for 5 minutes
            response.raise_for_status()
            data = response.json()
            
            # Extract key information
            comprehensive_data = {
                "symbol": symbol,
                "coin_id": coin_id,
                "name": data.get("name", ""),
                "current_price": data.get("market_data", {}).get("current_price", {}).get("usd"),
                "market_cap": data.get("market_data", {}).get("market_cap", {}).get("usd"),
                "volume_24h": data.get("market_data", {}).get("total_volume", {}).get("usd"),
                "price_change_24h": data.get("market_data", {}).get("price_change_percentage_24h"),
                "price_change_7d": data.get("market_data", {}).get("price_change_percentage_7d"),
                "price_change_30d": data.get("market_data", {}).get("price_change_percentage_30d"),
                "market_cap_rank": data.get("market_cap_rank"),
                "circulating_supply": data.get("market_data", {}).get("circulating_supply"),
                "total_supply": data.get("market_data", {}).get("total_supply"),
                "max_supply": data.get("market_data", {}).get("max_supply"),
                "ath": data.get("market_data", {}).get("ath", {}).get("usd"),
                "ath_change_percentage": data.get("market_data", {}).get("ath_change_percentage", {}).get("usd"),
                "atl": data.get("market_data", {}).get("atl", {}).get("usd"),
                "atl_change_percentage": data.get("market_data", {}).get("atl_change_percentage", {}).get("usd"),
                
                # Social and community data
                "community_score": data.get("community_score"),
                "developer_score": data.get("developer_score"),
                "liquidity_score": data.get("liquidity_score"),
                "public_interest_score": data.get("public_interest_score"),
                "twitter_followers": data.get("community_data", {}).get("twitter_followers"),
                "reddit_subscribers": data.get("community_data", {}).get("reddit_subscribers"),
                "telegram_channel_user_count": data.get("community_data", {}).get("telegram_channel_user_count"),
                
                # Exchange availability
                "exchanges": self._extract_exchange_info(data.get("tickers", [])),
                
                # Sentiment indicators
                "sentiment_votes_up_percentage": data.get("sentiment_votes_up_percentage"),
                "sentiment_votes_down_percentage": data.get("sentiment_votes_down_percentage"),
                
                # Technical indicators
                "sparkline_7d": data.get("market_data", {}).get("sparkline_7d", {}).get("price", []),
                
                "last_updated": data.get("last_updated"),
                "fetch_timestamp": time.time()
            }
            
            # Cache for 5 minutes
            set_cached_data(cache_key, comprehensive_data, ttl=300)
            logger.info(f"Fetched comprehensive data for {symbol} from CoinGecko")
            return comprehensive_data
            
        except Exception as e:
            logger.warning(f"Failed to fetch comprehensive data for {symbol}: {e}")
            return {}
    
    def _extract_exchange_info(self, tickers: List[Dict]) -> Dict[str, Any]:
        """Extract exchange availability and trading info from tickers."""
        exchanges = {}
        for ticker in tickers:
            market = ticker.get("market", {})
            exchange_name = market.get("name", "").lower()
            if exchange_name:
                if exchange_name not in exchanges:
                    exchanges[exchange_name] = {
                        "available": True,
                        "volume_usd": 0,
                        "pairs": []
                    }
                
                volume = ticker.get("converted_volume", {}).get("usd", 0)
                if volume:
                    exchanges[exchange_name]["volume_usd"] += volume
                
                base = ticker.get("base", "")
                target = ticker.get("target", "")
                if base and target:
                    pair = f"{base}/{target}"
                    if pair not in exchanges[exchange_name]["pairs"]:
                        exchanges[exchange_name]["pairs"].append(pair)
        
        return exchanges
    
    def get_market_trends(self, symbol: str) -> Dict[str, Any]:
        """
        Get market trends and technical indicators for a token.
        
        Args:
            symbol: Token symbol (e.g., "BTC-USDT")
            
        Returns:
            Dictionary with market trend data
        """
        cache_key = f"coingecko_trends_{symbol}"
        cached_data = get_cached_data(cache_key)
        if cached_data is not None:
            return cached_data
        
        coin_id = self._get_coin_id(symbol)
        
        try:
            self._rate_limit()
            # Get historical data for trend analysis
            url = f"{self.BASE_URL}/coins/{coin_id}/market_chart"
            params = {
                "vs_currency": "usd",
                "days": "30",
                "interval": "daily"
            }
            
            response = get(url, params=params, timeout=15, cache_ttl=600)  # Cache for 10 minutes
            response.raise_for_status()
            data = response.json()
            
            prices = data.get("prices", [])
            volumes = data.get("total_volumes", [])
            market_caps = data.get("market_caps", [])
            
            trend_data = {
                "symbol": symbol,
                "coin_id": coin_id,
                "price_trend_30d": self._calculate_trend(prices),
                "volume_trend_30d": self._calculate_trend(volumes),
                "market_cap_trend_30d": self._calculate_trend(market_caps),
                "volatility_30d": self._calculate_volatility(prices),
                "support_resistance": self._find_support_resistance(prices),
                "fetch_timestamp": time.time()
            }
            
            # Cache for 10 minutes
            set_cached_data(cache_key, trend_data, ttl=600)
            logger.info(f"Fetched market trends for {symbol} from CoinGecko")
            return trend_data
            
        except Exception as e:
            logger.warning(f"Failed to fetch market trends for {symbol}: {e}")
            return {}
    
    def _calculate_trend(self, data_points: List[List]) -> str:
        """Calculate trend direction from price/volume data points."""
        if len(data_points) < 2:
            return "insufficient_data"
        
        # Get first and last values
        first_value = data_points[0][1] if len(data_points[0]) > 1 else 0
        last_value = data_points[-1][1] if len(data_points[-1]) > 1 else 0
        
        if last_value > first_value * 1.05:  # 5% increase
            return "bullish"
        elif last_value < first_value * 0.95:  # 5% decrease
            return "bearish"
        else:
            return "sideways"
    
    def _calculate_volatility(self, prices: List[List]) -> float:
        """Calculate price volatility over the given period."""
        if len(prices) < 2:
            return 0.0
        
        price_values = [point[1] for point in prices if len(point) > 1]
        if len(price_values) < 2:
            return 0.0
        
        # Calculate daily returns
        returns = []
        for i in range(1, len(price_values)):
            if price_values[i-1] != 0:
                daily_return = (price_values[i] - price_values[i-1]) / price_values[i-1]
                returns.append(daily_return)
        
        if not returns:
            return 0.0
        
        # Calculate standard deviation of returns
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        volatility = variance ** 0.5
        
        return volatility
    
    def _find_support_resistance(self, prices: List[List]) -> Dict[str, float]:
        """Find support and resistance levels from price data."""
        if len(prices) < 10:
            return {"support": 0.0, "resistance": 0.0}
        
        price_values = [point[1] for point in prices if len(point) > 1]
        if not price_values:
            return {"support": 0.0, "resistance": 0.0}
        
        # Simple support/resistance calculation
        sorted_prices = sorted(price_values)
        support = sorted_prices[len(sorted_prices) // 4]  # 25th percentile
        resistance = sorted_prices[3 * len(sorted_prices) // 4]  # 75th percentile
        
        return {
            "support": support,
            "resistance": resistance
        }
    
    def get_news_sentiment(self, symbol: str) -> Dict[str, Any]:
        """
        Get news and sentiment data for a token from CoinGecko.
        
        Args:
            symbol: Token symbol (e.g., "BTC-USDT")
            
        Returns:
            Dictionary with news and sentiment data
        """
        cache_key = f"coingecko_news_{symbol}"
        cached_data = get_cached_data(cache_key)
        if cached_data is not None:
            return cached_data
        
        coin_id = self._get_coin_id(symbol)
        
        try:
            self._rate_limit()
            # Get status updates (news-like content)
            url = f"{self.BASE_URL}/status_updates"
            params = {
                "category": "general",
                "project_type": "coin"
            }
            
            response = get(url, params=params, timeout=15, cache_ttl=1800)  # Cache for 30 minutes
            response.raise_for_status()
            data = response.json()
            
            # Filter for relevant coin updates
            relevant_updates = []
            for update in data.get("status_updates", []):
                if coin_id.lower() in update.get("description", "").lower():
                    relevant_updates.append({
                        "description": update.get("description", ""),
                        "category": update.get("category", ""),
                        "created_at": update.get("created_at", ""),
                        "user": update.get("user", ""),
                        "user_title": update.get("user_title", ""),
                        "pin": update.get("pin", False)
                    })
            
            sentiment_data = {
                "symbol": symbol,
                "coin_id": coin_id,
                "relevant_updates": relevant_updates,
                "update_count": len(relevant_updates),
                "fetch_timestamp": time.time()
            }
            
            # Cache for 30 minutes
            set_cached_data(cache_key, sentiment_data, ttl=1800)
            logger.info(f"Fetched news sentiment for {symbol} from CoinGecko")
            return sentiment_data
            
        except Exception as e:
            logger.warning(f"Failed to fetch news sentiment for {symbol}: {e}")
            return {}
    
    def is_token_available_on_binance(self, symbol: str) -> bool:
        """
        Check if a token is available on Binance using CoinGecko data.
        
        Args:
            symbol: Token symbol (e.g., "BTC-USDT")
            
        Returns:
            True if token is available on Binance, False otherwise
        """
        comprehensive_data = self.get_comprehensive_token_data(symbol)
        exchanges = comprehensive_data.get("exchanges", {})
        return "binance" in exchanges
    
    def get_trading_signals(self, symbol: str) -> Dict[str, Any]:
        """
        Generate trading signals based on CoinGecko data.
        
        Args:
            symbol: Token symbol (e.g., "BTC-USDT")
            
        Returns:
            Dictionary with trading signals and recommendations
        """
        cache_key = f"coingecko_signals_{symbol}"
        cached_data = get_cached_data(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            # Get comprehensive data
            token_data = self.get_comprehensive_token_data(symbol)
            trend_data = self.get_market_trends(symbol)
            news_data = self.get_news_sentiment(symbol)
            
            if not token_data:
                return {}
            
            # Generate signals based on multiple factors
            signals = {
                "symbol": symbol,
                "overall_signal": "neutral",
                "confidence": 0.0,
                "factors": {},
                "recommendations": [],
                "fetch_timestamp": time.time()
            }
            
            confidence_score = 0.0
            signal_weight = 0.0
            
            # Price trend signals
            price_change_24h = token_data.get("price_change_24h", 0)
            price_change_7d = token_data.get("price_change_7d", 0)
            
            if price_change_24h > 5 and price_change_7d > 10:
                signals["factors"]["price_trend"] = "bullish"
                signal_weight += 2
                confidence_score += 0.3
            elif price_change_24h < -5 and price_change_7d < -10:
                signals["factors"]["price_trend"] = "bearish"
                signal_weight -= 2
                confidence_score += 0.3
            else:
                signals["factors"]["price_trend"] = "neutral"
                confidence_score += 0.1
            
            # Volume analysis
            volume_trend = trend_data.get("volume_trend_30d", "sideways")
            if volume_trend == "bullish":
                signals["factors"]["volume"] = "increasing"
                signal_weight += 1
                confidence_score += 0.2
            elif volume_trend == "bearish":
                signals["factors"]["volume"] = "decreasing"
                signal_weight -= 1
                confidence_score += 0.2
            
            # Market sentiment
            sentiment_up = token_data.get("sentiment_votes_up_percentage", 50)
            if sentiment_up > 70:
                signals["factors"]["sentiment"] = "positive"
                signal_weight += 1
                confidence_score += 0.2
            elif sentiment_up < 30:
                signals["factors"]["sentiment"] = "negative"
                signal_weight -= 1
                confidence_score += 0.2
            
            # Social activity
            twitter_followers = token_data.get("twitter_followers", 0)
            reddit_subscribers = token_data.get("reddit_subscribers", 0)
            if twitter_followers > 100000 or reddit_subscribers > 50000:
                signals["factors"]["social_activity"] = "high"
                confidence_score += 0.1
            
            # News activity
            news_count = news_data.get("update_count", 0)
            if news_count > 3:
                signals["factors"]["news_activity"] = "high"
                confidence_score += 0.1
            
            # Determine overall signal
            if signal_weight > 2:
                signals["overall_signal"] = "bullish"
            elif signal_weight < -2:
                signals["overall_signal"] = "bearish"
            else:
                signals["overall_signal"] = "neutral"
            
            signals["confidence"] = min(confidence_score, 1.0)
            
            # Generate recommendations
            if signals["overall_signal"] == "bullish" and signals["confidence"] > 0.6:
                signals["recommendations"].append("Consider buying opportunity")
            elif signals["overall_signal"] == "bearish" and signals["confidence"] > 0.6:
                signals["recommendations"].append("Consider selling or avoiding")
            else:
                signals["recommendations"].append("Monitor for clearer signals")
            
            # Cache for 15 minutes
            set_cached_data(cache_key, signals, ttl=900)
            logger.info(f"Generated trading signals for {symbol}")
            return signals
            
        except Exception as e:
            logger.warning(f"Failed to generate trading signals for {symbol}: {e}")
            return {}


    def batch_get_comprehensive_data(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get comprehensive data for multiple tokens in batch with intelligent processing.
        
        Args:
            symbols: List of token symbols (e.g., ["BTC-USDT", "ETH-USDT"])
            
        Returns:
            Dictionary mapping symbols to their comprehensive data
        """
        logger.info(f"Starting batch comprehensive data fetch for {len(symbols)} symbols")
        
        results = {}
        batch_size = 5  # Smaller batches to respect rate limits
        
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            logger.info(f"Processing comprehensive data batch {i//batch_size + 1}: {batch}")
            
            for symbol in batch:
                try:
                    data = self.get_comprehensive_token_data(symbol)
                    if data:
                        results[symbol] = data
                        logger.debug(f"Successfully fetched comprehensive data for {symbol}")
                    else:
                        logger.warning(f"No comprehensive data returned for {symbol}")
                        results[symbol] = {}
                except Exception as e:
                    logger.error(f"Failed to fetch comprehensive data for {symbol}: {e}")
                    results[symbol] = {}
                
                # Rate limiting between individual requests
                self._rate_limit()
            
            # Additional delay between batches
            if i + batch_size < len(symbols):
                time.sleep(2)
        
        logger.info(f"Batch comprehensive data fetch complete: {len(results)} symbols processed")
        return results
    
    def batch_get_trading_signals(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get trading signals for multiple tokens in batch.
        
        Args:
            symbols: List of token symbols (e.g., ["BTC-USDT", "ETH-USDT"])
            
        Returns:
            Dictionary mapping symbols to their trading signals
        """
        logger.info(f"Starting batch trading signals fetch for {len(symbols)} symbols")
        
        results = {}
        batch_size = 3  # Even smaller batches for complex operations
        
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            logger.info(f"Processing trading signals batch {i//batch_size + 1}: {batch}")
            
            for symbol in batch:
                try:
                    signals = self.get_trading_signals(symbol)
                    if signals:
                        results[symbol] = signals
                        logger.debug(f"Successfully generated trading signals for {symbol}")
                    else:
                        logger.warning(f"No trading signals generated for {symbol}")
                        results[symbol] = {}
                except Exception as e:
                    logger.error(f"Failed to generate trading signals for {symbol}: {e}")
                    results[symbol] = {}
                
                # Rate limiting between individual requests
                self._rate_limit()
            
            # Additional delay between batches
            if i + batch_size < len(symbols):
                time.sleep(3)
        
        logger.info(f"Batch trading signals fetch complete: {len(results)} symbols processed")
        return results
    
    def batch_check_binance_availability(self, symbols: List[str]) -> Dict[str, bool]:
        """
        Check Binance availability for multiple tokens in batch.
        
        Args:
            symbols: List of token symbols (e.g., ["BTC-USDT", "ETH-USDT"])
            
        Returns:
            Dictionary mapping symbols to their Binance availability
        """
        logger.info(f"Starting batch Binance availability check for {len(symbols)} symbols")
        
        results = {}
        batch_size = 10  # Larger batches for simple availability checks
        
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            logger.info(f"Processing Binance availability batch {i//batch_size + 1}: {batch}")
            
            for symbol in batch:
                try:
                    available = self.is_token_available_on_binance(symbol)
                    results[symbol] = available
                    logger.debug(f"Binance availability for {symbol}: {available}")
                except Exception as e:
                    logger.error(f"Failed to check Binance availability for {symbol}: {e}")
                    results[symbol] = False
                
                # Minimal rate limiting for cached operations
                time.sleep(0.2)
            
            # Small delay between batches
            if i + batch_size < len(symbols):
                time.sleep(1)
        
        logger.info(f"Batch Binance availability check complete: {len(results)} symbols processed")
        return results


# Global instance for easy access
coingecko_enhanced = CoinGeckoEnhanced()

# Convenience batch functions
async def batch_get_comprehensive_token_data(symbols: List[str]) -> Dict[str, Dict[str, Any]]:
    """Async wrapper for batch comprehensive data fetching"""
    import asyncio
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, coingecko_enhanced.batch_get_comprehensive_data, symbols)

async def batch_get_trading_signals(symbols: List[str]) -> Dict[str, Dict[str, Any]]:
    """Async wrapper for batch trading signals"""
    import asyncio
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, coingecko_enhanced.batch_get_trading_signals, symbols)

async def batch_check_binance_availability(symbols: List[str]) -> Dict[str, bool]:
    """Async wrapper for batch Binance availability checking"""
    import asyncio
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, coingecko_enhanced.batch_check_binance_availability, symbols)
