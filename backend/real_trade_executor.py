import logging
from typing import Optional, Dict, Any
from kucoin_api import <PERSON><PERSON>oin<PERSON><PERSON>
from config import TRADING_MODE, BASE_ORDER_SIZE_USDT
from trade_executor import log_trade, load_portfolio, save_portfolio
import time

logger = logging.getLogger(__name__)


class RealTradeExecutor:
    """Real trading executor using KuCoin API"""

    def __init__(self):
        self.kucoin = KuCoinAPI()
        self.min_order_size = 1.0  # Minimum $1 order

    def execute_real_trade(
        self,
        token_symbol: str,
        side: str,
        amount_usd: Optional[float] = None,
        strategy: str = "AI",
        reason: str = "",
    ) -> Dict[str, Any]:
        """
        Execute a real trade on KuCoin

        Args:
            token_symbol: Trading pair (e.g., "BTC-USDT")
            side: "BUY" or "SELL"
            amount_usd: USD amount to trade
            strategy: Strategy name
            reason: Reason for trade

        Returns:
            Dict with success status and details
        """

        if TRADING_MODE.upper() != "LIVE":
            return {
                "success": False,
                "message": f"Not in live trading mode (current: {TRADING_MODE})",
            }

        if not self.kucoin.authenticated:
            return {"success": False, "message": "KuCoin not authenticated"}

        try:
            # Ensure proper symbol format
            if not token_symbol.endswith("-USDT"):
                token_symbol = f"{token_symbol}-USDT"

            # Get current market price
            ticker = self.kucoin.get_ticker(token_symbol)
            if not ticker:
                return {
                    "success": False,
                    "message": f"Could not get price for {token_symbol}",
                }

            current_price = float(ticker.get("price", 0))
            if current_price <= 0:
                return {
                    "success": False,
                    "message": f"Invalid price for {token_symbol}",
                }

            # Calculate order details
            if amount_usd is None:
                amount_usd = BASE_ORDER_SIZE_USDT

            if amount_usd < self.min_order_size:
                return {
                    "success": False,
                    "message": f"Order size too small: ${amount_usd}",
                }

            # Check balances
            if side.upper() == "BUY":
                usdt_balance = self.kucoin.get_account_balance("USDT")
                if usdt_balance < amount_usd:
                    return {
                        "success": False,
                        "message": f"Insufficient USDT balance: {usdt_balance}",
                    }

                # Calculate token amount to buy
                token_amount = amount_usd / current_price
                # Round price to appropriate precision for KuCoin (2 decimal places for USDT pairs)
                order_price = round(
                    current_price * 1.001, 2
                )  # Slightly above market for quick fill

            else:  # SELL
                token_symbol_base = token_symbol.replace("-USDT", "")
                token_balance = self.kucoin.get_account_balance(token_symbol_base)
                token_amount = amount_usd / current_price

                if token_balance < token_amount:
                    return {
                        "success": False,
                        "message": f"Insufficient {token_symbol_base} balance: {token_balance}",
                    }

                # Round price to appropriate precision for KuCoin
                order_price = round(
                    current_price * 0.999, 2
                )  # Slightly below market for quick fill

            # Place the order
            logger.info(
                f"Placing {side} order: {token_amount:.8f} {token_symbol} at ${order_price:.8f}"
            )

            # Use market order instead of limit order to avoid price precision issues
            order_result = self.kucoin.place_market_order(
                symbol=token_symbol,
                side=side.lower(),
                size=token_amount if side.upper() == "SELL" else None,
                funds=amount_usd if side.upper() == "BUY" else None,
            )

            if order_result:
                # Log the trade
                log_trade(
                    token_symbol, side, token_amount, order_price, strategy, reason
                )

                return {
                    "success": True,
                    "order_id": order_result.get("orderId"),
                    "message": f"{side} order placed: {token_amount:.8f} {token_symbol} at ${order_price:.8f}",
                    "amount": token_amount,
                    "price": order_price,
                    "value": token_amount * order_price,
                }
            else:
                return {"success": False, "message": "Failed to place order on KuCoin"}

        except Exception as e:
            logger.error(f"Error executing real trade: {e}")
            return {"success": False, "message": str(e)}


# Global instance
real_trade_executor = RealTradeExecutor()
