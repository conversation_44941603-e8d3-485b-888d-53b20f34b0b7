#!/usr/bin/env python3
"""
💰 REAL-TIME COST MONITORING SYSTEM
Track actual API usage and costs in real-time
"""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from pathlib import Path
import threading
from collections import defaultdict

logger = logging.getLogger(__name__)


class RealTimeCostMonitor:
    """Real-time cost monitoring and usage tracking."""

    def __init__(self):
        self.usage_file = Path("data/api_usage.json")
        self.costs_file = Path("data/cost_tracking.json")

        # Ensure data directory exists
        self.usage_file.parent.mkdir(exist_ok=True)

        # Updated API cost rates (2024-2025 pricing per 1000 tokens/requests)
        self.cost_rates = {
            "openai": {
                "gpt-4o": 0.0025,  # GPT-4o input tokens per 1K (Dec 2024)
                "gpt-4o-output": 0.01,  # GPT-4o output tokens per 1K
                "gpt-4-turbo": 0.01,  # GPT-4 Turbo input per 1K
                "gpt-3.5-turbo": 0.0005,  # GPT-3.5 Turbo per 1K
                "requests": 0.002,  # per request
            },
            "deepseek": {
                "requests": 0.0002,
                "tokens": 0.00014,
            },  # DeepSeek V3 via Together AI
            "gemini": {"requests": 0.0005, "tokens": 0.000075},  # Gemini 1.5 Flash
            "tokenmetrics": {
                "requests": 0.02
            },  # TokenMetrics Advanced ~$200/month ÷ 10K calls
            "coingecko": {"requests": 0.003},  # CoinGecko Pro $129/month ÷ 50K calls
            "kucoin": {"requests": 0.0},  # free (rate limited)
            "reddit": {"requests": 0.0024},  # Reddit API Premium $0.24 per 1K
            "discord": {"requests": 0.0},  # free bot
            "github": {"requests": 0.0},  # free API (rate limited)
            "cryptopanic": {"requests": 0.004},  # CryptoPanic Pro $99/month ÷ 25K calls
        }

        # Daily limits
        self.daily_limits = {
            "openai": 2000,
            "deepseek": 1500,
            "gemini": 1000,
            "claude": 500,
            "tokenmetrics": 1000,
            "coingecko": 10000,
            "kucoin": 50000,
            "reddit": 1000,
            "github": 5000,
            "cointelegraph": 1000,
            "coindesk": 1000,
            "decrypt": 1000,
            "cryptonews": 1000,
            "newsbtc": 1000,
            "cryptopotato": 1000,
            "bitcoinist": 1000,
            "external_api": 5000,
        }

        # Load existing usage data
        self.usage_data = self._load_usage_data()
        self.lock = threading.Lock()

    def _load_usage_data(self) -> Dict[str, Any]:
        """Load existing usage data."""
        try:
            if self.usage_file.exists():
                with open(self.usage_file, "r") as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load usage data: {e}")

        # Return default structure
        return {
            "daily_usage": defaultdict(int),
            "monthly_usage": defaultdict(int),
            "last_reset": datetime.now().date().isoformat(),
            "total_costs": defaultdict(float),
            "request_history": [],
        }

    def _save_usage_data(self):
        """Save usage data to file."""
        try:
            with open(self.usage_file, "w") as f:
                json.dump(self.usage_data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save usage data: {e}")

    def record_api_call(
        self, service: str, endpoint: str = "", tokens: int = 0, cost: float = 0.0
    ):
        """Record an API call for cost tracking."""
        with self.lock:
            current_date = datetime.now().date().isoformat()

            # Reset daily counters if new day
            if self.usage_data.get("last_reset") != current_date:
                self.usage_data["daily_usage"] = defaultdict(int)
                self.usage_data["last_reset"] = current_date

            # Record usage (ensure service exists in dict)
            if service not in self.usage_data["daily_usage"]:
                self.usage_data["daily_usage"][service] = 0
            if service not in self.usage_data["monthly_usage"]:
                self.usage_data["monthly_usage"][service] = 0

            self.usage_data["daily_usage"][service] += 1
            self.usage_data["monthly_usage"][service] += 1

            # Calculate cost if not provided
            if cost == 0.0 and service in self.cost_rates:
                if tokens > 0 and "tokens" in self.cost_rates[service]:
                    cost = (tokens / 1000) * self.cost_rates[service]["tokens"]
                else:
                    cost = self.cost_rates[service].get("requests", 0.001)

            # Ensure total_costs has the service key
            if service not in self.usage_data["total_costs"]:
                self.usage_data["total_costs"][service] = 0.0

            self.usage_data["total_costs"][service] += cost

            # Add to request history (keep last 1000)
            self.usage_data["request_history"].append(
                {
                    "service": service,
                    "endpoint": endpoint,
                    "timestamp": datetime.now().isoformat(),
                    "tokens": tokens,
                    "cost": cost,
                }
            )

            # Keep only last 1000 requests
            if len(self.usage_data["request_history"]) > 1000:
                self.usage_data["request_history"] = self.usage_data["request_history"][
                    -1000:
                ]

            # Save periodically
            if len(self.usage_data["request_history"]) % 10 == 0:
                self._save_usage_data()

    def get_real_time_costs(self) -> Dict[str, Any]:
        """Get real-time cost and usage data."""
        with self.lock:
            current_date = datetime.now().date().isoformat()

            # Reset daily counters if new day
            if self.usage_data.get("last_reset") != current_date:
                self.usage_data["daily_usage"] = defaultdict(int)
                self.usage_data["last_reset"] = current_date

            # Calculate current costs
            daily_usage = dict(self.usage_data["daily_usage"])
            monthly_costs = dict(self.usage_data["total_costs"])

            # Calculate usage percentages
            limits_data = {}
            alerts = []

            for service, limit in self.daily_limits.items():
                used = daily_usage.get(service, 0)
                percentage = (used / limit) * 100 if limit > 0 else 0

                limits_data[service] = {
                    "used": used,
                    "limit": limit,
                    "percentage": round(percentage, 1),
                }

                # Generate alerts
                if percentage >= 80:
                    alerts.append(
                        {
                            "service": service.title(),
                            "usage": percentage,
                            "threshold": 80,
                            "status": "critical" if percentage >= 95 else "warning",
                        }
                    )

            # Calculate total API calls today
            total_calls = sum(daily_usage.values())

            # Get recent activity (last hour)
            one_hour_ago = datetime.now() - timedelta(hours=1)
            recent_requests = [
                req
                for req in self.usage_data["request_history"]
                if datetime.fromisoformat(req["timestamp"]) > one_hour_ago
            ]

            return {
                "monthly_costs": {
                    "ai_providers": {
                        "openai": round(monthly_costs.get("openai", 0), 2),
                        "deepseek": round(monthly_costs.get("deepseek", 0), 2),
                        "gemini": round(monthly_costs.get("gemini", 0), 2),
                        "claude": round(monthly_costs.get("claude", 0), 2),
                    },
                    "data_apis": {
                        "tokenmetrics": round(monthly_costs.get("tokenmetrics", 0), 2),
                        "kucoin": round(monthly_costs.get("kucoin", 0), 2),
                        "coingecko": round(monthly_costs.get("coingecko", 0), 2),
                        "cryptopanic": round(monthly_costs.get("cryptopanic", 0), 2),
                    },
                    "infrastructure": {
                        "hosting": 25.00,  # Fixed costs
                        "database": 10.00,
                        "monitoring": 5.00,
                    },
                },
                "daily_usage": {
                    "openai_requests": daily_usage.get("openai", 0),
                    "deepseek_requests": daily_usage.get("deepseek", 0),
                    "gemini_requests": daily_usage.get("gemini", 0),
                    "claude_requests": daily_usage.get("claude", 0),
                    "tokenmetrics_calls": daily_usage.get("tokenmetrics", 0),
                    "total_api_calls": total_calls,
                },
                "limits": limits_data,
                "alerts": alerts,
                "recent_activity": {
                    "last_hour_requests": len(recent_requests),
                    "avg_requests_per_minute": round(len(recent_requests) / 60, 1),
                    "most_used_service": (
                        max(daily_usage.items(), key=lambda x: x[1])[0]
                        if daily_usage
                        else "none"
                    ),
                },
                "timestamp": datetime.now().isoformat(),
                "status": (
                    "critical"
                    if any(alert["status"] == "critical" for alert in alerts)
                    else "warning" if alerts else "operational"
                ),
                "data_source": "real_time_tracking",
            }


# Global instance
cost_monitor = RealTimeCostMonitor()


def record_api_usage(
    service: str, endpoint: str = "", tokens: int = 0, cost: float = 0.0
):
    """Record API usage for cost tracking."""
    cost_monitor.record_api_call(service, endpoint, tokens, cost)


def get_real_time_cost_data() -> Dict[str, Any]:
    """Get real-time cost monitoring data."""
    return cost_monitor.get_real_time_costs()


# Decorator to automatically track API calls
def track_api_call(service: str, endpoint: str = ""):
    """Decorator to automatically track API calls."""

    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                # Record successful call
                record_api_usage(service, endpoint)
                return result
            except Exception as e:
                # Record failed call (still counts toward usage)
                record_api_usage(service, endpoint)
                raise

        return wrapper

    return decorator


if __name__ == "__main__":
    # Test the cost monitor
    print("💰 TESTING REAL-TIME COST MONITOR")
    print("=" * 50)

    # Simulate some API calls
    record_api_usage("openai", "/chat/completions", tokens=150, cost=0.005)
    record_api_usage("deepseek", "/completions", tokens=200, cost=0.002)
    record_api_usage("tokenmetrics", "/token/BTC", cost=0.01)

    # Get real-time data
    data = get_real_time_cost_data()

    print(f"Total API calls today: {data['daily_usage']['total_api_calls']}")
    print(
        f"OpenAI usage: {data['limits']['openai']['used']}/{data['limits']['openai']['limit']} ({data['limits']['openai']['percentage']}%)"
    )
    print(f"Monthly OpenAI cost: ${data['monthly_costs']['ai_providers']['openai']}")
    print(f"System status: {data['status']}")
    print(f"Data source: {data['data_source']}")

    print("\n✅ Real-time cost monitor working correctly!")
