import re
import json
import logging
from collections import defaultdict
from typing import Dict

def classify_sentiment(text: str) -> str:
    """
    Dummy sentiment classifier. Replace with actual logic.
    """
    if not text:
        return "neutral"
    text = text.lower()
    if "good" in text or "great" in text or "positive" in text:
        return "positive"
    elif "bad" in text or "terrible" in text or "negative" in text:
        return "negative"
    else:
        return "neutral"

import openai

from config import OPENAI_API_KEY
from telegram_utils import notify_news_alert, AppType
from token_utils import extract_token_from_text
# from discord_news_bot import telegram_app_instance # Import the global instance
telegram_app_instance = None  # TODO: Replace with actual instance if needed

openai.api_key = OPENAI_API_KEY
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

TAG_PATTERNS = {
    "hack": [r"\bhack\b", r"\bexploit\b", r"\bbreach\b", r"\battack\b"],
    "listing": [r"\blisting on\b", r"\blisted on\b", r"\bavailable on\b", r"\badded to\b", r"\blaunching on\b"],
    "partnership": [r"\bpartnership\b", r"\bcollaboration\b", r"\bpartnered with\b", r"\bjoined forces\b"],
    "regulation": [r"\bregulation\b", r"\bsec\b", r"\bban\b", r"\blegal action\b", r"\bcompliance\b"],
    "price-action": [r"\bprice spike\b", r"\bsurge\b", r"\bpump\b", r"\brally\b", r"\ball-time high\b"],
    "scam": [r"\brug pull\b", r"\bscam\b", r"\bfraud\b", r"\bexit scam\b"],
    "bullish": [r"\bbullish\b", r"\bbuy pressure\b", r"\buptrend\b"],
    "bearish": [r"\bbearish\b", r"\bsell-off\b", r"\bdowntrend\b"],
    "lawsuit": [r"\blawsuit\b", r"\bsued\b", r"\blegal battle\b"],
    "upgrade": [r"\bupgrade\b", r"\bmainnet launch\b", r"\bhard fork\b", r"\bprotocol update\b"],
    "partnership termination": [r"\bended partnership\b", r"\bterminated agreement\b", r"\bpartnership ended\b"],
    "milestone": [r"\bmilestone\b", r"\bachieved\b", r"\brecord breaking\b"],
    "token-burn": [r"\bburned\b", r"\bburning\b", r"\bburn event\b"],
    "airdrop": [r"\bairdrop\b", r"\bgiveaway\b", r"\bfree tokens\b"],
    "investment": [r"\binvestment\b", r"\braised\b", r"\bfunding round\b"],
    "exchange-down": [r"\bexchange\b.*\bdown\b", r"\boutage\b", r"\bdowntime\b"],
    "insider-trading": [r"\binsider trading\b", r"\bleaked\b.*\bbuy\b", r"\bleaked\b.*\bsell\b"],
    "kucoin listing": [r"\bkucoin\b.*\blisting\b", r"\bkucoin\b.*\bnew token\b", r"\bnew token on kucoin\b"],
    "kucoin delisting": [r"\bkucoin\b.*\bdelisting\b", r"\bkucoin\b.*\bremoved\b", r"\bdelisted from kucoin\b"],
    "kucoin update": [r"\bkucoin\b.*\bupdate\b", r"\bkucoin\b.*\bmaintenance\b", r"\bkucoin\b.*\boutage\b"],
}

CONFIDENCE_THRESHOLD = 0.3

def classify_news_type_with_confidence(content: str) -> Dict[str, float]:
    """Perform rule-based classification using keyword confidence scoring."""
    if not content:
        return {}

    text = content.lower()
    tag_confidence = defaultdict(float)

    for tag, patterns in TAG_PATTERNS.items():
        matches = sum(1 for pattern in patterns if re.search(pattern, text))
        confidence = matches / len(patterns) if patterns else 0.0
        if confidence > 0:
            tag_confidence[tag] = round(confidence, 2)

    return dict(tag_confidence)

def ai_fallback_classifier(content: str) -> Dict[str, float]:
    """Use OpenAI API to classify news content with confidence values."""
    prompt = (
        "You are a cryptocurrency news classifier. "
        "Classify the following news content into categories such as hack, listing, partnership, regulation, price-action, scam, bullish, bearish, lawsuit, upgrade, "
        "partnership termination, milestone, token-burn, airdrop, investment, exchange-down, insider-trading, kucoin listing, kucoin delisting, kucoin update. "
        "Return a JSON dictionary with categories as keys and confidence scores (0.0 to 1.0) as values.\n\n"
        f"News Content:\n\"\"\"\n{content}\n\"\"\"\n\nClassification:"
    )
    try:
        response = openai.chat.completions.create(  # type: ignore
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=120,
            temperature=0.3,
        )
        content_field = response.choices[0].message.content
        text = content_field.strip() if content_field is not None else ""
        try:
            data = json.loads(text)
        except json.JSONDecodeError:
            data = {}
            for line in text.splitlines():
                if ":" in line:
                    k, v = map(str.strip, line.split(":", 1))
                    try:
                        data[k.lower()] = float(v)
                    except ValueError:
                        continue
        result = {}
        for k, v in data.items():
            normalized = k.replace(" ", "-")
            if normalized in TAG_PATTERNS:
                result[normalized] = round(v, 2)
        return result or data
    except Exception as e:
        logger.error(f"[AI Fallback Error] {e}")
        return {}

async def send_telegram_alert(content: str, tags_conf: Dict[str, float], application) -> None:
    """Send Telegram alert with classification results."""
    top_tag = max(tags_conf.items(), key=lambda x: x[1])[0]
    confidence = tags_conf[top_tag]
    token = extract_token_from_text(content)
    try:
        await notify_news_alert(
            headline=content.strip()[:150],
            sentiment="positive" if confidence > 0.5 else "neutral"
        )
    except Exception as e:
        logger.error(f"[Telegram Alert Error] {e}")


def is_spam_or_fud(content: str) -> bool:
    """Basic spam/fud detection using keyword rules."""
    if not content:
        return False
    content = content.lower()
    patterns = [
        r"join now", r"giveaway", r"airdrops?", r"1000x", r"moon soon", r"next gem",
        r"not financial advice", r"buy now", r"get rich", r"shill", r"telegram", r"discord.gg", r"free tokens",
        r"quick gains", r"pump group", r"buy alerts?", r"\$\w+\s+(up\s+)?\d{2,}x", r"🔥", r"🚀"
    ]
    count = sum(1 for p in patterns if re.search(p, content))
    return count / len(patterns) > 0.25

def classify_news(content: str) -> Dict[str, float]:
    """Main classification logic. Uses rules, falls back to AI, and triggers alert."""
    tags_conf = classify_news_type_with_confidence(content)
    if not tags_conf or max(tags_conf.values(), default=0) < CONFIDENCE_THRESHOLD:
        ai_tags = ai_fallback_classifier(content)
        for k, v in ai_tags.items():
            if k not in tags_conf or v > tags_conf[k]:
                tags_conf[k] = v

    if tags_conf and not is_spam_or_fud(content):
        import asyncio
        asyncio.run(send_telegram_alert(content, tags_conf, telegram_app_instance))

    return tags_conf



if __name__ == "__main__":
    test_samples = [
        "KuCoin announced a new token listing today that is creating bullish price action.",
        "A major hack has impacted a leading DeFi protocol.",
        "Binance is being sued by the SEC for regulatory violations.",
        "Airdrop announced for token holders of XYZ coin!",
        "Join now to get free tokens and 1000x gains 🚀"
    ]

    for idx, news in enumerate(test_samples, 1):
        print(f"\nSample {idx}: {news}")
        tags = classify_news(news)
        print("Tags:", tags)
