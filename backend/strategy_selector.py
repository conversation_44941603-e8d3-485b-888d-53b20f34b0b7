"""
AlphaPredatorBot — Strategy Selector

Determines the market condition using technical indicators (RSI, EMA, SMA) and sentiment,
then selects an appropriate trading strategy accordingly.
"""

import logging
from typing import Optional, List, Dict, Any
from strategy_evaluator import evaluate_event_driven_signals

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def determine_market_condition(rsi: float, ema: float, sma: float, sentiment: float) -> str:
    """
    Determines the current market condition using RSI, moving averages, and sentiment score.

    Args:
        rsi (float): Relative Strength Index value (0–100)
        ema (float): Exponential Moving Average
        sma (float): Simple Moving Average
        sentiment (float): Sentiment score from -5 (bearish) to +5 (bullish)

    Returns:
        str: Market condition label
    """
    logger.info(f"[StrategySelector] RSI: {rsi}, EMA: {ema}, SMA: {sma}, Sentiment: {sentiment}")

    if rsi < 0 or rsi > 100:
        logger.warning(f"[StrategySelector] RSI value out of expected range: {rsi}")
    if sentiment < -5 or sentiment > 5:
        logger.warning(f"[StrategySelector] Sentiment value out of expected range: {sentiment}")

    if rsi < 30 and sentiment > 0:
        return "oversold_bullish"
    elif rsi > 70 and sentiment < 0:
        return "overbought_bearish"
    elif ema > sma:
        return "uptrend"
    elif ema < sma:
        return "downtrend"
    else:
        return "sideways"

def choose_strategy(condition: str, events: List[Dict[str, Any]], target_symbol: str, volatility: Optional[float] = None) -> str:
    """
    Selects a trading strategy based on the identified market condition and event-driven signals.

    Args:
        condition (str): Market condition label
        events (List[Dict[str, Any]]): List of upcoming events.
        target_symbol (str): The symbol of the token being evaluated.
        volatility (float, optional): Not used yet but reserved for future tuning

    Returns:
        str: Strategy name
    """
    logger.info(f"[StrategySelector] Market condition: {condition}")

    # Evaluate event-driven signals
    event_signal = evaluate_event_driven_signals(events, target_symbol)
    if event_signal == "BUY":
        logger.info(f"[StrategySelector] Event-driven signal: BUY for {target_symbol}. Prioritizing BUY strategy.")
        return "EVENT_DRIVEN_BUY"

    strategy_map = {
        "oversold_bullish": "MARTINGALE",
        "overbought_bearish": "FUTURES_GRID",
        "uptrend": "INFINITY_GRID",
        "downtrend": "SPOT_GRID",
        "sideways": "DCA"
    }

    return strategy_map.get(condition, "SMART_REBALANCE")
