
import os
import asyncio
import logging
from typing import Optional
from telegram import Bo<PERSON>
from telegram.ext import Application as TelegramApplication
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env.production')
load_dotenv('.env')

logger = logging.getLogger(__name__)

# Global bot instance
_bot_instance = None
_application_instance = None

def get_telegram_bot():
    """Get or create telegram bot instance"""
    global _bot_instance
    if _bot_instance is None:
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        if token:
            _bot_instance = Bot(token=token)
    return _bot_instance

def get_telegram_application():
    """Get or create telegram application instance"""
    global _application_instance
    if _application_instance is None:
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        if token:
            _application_instance = TelegramApplication.builder().token(token).build()
    return _application_instance

async def send_telegram_message(message: str, chat_id: Optional[str] = None):
    """Send a message via Telegram"""
    try:
        bot = get_telegram_bot()
        if not bot:
            logger.error("❌ Telegram bot not initialized")
            return False
            
        target_chat = chat_id or os.getenv("TELEGRAM_CHANNEL_ID")
        if not target_chat:
            logger.error("❌ No Telegram chat ID configured")
            return False
            
        await bot.send_message(chat_id=target_chat, text=message, parse_mode="Markdown")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to send Telegram message: {e}")
        return False

async def notify_trade(application, token: str, action: str, quantity: float, 
                      price: float, strategy: str, reason: str):
    """Send trade notification"""
    try:
        message = f"""
🚀 **TRADE ALERT**
Token: `{token}`
Action: **{action}**
Quantity: `{quantity}`
Price: `${price:.6f}`
Strategy: `{strategy}`
Reason: {reason}
"""
        return await send_telegram_message(message)
    except Exception as e:
        logger.error(f"❌ Trade notification failed: {e}")
        return False

async def notify_bot_status(bot_name: str, status: str, details: str = ""):
    """Send bot status notification"""
    try:
        message = f"""
🤖 **BOT STATUS UPDATE**
Bot: `{bot_name}`
Status: **{status}**
{details}
Time: `{asyncio.get_event_loop().time()}`
"""
        return await send_telegram_message(message)
    except Exception as e:
        logger.error(f"❌ Bot status notification failed: {e}")
        return False

# Enum for backward compatibility
class AppType:
    ALPHA = "alpha"
    MICRO = "micro"

async def notify_pnl_summary(context):
    """Send daily PnL summary"""
    try:
        message = "📊 **Daily PnL Summary**\nCalculating..."
        return await send_telegram_message(message)
    except Exception as e:
        logger.error(f"❌ PnL summary notification failed: {e}")
        return False

async def notify_news_alert(headline: str, sentiment: str):
    """Send news alert"""
    try:
        message = f"""
📰 **NEWS ALERT**
Headline: {headline}
Sentiment: **{sentiment}**
"""
        return await send_telegram_message(message)
    except Exception as e:
        logger.error(f"❌ News alert failed: {e}")
        return False

async def notify_new_gem_suggestion(token: str, score: float, reason: str):
    """Send gem suggestion"""
    try:
        message = f"""
💎 **GEM SUGGESTION**
Token: `{token}`
Score: `{score}`
Reason: {reason}
"""
        return await send_telegram_message(message)
    except Exception as e:
        logger.error(f"❌ Gem suggestion failed: {e}")
        return False
