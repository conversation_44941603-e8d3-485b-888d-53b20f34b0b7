#!/usr/bin/env python3
"""
🛡️ SENTIMENT ANALYSIS FALLBACK SYSTEM
Robust sentiment analysis with multiple fallback mechanisms
"""

import logging
import os
import tempfile
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class SentimentFallbackSystem:
    """Robust sentiment analysis with multiple fallback layers."""

    def __init__(self):
        self.fallback_scores = {
            # Positive sentiment tokens
            "BTC": 0.75,
            "ETH": 0.70,
            "SOL": 0.65,
            "ADA": 0.60,
            "BNB": 0.65,
            "MATIC": 0.60,
            "LINK": 0.65,
            "DOT": 0.60,
            "AVAX": 0.65,
            "UNI": 0.60,
            "ATOM": 0.55,
            "NEAR": 0.60,
            # Neutral sentiment tokens
            "DOGE": 0.50,
            "SHIB": 0.45,
            "LTC": 0.55,
            "BCH": 0.50,
            "XRP": 0.50,
            "TRX": 0.45,
            "ETC": 0.45,
            "BSV": 0.40,
            # Default for unknown tokens
            "DEFAULT": 0.50,
        }

    def get_robust_sentiment(
        self, token: str, news_items: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Get sentiment with multiple fallback mechanisms."""
        try:
            # Try primary sentiment analysis
            return self._try_primary_sentiment(token, news_items)
        except Exception as e:
            logger.warning(f"Primary sentiment failed for {token}: {e}")

            try:
                # Try secondary sentiment analysis
                return self._try_secondary_sentiment(token, news_items)
            except Exception as e:
                logger.warning(f"Secondary sentiment failed for {token}: {e}")

                # Use fallback sentiment
                return self._get_fallback_sentiment(token)

    def _try_primary_sentiment(
        self, token: str, news_items: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Try primary sentiment analysis methods."""
        try:
            from sentiment_engine import get_combined_sentiment

            result = get_combined_sentiment(token)

            if isinstance(result, dict) and "combined_score" in result:
                return {
                    "score": result["combined_score"],
                    "confidence": result.get("confidence", 0.5),
                    "method": "primary_engine",
                    "details": result,
                }
            else:
                raise ValueError("Invalid result from primary sentiment engine")
        except Exception as e:
            logger.debug(f"Primary sentiment engine failed: {e}")
            raise

    def _try_secondary_sentiment(
        self, token: str, news_items: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Try secondary sentiment analysis methods."""
        try:
            from news_sentiment_fallback import simple_sentiment_score

            # Create sentiment text from token characteristics
            sentiment_text = f"{token} cryptocurrency market analysis trading"
            if news_items:
                # Add news content if available
                news_text = " ".join(
                    [
                        item.get("title", "") + " " + item.get("description", "")
                        for item in news_items[:3]  # Use first 3 news items
                    ]
                )
                sentiment_text += " " + news_text

            raw_score = simple_sentiment_score(sentiment_text)
            normalized_score = (raw_score + 1) / 2  # Convert -1,1 to 0,1

            return {
                "score": normalized_score,
                "confidence": 0.6,
                "method": "secondary_fallback",
                "raw_score": raw_score,
            }
        except Exception as e:
            logger.debug(f"Secondary sentiment analysis failed: {e}")
            raise

    def _get_fallback_sentiment(self, token: str) -> Dict[str, Any]:
        """Get fallback sentiment based on token characteristics."""
        base_token = token.replace("-USDT", "").replace("-USD", "").upper()

        # Use predefined scores or default
        score = self.fallback_scores.get(base_token, self.fallback_scores["DEFAULT"])

        # Add small variation based on token hash for consistency
        import hashlib

        hash_val = int(hashlib.md5(base_token.encode()).hexdigest()[:8], 16)
        variation = (hash_val % 20 - 10) / 100.0  # -0.1 to +0.1
        final_score = max(0.1, min(0.9, score + variation))

        return {
            "score": final_score,
            "confidence": 0.4,
            "method": "fallback_system",
            "base_score": score,
            "variation": variation,
        }

    def fix_file_system_errors(self) -> bool:
        """Fix common file system errors in sentiment analysis."""
        try:
            # Create temporary directory for sentiment analysis if needed
            temp_dir = tempfile.gettempdir()
            sentiment_temp = os.path.join(temp_dir, "sentiment_analysis")

            if not os.path.exists(sentiment_temp):
                os.makedirs(sentiment_temp, exist_ok=True)
                logger.info(f"✅ Created sentiment temp directory: {sentiment_temp}")

            # Set environment variable for sentiment analysis
            os.environ["SENTIMENT_TEMP_DIR"] = sentiment_temp

            # Fix home directory issues
            if not os.environ.get("HOME"):
                os.environ["HOME"] = temp_dir
                logger.info(f"✅ Set HOME environment variable: {temp_dir}")

            return True

        except Exception as e:
            logger.error(f"Failed to fix file system errors: {e}")
            return False


# Global instance
sentiment_fallback = SentimentFallbackSystem()


def get_robust_sentiment_score(
    token: str, news_items: Optional[List[Dict]] = None
) -> float:
    """Get robust sentiment score with fallbacks."""
    try:
        result = sentiment_fallback.get_robust_sentiment(token, news_items)
        return result["score"]
    except Exception as e:
        logger.error(f"All sentiment analysis methods failed for {token}: {e}")
        return 0.5  # Neutral fallback


def get_robust_sentiment_details(
    token: str, news_items: Optional[List[Dict]] = None
) -> Dict[str, Any]:
    """Get detailed sentiment analysis with fallbacks."""
    return sentiment_fallback.get_robust_sentiment(token, news_items)


def fix_sentiment_file_system():
    """Fix file system issues for sentiment analysis."""
    return sentiment_fallback.fix_file_system_errors()


# Initialize file system fixes on import
fix_sentiment_file_system()

if __name__ == "__main__":
    # Test the fallback system
    test_tokens = ["BTC", "ETH", "UNKNOWN_TOKEN"]

    print("🛡️ TESTING SENTIMENT FALLBACK SYSTEM")
    print("=" * 50)

    for token in test_tokens:
        result = get_robust_sentiment_details(token)
        print(
            f"{token:15s}: {result['score']:.3f} ({result['method']}) confidence: {result['confidence']:.2f}"
        )

    print("\n✅ Sentiment fallback system working correctly!")
