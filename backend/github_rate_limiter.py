#!/usr/bin/env python3
"""
🚫 GITHUB API RATE LIMITER
Intelligent rate limiting and caching for GitHub API
"""

import time
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pathlib import Path

from config import GITHUB_VALID, GITHUB_OAUTH_TOKEN
from cache import get_cached_data, set_cached_data

logger = logging.getLogger(__name__)

class GitHubRateLimiter:
    """Intelligent GitHub API rate limiter with caching."""
    
    def __init__(self):
        self.last_request_time = 0
        self.requests_made = 0
        self.rate_limit_reset = 0
        self.rate_limit_remaining = 5000  # Default GitHub limit
        self.min_delay = 1.0  # Minimum delay between requests
        self.cache_duration = 3600  # 1 hour cache for GitHub data
        
        # Fallback data for when GitHub API is unavailable
        self.fallback_repos = [
            {"name": "bitcoin", "stars": 70000, "language": "C++", "description": "Bitcoin Core integration/staging tree"},
            {"name": "ethereum", "stars": 45000, "language": "Go", "description": "Official Go implementation of the Ethereum protocol"},
            {"name": "solana", "stars": 12000, "language": "Rust", "description": "Web-Scale Blockchain for fast, secure, scalable, decentralized apps"},
            {"name": "cardano-node", "stars": 3000, "language": "Haskell", "description": "The core component that is used to participate in a Cardano decentralised blockchain"},
            {"name": "binance-chain", "stars": 2500, "language": "Go", "description": "A blockchain built on Cosmos SDK"},
        ]
    
    def can_make_request(self) -> bool:
        """Check if we can make a GitHub API request."""
        if not GITHUB_VALID:
            logger.debug("GitHub API key not valid, using fallback data")
            return False
        
        current_time = time.time()
        
        # Check if we need to wait for rate limit reset
        if self.rate_limit_remaining <= 1 and current_time < self.rate_limit_reset:
            logger.warning(f"GitHub rate limit exceeded, reset at {datetime.fromtimestamp(self.rate_limit_reset)}")
            return False
        
        # Check minimum delay between requests
        if current_time - self.last_request_time < self.min_delay:
            return False
        
        return True
    
    def wait_if_needed(self):
        """Wait if necessary before making a request."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_delay:
            wait_time = self.min_delay - time_since_last
            logger.debug(f"Waiting {wait_time:.2f}s for GitHub rate limit")
            time.sleep(wait_time)
    
    def update_rate_limit_info(self, response_headers: Dict[str, str]):
        """Update rate limit information from response headers."""
        try:
            self.rate_limit_remaining = int(response_headers.get('X-RateLimit-Remaining', 5000))
            self.rate_limit_reset = int(response_headers.get('X-RateLimit-Reset', time.time() + 3600))
            self.last_request_time = time.time()
            self.requests_made += 1
            
            logger.debug(f"GitHub rate limit: {self.rate_limit_remaining} remaining, resets at {datetime.fromtimestamp(self.rate_limit_reset)}")
        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to parse GitHub rate limit headers: {e}")
    
    def get_cached_or_fallback_repos(self, query: str = "crypto") -> List[Dict[str, Any]]:
        """Get repositories with caching and fallback."""
        cache_key = f"github_repos_{query}"
        
        # Try cache first
        cached_data = get_cached_data(cache_key)
        if cached_data:
            logger.info(f"✅ Using cached GitHub repos for query: {query}")
            return cached_data
        
        # Try API if rate limit allows
        if self.can_make_request():
            try:
                repos = self._fetch_repos_from_api(query)
                if repos:
                    # Cache successful results
                    set_cached_data(cache_key, repos, ttl=self.cache_duration)
                    logger.info(f"✅ Fetched and cached {len(repos)} GitHub repos")
                    return repos
            except Exception as e:
                logger.warning(f"GitHub API request failed: {e}")
        
        # Use fallback data
        logger.info(f"🔄 Using fallback GitHub data for query: {query}")
        return self.fallback_repos
    
    def _fetch_repos_from_api(self, query: str) -> List[Dict[str, Any]]:
        """Fetch repositories from GitHub API."""
        import requests
        
        self.wait_if_needed()
        
        url = "https://api.github.com/search/repositories"
        headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "AlphaPredator-Bot/1.0"
        }
        
        if GITHUB_OAUTH_TOKEN:
            headers["Authorization"] = f"token {GITHUB_OAUTH_TOKEN}"
        
        params = {
            "q": f"{query} language:python OR language:javascript OR language:rust OR language:go",
            "sort": "stars",
            "order": "desc",
            "per_page": 30
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        # Update rate limit info
        self.update_rate_limit_info(dict(response.headers))
        
        if response.status_code == 200:
            data = response.json()
            repos = []
            
            for item in data.get("items", []):
                repos.append({
                    "name": item.get("name", ""),
                    "full_name": item.get("full_name", ""),
                    "description": item.get("description", ""),
                    "stars": item.get("stargazers_count", 0),
                    "language": item.get("language", ""),
                    "url": item.get("html_url", ""),
                    "created_at": item.get("created_at", ""),
                    "updated_at": item.get("updated_at", "")
                })
            
            return repos
        
        elif response.status_code == 403:
            logger.warning("GitHub API rate limit exceeded")
            raise Exception("Rate limit exceeded")
        
        else:
            logger.warning(f"GitHub API returned status {response.status_code}")
            raise Exception(f"API error: {response.status_code}")
    
    def get_sentiment_from_repos(self, token: str) -> float:
        """Get sentiment score based on GitHub repository activity."""
        try:
            repos = self.get_cached_or_fallback_repos(token)
            
            if not repos:
                return 0.5  # Neutral
            
            # Calculate sentiment based on repository metrics
            total_score = 0
            for repo in repos[:5]:  # Use top 5 repos
                stars = repo.get("stars", 0)
                
                # Convert stars to sentiment score
                if stars > 10000:
                    score = 0.8
                elif stars > 5000:
                    score = 0.7
                elif stars > 1000:
                    score = 0.6
                elif stars > 100:
                    score = 0.5
                else:
                    score = 0.4
                
                total_score += score
            
            avg_score = total_score / min(len(repos), 5)
            return max(0.1, min(0.9, avg_score))
            
        except Exception as e:
            logger.warning(f"GitHub sentiment analysis failed for {token}: {e}")
            return 0.5  # Neutral fallback


# Global instance
github_limiter = GitHubRateLimiter()

def get_github_repos_safe(query: str = "crypto") -> List[Dict[str, Any]]:
    """Safely get GitHub repositories with rate limiting."""
    return github_limiter.get_cached_or_fallback_repos(query)

def get_github_sentiment_safe(token: str) -> float:
    """Safely get GitHub-based sentiment score."""
    return github_limiter.get_sentiment_from_repos(token)

if __name__ == "__main__":
    # Test the rate limiter
    print("🚫 TESTING GITHUB RATE LIMITER")
    print("=" * 40)
    
    test_queries = ["bitcoin", "ethereum", "solana"]
    
    for query in test_queries:
        repos = get_github_repos_safe(query)
        sentiment = get_github_sentiment_safe(query)
        print(f"{query:10s}: {len(repos)} repos, sentiment: {sentiment:.3f}")
    
    print("\n✅ GitHub rate limiter working correctly!")
