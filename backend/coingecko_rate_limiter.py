"""
CoinGecko Rate Limiter
Implements intelligent rate limiting and backoff strategies for CoinGecko API calls
"""

import time
import logging
from typing import Dict, Optional
from datetime import datetime, timedelta
import asyncio
from threading import Lock

logger = logging.getLogger(__name__)


class CoinGeckoRateLimiter:
    def __init__(self):
        self.last_request_time = 0
        self.request_count = 0
        self.reset_time = time.time() + 60  # Reset every minute
        self.lock = Lock()

        # 🔧 FIX: Updated rate limits for CoinGecko API (2024)
        self.rate_limits = {
            "free": {
                "requests_per_minute": 30,
                "delay_between_requests": 2.1,
            },  # 30 calls/min for free
            "pro": {
                "requests_per_minute": 500,
                "delay_between_requests": 0.12,
            },  # 500 calls/min for pro
            "demo": {
                "requests_per_minute": 50,
                "delay_between_requests": 1.3,
            },  # 50 calls/min for demo
            "default": {"requests_per_minute": 30, "delay_between_requests": 2.1},
        }

        # Detect API tier based on environment
        import os

        self.api_key = os.getenv("COINGECKO_API_KEY")

        # 🔧 FIX: Determine tier based on API key format
        if not self.api_key:
            self.tier = "free"
        elif self.api_key.startswith("CG-"):
            self.tier = "pro"  # Pro API keys start with CG-
        else:
            self.tier = "demo"  # Demo keys are usually shorter

        self.config = self.rate_limits.get(self.tier, self.rate_limits["default"])

        logger.info(f"CoinGecko Rate Limiter initialized for {self.tier} tier")

    def wait_if_needed(self):
        """Wait if necessary to respect rate limits"""
        with self.lock:
            current_time = time.time()

            # Reset counter if minute has passed
            if current_time >= self.reset_time:
                self.request_count = 0
                self.reset_time = current_time + 60

            # Check if we've hit the rate limit
            if self.request_count >= self.config["requests_per_minute"]:
                wait_time = self.reset_time - current_time
                if wait_time > 0:
                    logger.warning(
                        f"Rate limit reached, waiting {wait_time:.2f} seconds"
                    )
                    time.sleep(wait_time)
                    self.request_count = 0
                    self.reset_time = time.time() + 60

            # Ensure minimum delay between requests
            time_since_last = current_time - self.last_request_time
            min_delay = self.config["delay_between_requests"]

            if time_since_last < min_delay:
                sleep_time = min_delay - time_since_last
                logger.debug(f"Waiting {sleep_time:.2f}s between requests")
                time.sleep(sleep_time)

            self.last_request_time = time.time()
            self.request_count += 1

    def get_backoff_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay"""
        base_delay = 2.0
        max_delay = 300.0  # 5 minutes max
        delay = min(base_delay * (2**attempt), max_delay)

        # Add jitter to prevent thundering herd
        import random

        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter


# Global rate limiter instance
rate_limiter = CoinGeckoRateLimiter()


def apply_rate_limit():
    """Decorator to apply rate limiting to CoinGecko API calls"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            rate_limiter.wait_if_needed()
            return func(*args, **kwargs)

        return wrapper

    return decorator


async def async_apply_rate_limit():
    """Async version of rate limiting"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Convert sync wait to async
            await asyncio.get_event_loop().run_in_executor(
                None, rate_limiter.wait_if_needed
            )
            return await func(*args, **kwargs)

        return wrapper

    return decorator
