#!/usr/bin/env python3
"""
🚀 AI CLIENTS SDK MIGRATION
Replace custom HTTP clients with official AI provider SDKs
"""

import os
import logging
import json
from typing import Dict, Any

# Official SDK imports with fallback stubs
try:
    from openai import OpenAI  # type: ignore

    OPENAI_SDK_AVAILABLE = True
except ImportError:
    OPENAI_SDK_AVAILABLE = False
    OpenAI = None  # type: ignore

    class OpenAI:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        @property
        def chat(self):
            return self._ChatStub()

        class _ChatStub:
            @property
            def completions(self):
                return self._CompletionsStub()

            class _CompletionsStub:
                def create(self, **kwargs):
                    return self._ResponseStub()

                class _ResponseStub:
                    @property
                    def choices(self):
                        return [self._ChoiceStub()]

                    class _ChoiceStub:
                        @property
                        def message(self):
                            return self._MessageStub()

                        class _MessageStub:
                            @property
                            def content(self):
                                return '{"decision": "HOLD", "confidence": 50, "reason": "SDK not available"}'


try:
    import anthropic  # type: ignore

    Anthropic = anthropic.Anthropic
    ANTHROPIC_SDK_AVAILABLE = True
except ImportError:
    ANTHROPIC_SDK_AVAILABLE = False
    anthropic = None  # type: ignore
    Anthropic = None  # type: ignore

    class Anthropic:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        @property
        def messages(self):
            return self._MessagesStub()

        class _MessagesStub:
            def create(self, **kwargs):
                return self._ResponseStub()

            class _ResponseStub:
                @property
                def content(self):
                    return [self._ContentStub()]

                class _ContentStub:
                    @property
                    def text(self):
                        return '{"decision": "HOLD", "confidence": 50, "reason": "SDK not available"}'


try:
    import google.generativeai as genai  # type: ignore

    GEMINI_SDK_AVAILABLE = True
except ImportError:
    GEMINI_SDK_AVAILABLE = False
    genai = None  # type: ignore

    class _GeminiStub:  # type: ignore
        def configure(self, **kwargs):
            pass

        def GenerativeModel(self, model_name: str):
            return self._ModelStub()

        @property
        def types(self):
            return self._TypesStub()

        class _ModelStub:
            def generate_content(self, *args, **kwargs):
                return self._ResponseStub()

            class _ResponseStub:
                @property
                def text(self):
                    return '{"decision": "HOLD", "confidence": 50, "reason": "SDK not available"}'

        class _TypesStub:
            def GenerationConfig(self, **kwargs):
                return {}

    genai = _GeminiStub()

from config import OPENAI_API_KEY, GEMINI_API_KEY, TOGETHER_API_KEY

# Get Anthropic API key from environment (not in config.py)
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")

logger = logging.getLogger(__name__)


class EnhancedOpenAIClient:
    """Enhanced OpenAI client using official SDK (already implemented)"""

    def __init__(self):
        self.api_key = OPENAI_API_KEY
        if self.api_key and not self.api_key.startswith("YOUR_") and OpenAI is not None:
            self.client = OpenAI(api_key=self.api_key)
            self.available = True
        else:
            self.client = None
            self.available = False

    def call_openai(self, prompt: str, model: str = "gpt-4o-mini") -> Dict[str, Any]:
        """Call OpenAI using official SDK (already working)"""
        if not self.available or self.client is None:
            return self._get_fallback_response("OpenAI SDK not available")

        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": 'You are an expert cryptocurrency trading AI. Respond with JSON: {"decision": "BUY|SELL|HOLD", "confidence": 0-100, "reason": "explanation"}',
                    },
                    {"role": "user", "content": prompt},
                ],
                max_tokens=200,
                temperature=0.3,
            )

            content = response.choices[0].message.content

            try:
                parsed = json.loads(content)
                return parsed
            except json.JSONDecodeError:
                return self._extract_decision_from_text(content)

        except Exception as e:
            logger.error(f"❌ OpenAI SDK error: {e}")
            return self._get_fallback_response(str(e))

    def _extract_decision_from_text(self, content: str) -> Dict[str, Any]:
        """Extract decision from non-JSON response"""
        content_upper = content.upper()
        if "BUY" in content_upper:
            decision = "BUY"
        elif "SELL" in content_upper:
            decision = "SELL"
        else:
            decision = "HOLD"

        return {
            "decision": decision,
            "confidence": 50.0,
            "reason": "Extracted from text response",
            "source": "openai_sdk",
        }

    def _get_fallback_response(self, error: str) -> Dict[str, Any]:
        """Fallback response when SDK fails"""
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": f"OpenAI SDK fallback: {error}",
            "source": "openai_fallback",
        }


class EnhancedAnthropicClient:
    """Enhanced Anthropic client using official SDK"""

    def __init__(self):
        self.api_key = ANTHROPIC_API_KEY
        if self.api_key and not self.api_key.startswith("YOUR_") and Anthropic:
            try:
                self.client = Anthropic(api_key=self.api_key)
                self.available = True
                logger.info("✅ Anthropic SDK client initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Anthropic SDK: {e}")
                self.client = None
                self.available = False
        else:
            self.client = None
            self.available = False

    def call_claude(
        self, prompt: str, model: str = "claude-3-5-haiku-20241022"
    ) -> Dict[str, Any]:
        """Call Claude using official SDK"""
        if not self.available or self.client is None:
            return self._get_fallback_response("Anthropic SDK not available")

        try:
            response = self.client.messages.create(
                model=model,
                max_tokens=200,
                temperature=0.3,
                system='You are an expert cryptocurrency trading AI. Respond with JSON: {"decision": "BUY|SELL|HOLD", "confidence": 0-100, "reason": "explanation"}',
                messages=[{"role": "user", "content": prompt}],
            )

            content = response.content[0].text

            try:
                parsed = json.loads(content)
                parsed["source"] = "anthropic_sdk"
                return parsed
            except json.JSONDecodeError:
                return self._extract_decision_from_text(content)

        except Exception as e:
            logger.error(f"❌ Anthropic SDK error: {e}")
            return self._get_fallback_response(str(e))

    def _extract_decision_from_text(self, content: str) -> Dict[str, Any]:
        """Extract decision from non-JSON response"""
        content_lower = content.lower()
        if "buy" in content_lower and "sell" not in content_lower:
            decision = "BUY"
            confidence = 70.0
        elif "sell" in content_lower and "buy" not in content_lower:
            decision = "SELL"
            confidence = 70.0
        else:
            decision = "HOLD"
            confidence = 50.0

        return {
            "decision": decision,
            "confidence": confidence,
            "reason": content[:200],
            "source": "anthropic_sdk",
        }

    def _get_fallback_response(self, error: str) -> Dict[str, Any]:
        """Fallback response when SDK fails"""
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": f"Anthropic SDK fallback: {error}",
            "source": "anthropic_fallback",
        }


class EnhancedGeminiClient:
    """Enhanced Gemini client using official SDK"""

    def __init__(self):
        self.api_key = GEMINI_API_KEY
        if self.api_key and not self.api_key.startswith("YOUR_") and genai:
            try:
                genai.configure(api_key=self.api_key)
                self.client = genai.GenerativeModel("gemini-1.5-flash")
                self.available = True
                logger.info("✅ Gemini SDK client initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini SDK: {e}")
                self.client = None
                self.available = False
        else:
            self.client = None
            self.available = False

    def call_gemini(self, prompt: str) -> Dict[str, Any]:
        """Call Gemini using official SDK"""
        if not self.available or self.client is None or genai is None:
            return self._get_fallback_response("Gemini SDK not available")

        try:
            full_prompt = f"""Crypto trading analysis. Respond ONLY with JSON:
{{"decision": "BUY|SELL|HOLD", "confidence": 0-100, "reason": "brief explanation"}}

Data: {prompt}"""

            response = self.client.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=100,
                    temperature=0.3,
                ),
            )

            content = response.text

            try:
                parsed = json.loads(content)
                parsed["source"] = "gemini_sdk"
                return parsed
            except json.JSONDecodeError:
                return self._extract_decision_from_text(content)

        except Exception as e:
            logger.error(f"❌ Gemini SDK error: {e}")
            return self._get_fallback_response(str(e))

    def _extract_decision_from_text(self, content: str) -> Dict[str, Any]:
        """Extract decision from non-JSON response"""
        content_upper = content.upper()
        if "BUY" in content_upper and "SELL" not in content_upper:
            decision = "BUY"
        elif "SELL" in content_upper and "BUY" not in content_upper:
            decision = "SELL"
        else:
            decision = "HOLD"

        return {
            "decision": decision,
            "confidence": 60.0,
            "reason": content[:100],
            "source": "gemini_sdk",
        }

    def _get_fallback_response(self, error: str) -> Dict[str, Any]:
        """Fallback response when SDK fails"""
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": f"Gemini SDK fallback: {error}",
            "source": "gemini_fallback",
        }


class EnhancedDeepSeekClient:
    """Enhanced DeepSeek client (via Together API - no official SDK)"""

    def __init__(self):
        self.api_key = TOGETHER_API_KEY
        self.available = bool(self.api_key and not self.api_key.startswith("YOUR_"))

    def call_deepseek(self, prompt: str) -> Dict[str, Any]:
        """Call DeepSeek via Together API (keep current implementation)"""
        if not self.available:
            return self._get_fallback_response("DeepSeek API not available")

        try:
            import requests

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            data = {
                "model": "deepseek-ai/deepseek-v3",
                "messages": [
                    {
                        "role": "system",
                        "content": 'You are an expert cryptocurrency trading AI. Respond with JSON: {"decision": "BUY|SELL|HOLD", "confidence": 0-100, "reason": "explanation"}',
                    },
                    {"role": "user", "content": prompt},
                ],
                "max_tokens": 200,
                "temperature": 0.3,
            }

            response = requests.post(
                "https://api.together.xyz/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30,
            )

            response.raise_for_status()
            result = response.json()

            content = result["choices"][0]["message"]["content"]

            try:
                parsed = json.loads(content)
                parsed["source"] = "deepseek_together"
                return parsed
            except json.JSONDecodeError:
                return self._extract_decision_from_text(content)

        except Exception as e:
            logger.error(f"❌ DeepSeek API error: {e}")
            return self._get_fallback_response(str(e))

    def _extract_decision_from_text(self, content: str) -> Dict[str, Any]:
        """Extract decision from non-JSON response"""
        content_upper = content.upper()
        if "BUY" in content_upper:
            decision = "BUY"
        elif "SELL" in content_upper:
            decision = "SELL"
        else:
            decision = "HOLD"

        return {
            "decision": decision,
            "confidence": 55.0,
            "reason": content[:150],
            "source": "deepseek_together",
        }

    def _get_fallback_response(self, error: str) -> Dict[str, Any]:
        """Fallback response when SDK fails"""
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": f"DeepSeek fallback: {error}",
            "source": "deepseek_fallback",
        }


# Create singleton instances
openai_client = EnhancedOpenAIClient()
anthropic_client = EnhancedAnthropicClient()
gemini_client = EnhancedGeminiClient()
deepseek_client = EnhancedDeepSeekClient()


# Compatibility functions for existing code
def call_openai(prompt: str, **_kwargs) -> Dict[str, Any]:
    """Compatibility function"""
    return openai_client.call_openai(prompt)


def call_claude(prompt: str, **_kwargs) -> Dict[str, Any]:
    """Compatibility function"""
    return anthropic_client.call_claude(prompt)


def call_gemini(prompt: str, **_kwargs) -> Dict[str, Any]:
    """Compatibility function"""
    return gemini_client.call_gemini(prompt)


def call_deepseek(prompt: str, **_kwargs) -> Dict[str, Any]:
    """Compatibility function"""
    return deepseek_client.call_deepseek(prompt)


def get_ai_clients_status() -> Dict[str, Any]:
    """Get status of all AI clients"""
    return {
        "openai": {
            "available": openai_client.available,
            "sdk": "official",
            "status": "✅" if openai_client.available else "❌",
        },
        "anthropic": {
            "available": anthropic_client.available,
            "sdk": "official",
            "status": "✅" if anthropic_client.available else "❌",
        },
        "gemini": {
            "available": gemini_client.available,
            "sdk": "official",
            "status": "✅" if gemini_client.available else "❌",
        },
        "deepseek": {
            "available": deepseek_client.available,
            "sdk": "together_api",
            "status": "✅" if deepseek_client.available else "❌",
        },
    }


if __name__ == "__main__":
    # Test all AI clients
    print("🧪 Testing AI Clients SDK Migration")
    print("=" * 40)

    test_prompt = "BTC price is $45000, volume increasing, RSI at 65. Should I buy?"

    # Test OpenAI
    if openai_client.available:
        result = call_openai(test_prompt)
        print(f"✅ OpenAI: {result['decision']} ({result['confidence']}%)")
    else:
        print("❌ OpenAI: Not available")

    # Test Anthropic
    if anthropic_client.available:
        result = call_claude(test_prompt)
        print(f"✅ Anthropic: {result['decision']} ({result['confidence']}%)")
    else:
        print("❌ Anthropic: Not available")

    # Test Gemini
    if gemini_client.available:
        result = call_gemini(test_prompt)
        print(f"✅ Gemini: {result['decision']} ({result['confidence']}%)")
    else:
        print("❌ Gemini: Not available")

    # Test DeepSeek
    if deepseek_client.available:
        result = call_deepseek(test_prompt)
        print(f"✅ DeepSeek: {result['decision']} ({result['confidence']}%)")
    else:
        print("❌ DeepSeek: Not available")

    # Show status
    status = get_ai_clients_status()
    print(f"\n📊 AI Clients Status:")
    for client, info in status.items():
        print(f"  {info['status']} {client.upper()}: {info['sdk']} SDK")
