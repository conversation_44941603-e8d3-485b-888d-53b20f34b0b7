# === 📡 TELEGRAM & DISCORD ===
DISCORD_BOT_TOKEN=your_discord_bot_token_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHANNEL_ID=your_telegram_channel_id_here
KRYPTONEWS_CHANNEL_ID=your_kryptonews_channel_id_here
WEBHOOK_URL=https://yourdomain.com/webhook

RSS_FEEDS=https://cointelegraph.com/rss,https://cryptopotato.com/feed/,https://cryptopanic.com/news/rss/,https://coinmarketcap.com/headlines/news/feed/

# === 💹 KUCOIN API SETTINGS ===
KUCOIN_API_KEY=your_kucoin_api_key_here
KUCOIN_API_SECRET=your_kucoin_api_secret_here
KUCOIN_API_PASSPHRASE=your_kucoin_passphrase_here
KUCOIN_BASE_URL=https://api.kucoin.com
COINDESK_API_KEY=your_coindesk_api_key_here
COINMARKETCAL_API_KEY=your_coinmarketcal_api_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here

# === 🤖 AI API KEYS ===
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
TOGETHER_API_KEY=your_together_api_key_here

# === 🔐 LOGIN AUTH (Google OAuth) ===
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/callback

# === 🔐 FIREBASE SERVICE ACCOUNT & GOOGLE AUTH ===
FIRESTORE_CREDENTIALS_PATH=./config/firebase-service-account.json
FIREBASE_PROJECT_ID=your_firebase_project_id_here
FIREBASE_CLIENT_ID=your_firebase_client_id_here
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id_here
GOOGLE_APPLICATION_CREDENTIALS=./config/firebase-service-account.json

# === 🔐 GOOGLE AUTHORIZED USERS ===
# Comma-separated list of emails allowed to log in via Google OAuth
ALLOWED_EMAILS=<EMAIL>
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_admin_password_here

# === ⚙️ TRADING LOGIC ===
TRADE_INTERVAL_SECONDS=60
MAX_BUY=100
STOP_LOSS=0.10
TAKE_PROFIT=0.20
MIN_24H_VOLUME_USD=1000000
TRADING_MODE=PAPER

# === 📊 ANALYTICS & STRATEGY SETTINGS ===
USE_REAL_NEWS=true
FALLBACK_TO_OPENAI=true

# === 🔧 DEV TOOLS (Optional) ===
GITHUB_OAUTH_TOKEN=your_github_token_here

# === 🌐 SERVER SETTINGS ===
PORT=3005
BACKEND_BASE_URL=https://yourdomain.com

# === 🔑 JWT signing secret (MUST be 32+ character random string in production) ===
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_minimum_32_characters_long

# === 🔧 ALGORITHM SETTINGS ===
JWT_ALGORITHM=HS256

# === 🚨 SPAM PROTECTION ===
SPAM_WINDOW_SECONDS=60
SPAM_MAX_MESSAGES=5

# === 🏠 LOCAL DEVELOPMENT ===
LOCAL_PORT=8000
BACKEND_BASE_URL_LOCAL=http://localhost:8000
