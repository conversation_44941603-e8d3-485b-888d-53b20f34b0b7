import logging
from typing import Dict, Any, List, Optional
from ai_clients.ai_request_manager import get_ai_decision

# Strategy engine import with fallback
try:
    from ai_clients.strategy_engine import decide_strategy  # type: ignore
except ImportError:
    # Fallback: define a dummy decide_strategy if import fails
    def decide_strategy(*_args, **_kwargs):  # type: ignore
        return "HOLD"


logger = logging.getLogger(__name__)


def get_final_ai_decision(
    prompt: str,
    events: Optional[List[Dict]] = None,
    target_symbol: Optional[str] = None,
    **kwargs,
) -> Dict[str, Any]:
    """
    Get final AI decision with robust error handling and fallbacks.

    Args:
        prompt: Trading prompt for AI analysis
        events: Market events (can be empty)
        target_symbol: Token symbol being analyzed
        **kwargs: Additional parameters (ignored for compatibility)

    Returns:
        Dict: Contains decision, confidence, and reason
    """
    if events is None:
        events = []
    if target_symbol is None:
        target_symbol = kwargs.get("token", "UNKNOWN")

    try:
        # Use the imported get_ai_decision function
        logger.info(f"Getting AI decision for {target_symbol or 'UNKNOWN'}")
        ai_result = get_ai_decision(prompt)

        if ai_result and isinstance(ai_result, dict):
            response = ai_result.get("response", {})
            if response:
                return {
                    "decision": response.get("decision", "HOLD"),
                    "confidence": response.get("confidence", 0.5),
                    "reason": response.get("reason", "AI decision"),
                }

        # Fallback if AI decision fails
        logger.info(f"Using strategy AI fallback for {target_symbol or 'UNKNOWN'}")
        # Provide default parameters to avoid signature mismatch
        strategy_decision = decide_strategy(
            token=target_symbol,
            prices=[1.0, 1.01, 0.99, 1.02, 1.0],  # Default price data
            high_prices=[1.05, 1.06, 1.04, 1.07, 1.05],
            low_prices=[0.95, 0.94, 0.96, 0.93, 0.95],
            volumes=[1000, 1100, 900, 1200, 1000],
            sentiment_score=0.5,  # Neutral sentiment
            news_keywords=[],
            events=[],  # Empty events list
            target_symbol=target_symbol,  # Required parameter
            current_price=1.0,  # Default current price
        )

        if strategy_decision and isinstance(strategy_decision, str):
            # Extract decision from strategy response
            if "BUY" in strategy_decision.upper():
                return {
                    "decision": "BUY",
                    "confidence": 0.6,
                    "reason": "Strategy AI recommendation",
                }
            elif "SELL" in strategy_decision.upper():
                return {
                    "decision": "SELL",
                    "confidence": 0.6,
                    "reason": "Strategy AI recommendation",
                }
            else:
                return {
                    "decision": "HOLD",
                    "confidence": 0.5,
                    "reason": "Strategy AI neutral recommendation",
                }

    except Exception as e:
        logger.warning(f"Strategy AI error for {target_symbol}: {e}")

    # Ultimate fallback
    return {
        "decision": "HOLD",
        "confidence": 0.0,
        "reason": "Fallback decision due to errors",
    }


def validate_ai_response(response: Any) -> bool:
    """Validate AI response format"""
    if not response:
        return False

    if isinstance(response, dict):
        decision = response.get("decision", "").upper()
        return decision in ["BUY", "SELL", "HOLD"]

    if isinstance(response, str):
        return response.upper() in ["BUY", "SELL", "HOLD"]

    return False
