import logging
from datetime import datetime
from typing import List, Dict, Any
import pandas as pd
import json
import os

from error_codes import error_response

# Assuming these modules exist and can be imported
from token_selector import get_top_tokens
from prompt_builder import build_trade_prompt
from ai_validation_engine import get_final_ai_decision
from trade_engine import trade_token
from indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
    calculate_breakout_level,
)

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class BacktestEngine:
    def __init__(self, initial_capital: float = 10000.0, trading_mode: str = "PAPER"):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.portfolio = {}
        self.trades = []
        self.trading_mode = trading_mode
        self.price_history: Dict[str, List[float]] = {}
        self.equity_curve = [] # To store capital at each step
        self.trade_log_details = [] # Detailed log for export
        logger.info(f"Backtest Engine initialized with capital: {self.initial_capital}")

    def _execute_trade(self, symbol: str, action: str, price: float, amount: float) -> Dict[str, Any]:
        """
        Simulates a trade execution.
        """
        trade_info = {
            "timestamp": datetime.now().isoformat(),
            "symbol": symbol,
            "action": action,
            "price": price,
            "amount": amount,
            "status": "failed"
        }

        if action == "BUY":
            cost = price * amount
            if self.current_capital >= cost:
                self.current_capital -= cost
                self.portfolio[symbol] = self.portfolio.get(symbol, 0) + amount
                trade_info.update({"status": "executed", "cost": cost})
                logger.info(f"SIMULATED BUY: {amount:.4f} of {symbol} at {price:.6f}. Remaining capital: {self.current_capital:.2f}")
            else:
                logger.warning(f"SIMULATED BUY FAILED: Insufficient capital for {symbol}")
                return error_response("INSUFFICIENT_CAPITAL", f"Insufficient capital for {symbol}", 400)
        elif action == "SELL":
            if self.portfolio.get(symbol, 0) >= amount:
                revenue = price * amount
                self.current_capital += revenue
                self.portfolio[symbol] -= amount
                trade_info.update({"status": "executed", "revenue": revenue})
                logger.info(f"SIMULATED SELL: {amount:.4f} of {symbol} at {price:.6f}. Current capital: {self.current_capital:.2f}")
            else:
                logger.warning(f"SIMULATED SELL FAILED: Insufficient {symbol} in portfolio")
                return error_response("INSUFFICIENT_ASSETS", f"Insufficient {symbol} in portfolio", 400)
        elif action == "HOLD":
            trade_info["status"] = "executed"
            trade_info["reason"] = "Hold decision"
            logger.info(f"SIMULATED HOLD: {symbol}")
        else:
            logger.warning(f"SIMULATED TRADE FAILED: Unknown action {action}")
            return error_response("UNKNOWN_TRADE_ACTION", f"Unknown action {action}", 400)
        
        self.trades.append(trade_info)
        self.trade_log_details.append(trade_info) # Add to detailed log
        return trade_info

    def run_backtest(self, historical_data: List[Dict[str, Any]]):
        """
        Runs a backtest simulation over historical data.
        historical_data: List of dictionaries, each representing a time step
                         and containing relevant market data (e.g., 'timestamp', 'tokens').
                         'tokens' should be a list of token_info dicts with 'symbol' and 'price'.
        """
        logger.info("Starting backtest simulation...")
        for i, data_point in enumerate(historical_data):
            timestamp = data_point.get('timestamp', f"Time Step {i}")
            logger.info(f"\n--- Simulating {timestamp} ---")

            current_tokens_data = data_point.get('tokens', [])
            if not current_tokens_data:
                logger.warning(f"No tokens for {timestamp}. Skipping.")
                self.equity_curve.append(self.current_capital) # Append current capital even if no trades
                continue

            for token_info in current_tokens_data:
                symbol = token_info.get('symbol')
                price = token_info.get('price')
                if not symbol or price is None:
                    logger.warning(f"Invalid token_info for {timestamp}: {token_info}. Skipping.")
                    continue

                # Update price history
                if symbol not in self.price_history:
                    self.price_history[symbol] = []
                self.price_history[symbol].append(price)

                # --- Calculate Indicators ---
                current_prices = self.price_history[symbol]
                
                # SMA (e.g., 10-period and 20-period)
                sma_10 = calculate_sma(current_prices, 10)
                sma_20 = calculate_sma(current_prices, 20)

                # EMA (e.g., 12-period and 26-period for MACD)
                ema_12 = calculate_ema(current_prices, 12)
                ema_26 = calculate_ema(current_prices, 26)

                # RSI (e.g., 14-period)
                rsi_val = calculate_rsi(current_prices, 14)

                # MACD
                macd_line, macd_signal_line = calculate_macd(current_prices)

                # Bollinger Bands
                middle_band, upper_band, lower_band = calculate_bollinger_bands(current_prices)

                # --- Build Prompt with Indicators ---
                prompt = build_trade_prompt(
                    token=symbol,
                    price=price,
                    # Pass calculated indicators
                    short_ma=sma_10, # Using SMA for short MA example
                    long_ma=sma_20,  # Using SMA for long MA example
                    rsi=rsi_val,
                    macd=macd_line,
                    macd_signal=macd_signal_line,
                    upper_band=upper_band,
                    lower_band=lower_band,
                    # For breakout_level, you'd need to define how it's determined historically
                    # For now, leaving it as None or a placeholder
                    breakout_level=calculate_breakout_level(current_prices), 
                    # Other parameters like news_data, price_change_24h, news_snippets would also come from historical_data
                    # For this basic setup, they are omitted or assumed to be handled elsewhere
                )

                # Simulate AI decision
                # Prepare required arguments for get_final_ai_decision
                events = data_point.get('events', [])  # or [] if not present
                target_symbol = symbol
                decision = get_final_ai_decision(prompt, events, target_symbol)

                logger.info(f"AI Decision for {symbol} at {timestamp}: {decision}")

                # Extract action string from decision (handle both dict and str cases)
                if isinstance(decision, dict):
                    action = decision.get("action", "HOLD")
                else:
                    action = decision

                # Simulate trade execution
                # Calculate position size based on risk parameters
                # Assuming a fixed stop-loss percentage for simplicity in backtesting
                from config import MAX_TRADE_RISK_PCT, STOP_LOSS_PCT

                trade_usd_amount = 0.0
                if action == "BUY":
                    # Risk per trade = current_capital * MAX_TRADE_RISK_PCT
                    # Assuming stop_loss_price = current_price * (1 - STOP_LOSS_PCT)
                    # Amount to risk = current_capital * MAX_TRADE_RISK_PCT
                    # Price difference to stop loss = current_price - stop_loss_price = current_price * STOP_LOSS_PCT
                    # Quantity = Amount to risk / (Price difference to stop loss)
                    # Trade USD Amount = Quantity * current_price
                    
                    # Simplified: Risk a percentage of capital per trade
                    risk_amount = self.current_capital * MAX_TRADE_RISK_PCT
                    
                    # If we assume a fixed stop-loss percentage, then the position size
                    # can be calculated as: Position_Size = Risk_Amount / Stop_Loss_Percentage
                    # However, this is for a fixed dollar risk. If we want to risk a % of capital
                    # and have a % stop loss, the calculation is simpler:
                    # Trade_USD_Amount = (Current_Capital * MAX_TRADE_RISK_PCT) / STOP_LOSS_PCT
                    
                    # For simplicity in backtesting, let's use a fixed trade amount for now
                    # and assume MAX_TRADE_RISK_PCT is a conceptual limit.
                    # A more advanced backtest would simulate stop-loss triggers.
                    trade_usd_amount = 100.0 # Fixed $100 trade for now

                elif action == "SELL":
                    # For sell decisions, we assume we are selling existing holdings
                    # The amount would be the quantity held for that symbol
                    trade_usd_amount = self.portfolio.get(symbol, 0) * price

                if trade_usd_amount > 0 and price > 0:
                    amount_to_trade = trade_usd_amount / price
                    self._execute_trade(symbol, action, price, amount_to_trade)
                else:
                    logger.warning(f"Skipping trade for {symbol}: Invalid trade amount or price.")
            
            # Record equity at the end of each time step
            self.equity_curve.append(self.current_capital)

        logger.info("Backtest simulation finished.")
        self.summarize_results()
        self.export_results()

    def summarize_results(self):
        """
        Prints a summary of the backtest results and calculates key metrics.
        """
        final_capital = self.current_capital
        # Add value of assets in portfolio at the end
        for symbol, amount in self.portfolio.items():
            # This is a simplification: in a real backtest, you'd need the closing price
            # of the asset at the end of the backtest period.
            # For now, we'll use the last known price from the historical data for that symbol
            last_price = self.price_history.get(symbol, [0])[-1]
            final_capital += amount * last_price

        profit_loss = final_capital - self.initial_capital
        return_pct = (profit_loss / self.initial_capital) * 100 if self.initial_capital != 0 else 0

        # Calculate Max Drawdown
        if self.equity_curve:
            equity_series = pd.Series(self.equity_curve)
            peak = equity_series.expanding(min_periods=1).max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = drawdown.min() * 100
        else:
            max_drawdown = 0.0

        # Calculate win rate
        winning_trades = 0
        total_trades = 0
        for trade in self.trade_log_details:
            if trade['action'] == 'SELL' and trade['status'] == 'executed':
                total_trades += 1
                if trade.get('revenue', 0) > trade.get('cost', 0): 
                    winning_trades += 1
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

        logger.info("\n--- Backtest Results Summary ---")
        logger.info(f"Initial Capital: ${self.initial_capital:.2f}")
        logger.info(f"Final Capital (incl. assets): ${final_capital:.2f}")
        logger.info(f"Total Profit/Loss: ${profit_loss:.2f}")
        logger.info(f"Return Percentage: {return_pct:.2f}%")
        logger.info(f"Total Trades Executed: {len([t for t in self.trade_log_details if t['status'] == 'executed'])}")
        logger.info(f"Win Rate: {win_rate:.2f}%")
        logger.info(f"Max Drawdown: {max_drawdown:.2f}%")
        logger.info("--------------------------------")

    def export_results(self, filename: str = "backtest_results.json"):
        """
        Exports detailed backtest results to a JSON file.
        """
        # Recalculate summary metrics to ensure they are up-to-date for export
        final_capital = self.current_capital
        for symbol, amount in self.portfolio.items():
            last_price = self.price_history.get(symbol, [0])[-1]
            final_capital += amount * last_price

        profit_loss = final_capital - self.initial_capital
        return_pct = (profit_loss / self.initial_capital) * 100 if self.initial_capital != 0 else 0

        winning_trades = 0
        total_trades = 0
        for trade in self.trade_log_details:
            if trade['action'] == 'SELL' and trade['status'] == 'executed':
                total_trades += 1
                if trade.get('revenue', 0) > trade.get('cost', 0): 
                    winning_trades += 1
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

        max_drawdown = 0.0
        if self.equity_curve:
            equity_series = pd.Series(self.equity_curve)
            peak = equity_series.expanding(min_periods=1).max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = drawdown.min() * 100

        results = {
            "initial_capital": self.initial_capital,
            "final_capital": final_capital,
            "total_profit_loss": profit_loss,
            "return_percentage": return_pct,
            "total_trades": len([t for t in self.trade_log_details if t['status'] == 'executed']),
            "win_rate": win_rate,
            "max_drawdown": max_drawdown,
            "equity_curve": self.equity_curve,
            "trades": self.trade_log_details,
        }
        
        # Ensure the data directory exists
        os.makedirs("backend/data", exist_ok=True)
        filepath = os.path.join("backend/data", filename)
        with open(filepath, "w") as f:
            json.dump(results, f, indent=4)
        logger.info(f"Backtest results exported to {filepath}")

# Example Usage (for testing purposes, not part of the main application flow)
if __name__ == "__main__":
    # Mock historical data (replace with actual data loading)
    mock_historical_data = [
        {
            "timestamp": "2023-01-01 00:00:00",
            "tokens": [
                {"symbol": "BTC-USDT", "price": 16500.0},
                {"symbol": "ETH-USDT", "price": 1200.0},
            ]
        },
        {
            "timestamp": "2023-01-01 01:00:00",
            "tokens": [
                {"symbol": "BTC-USDT", "price": 16600.0},
                {"symbol": "ETH-USDT", "price": 1210.0},
            ]
        },
        {
            "timestamp": "2023-01-01 02:00:00",
            "tokens": [
                {"symbol": "BTC-USDT", "price": 16400.0},
                {"symbol": "ETH-USDT", "price": 1190.0},
            ]
        },
    ]

    # Create a backtest engine instance
    engine = BacktestEngine(initial_capital=10000)

    # Run the backtest
    engine.run_backtest(mock_historical_data)
