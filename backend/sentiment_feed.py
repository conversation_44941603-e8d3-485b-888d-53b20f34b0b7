"""
AlphaPredatorBot — Sentiment Feed API

Serves the latest token-wise sentiment scores stored in news_sentiment.jsonl.
This endpoint powers the real-time frontend dashboard.
"""

import json
import os
import logging
from typing import Union
from fastapi.responses import JSONResponse

SENTIMENT_FILE = "backend/data/news_sentiment.jsonl"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_sentiment_feed(limit: Union[int, str] = 100) -> JSONResponse:
    """
    Load the latest sentiment entries from a JSONL file and return them via API.
    
    Args:
        limit (int or str): Number of latest entries to fetch (default = 100)
        
    Returns:
        JSONResponse: Dictionary with sentiment entries or error message
    """
    try:
        limit = int(limit)
        if not os.path.exists(SENTIMENT_FILE):
            logger.warning("[sentiment_feed] Sentiment file not found.")
            return JSONResponse(content={"feed": [], "error": "Sentiment file not found"}, status_code=404)

        with open(SENTIMENT_FILE, "r", encoding="utf-8") as f:
            lines = f.readlines()[-limit:]  # Get the last `limit` lines
            entries = [json.loads(line.strip()) for line in lines if line.strip()]
            return JSONResponse(content={"feed": entries})

    except ValueError:
        logger.error(f"[sentiment_feed] Invalid limit provided: {limit}")
        return JSONResponse(content={"feed": [], "error": "Invalid limit parameter"}, status_code=400)

    except Exception as e:
        logger.exception(f"[sentiment_feed] Unexpected error: {e}")
        return JSONResponse(content={"feed": [], "error": str(e)}, status_code=500)
