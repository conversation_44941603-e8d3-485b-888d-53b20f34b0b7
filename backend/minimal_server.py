#!/usr/bin/env python3
"""
Minimal FastAPI server for testing
"""

import os
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Suppress optimization reports
os.environ['SUPPRESS_OPTIMIZATION_REPORT'] = '1'

app = FastAPI(title="Alpha Predator Bot API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Alpha Predator Bot API is running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "API server is running"}

@app.post("/api/login")
async def login():
    return {
        "success": True,
        "message": "Login successful",
        "user": {"email": "<EMAIL>", "name": "Test User"},
        "token": "test-jwt-token"
    }

@app.get("/api/tokens")
async def get_tokens():
    return {
        "status": "success",
        "tokens": [
            {"symbol": "BTC-USDT", "price": 95000, "change": 2.5},
            {"symbol": "ETH-USDT", "price": 3500, "change": 1.8},
            {"symbol": "ADA-USDT", "price": 0.45, "change": -0.5}
        ]
    }

@app.get("/api/pnl")
async def get_pnl():
    return {
        "status": "success",
        "data": {
            "total_pnl": 1250.75,
            "daily_pnl": 85.50,
            "total_trades": 45,
            "win_rate": 68.9
        }
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 3005))
    host = os.getenv("HOST", "0.0.0.0")
    
    print(f"🚀 Starting minimal API server on {host}:{port}")
    print(f"📡 Frontend should connect to: http://{host}:{port}")
    print(f"🔗 Test endpoints:")
    print(f"   - http://{host}:{port}/")
    print(f"   - http://{host}:{port}/health")
    print(f"   - http://{host}:{port}/api/login")
    print(f"   - http://{host}:{port}/api/tokens")
    
    uvicorn.run(
        "minimal_server:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
