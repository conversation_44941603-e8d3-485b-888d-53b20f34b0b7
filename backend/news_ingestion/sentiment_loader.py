# backend/news_ingestion/sentiment_loader.py

import os
import json

SENTIMENT_FILE = "backend/data/cmc_news.json"  # or path to your sentiment cache

def load_sentiment_score_for_token(symbol: str) -> float:
    """
    Load the sentiment score for a given token symbol from cached news file.
    Returns a float between -1 and 1.
    """
    try:
        if not os.path.exists(SENTIMENT_FILE):
            # Create empty file if it doesn't exist
            with open(SENTIMENT_FILE, "w") as f:
                json.dump([], f)
            return 0.0
            
        with open(SENTIMENT_FILE, "r") as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError:
                print(f"[ERROR] Invalid JSON format in {SENTIMENT_FILE}")
                return 0.0

        # Handle both list and dict formats
        if isinstance(data, list):
            # Filter entries that mention the symbol
            relevant_entries = []
            for entry in data:
                if isinstance(entry, dict):
                    content = entry.get("content", "")
                    title = entry.get("title", "")
                    if symbol.upper() in content.upper() or symbol.upper() in title.upper():
                        relevant_entries.append(entry)
            
            if not relevant_entries:
                return 0.0
                
            # Average score from relevant entries
            scores = [entry.get("sentiment_score", 0) for entry in relevant_entries if "sentiment_score" in entry]
            if not scores:
                return 0.0
                
        elif isinstance(data, dict):
            # Handle dict format with symbol keys
            entries = data.get(symbol.upper(), [])
            if not entries:
                return 0.0
                
            scores = [entry.get("sentiment_score", 0) for entry in entries if isinstance(entry, dict) and "sentiment_score" in entry]
            if not scores:
                return 0.0
        else:
            return 0.0

        avg_score = sum(scores) / len(scores)
        return max(-1.0, min(1.0, round(avg_score, 2)))

    except Exception as e:
        print(f"[ERROR] Could not load sentiment for {symbol}: {e}")
        return 0.0
