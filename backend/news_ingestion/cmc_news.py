# backend/news_ingestion/cmc_news.py
import os
from utils.api_client import get
import json
from datetime import datetime

def extract_tickers(text):
    tokens = ["BTC", "ETH", "XRP", "DOGE", "SOL", "TON", "TRX", "ADA", "MATIC", "BNB", "LTC"]
    text_upper = text.upper()
    return list({token for token in tokens if token in text_upper})

def fetch_cmc_news(limit=10):
    url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/info"
    headers = {
        "User-Agent": "Mozilla/5.0",
        "Accept": "application/json",
        "X-CMC_PRO_API_KEY": os.getenv("CMC_API_KEY", "")
    }
    try:
        response = get(url, headers=headers, timeout=10, cache_ttl=300) # Cache CMC news for 5 minutes
        if "application/json" not in response.headers.get("Content-Type", ""):
            print("❌ Invalid content-type returned from CMC.")
            return []
        response.raise_for_status()
        raw_news = response.json().get("data", [])
        articles = []

        for item in raw_news[:limit]:
            article = {
                "title": item.get("title", ""),
                "url": item.get("url", ""),
                "published": item.get("createdAt") or datetime.utcnow().isoformat(),
                "source": "CoinMarketCap",
                "content": item.get("body", "") or item.get("title", ""),
                "tickers": extract_tickers(item.get("title", ""))
            }
            articles.append(article)

        return articles

    except Exception as e:
        print(f"❌ Failed to fetch CMC news: {e}")
        return []

def save_news_to_file():
    news = fetch_cmc_news()
    with open("backend/data/cmc_news.json", "w") as f:
        json.dump(news, f, indent=2)
    print(f"✅ Saved {len(news)} CMC news articles.")

if __name__ == "__main__":
    save_news_to_file()
