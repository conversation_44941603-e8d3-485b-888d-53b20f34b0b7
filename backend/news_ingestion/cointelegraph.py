from utils.api_client import get
import json
from datetime import datetime
from bs4 import BeautifulSoup

def extract_tickers(text):
    tokens = ["BTC", "ETH", "XRP", "DOGE", "SOL", "TON", "TRX", "ADA", "MATIC", "BNB", "LTC"]
    text_upper = text.upper()
    return list({token for token in tokens if token in text_upper})

def fetch_cointelegraph_articles(limit=10):
    url = "https://cointelegraph.com/rss"
    response = get(url, cache_ttl=300) # Cache CoinTelegraph articles for 5 minutes
    soup = BeautifulSoup(response.content, features="xml")

    articles = []

    for item in soup.find_all("item")[:limit]:
        title = item.title.text
        link = item.link.text
        pub_date_tag = item.find("pubDate")
        if not pub_date_tag:
            continue
        pub_date = pub_date_tag.text
        try:
            published_date = datetime.strptime(pub_date, "%a, %d %b %Y %H:%M:%S %z").isoformat()
        except ValueError:
            published_date = datetime.utcnow().isoformat()

        article = {
            "title": title,
            "url": link,
            "published": published_date,
            "source": "CoinTelegraph",
            "content": title,
            "tickers": extract_tickers(title)
        }

        articles.append(article)

    return articles

if __name__ == "__main__":
    articles = fetch_cointelegraph_articles()
    with open("backend/data/cointelegraph.json", "w") as f:
        json.dump(articles, f, indent=2)
    print(f"✅ Saved {len(articles)} articles from CoinTelegraph")
