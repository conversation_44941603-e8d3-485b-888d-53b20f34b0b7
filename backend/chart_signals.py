
import asyncio
from chart_analyzer import analyze_token

async def get_chart_signal(token_symbol: str) -> str:
    """
    Get chart analysis signal for a token (async version)
    """
    try:
        # Run the synchronous chart analysis in a thread pool
        loop = asyncio.get_event_loop()
        analysis = await loop.run_in_executor(None, analyze_token, token_symbol)
        
        if not analysis:
            return "NEUTRAL"
            
        # Determine signal based on analysis
        rsi = analysis.get("rsi", 50)
        trend = analysis.get("trend_direction", "SIDEWAYS")
        volume_spike = analysis.get("volume_spike", False)
        
        if trend == "UPTREND" and rsi < 70 and volume_spike:
            return "BUY"
        elif trend == "DOWNTREND" and rsi > 30:
            return "SELL"
        else:
            return "HOLD"
            
    except Exception as e:
        print(f"❌ Chart signal error for {token_symbol}: {e}")
        return "NEUTRAL"

def get_chart_signal_sync(token_symbol: str) -> str:
    """
    Synchronous version for backward compatibility
    """
    try:
        analysis = analyze_token(token_symbol)
        if not analysis:
            return "NEUTRAL"
            
        rsi = analysis.get("rsi", 50)
        trend = analysis.get("trend_direction", "SIDEWAYS")
        volume_spike = analysis.get("volume_spike", False)
        
        if trend == "UPTREND" and rsi < 70 and volume_spike:
            return "BUY"
        elif trend == "DOWNTREND" and rsi > 30:
            return "SELL"
        else:
            return "HOLD"
            
    except Exception as e:
        print(f"❌ Chart signal error for {token_symbol}: {e}")
        return "NEUTRAL"
