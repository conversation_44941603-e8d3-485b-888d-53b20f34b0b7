import asyncio
import logging
import json
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import math

logger = logging.getLogger(__name__)

class ProfitableAICore:
    """
    Advanced AI trading system designed for profitability
    Uses sophisticated analysis combining technical, fundamental, and sentiment data
    """
    
    def __init__(self):
        # Profitability parameters
        self.min_profit_threshold = 0.02  # 2% minimum expected profit
        self.max_risk_per_trade = 0.05    # 5% max risk per trade
        self.confidence_multiplier = 1.5   # Boost confidence for strong signals
        
        # Market condition weights
        self.weights = {
            'technical': 0.40,
            'sentiment': 0.25, 
            'volume': 0.20,
            'momentum': 0.15
        }
    
    async def analyze_profitability(self, symbol: str) -> Dict[str, Any]:
        """
        Comprehensive profitability analysis for a trading symbol
        """
        try:
            logger.info(f"🔍 Starting profitability analysis for {symbol}")
            
            # 1. Get current market data
            market_data = await self._get_market_data(symbol)
            if not market_data:
                return self._create_hold_decision(symbol, "Insufficient market data")
            
            # 2. Technical analysis across multiple timeframes
            technical_score = await self._analyze_technical_indicators(symbol, market_data)
            
            # 3. Sentiment and news analysis
            sentiment_score = await self._analyze_sentiment(symbol)
            
            # 4. Volume and momentum analysis
            volume_score = await self._analyze_volume_momentum(symbol, market_data)
            
            # 5. Calculate composite score
            composite_score = self._calculate_composite_score(
                technical_score, sentiment_score, volume_score
            )
            
            # 6. Generate trading decision
            decision = await self._generate_trading_decision(
                symbol, composite_score, market_data
            )
            
            logger.info(f"✅ Profitability analysis complete for {symbol}: {decision['decision']} ({decision['confidence']:.2f})")
            return decision
            
        except Exception as e:
            logger.error(f"❌ Error in profitability analysis for {symbol}: {e}")
            return self._create_hold_decision(symbol, f"Analysis error: {str(e)}")
    
    async def _get_market_data(self, symbol: str) -> Optional[Dict]:
        """Get comprehensive market data for analysis"""
        try:
            # Try to import price fetcher
            try:
                from price_fetcher import get_prices_batch_coingecko
                prices = get_prices_batch_coingecko([symbol])
                current_price = prices.get(symbol)
            except ImportError:
                logger.warning("price_fetcher not available, using fallback price")
                current_price = 1.0  # Fallback price
            
            if not current_price:
                return None
            
            # Try to get candlestick data
            try:
                from kucoin_data import fetch_kucoin_candlestick_data as get_kucoin_candlesticks
                candles_1h = get_kucoin_candlesticks(symbol, '1hour', 100)
                candles_4h = get_kucoin_candlesticks(symbol, '4hour', 50)
                candles_1d = get_kucoin_candlesticks(symbol, '1day', 20)
            except (ImportError, Exception) as e:
                logger.warning(f"kucoin_data not available or error: {e}, using empty candles")
                candles_1h = []
                candles_4h = []
                candles_1d = []
            
            return {
                'current_price': current_price,
                'candles_1h': candles_1h or [],
                'candles_4h': candles_4h or [], 
                'candles_1d': candles_1d or [],
                'symbol': symbol
            }
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None
    
    async def _analyze_technical_indicators(self, symbol: str, market_data: Dict) -> float:
        """
        Advanced technical analysis with multiple indicators
        Returns score from 0.0 to 1.0
        """
        try:
            candles_1h = market_data.get('candles_1h', [])
            candles_4h = market_data.get('candles_4h', [])
            
            if not candles_1h or not candles_4h:
                return 0.5
            
            scores = []
            
            # 1. RSI Analysis (multiple timeframes)
            rsi_1h = self._calculate_rsi([float(c[4]) for c in candles_1h[-14:]])
            rsi_4h = self._calculate_rsi([float(c[4]) for c in candles_4h[-14:]])
            
            # RSI scoring: oversold = bullish, overbought = bearish
            rsi_score = 0.5
            if rsi_1h < 30 and rsi_4h < 40:  # Strong oversold
                rsi_score = 0.8
            elif rsi_1h < 40:  # Mild oversold
                rsi_score = 0.65
            elif rsi_1h > 70 and rsi_4h > 60:  # Strong overbought
                rsi_score = 0.2
            elif rsi_1h > 60:  # Mild overbought
                rsi_score = 0.35
            
            scores.append(rsi_score)
            
            # 2. Moving Average Convergence
            prices_1h = [float(c[4]) for c in candles_1h]
            ma_20 = np.mean(prices_1h[-20:])
            ma_50 = np.mean(prices_1h[-50:]) if len(prices_1h) >= 50 else ma_20
            
            current_price = market_data['current_price']
            
            # Price above MAs = bullish
            ma_score = 0.5
            if current_price > ma_20 > ma_50:
                ma_score = 0.75
            elif current_price > ma_20:
                ma_score = 0.6
            elif current_price < ma_20 < ma_50:
                ma_score = 0.25
            
            scores.append(ma_score)
            
            # 3. Volume Analysis
            volumes_1h = [float(c[5]) for c in candles_1h[-10:]]
            avg_volume = np.mean(volumes_1h[:-1])
            current_volume = volumes_1h[-1]
            
            volume_score = 0.5
            if current_volume > avg_volume * 2:  # High volume
                volume_score = 0.7
            elif current_volume > avg_volume * 1.5:
                volume_score = 0.6
            elif current_volume < avg_volume * 0.5:  # Low volume
                volume_score = 0.4
            
            scores.append(volume_score)
            
            # 4. Price momentum
            price_change_1h = (prices_1h[-1] - prices_1h[-2]) / prices_1h[-2]
            price_change_4h = (float(candles_4h[-1][4]) - float(candles_4h[-2][4])) / float(candles_4h[-2][4])
            
            momentum_score = 0.5
            if price_change_1h > 0.02 and price_change_4h > 0.01:  # Strong upward momentum
                momentum_score = 0.8
            elif price_change_1h > 0.01:  # Mild upward momentum
                momentum_score = 0.65
            elif price_change_1h < -0.02 and price_change_4h < -0.01:  # Strong downward momentum
                momentum_score = 0.2
            elif price_change_1h < -0.01:  # Mild downward momentum
                momentum_score = 0.35
            
            scores.append(momentum_score)
            
            # Return weighted average
            return float(np.mean(scores))
            
        except Exception as e:
            logger.error(f"Error in technical analysis for {symbol}: {e}")
            return 0.5
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate RSI indicator"""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            gains = [d if d > 0 else 0 for d in deltas]
            losses = [-d if d < 0 else 0 for d in deltas]
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return float(rsi)
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return 50.0
    
    async def _analyze_sentiment(self, symbol: str) -> float:
        """Analyze sentiment for the symbol"""
        try:
            # Simplified sentiment analysis - return neutral for now
            return 0.5
        except Exception as e:
            logger.error(f"Error in sentiment analysis for {symbol}: {e}")
            return 0.5
    
    async def _analyze_volume_momentum(self, symbol: str, market_data: Dict) -> float:
        """Analyze volume and momentum patterns"""
        try:
            candles = market_data.get('candles_1h', [])
            if len(candles) < 10:
                return 0.5
            
            # Volume trend analysis
            volumes = [float(c[5]) for c in candles[-10:]]
            volume_trend = np.mean(volumes[-3:]) / np.mean(volumes[-10:-3])
            
            # Price momentum
            prices = [float(c[4]) for c in candles[-5:]]
            price_momentum = (prices[-1] - prices[0]) / prices[0]
            
            # Combine volume and momentum
            score = 0.5
            if volume_trend > 1.2 and price_momentum > 0.01:
                score = 0.75
            elif volume_trend > 1.1 or price_momentum > 0.005:
                score = 0.6
            elif volume_trend < 0.8 and price_momentum < -0.01:
                score = 0.25
            elif volume_trend < 0.9 or price_momentum < -0.005:
                score = 0.4
            
            return score
            
        except Exception as e:
            logger.error(f"Error in volume momentum analysis for {symbol}: {e}")
            return 0.5
    
    def _calculate_composite_score(self, technical: float, sentiment: float, volume: float) -> Dict[str, Any]:
        """Calculate composite score from all analysis components"""
        try:
            # Apply weights
            composite = (
                technical * self.weights['technical'] +
                sentiment * self.weights['sentiment'] +
                volume * self.weights['volume']
            )
            
            return {
                'composite': composite,
                'technical': technical,
                'sentiment': sentiment,
                'volume': volume,
                'confidence': min(composite * self.confidence_multiplier, 1.0)
            }
            
        except Exception as e:
            logger.error(f"Error calculating composite score: {e}")
            return {
                'composite': 0.5,
                'technical': 0.5,
                'sentiment': 0.5,
                'volume': 0.5,
                'confidence': 0.5
            }
    
    async def _generate_trading_decision(self, symbol: str, scores: Dict[str, Any], market_data: Dict) -> Dict[str, Any]:
        """Generate final trading decision based on analysis"""
        try:
            composite_score = scores['composite']
            confidence = scores['confidence']
            
            # Decision thresholds
            if composite_score >= 0.7 and confidence >= 0.6:
                decision = "BUY"
                reason = f"Strong bullish signals: Technical={scores['technical']:.2f}, Sentiment={scores['sentiment']:.2f}, Volume={scores['volume']:.2f}"
            elif composite_score <= 0.3 and confidence >= 0.6:
                decision = "SELL"
                reason = f"Strong bearish signals: Technical={scores['technical']:.2f}, Sentiment={scores['sentiment']:.2f}, Volume={scores['volume']:.2f}"
            else:
                decision = "HOLD"
                reason = f"Mixed or weak signals: Composite={composite_score:.2f}, Confidence={confidence:.2f}"
            
            return {
                'symbol': symbol,
                'decision': decision,
                'confidence': confidence,
                'reason': reason,
                'scores': scores,
                'current_price': market_data.get('current_price'),
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'profitable_ai_core'
            }
            
        except Exception as e:
            logger.error(f"Error generating trading decision for {symbol}: {e}")
            return self._create_hold_decision(symbol, f"Decision generation error: {str(e)}")
    
    def _create_hold_decision(self, symbol: str, reason: str) -> Dict[str, Any]:
        """Create a default HOLD decision"""
        return {
            'symbol': symbol,
            'decision': 'HOLD',
            'confidence': 0.5,
            'reason': reason,
            'scores': {
                'composite': 0.5,
                'technical': 0.5,
                'sentiment': 0.5,
                'volume': 0.5,
                'confidence': 0.5
            },
            'current_price': None,
            'timestamp': datetime.now().isoformat(),
            'analysis_type': 'profitable_ai_core'
        }

# Global instance
profitable_ai_core = ProfitableAICore()

# API functions
async def analyze_token_profitability(symbol: str) -> Dict[str, Any]:
    """Analyze token profitability using the profitable AI core"""
    return await profitable_ai_core.analyze_profitability(symbol)

def get_profitable_ai_stats() -> Dict[str, Any]:
    """Get profitable AI core statistics"""
    return {
        'min_profit_threshold': profitable_ai_core.min_profit_threshold,
        'max_risk_per_trade': profitable_ai_core.max_risk_per_trade,
        'confidence_multiplier': profitable_ai_core.confidence_multiplier,
        'weights': profitable_ai_core.weights,
        'status': 'active'
    }
