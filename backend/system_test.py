#!/usr/bin/env python3
"""
🧪 ALPHA PREDATOR COMPREHENSIVE SYSTEM TEST
Tests all backend files for proper loading and functionality
"""

import sys
import time
import traceback
import importlib
from datetime import datetime
from typing import Dict, List, Any

# Test results storage
test_results = {
    "imports": {},
    "functions": {},
    "integrations": {},
    "errors": [],
    "warnings": [],
}


def log_test(test_name: str, status: str, message: str = "", error: str = ""):
    """Log test results"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"[{timestamp}] {status_icon} {test_name}: {message}")

    if error:
        test_results["errors"].append(f"{test_name}: {error}")
    if status == "WARN":
        test_results["warnings"].append(f"{test_name}: {message}")


def test_import(module_name: str, description: str = "") -> bool:
    """Test if a module can be imported"""
    try:
        module = importlib.import_module(module_name)
        test_results["imports"][module_name] = "PASS"
        log_test(f"Import {module_name}", "PASS", description)
        return True
    except Exception as e:
        test_results["imports"][module_name] = "FAIL"
        log_test(f"Import {module_name}", "FAIL", description, str(e))
        return False


def test_function_exists(module_name: str, function_name: str) -> bool:
    """Test if a function exists in a module"""
    try:
        module = importlib.import_module(module_name)
        if hasattr(module, function_name):
            test_results["functions"][f"{module_name}.{function_name}"] = "PASS"
            log_test(
                f"Function {module_name}.{function_name}", "PASS", "Function exists"
            )
            return True
        else:
            test_results["functions"][f"{module_name}.{function_name}"] = "FAIL"
            log_test(
                f"Function {module_name}.{function_name}", "FAIL", "Function not found"
            )
            return False
    except Exception as e:
        test_results["functions"][f"{module_name}.{function_name}"] = "FAIL"
        log_test(
            f"Function {module_name}.{function_name}",
            "FAIL",
            "Module import failed",
            str(e),
        )
        return False


def test_config_values():
    """Test configuration values"""
    try:
        from config import (
            KUCOIN_API_KEY,
            KUCOIN_API_SECRET,
            KUCOIN_API_PASSPHRASE,
            COINGECKO_API_KEY,
            TOKENMETRICS_API_KEY,
            TRADING_MODE,
            MAX_BUY,
            STOP_LOSS,
            TAKE_PROFIT,
        )

        # Check critical config values
        configs = {
            "KUCOIN_API_KEY": bool(KUCOIN_API_KEY),
            "KUCOIN_API_SECRET": bool(KUCOIN_API_SECRET),
            "KUCOIN_API_PASSPHRASE": bool(KUCOIN_API_PASSPHRASE),
            "COINGECKO_API_KEY": bool(COINGECKO_API_KEY),
            "TOKENMETRICS_API_KEY": bool(TOKENMETRICS_API_KEY),
            "TRADING_MODE": TRADING_MODE in ["LIVE", "PAPER"],
            "MAX_BUY": isinstance(MAX_BUY, (int, float)) and MAX_BUY > 0,
            "STOP_LOSS": isinstance(STOP_LOSS, (int, float)) and 0 < STOP_LOSS < 1,
            "TAKE_PROFIT": isinstance(TAKE_PROFIT, (int, float)) and TAKE_PROFIT > 0,
        }

        for config_name, is_valid in configs.items():
            if is_valid:
                log_test(f"Config {config_name}", "PASS", "Valid configuration")
            else:
                log_test(
                    f"Config {config_name}", "FAIL", "Invalid or missing configuration"
                )

        return all(configs.values())

    except Exception as e:
        log_test("Config Test", "FAIL", "Configuration loading failed", str(e))
        return False


def test_alpha_predator_flow():
    """Test the Alpha Predator trading flow"""
    try:
        from token_selector import alpha_predator_trading_flow

        log_test("Alpha Predator Flow", "PASS", "Flow function exists")
        return True
    except Exception as e:
        log_test("Alpha Predator Flow", "FAIL", "Flow function missing", str(e))
        return False


def test_ai_integration():
    """Test AI integration"""
    try:
        from ai_core import get_ai_engine

        ai_engine = get_ai_engine()
        log_test("AI Integration", "PASS", "AI engine accessible")
        return True
    except Exception as e:
        log_test("AI Integration", "FAIL", "AI engine not accessible", str(e))
        return False


def test_price_fetching():
    """Test price fetching capabilities"""
    try:
        from price_fetcher import get_enhanced_price_data

        log_test("Price Fetching", "PASS", "Price fetcher accessible")
        return True
    except Exception as e:
        log_test("Price Fetching", "FAIL", "Price fetcher not accessible", str(e))
        return False


def test_sentiment_analysis():
    """Test sentiment analysis"""
    try:
        from sentiment_engine import get_sentiment_score, get_combined_sentiment

        log_test("Sentiment Analysis", "PASS", "Sentiment functions accessible")
        return True
    except Exception as e:
        log_test(
            "Sentiment Analysis", "FAIL", "Sentiment functions not accessible", str(e)
        )
        return False


def test_tokenmetrics_integration():
    """Test TokenMetrics integration"""
    try:
        from tokenmetrics_client import get_tokenmetrics_data

        log_test("TokenMetrics Integration", "PASS", "TokenMetrics client accessible")
        return True
    except Exception as e:
        log_test(
            "TokenMetrics Integration",
            "FAIL",
            "TokenMetrics client not accessible",
            str(e),
        )
        return False


def test_trade_execution():
    """Test trade execution"""
    try:
        from trade_engine import execute_trade_with_strategy

        log_test("Trade Execution", "PASS", "Trade execution function accessible")
        return True
    except Exception as e:
        log_test(
            "Trade Execution", "FAIL", "Trade execution function not accessible", str(e)
        )
        return False


def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🚀 ALPHA PREDATOR COMPREHENSIVE SYSTEM TEST")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now()}")
    print()

    # Core module imports
    print("📦 TESTING CORE MODULE IMPORTS")
    print("-" * 40)
    core_modules = [
        ("config", "Configuration module"),
        ("main", "FastAPI main application"),
        ("ai_core", "AI decision engine"),
        ("token_selector", "Token selection and Alpha Predator flow"),
        ("trade_engine", "Trade execution engine"),
        ("live_runner", "Live trading runner"),
        ("sentiment_engine", "Sentiment analysis engine"),
        ("price_fetcher", "Price data fetching"),
        ("tokenmetrics_client", "TokenMetrics API client"),
        ("enhanced_api_endpoints", "API endpoints"),
    ]

    import_success = 0
    for module, desc in core_modules:
        if test_import(module, desc):
            import_success += 1

    print(
        f"\n📊 Import Results: {import_success}/{len(core_modules)} modules loaded successfully"
    )

    # Configuration tests
    print("\n⚙️ TESTING CONFIGURATION")
    print("-" * 40)
    config_ok = test_config_values()

    # Function existence tests
    print("\n🔧 TESTING CRITICAL FUNCTIONS")
    print("-" * 40)
    critical_functions = [
        ("token_selector", "alpha_predator_trading_flow"),
        ("ai_core", "get_ai_engine"),
        ("trade_engine", "execute_trade_with_strategy"),
        ("sentiment_engine", "get_sentiment_score"),
        ("price_fetcher", "get_enhanced_price_data"),
        ("tokenmetrics_client", "get_tokenmetrics_data"),
    ]

    function_success = 0
    for module, func in critical_functions:
        if test_function_exists(module, func):
            function_success += 1

    print(
        f"\n📊 Function Results: {function_success}/{len(critical_functions)} functions accessible"
    )

    # Integration tests
    print("\n🔗 TESTING INTEGRATIONS")
    print("-" * 40)
    integration_tests = [
        ("Alpha Predator Flow", test_alpha_predator_flow),
        ("AI Integration", test_ai_integration),
        ("Price Fetching", test_price_fetching),
        ("Sentiment Analysis", test_sentiment_analysis),
        ("TokenMetrics Integration", test_tokenmetrics_integration),
        ("Trade Execution", test_trade_execution),
    ]

    integration_success = 0
    for test_name, test_func in integration_tests:
        if test_func():
            integration_success += 1

    print(
        f"\n📊 Integration Results: {integration_success}/{len(integration_tests)} integrations working"
    )

    # Final summary
    print("\n" + "=" * 60)
    total_tests = (
        len(core_modules) + len(critical_functions) + len(integration_tests) + 1
    )  # +1 for config
    total_passed = (
        import_success
        + function_success
        + integration_success
        + (1 if config_ok else 0)
    )

    if total_passed == total_tests:
        print("🎉 ALL TESTS PASSED - SYSTEM FULLY OPERATIONAL!")
        print("✅ Alpha Predator backend is ready for trading")
    else:
        print(f"⚠️ {total_tests - total_passed} TESTS FAILED")
        print("❌ Some components need attention")

    print(
        f"\n📈 Overall Success Rate: {total_passed}/{total_tests} ({(total_passed/total_tests)*100:.1f}%)"
    )

    if test_results["errors"]:
        print(f"\n❌ ERRORS ({len(test_results['errors'])}):")
        for error in test_results["errors"][:5]:  # Show first 5 errors
            print(f"  • {error}")

    if test_results["warnings"]:
        print(f"\n⚠️ WARNINGS ({len(test_results['warnings'])}):")
        for warning in test_results["warnings"][:3]:  # Show first 3 warnings
            print(f"  • {warning}")

    print(f"\n⏰ Test completed at: {datetime.now()}")
    return total_passed == total_tests


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
