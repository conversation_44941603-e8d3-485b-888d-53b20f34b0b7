#!/usr/bin/env python3
"""
Test AI Signals Implementation
"""

import requests
import json
import time

def test_ai_signals():
    """Test the AI signals endpoint"""
    print("🧪 Testing AI Signals from TokenMetrics")
    print("=" * 40)
    
    try:
        # Clear cache first
        import os
        cache_file = "data/ai_signals_cache.json"
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print("✅ Cache cleared")
        
        # Test the endpoint
        print("🔄 Fetching AI signals...")
        response = requests.get("http://localhost:3005/api/dashboard/ai-signals-fast", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Response received: {response.status_code}")
            print(f"📊 Source: {data.get('source', 'Unknown')}")
            print(f"🎯 Total signals: {data.get('total_signals', 0)}")
            
            signals = data.get('signals', [])
            if signals:
                print(f"\n📈 First signal:")
                first_signal = signals[0]
                print(f"  Symbol: {first_signal.get('symbol', 'N/A')}")
                print(f"  Signal: {first_signal.get('signal', 'N/A')}")
                print(f"  Confidence: {first_signal.get('confidence', 0)}%")
                print(f"  Source: {first_signal.get('source', 'N/A')}")
                print(f"  Reason: {first_signal.get('reason', 'N/A')}")
                
                # Check if it's using TokenMetrics
                if "TokenMetrics" in first_signal.get('source', ''):
                    print("✅ Using TokenMetrics AI data!")
                else:
                    print("⚠️ Not using TokenMetrics AI data")
            else:
                print("⚠️ No signals returned")
                
            return True
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_tokenmetrics_direct():
    """Test TokenMetrics client directly"""
    print("\n🧪 Testing TokenMetrics Client Directly")
    print("=" * 40)
    
    try:
        import sys
        sys.path.append('.')
        
        from smart_tokenmetrics_client import get_token_analysis
        import asyncio
        
        # Test with BTC
        print("🔄 Testing TokenMetrics analysis for BTC...")
        result = asyncio.run(get_token_analysis("BTC"))
        
        print(f"✅ TokenMetrics response received")
        print(f"📊 Keys: {list(result.keys())}")
        
        if 'trading_signal' in result:
            trading_signal = result['trading_signal']
            print(f"🎯 Trading signal found: {trading_signal}")
        else:
            print("⚠️ No trading signal in response")
            
        return True
        
    except Exception as e:
        print(f"❌ TokenMetrics test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AI Signals Testing Suite")
    print("=" * 50)
    
    # Test 1: AI Signals endpoint
    success1 = test_ai_signals()
    
    # Test 2: TokenMetrics direct
    success2 = test_tokenmetrics_direct()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All AI signals tests passed!")
    else:
        print("⚠️ Some tests failed - check implementation")
