import logging
from typing import List, Dict, Any

from exchange_data import get_prices_from_all_exchanges
from token_selector import generate_top_token_list
from error_codes import error_response

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

ARBITRAGE_THRESHOLD_PCT = 0.005  # 0.5% price difference
MIN_VOLUME_USD = 10000  # Minimum volume to consider for arbitrage

async def find_arbitrage_opportunities(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Finds potential arbitrage opportunities across supported exchanges.
    """
    opportunities = []
    try:
        top_tokens = generate_top_token_list(limit=limit) # Use top tokens for efficiency
    except Exception as e:
        logger.error(f"Error generating top token list for arbitrage: {e}")
        return [error_response("ARBITRAGE_TOKEN_LIST_FAILED", f"Failed to get top tokens for arbitrage: {e}", 500)]

    for token_info in top_tokens:
        symbol = token_info.get("symbol")
        if not symbol:
            continue

        logger.info(f"Searching arbitrage for {symbol}...")
        try:
            prices = await get_prices_from_all_exchanges(symbol)
        except Exception as e:
            logger.warning(f"Could not fetch prices for {symbol} from all exchanges: {e}")
            continue

        if not prices or len(prices) < 2:
            logger.info(f"Skipping {symbol}: not enough exchanges with price data.")
            continue

        # Filter out non-numeric prices or zero prices
        valid_prices = {exch: price for exch, price in prices.items() if isinstance(price, (int, float)) and price > 0}

        if len(valid_prices) < 2:
            logger.info(f"Skipping {symbol}: not enough valid price data after filtering.")
            continue

        min_price_exchange = min(valid_prices, key=lambda k: valid_prices[k])
        max_price_exchange = max(valid_prices, key=lambda k: valid_prices[k])

        min_price = valid_prices[min_price_exchange]
        max_price = valid_prices[max_price_exchange]

        price_diff = max_price - min_price
        percentage_diff = (price_diff / min_price) if min_price > 0 else 0

        if percentage_diff >= ARBITRAGE_THRESHOLD_PCT:
            opportunity = {
                "symbol": symbol,
                "buy_exchange": min_price_exchange,
                "buy_price": min_price,
                "sell_exchange": max_price_exchange,
                "sell_price": max_price,
                "percentage_diff": round(percentage_diff * 100, 4),
                "profit_usd": round(price_diff * 100, 2) # Assuming a notional of $100 for profit est.
            }
            opportunities.append(opportunity)
            logger.info(f"Found arbitrage opportunity for {symbol}: {opportunity}")

    return opportunities
