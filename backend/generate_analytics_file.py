import json
from pathlib import Path
import sys
import logging

# Add current directory to sys.path to ensure imports work
sys.path.append(str(Path(__file__).resolve().parent))

try:
    from analytics_engine import generate_analytics
except ImportError as e:
    print("Failed to import generate_analytics from backend.analytics_engine:", e)
    sys.exit(1)

def create_analytics_file():
    analytics_file = Path("backend/data/analytics.json")

    data = generate_analytics()
    with analytics_file.open("w") as f:
        json.dump(data, f, indent=2)

    print(f"Analytics JSON file created at {analytics_file}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    create_analytics_file()