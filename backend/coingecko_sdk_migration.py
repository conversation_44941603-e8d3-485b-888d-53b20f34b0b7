#!/usr/bin/env python3
"""
🚀 COINGECKO SDK MIGRATION
Replace custom HTTP client with official CoinGecko Python SDK
"""

import logging
import time
from typing import Dict, List, Optional, Any

# CoinGecko SDK imports with fallback stubs
try:
    from pycoingecko import CoinGeckoAPI  # type: ignore

    COINGECKO_SDK_AVAILABLE = True
except ImportError:
    COINGECKO_SDK_AVAILABLE = False
    CoinGeckoAPI = None  # type: ignore

    class CoinGeckoAPI:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_coins_markets(self, **kwargs):
            return []

        def get_coin_by_id(self, coin_id: str, **kwargs):
            return {"id": coin_id, "current_price": 0}

        def get_price(self, **kwargs):
            return {}

        def get_coin_history_by_id(self, **kwargs):
            return {"market_data": {"current_price": {"usd": 0}}}

        def get_coin_ohlc_by_id(self, **kwargs):
            return []

        def get_coin_market_chart_by_id(self, coin_id: str, **kwargs):
            return {"prices": [], "market_caps": [], "total_volumes": []}

        def search(self, query: str, **kwargs):
            return {
                "coins": [],
                "exchanges": [],
                "icos": [],
                "categories": [],
                "nfts": [],
            }

        def get_search_trending(self, **kwargs):
            return {"coins": [], "nfts": [], "categories": []}


from requests.exceptions import HTTPError, RequestException
from config import COINGECKO_API_KEY

logger = logging.getLogger(__name__)


class CoinGeckoSDKWrapper:
    """Enhanced CoinGecko client using official SDK"""

    def __init__(self):
        """Initialize CoinGecko SDK client"""
        self.api_key = COINGECKO_API_KEY

        try:
            # Initialize official SDK client
            if CoinGeckoAPI is None:
                raise ImportError("pycoingecko package not installed")

            if self.api_key and not self.api_key.startswith("YOUR_"):
                # Use Pro API if key is available
                self.client = CoinGeckoAPI(api_key=self.api_key)
                self.is_pro = True
                logger.info("✅ CoinGecko Pro SDK client initialized")
            else:
                # Use free API
                self.client = CoinGeckoAPI()
                self.is_pro = False
                logger.info("✅ CoinGecko Free SDK client initialized")

            # Rate limiting for free tier
            self.last_request_time = 0
            self.rate_limit_delay = (
                1.2 if not self.is_pro else 0.1
            )  # Free: 1.2s, Pro: 0.1s

        except Exception as e:
            logger.error(f"❌ Failed to initialize CoinGecko SDK: {e}")
            self.client = None
            self.is_pro = False

    def _rate_limit(self):
        """Apply rate limiting"""
        if not self.is_pro:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.rate_limit_delay:
                time.sleep(self.rate_limit_delay - time_since_last)
            self.last_request_time = time.time()

    def get_coins_markets(
        self, vs_currency: str = "usd", limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get coins market data using SDK"""
        try:
            if not self.client:
                return self._get_fallback_market_data()

            self._rate_limit()

            # Use official SDK method
            markets = self.client.get_coins_markets(
                vs_currency=vs_currency,
                order="market_cap_desc",
                per_page=limit,
                page=1,
                sparkline=False,
                price_change_percentage="24h",
            )

            # Transform to our expected format
            transformed_markets = []
            for coin in markets:
                transformed_markets.append(
                    {
                        "id": coin.get("id"),
                        "symbol": coin.get("symbol", "").upper(),
                        "name": coin.get("name"),
                        "price": coin.get("current_price", 0),
                        "market_cap": coin.get("market_cap", 0),
                        "volume_24h": coin.get("total_volume", 0),
                        "price_change_24h": coin.get("price_change_24h", 0),
                        "price_change_percentage_24h": coin.get(
                            "price_change_percentage_24h", 0
                        ),
                        "market_cap_rank": coin.get("market_cap_rank", 0),
                        "circulating_supply": coin.get("circulating_supply", 0),
                        "total_supply": coin.get("total_supply", 0),
                        "max_supply": coin.get("max_supply", 0),
                        "ath": coin.get("ath", 0),
                        "atl": coin.get("atl", 0),
                        "last_updated": coin.get("last_updated"),
                        "source": "coingecko_sdk",
                    }
                )

            logger.info(
                f"✅ Fetched {len(transformed_markets)} coins from CoinGecko SDK"
            )
            return transformed_markets

        except (HTTPError, RequestException) as e:
            logger.error(f"❌ CoinGecko SDK API error: {e}")
            return self._get_fallback_market_data()
        except Exception as e:
            logger.error(f"❌ CoinGecko SDK unexpected error: {e}")
            return self._get_fallback_market_data()

    def get_coin_by_id(self, coin_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed coin data by ID using SDK"""
        try:
            if not self.client:
                return None

            self._rate_limit()

            # Use official SDK method
            coin = self.client.get_coin_by_id(  # type: ignore
                id=coin_id,
                localization=False,
                tickers=False,
                market_data=True,
                community_data=False,
                developer_data=False,
                sparkline=False,
            )

            market_data = coin.get("market_data", {})

            return {
                "id": coin.get("id"),
                "symbol": coin.get("symbol", "").upper(),
                "name": coin.get("name"),
                "description": coin.get("description", {}).get("en", ""),
                "price": market_data.get("current_price", {}).get("usd", 0),
                "market_cap": market_data.get("market_cap", {}).get("usd", 0),
                "volume_24h": market_data.get("total_volume", {}).get("usd", 0),
                "price_change_24h": market_data.get("price_change_24h", 0),
                "price_change_percentage_24h": market_data.get(
                    "price_change_percentage_24h", 0
                ),
                "market_cap_rank": market_data.get("market_cap_rank", 0),
                "circulating_supply": market_data.get("circulating_supply", 0),
                "total_supply": market_data.get("total_supply", 0),
                "max_supply": market_data.get("max_supply", 0),
                "ath": market_data.get("ath", {}).get("usd", 0),
                "atl": market_data.get("atl", {}).get("usd", 0),
                "ath_date": market_data.get("ath_date", {}).get("usd"),
                "atl_date": market_data.get("atl_date", {}).get("usd"),
                "last_updated": market_data.get("last_updated"),
                "source": "coingecko_sdk",
            }

        except (HTTPError, RequestException) as e:
            logger.error(f"❌ CoinGecko SDK coin error for {coin_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ CoinGecko SDK unexpected coin error for {coin_id}: {e}")
            return None

    def get_price(
        self, coin_ids: List[str], vs_currencies: List[str] = ["usd"]
    ) -> Dict[str, Any]:
        """Get simple price data using SDK"""
        try:
            if not self.client:
                return {}

            self._rate_limit()

            # Use official SDK method
            prices = self.client.get_price(
                ids=coin_ids,
                vs_currencies=vs_currencies,
                include_market_cap=True,
                include_24hr_vol=True,
                include_24hr_change=True,
            )

            logger.info(
                f"✅ Fetched prices for {len(coin_ids)} coins from CoinGecko SDK"
            )
            return prices

        except (HTTPError, RequestException) as e:
            logger.warning(f"⚠️ CoinGecko SDK price error: {e}")
            return {}
        except Exception as e:
            # Check if it's an API key error
            error_str = str(e)
            if "API Key Missing" in error_str or "error_code" in error_str:
                logger.debug(f"CoinGecko API key issue: {error_str}")
            else:
                logger.warning(f"CoinGecko SDK unexpected error: {error_str}")
            return {}

    def get_single_price(self, coin_id: str) -> Optional[float]:
        """Get simple price for a single coin (convenience method)"""
        try:
            if isinstance(coin_id, str):
                # Convert single coin_id to list format expected by original get_price method
                prices = self.get_price([coin_id])
                if prices and coin_id in prices:
                    return prices[coin_id].get("usd")
            return None
        except Exception as e:
            logger.error(f"❌ CoinGecko single price error for {coin_id}: {e}")
            return None

    def get_market_data(self, coin_id: str) -> Optional[Dict[str, Any]]:
        """Get market data for a specific coin"""
        try:
            coin_data = self.get_coin_by_id(coin_id)
            if coin_data and "market_data" in coin_data:
                market_data = coin_data["market_data"]
                return {
                    "current_price": market_data.get("current_price", {}).get("usd", 0),
                    "market_cap": market_data.get("market_cap", {}).get("usd", 0),
                    "total_volume": market_data.get("total_volume", {}).get("usd", 0),
                    "price_change_24h": market_data.get("price_change_24h", 0),
                    "price_change_percentage_24h": market_data.get(
                        "price_change_percentage_24h", 0
                    ),
                    "market_cap_rank": market_data.get("market_cap_rank", 0),
                }
            return None
        except Exception as e:
            logger.error(f"❌ CoinGecko market data error for {coin_id}: {e}")
            return None

    def get_coin_history(self, coin_id: str, date: str) -> Optional[Dict[str, Any]]:
        """Get historical coin data using SDK"""
        try:
            if not self.client:
                return None

            self._rate_limit()

            # Use official SDK method
            history = self.client.get_coin_history_by_id(
                id=coin_id, date=date, localization=False  # Format: dd-mm-yyyy
            )

            market_data = history.get("market_data", {})

            return {
                "id": coin_id,
                "date": date,
                "price": market_data.get("current_price", {}).get("usd", 0),
                "market_cap": market_data.get("market_cap", {}).get("usd", 0),
                "volume_24h": market_data.get("total_volume", {}).get("usd", 0),
                "source": "coingecko_sdk",
            }

        except (HTTPError, RequestException) as e:
            logger.error(f"❌ CoinGecko SDK history error for {coin_id}: {e}")
            return None
        except Exception as e:
            logger.error(
                f"❌ CoinGecko SDK unexpected history error for {coin_id}: {e}"
            )
            return None

    def get_coin_market_chart(
        self, coin_id: str, vs_currency: str = "usd", days: int = 30
    ) -> Optional[Dict[str, Any]]:
        """Get market chart data using SDK"""
        try:
            if not self.client:
                return None

            self._rate_limit()

            # Use official SDK method
            chart = self.client.get_coin_market_chart_by_id(  # type: ignore
                id=coin_id, vs_currency=vs_currency, days=days
            )

            return {
                "coin_id": coin_id,
                "vs_currency": vs_currency,
                "days": days,
                "prices": chart.get("prices", []),
                "market_caps": chart.get("market_caps", []),
                "total_volumes": chart.get("total_volumes", []),
                "source": "coingecko_sdk",
            }

        except (HTTPError, RequestException) as e:
            logger.error(f"❌ CoinGecko SDK chart error for {coin_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ CoinGecko SDK unexpected chart error for {coin_id}: {e}")
            return None

    def search_coins(self, query: str) -> List[Dict[str, Any]]:
        """Search coins using SDK"""
        try:
            if not self.client:
                return []

            self._rate_limit()

            # Use official SDK method
            results = self.client.search(query=query)

            coins = results.get("coins", [])

            return [
                {
                    "id": coin.get("id"),
                    "name": coin.get("name"),
                    "symbol": coin.get("symbol", "").upper(),
                    "market_cap_rank": coin.get("market_cap_rank", 0),
                    "thumb": coin.get("thumb"),
                    "large": coin.get("large"),
                    "source": "coingecko_sdk",
                }
                for coin in coins
            ]

        except (HTTPError, RequestException) as e:
            logger.error(f"❌ CoinGecko SDK search error: {e}")
            return []
        except Exception as e:
            logger.error(f"❌ CoinGecko SDK unexpected search error: {e}")
            return []

    def get_trending_coins(self) -> List[Dict[str, Any]]:
        """Get trending coins using SDK"""
        try:
            if not self.client:
                return []

            self._rate_limit()

            # Use official SDK method
            trending = self.client.get_search_trending()

            coins = trending.get("coins", [])

            return [
                {
                    "id": coin["item"].get("id"),
                    "name": coin["item"].get("name"),
                    "symbol": coin["item"].get("symbol", "").upper(),
                    "market_cap_rank": coin["item"].get("market_cap_rank", 0),
                    "thumb": coin["item"].get("thumb"),
                    "score": coin["item"].get("score", 0),
                    "source": "coingecko_sdk",
                }
                for coin in coins
            ]

        except (HTTPError, RequestException) as e:
            logger.error(f"❌ CoinGecko SDK trending error: {e}")
            return []
        except Exception as e:
            logger.error(f"❌ CoinGecko SDK unexpected trending error: {e}")
            return []

    def _get_fallback_market_data(self) -> List[Dict[str, Any]]:
        """Fallback market data when SDK fails"""
        return [
            {
                "id": "bitcoin",
                "symbol": "BTC",
                "name": "Bitcoin",
                "price": 45000.0,
                "market_cap": 900000000000,
                "volume_24h": 20000000000,
                "price_change_24h": 1000.0,
                "price_change_percentage_24h": 2.27,
                "market_cap_rank": 1,
                "source": "coingecko_fallback",
            },
            {
                "id": "ethereum",
                "symbol": "ETH",
                "name": "Ethereum",
                "price": 3000.0,
                "market_cap": 360000000000,
                "volume_24h": 15000000000,
                "price_change_24h": 50.0,
                "price_change_percentage_24h": 1.69,
                "market_cap_rank": 2,
                "source": "coingecko_fallback",
            },
        ]


# Create singleton instance
coingecko_sdk = CoinGeckoSDKWrapper()


# Compatibility functions for existing code
def get_coingecko_markets(limit: int = 100):
    """Compatibility function"""
    return coingecko_sdk.get_coins_markets(limit=limit)


def get_coingecko_coin(coin_id: str):
    """Compatibility function"""
    return coingecko_sdk.get_coin_by_id(coin_id)


def get_coingecko_price(coin_ids: List[str]):
    """Compatibility function"""
    return coingecko_sdk.get_price(coin_ids)


def get_coingecko_single_price(coin_id: str):
    """Compatibility function for single coin price"""
    return coingecko_sdk.get_single_price(coin_id)


if __name__ == "__main__":
    # Test the SDK wrapper
    print("🧪 Testing CoinGecko SDK Wrapper")
    print("=" * 40)

    # Test markets
    markets = coingecko_sdk.get_coins_markets(limit=10)
    print(f"✅ Fetched {len(markets)} market entries")

    # Test single coin
    btc = coingecko_sdk.get_coin_by_id("bitcoin")
    if btc:
        print(f"✅ BTC Price: ${btc['price']}")

    # Test trending
    trending = coingecko_sdk.get_trending_coins()
    print(f"✅ Fetched {len(trending)} trending coins")
