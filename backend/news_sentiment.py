import random
import logging
import json
import os

# Try to import TextBlob, fall back to our simple implementation
TextBlob = None  # type: ignore

try:
    from textblob import TextBlob  # type: ignore
except ImportError:
    try:
        from news_sentiment_fallback import TextBlob  # type: ignore
    except ImportError:
        # Simple fallback sentiment analyzer
        class TextBlob:  # type: ignore
            def __init__(self, text):
                self.text = text
                self.sentiment = self
            
            @property
            def polarity(self):
                # Simple keyword-based sentiment
                positive_words = ['good', 'great', 'excellent', 'amazing', 'bullish', 'surge', 'pump', 'moon', 'rocket']
                negative_words = ['bad', 'terrible', 'awful', 'bearish', 'crash', 'dump', 'rug', 'scam', 'fail']
                
                text_lower = self.text.lower()
                pos_count = sum(1 for word in positive_words if word in text_lower)
                neg_count = sum(1 for word in negative_words if word in text_lower)
                
                if pos_count + neg_count == 0:
                    return 0.0
                return (pos_count - neg_count) / (pos_count + neg_count)

from typing import List, Optional, Dict, Any
from datetime import datetime

# Import config safely
try:
    from config import USE_REAL_NEWS
except ImportError:
    USE_REAL_NEWS = False

# Import other modules safely
try:
    from kryptomerch_scraper import analyze_sentiment_from_blog  # type: ignore
except (ImportError, ModuleNotFoundError, AttributeError):
    def analyze_sentiment_from_blog(token):  # type: ignore
        return 0.0

try:
    from reddit_github_alpha import fetch_signal_sentiment
except ImportError:
    def fetch_signal_sentiment(token):  # type: ignore
        return 0.0

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# === Mock Headline Generator ===
def mock_news_articles(symbol: str) -> List[str]:
    """Generate mock news articles for a given symbol."""
    headlines = [
        f"{symbol.upper()} price surges as whales accumulate more tokens",
        f"Mixed outlook for {symbol.upper()} amid market volatility",
        f"{symbol.upper()} crashes after negative sentiment spreads on Twitter",
        f"Investors bullish on {symbol.upper()} ahead of upcoming update",
        f"Crypto experts warn of rug pull risks in {symbol.upper()}"
    ]
    return random.sample(headlines, k=3)

# === Sentiment Analyzer ===
def analyze_sentiment(text: str) -> float:
    """Analyze sentiment using TextBlob polarity score."""
    blob = TextBlob(text)
    return blob.sentiment.polarity  # type: ignore

def get_sentiment(text: str) -> float:
    """Analyze sentiment using TextBlob polarity score."""
    blob = TextBlob(text)
    return blob.sentiment.polarity  # type: ignore

# === Unified Sentiment Scorer ===
def get_combined_sentiment_score(symbol: str, news_data: Optional[List[dict]] = None) -> Dict[str, Any]:
    """Compute combined sentiment score from all available sources."""
    try:
        relevant_news_snippets = []

        # Initialize all weights and scores to 0.0
        discord_score = 0.0
        discord_weight = 0.0
        cmc_score = 0.0
        cmc_weight = 0.0
        reddit_score = 0.0
        reddit_weight = 0.0
        blog_score = 0.0
        blog_weight = 0.0
        panic_score = 0.0
        panic_weight = 0.0
        mock_score = 0.0
        mock_weight = 0.1  # Assign a default weight for mock articles

        try:
            with open(os.path.join("backend", "data", "discord_news.json")) as f:
                news_items = json.load(f)
                if not isinstance(news_items, list):
                    logger.warning(f"[discord_news] Expected list, got {type(news_items)} — skipping.")
                    news_items = []
                relevant = [n for n in news_items if symbol.upper() in n["content"].upper()]
                if relevant:
                    discord_score = sum(analyze_sentiment(n["content"]) for n in relevant) / len(relevant)
                    discord_weight = 0.4
                    relevant_news_snippets.extend([n["content"] for n in relevant])
        except FileNotFoundError:
            with open(os.path.join("backend", "data", "discord_news.json"), "w") as f:
                json.dump([], f)
            news_items = []
        except Exception as e:
            logger.error(f"[ERROR] Could not parse discord_news.json for {symbol}: {e}")
            news_items = []

        try:
            with open(os.path.join("backend", "data", "cmc_news.json")) as f:
                cmc_news = json.load(f)
                if not isinstance(cmc_news, list):
                    logger.warning(f"[cmc_news] Expected list, got {type(cmc_news)} — skipping.")
                    cmc_news = []
                relevant = [n for n in cmc_news if symbol.upper() in n["content"].upper()]
                if relevant:
                    cmc_score = sum(analyze_sentiment(n["content"]) for n in relevant) / len(relevant)
                    cmc_weight = 0.2
                    relevant_news_snippets.extend([n["content"] for n in relevant])
        except FileNotFoundError:
            with open(os.path.join("backend", "data", "cmc_news.json"), "w") as f:
                json.dump([], f)
            cmc_news = []
        except Exception as e:
            logger.error(f"[ERROR] Could not parse cmc_news.json for {symbol}: {e}")
            cmc_news = []

        reddit_score = fetch_signal_sentiment(symbol)
        reddit_weight = 0.3 if reddit_score is not None else 0.0
        reddit_score = reddit_score if reddit_score is not None else 0.0

        blog_score = analyze_sentiment_from_blog(symbol) if USE_REAL_NEWS else 0.0
        blog_weight = 0.2 if USE_REAL_NEWS else 0.0

        try:
            with open(os.path.join("backend", "data", "cryptopanic.json")) as f:
                panic_news = json.load(f)
                if not isinstance(panic_news, list):
                    logger.warning(f"[cryptopanic] Expected list, got {type(panic_news)} — skipping.")
                    panic_news = []
                relevant = [n for n in panic_news if symbol.upper() in n["content"].upper()]
                if relevant:
                    panic_score = sum(analyze_sentiment(n["content"]) for n in relevant) / len(relevant)
                    panic_weight = 0.2
                    relevant_news_snippets.extend([n["content"] for n in relevant])
        except FileNotFoundError:
            with open(os.path.join("backend", "data", "cryptopanic.json"), "w") as f:
                json.dump([], f)
            panic_news = []
        except Exception as e:
            logger.error(f"[ERROR] Could not parse cryptopanic.json for {symbol}: {e}")
            panic_news = []

        mock_articles = mock_news_articles(symbol)
        mock_score = sum(analyze_sentiment(a) for a in mock_articles) / len(mock_articles) if mock_articles else 0.0
        relevant_news_snippets.extend(mock_articles)

        total_weight = discord_weight + reddit_weight + cmc_weight + panic_weight + mock_weight
        if total_weight == 0:
            return {"score": 0.0, "news_snippets": relevant_news_snippets}

        final_score = (
            (discord_score * discord_weight) +
            (reddit_score * reddit_weight) +
            (cmc_score * cmc_weight) +
            (blog_score * blog_weight) +
            (panic_score * panic_weight) +
            (mock_score * mock_weight)
        ) / total_weight

        # Save sentiment to news_sentiment.jsonl
        sentiment_entry = {
            "timestamp": datetime.now().isoformat(),
            "symbol": symbol,
            "score": round(final_score, 2),
            "news_snippets": relevant_news_snippets
        }
        with open(os.path.join("backend", "data", "news_sentiment.jsonl"), "a") as f:
            f.write(json.dumps(sentiment_entry) + "\n")
        logger.info(f"Saved sentiment for {symbol} to news_sentiment.jsonl")

        return {"score": round(final_score, 2), "news_snippets": relevant_news_snippets}

    except Exception as e:
        logger.error(f"[ERROR] Sentiment analysis failed for {symbol}: {e}")
        return {"score": 0.0, "news_snippets": []}

# === CLI Debug Tool ===
# Alias for backward compatibility
def get_news_sentiment(symbol: str) -> Dict[str, Any]:
    """
    Alias for get_combined_sentiment_score for backward compatibility.
    """
    return get_combined_sentiment_score(symbol)

if __name__ == "__main__":
    sample = "DOGE"
    score = get_combined_sentiment_score(sample)
    print(f"{sample.upper()} sentiment score: {score}")
