#!/usr/bin/env python3
"""
TokenMetrics API Client for comprehensive token data fetching
Provides price data, market metrics, and trading signals
"""

import logging
import time
import asyncio
import aiohttp
from typing import Optional, Dict, Any, List
from datetime import datetime
from dataclasses import dataclass
from config import TOKENMETRICS_API_KEY
from cache import get_cached_data, set_cached_data

logger = logging.getLogger(__name__)

# TokenMetrics API Configuration
TOKENMETRICS_BASE_URL = "https://api.tokenmetrics.com/v1"
TOKENMETRICS_RATE_LIMIT = 0.2  # 200ms between requests
TOKENMETRICS_CACHE_TTL = 60  # 1 minute cache for live data
TOKENMETRICS_TIMEOUT = 10


@dataclass
class TokenMetricsData:
    """TokenMetrics comprehensive token data"""

    symbol: str
    price: float
    volume_24h: float
    market_cap: float
    price_change_24h: float
    price_change_percentage_24h: float
    tm_grade: str  # TokenMetrics grade (A, B, C, D, F)
    tm_score: float  # TokenMetrics score (0-100)
    volatility: float
    liquidity_score: float
    social_sentiment: float
    technical_analysis: Dict[str, Any]
    last_updated: datetime
    source: str = "tokenmetrics"
    confidence: float = 0.95


class TokenMetricsClient:
    """Enhanced TokenMetrics API client for live data fetching"""

    def __init__(self):
        self.api_key = TOKENMETRICS_API_KEY
        self.base_url = TOKENMETRICS_BASE_URL
        self.last_request_time = 0
        self.session: Optional[aiohttp.ClientSession] = None

    async def get_session(self) -> aiohttp.ClientSession:
        """Create new aiohttp session for each request to avoid loop issues"""
        # Always create new session to prevent "Future attached to different loop" errors
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "AlphaPredatorBot/1.0",
        }
        timeout = aiohttp.ClientTimeout(total=TOKENMETRICS_TIMEOUT)
        connector = aiohttp.TCPConnector(
            limit=10, limit_per_host=5, enable_cleanup_closed=True
        )
        return aiohttp.ClientSession(
            headers=headers, timeout=timeout, connector=connector, connector_owner=True
        )

    async def _rate_limit(self):
        """Apply rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < TOKENMETRICS_RATE_LIMIT:
            await asyncio.sleep(TOKENMETRICS_RATE_LIMIT - time_since_last)
        self.last_request_time = time.time()

    async def get_token_data(self, symbol: str) -> Optional[TokenMetricsData]:
        """Get comprehensive token data from TokenMetrics with fallback"""
        if not self.api_key or self.api_key.startswith("YOUR_"):
            logger.debug("TokenMetrics API key not configured, using fallback data")
            return self._create_fallback_data(symbol)

        try:
            # Check cache first
            cache_key = f"tokenmetrics_{symbol}"
            cached_data = get_cached_data(cache_key)
            if cached_data:
                logger.debug(f"✅ TokenMetrics cache hit for {symbol}")
                return TokenMetricsData(**cached_data)

            await self._rate_limit()

            # Clean symbol for TokenMetrics API
            clean_symbol = symbol.replace("-USDT", "").replace("-USD", "").upper()

            # Fetch token data with timeout
            url = f"{self.base_url}/tokens/{clean_symbol}"

            # Create session and make request
            async with await self.get_session() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        token_data = self._parse_token_data(data, symbol)

                        if token_data:
                            # Cache the result
                            set_cached_data(
                                cache_key,
                                token_data.__dict__,
                                TOKENMETRICS_CACHE_TTL,
                                "high",
                            )
                            logger.info(
                                f"✅ TokenMetrics data fetched for {symbol}: ${token_data.price:.6f}"
                            )
                            return token_data

                    elif response.status == 429:
                        logger.warning(f"TokenMetrics rate limited for {symbol}")
                        await asyncio.sleep(5)  # Wait 5 seconds on rate limit

                    elif response.status == 404:
                        logger.debug(f"Token {symbol} not found on TokenMetrics")

                    else:
                        logger.warning(
                            f"TokenMetrics API error {response.status} for {symbol}"
                        )

        except Exception as e:
            logger.debug(f"TokenMetrics API error for {symbol}: {e}")
            # Return fallback data on error
            return self._create_fallback_data(symbol)

        # Return fallback data if no valid response
        return self._create_fallback_data(symbol)

    def _create_fallback_data(self, symbol: str) -> TokenMetricsData:
        """Create fallback TokenMetrics data when API is unavailable"""
        return TokenMetricsData(
            symbol=symbol,
            price=0.0,  # Will be filled by other price sources
            volume_24h=1000000.0,  # Default volume
            market_cap=10000000.0,  # Default market cap
            price_change_24h=0.0,
            price_change_percentage_24h=0.0,
            tm_grade="C",  # Neutral grade
            tm_score=50.0,  # Neutral score
            volatility=0.5,  # Moderate volatility
            liquidity_score=0.6,  # Moderate liquidity
            social_sentiment=0.5,  # Neutral sentiment
            technical_analysis={
                "trend": "neutral",
                "support": 0.0,
                "resistance": 0.0,
                "rsi": 50.0,
                "macd": 0.0,
            },
            last_updated=datetime.now(),
            source="fallback",
            confidence=0.3,  # Low confidence for fallback data
        )

    def _parse_token_data(
        self, data: Dict[str, Any], symbol: str
    ) -> Optional[TokenMetricsData]:
        """Parse TokenMetrics API response"""
        try:
            token_info = data.get("data", {})
            price_data = token_info.get("price", {})
            metrics = token_info.get("metrics", {})
            analysis = token_info.get("analysis", {})

            return TokenMetricsData(
                symbol=symbol,
                price=float(price_data.get("current", 0)),
                volume_24h=float(price_data.get("volume_24h", 0)),
                market_cap=float(price_data.get("market_cap", 0)),
                price_change_24h=float(price_data.get("change_24h", 0)),
                price_change_percentage_24h=float(
                    price_data.get("change_24h_percent", 0)
                ),
                tm_grade=metrics.get("grade", "N/A"),
                tm_score=float(metrics.get("score", 0)),
                volatility=float(metrics.get("volatility", 0)),
                liquidity_score=float(metrics.get("liquidity_score", 0)),
                social_sentiment=float(metrics.get("social_sentiment", 0.5)),
                technical_analysis=analysis.get("technical", {}),
                last_updated=datetime.now(),
                confidence=0.95,
            )

        except Exception as e:
            logger.error(f"Failed to parse TokenMetrics data for {symbol}: {e}")
            return None

    async def get_batch_token_data(self, symbols: List[str]) -> List[TokenMetricsData]:
        """Get token data for multiple symbols"""
        try:
            logger.info(f"🔄 Fetching TokenMetrics data for {len(symbols)} tokens...")

            tasks = [self.get_token_data(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Filter successful results
            token_data_list = [
                result for result in results if isinstance(result, TokenMetricsData)
            ]

            logger.info(
                f"✅ TokenMetrics batch fetch complete: {len(token_data_list)} successful"
            )
            return token_data_list

        except Exception as e:
            logger.error(f"TokenMetrics batch fetch failed: {e}")
            return []

    async def get_market_overview(self) -> Optional[Dict[str, Any]]:
        """Get market overview from TokenMetrics"""
        try:
            if not self.api_key:
                return None

            cache_key = "tokenmetrics_market_overview"
            cached_data = get_cached_data(cache_key)
            if cached_data:
                return cached_data

            await self._rate_limit()
            session = await self.get_session()

            url = f"{self.base_url}/market/overview"

            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    market_data = data.get("data", {})

                    # Cache for 5 minutes
                    set_cached_data(cache_key, market_data, 300, "normal")
                    return market_data

        except Exception as e:
            logger.error(f"TokenMetrics market overview error: {e}")

        return None

    async def cleanup(self):
        """Cleanup the session"""
        if self.session is not None:
            if hasattr(self.session, "close") and not getattr(
                self.session, "closed", True
            ):
                try:
                    # aiohttp.ClientSession.close() is always a coroutine
                    await self.session.close()
                except Exception as e:
                    logger.warning(f"Error closing session: {e}")
            self.session = None


# Global TokenMetrics client instance
tokenmetrics_client = TokenMetricsClient()


async def get_tokenmetrics_data(symbol: str) -> Optional[TokenMetricsData]:
    """Convenience function to get TokenMetrics data"""
    return await tokenmetrics_client.get_token_data(symbol)


async def get_tokenmetrics_batch_data(symbols: List[str]) -> List[TokenMetricsData]:
    """Convenience function to get batch TokenMetrics data"""
    return await tokenmetrics_client.get_batch_token_data(symbols)
