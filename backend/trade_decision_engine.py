import logging
import traceback
import time
import asyncio
from typing import List, Callable, Any, Dict, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

from kucoin_data import fetch_kucoin_candlestick_data
from indicators import (
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_macd,
    calculate_bollinger_bands,
)
from sentiment_engine import get_combined_sentiment
from strategy_selector import choose_strategy
from ai_trade_signal import score_token_with_ai_signals as ai_trade_signal
from cache import get_cached_data, set_cached_data

# Enhanced configuration
MAX_RETRIES = 3
CACHE_TTL_DECISIONS = 300  # 5 minutes
CACHE_TTL_INDICATORS = 600  # 10 minutes
BATCH_SIZE = 10
MAX_CONCURRENT_DECISIONS = 5

logger = logging.getLogger(__name__)


@dataclass
class EnhancedTradeDecision:
    """Enhanced trade decision with comprehensive analysis"""

    symbol: str
    decision: str
    confidence: float
    reasoning: str
    technical_score: float
    sentiment_score: float
    ai_score: float
    strategy: str
    timestamp: float
    indicators: Dict[str, Any]


def retry(
    func: Callable, retries: int = MAX_RETRIES, delay: int = 2, *args, **kwargs
) -> Any:
    """Retry wrapper for fault tolerance."""
    for attempt in range(1, retries + 1):
        try:
            logging.info(
                f"[Retry] Attempt {attempt} — {func.__name__} args={args}, kwargs={kwargs}"
            )
            return func(*args, **kwargs)
        except Exception as e:
            logging.warning(
                f"[Retry] Failure on attempt {attempt} — {func.__name__}: {e}"
            )
            if attempt == retries:
                logging.error(f"[Retry] Max retries reached — {func.__name__}")
                raise
            time.sleep(delay)


def get_trade_decision(symbol: str) -> str:
    """
    Gets the trade decision for a given token.
    """
    try:
        candlestick_data = retry(fetch_kucoin_candlestick_data, args=(symbol,))
        if not candlestick_data:
            return "HOLD"

        close_prices = [float(entry[2]) for entry in candlestick_data]

        # Calculate technical indicators
        rsi = calculate_rsi(close_prices)
        ema = calculate_ema(close_prices, 10)
        sma = calculate_sma(close_prices, 20)

        # Get sentiment score
        sentiment_data = get_combined_sentiment(symbol.replace("-USDT", ""))

        # Extract numeric sentiment score
        if isinstance(sentiment_data, dict):
            sentiment_score = sentiment_data.get("score", 0.0)
        elif isinstance(sentiment_data, (int, float)):
            sentiment_score = float(sentiment_data)
        else:
            sentiment_score = 0.0

        # Ensure sentiment_score is a number
        if not isinstance(sentiment_score, (int, float)):
            sentiment_score = 0.0

        # Get strategy name
        market_condition = "sideways"  # Default market condition
        if (
            rsi is not None
            and ema is not None
            and sma is not None
            and sentiment_score is not None
        ):
            market_condition = (
                "oversold_bullish"
                if rsi < 30 and sentiment_score > 0
                else (
                    "overbought_bearish"
                    if rsi > 70 and sentiment_score < 0
                    else "uptrend" if ema > sma else "downtrend"
                )
            )

        strategy_name = choose_strategy(market_condition, [], symbol)

        # Get trade signal
        decision = ai_trade_signal(
            indicators={"rsi": rsi, "ema": ema, "sma": sma},
            sentiment_score=sentiment_score,
            strategy_name=strategy_name,
        )

        # Handle different return types from ai_trade_signal
        if isinstance(decision, dict):
            decision_value = decision.get("score", 0)
        elif isinstance(decision, (int, float)):
            decision_value = float(decision)
        else:
            decision_value = 0

        if decision_value > 2:
            return "BUY"
        elif decision_value < -2:
            return "SELL"
        else:
            return "HOLD"

    except Exception as e:
        logging.error(f"[Error] Could not get trade decision for {symbol}: {e}")
        logging.error(traceback.format_exc())
        return "HOLD"


def get_enhanced_trade_decision(symbol: str) -> EnhancedTradeDecision:
    """
    Enhanced trade decision with comprehensive AI analysis and caching.
    """
    start_time = time.time()

    # Check cache first
    cache_key = f"trade_decision_{symbol}"
    cached_decision = get_cached_data(cache_key)
    if cached_decision:
        logger.debug(f"✅ Cache hit for trade decision: {symbol}")
        return EnhancedTradeDecision(**cached_decision)

    try:
        # Fetch market data with retry logic
        candlestick_data = retry(fetch_kucoin_candlestick_data, args=(symbol,))
        if not candlestick_data:
            return _create_fallback_decision(symbol, "No market data available")

        # Extract price data
        close_prices = [float(entry[2]) for entry in candlestick_data]
        high_prices = [float(entry[3]) for entry in candlestick_data]
        low_prices = [float(entry[4]) for entry in candlestick_data]
        volumes = [float(entry[5]) for entry in candlestick_data]

        # Enhanced technical analysis
        indicators = _calculate_enhanced_indicators(
            close_prices, high_prices, low_prices, volumes
        )
        technical_score = _calculate_technical_score(indicators)

        # Enhanced sentiment analysis
        sentiment_data = get_combined_sentiment(symbol.replace("-USDT", ""))
        sentiment_score = _extract_sentiment_score(sentiment_data)

        # AI signal analysis
        ai_score = _get_ai_signal_score(symbol, indicators, sentiment_score)

        # Strategy selection
        market_condition = "neutral"
        if technical_score > 60:
            market_condition = "bullish"
        elif technical_score < 40:
            market_condition = "bearish"

        try:
            strategy = choose_strategy(market_condition, [], symbol)
        except Exception:
            strategy = "ai_enhanced"

        # Generate final decision with confidence
        decision, confidence, reasoning = _generate_enhanced_decision(
            technical_score, sentiment_score, ai_score, indicators
        )

        # Create enhanced decision object
        enhanced_decision = EnhancedTradeDecision(
            symbol=symbol,
            decision=decision,
            confidence=confidence,
            reasoning=reasoning,
            technical_score=technical_score,
            sentiment_score=sentiment_score,
            ai_score=ai_score,
            strategy=strategy,
            timestamp=time.time(),
            indicators=indicators,
        )

        # Cache the decision
        set_cached_data(
            cache_key, enhanced_decision.__dict__, CACHE_TTL_DECISIONS, "high"
        )

        processing_time = time.time() - start_time
        logger.info(
            f"✅ Enhanced decision for {symbol}: {decision} ({confidence:.2f}) in {processing_time:.2f}s"
        )

        return enhanced_decision

    except Exception as e:
        logger.error(f"❌ Error in enhanced trade decision for {symbol}: {e}")
        return _create_fallback_decision(symbol, f"Analysis error: {str(e)}")


def _calculate_enhanced_indicators(
    close_prices: List[float],
    high_prices: List[float],
    low_prices: List[float],
    volumes: List[float],
) -> Dict[str, Any]:
    """Calculate comprehensive technical indicators with caching."""
    try:
        indicators = {}

        # Basic indicators
        indicators["rsi"] = calculate_rsi(close_prices)
        indicators["sma_20"] = calculate_sma(close_prices, 20)
        indicators["ema_10"] = calculate_ema(close_prices, 10)
        indicators["ema_20"] = calculate_ema(close_prices, 20)

        # Advanced indicators
        macd_line, macd_signal = calculate_macd(close_prices)
        indicators["macd"] = macd_line
        indicators["macd_signal"] = macd_signal
        indicators["macd_histogram"] = macd_line - macd_signal

        # Bollinger Bands
        bb_middle, bb_upper, bb_lower = calculate_bollinger_bands(close_prices)
        indicators["bb_middle"] = bb_middle
        indicators["bb_upper"] = bb_upper
        indicators["bb_lower"] = bb_lower
        indicators["bb_position"] = (
            (close_prices[-1] - bb_lower) / (bb_upper - bb_lower)
            if bb_upper != bb_lower
            else 0.5
        )

        # Price momentum
        if len(close_prices) >= 5:
            indicators["price_momentum_5"] = (
                close_prices[-1] - close_prices[-5]
            ) / close_prices[-5]
        else:
            indicators["price_momentum_5"] = 0.0

        # Volume analysis
        if len(volumes) >= 20:
            avg_volume = sum(volumes[-20:]) / 20
            indicators["volume_ratio"] = (
                volumes[-1] / avg_volume if avg_volume > 0 else 1.0
            )
        else:
            indicators["volume_ratio"] = 1.0

        # Volatility
        if len(close_prices) >= 20:
            price_changes = [
                (close_prices[i] - close_prices[i - 1]) / close_prices[i - 1]
                for i in range(1, min(21, len(close_prices)))
            ]
            indicators["volatility"] = sum(
                abs(change) for change in price_changes
            ) / len(price_changes)
        else:
            indicators["volatility"] = 0.02  # Default 2%

        return indicators

    except Exception as e:
        logger.error(f"Error calculating enhanced indicators: {e}")
        return {
            "rsi": 50.0,
            "sma_20": close_prices[-1] if close_prices else 0.0,
            "ema_10": close_prices[-1] if close_prices else 0.0,
            "macd": 0.0,
            "macd_signal": 0.0,
            "bb_position": 0.5,
            "price_momentum_5": 0.0,
            "volume_ratio": 1.0,
            "volatility": 0.02,
        }


def _calculate_technical_score(indicators: Dict[str, Any]) -> float:
    """Calculate technical analysis score (0-100)."""
    try:
        score = 50.0  # Neutral starting point

        # RSI analysis (30% weight)
        rsi = indicators.get("rsi", 50.0)
        if rsi < 30:
            score += 15  # Oversold - bullish
        elif rsi > 70:
            score -= 15  # Overbought - bearish
        elif 40 <= rsi <= 60:
            score += 5  # Neutral zone - slightly positive

        # Moving average analysis (25% weight)
        ema_10 = indicators.get("ema_10", 0)
        ema_20 = indicators.get("ema_20", 0)
        if ema_10 > ema_20:
            score += 12.5  # Uptrend
        else:
            score -= 12.5  # Downtrend

        # MACD analysis (20% weight)
        macd = indicators.get("macd", 0)
        macd_signal = indicators.get("macd_signal", 0)
        if macd > macd_signal:
            score += 10  # Bullish momentum
        else:
            score -= 10  # Bearish momentum

        # Bollinger Bands analysis (15% weight)
        bb_position = indicators.get("bb_position", 0.5)
        if bb_position < 0.2:
            score += 7.5  # Near lower band - potential bounce
        elif bb_position > 0.8:
            score -= 7.5  # Near upper band - potential reversal

        # Volume confirmation (10% weight)
        volume_ratio = indicators.get("volume_ratio", 1.0)
        if volume_ratio > 1.5:
            score += 5  # High volume confirmation
        elif volume_ratio < 0.5:
            score -= 2.5  # Low volume - less reliable

        return max(0.0, min(100.0, score))

    except Exception as e:
        logger.error(f"Error calculating technical score: {e}")
        return 50.0


def _extract_sentiment_score(sentiment_data: Any) -> float:
    """Extract numeric sentiment score from various data formats."""
    try:
        if isinstance(sentiment_data, dict):
            return float(sentiment_data.get("score", 0.0))
        elif isinstance(sentiment_data, (int, float)):
            return float(sentiment_data)
        else:
            return 0.0
    except Exception:
        return 0.0


def _get_ai_signal_score(
    symbol: str, indicators: Dict[str, Any], sentiment_score: float
) -> float:
    """Get AI signal score with fallback."""
    try:
        # Try to get AI signal score
        ai_signal_result = ai_trade_signal(symbol, indicators, sentiment_score)
        if isinstance(ai_signal_result, dict):
            return float(ai_signal_result.get("score", 50.0))
        elif isinstance(ai_signal_result, (int, float)):
            return float(ai_signal_result)
        else:
            return 50.0
    except Exception as e:
        logger.warning(f"AI signal score failed for {symbol}: {e}")
        return 50.0


def _generate_enhanced_decision(
    technical_score: float,
    sentiment_score: float,
    ai_score: float,
    indicators: Dict[str, Any],
) -> tuple[str, float, str]:
    """Generate final trading decision with confidence and reasoning."""
    try:
        # Weighted scoring (technical 40%, AI 35%, sentiment 25%)
        composite_score = (
            (technical_score * 0.4) + (ai_score * 0.35) + (sentiment_score * 25)
        )

        # Decision thresholds
        if composite_score >= 65:
            decision = "BUY"
            confidence = min(0.95, (composite_score - 50) / 50)
        elif composite_score <= 35:
            decision = "SELL"
            confidence = min(0.95, (50 - composite_score) / 50)
        else:
            decision = "HOLD"
            confidence = 0.5 + abs(composite_score - 50) / 100

        # Generate reasoning
        reasoning_parts = []
        if technical_score > 60:
            reasoning_parts.append("Strong technical signals")
        elif technical_score < 40:
            reasoning_parts.append("Weak technical signals")

        if ai_score > 60:
            reasoning_parts.append("Positive AI analysis")
        elif ai_score < 40:
            reasoning_parts.append("Negative AI analysis")

        if sentiment_score > 0.6:
            reasoning_parts.append("Bullish sentiment")
        elif sentiment_score < 0.4:
            reasoning_parts.append("Bearish sentiment")

        # Add specific indicator insights
        rsi = indicators.get("rsi", 50)
        if rsi < 30:
            reasoning_parts.append("Oversold conditions")
        elif rsi > 70:
            reasoning_parts.append("Overbought conditions")

        reasoning = (
            "; ".join(reasoning_parts)
            if reasoning_parts
            else "Neutral market conditions"
        )

        return decision, confidence, reasoning

    except Exception as e:
        logger.error(f"Error generating enhanced decision: {e}")
        return "HOLD", 0.5, "Decision generation error"


def _create_fallback_decision(symbol: str, reason: str) -> EnhancedTradeDecision:
    """Create fallback decision when analysis fails."""
    return EnhancedTradeDecision(
        symbol=symbol,
        decision="HOLD",
        confidence=0.0,
        reasoning=reason,
        technical_score=50.0,
        sentiment_score=0.0,
        ai_score=50.0,
        strategy="fallback",
        timestamp=time.time(),
        indicators={},
    )


async def get_batch_trade_decisions(symbols: List[str]) -> List[EnhancedTradeDecision]:
    """Get trade decisions for multiple symbols in parallel."""
    try:
        with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_DECISIONS) as executor:
            futures = [
                executor.submit(get_enhanced_trade_decision, symbol)
                for symbol in symbols
            ]
            results = []

            for future in as_completed(futures, timeout=30):
                try:
                    result = future.result(timeout=10)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Batch decision failed: {e}")

            return results

    except Exception as e:
        logger.error(f"Batch trade decisions failed: {e}")
        return [
            _create_fallback_decision(symbol, "Batch processing error")
            for symbol in symbols
        ]
