#!/usr/bin/env python3
"""
KuCoin Transaction Tracker
Fetches real transaction data from KuCoin API for live trades display
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    from kucoin.client import Client  # type: ignore
except ImportError:
    try:
        from kucoin import Client
    except ImportError:
        print("KuCoin client not available - install with: pip install kucoin-python")
        Client = None
from config import (
    KUCOIN_API_KEY,
    KUCOIN_API_SECRET,
    KUCOIN_API_PASSPHRASE,
    KUCOIN_SANDBOX,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KuCoinTransactionTracker:
    """Track and fetch real KuCoin transactions"""

    def __init__(self):
        """Initialize KuCoin client"""
        try:
            if Client is None:
                print("KuCoin client not available")
                self.client = None
                return

            self.client = Client(
                api_key=KUCOIN_API_KEY,
                api_secret=KUCOIN_API_SECRET,
                passphrase=KUCOIN_API_PASSPHRASE,
                sandbox=KUCOIN_SANDBOX,
            )
            logger.info("✅ KuCoin client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize KuCoin client: {e}")
            self.client = None

    def get_account_transactions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent account transactions (deposits, withdrawals, trades)

        Args:
            limit: Number of transactions to fetch (max 500)

        Returns:
            List of transaction dictionaries
        """
        if not self.client:
            logger.error("KuCoin client not initialized")
            return []

        try:
            # Get account activity (all account activities) - using correct method name
            try:
                ledger = self.client.get_account_activity(
                    currency=None,  # All currencies
                    direction=None,  # Both in and out
                    biz_type=None,  # All business types
                    start_at=None,
                    end_at=None,
                    page_size=min(limit, 500),
                )
            except Exception as e:
                # Fallback if method doesn't work - use accounts list
                logger.warning(f"get_account_activity failed: {e}, using accounts list")
                accounts = self.client.get_accounts()
                ledger = {"items": []}
                for account in accounts:
                    if float(account.get("balance", 0)) > 0:
                        ledger["items"].append(
                            {
                                "id": account.get("id"),
                                "currency": account.get("currency"),
                                "amount": account.get("balance"),
                                "fee": "0",
                                "balance": account.get("balance"),
                                "accountType": account.get("type"),
                                "bizType": "TRADE",
                                "direction": "in",
                                "createdAt": str(
                                    int(datetime.now().timestamp() * 1000)
                                ),
                                "context": {},
                            }
                        )

            transactions = []
            if ledger and "items" in ledger:
                for item in ledger["items"]:
                    transaction = {
                        "id": item.get("id"),
                        "currency": item.get("currency"),
                        "amount": float(item.get("amount", 0)),
                        "fee": float(item.get("fee", 0)),
                        "balance": float(item.get("balance", 0)),
                        "account_type": item.get("accountType"),
                        "biz_type": item.get("bizType"),
                        "direction": item.get("direction"),
                        "created_at": item.get("createdAt"),
                        "context": item.get("context", {}),
                        "timestamp": datetime.fromtimestamp(
                            int(item.get("createdAt", 0)) / 1000
                        ).isoformat(),
                    }
                    transactions.append(transaction)

            logger.info(f"✅ Fetched {len(transactions)} account transactions")
            return transactions

        except Exception as e:
            logger.error(f"❌ Failed to fetch account transactions: {e}")
            return []

    def get_trade_history(
        self, symbol: Optional[str] = None, limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get recent trade history

        Args:
            symbol: Trading pair symbol (e.g., 'BTC-USDT'), None for all
            limit: Number of trades to fetch

        Returns:
            List of trade dictionaries
        """
        if not self.client:
            logger.error("KuCoin client not initialized")
            return []

        try:
            # Get fills (completed trades)
            fills = self.client.get_fills(
                trade_type="TRADE",  # Only actual trades
                symbol=symbol,
                side=None,  # Both buy and sell
                type=None,  # All order types
                start_at=None,
                end_at=None,
                page_size=min(limit, 500),
            )

            trades = []
            if fills and "items" in fills:
                for fill in fills["items"]:
                    trade = {
                        "trade_id": fill.get("tradeId"),
                        "order_id": fill.get("orderId"),
                        "symbol": fill.get("symbol"),
                        "side": fill.get("side").upper(),  # BUY or SELL
                        "size": float(fill.get("size", 0)),
                        "price": float(fill.get("price", 0)),
                        "funds": float(fill.get("funds", 0)),
                        "fee": float(fill.get("fee", 0)),
                        "fee_currency": fill.get("feeCurrency"),
                        "type": fill.get("type"),
                        "created_at": fill.get("createdAt"),
                        "timestamp": datetime.fromtimestamp(
                            int(fill.get("createdAt", 0)) / 1000
                        ).isoformat(),
                        "liquidity": fill.get("liquidity"),
                        "force_hold": fill.get("forceHold", False),
                    }
                    trades.append(trade)

            logger.info(f"✅ Fetched {len(trades)} trade history records")
            return trades

        except Exception as e:
            logger.error(f"❌ Failed to fetch trade history: {e}")
            return []

    def get_order_history(
        self, symbol: Optional[str] = None, limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get recent order history

        Args:
            symbol: Trading pair symbol, None for all
            limit: Number of orders to fetch

        Returns:
            List of order dictionaries
        """
        if not self.client:
            logger.error("KuCoin client not initialized")
            return []

        try:
            # Get recent orders
            orders = self.client.get_orders(
                status="done",  # Only completed orders
                symbol=symbol,
                side=None,  # Both buy and sell
                type=None,  # All order types
                start_at=None,
                end_at=None,
                page_size=min(limit, 500),
            )

            order_list = []
            if orders and "items" in orders:
                for order in orders["items"]:
                    order_data = {
                        "order_id": order.get("id"),
                        "symbol": order.get("symbol"),
                        "side": order.get("side").upper(),
                        "type": order.get("type"),
                        "size": float(order.get("size", 0)),
                        "price": float(order.get("price", 0)),
                        "funds": float(order.get("funds", 0)),
                        "deal_size": float(order.get("dealSize", 0)),
                        "deal_funds": float(order.get("dealFunds", 0)),
                        "fee": float(order.get("fee", 0)),
                        "fee_currency": order.get("feeCurrency"),
                        "status": order.get("isActive"),
                        "created_at": order.get("createdAt"),
                        "timestamp": datetime.fromtimestamp(
                            int(order.get("createdAt", 0)) / 1000
                        ).isoformat(),
                        "stop_type": order.get("stop"),
                        "time_in_force": order.get("timeInForce"),
                    }
                    order_list.append(order_data)

            logger.info(f"✅ Fetched {len(order_list)} order history records")
            return order_list

        except Exception as e:
            logger.error(f"❌ Failed to fetch order history: {e}")
            return []

    def get_live_trades_data(self, limit: int = 50) -> Dict[str, Any]:
        """
        Get comprehensive live trades data combining all transaction types

        Args:
            limit: Number of records to fetch from each source

        Returns:
            Dictionary with live trades data
        """
        logger.info(f"🔍 Fetching live trades data from KuCoin (limit: {limit})")

        # Fetch data from multiple sources
        trade_history = self.get_trade_history(limit=limit)
        order_history = self.get_order_history(limit=limit)
        account_transactions = self.get_account_transactions(limit=limit)

        # Combine and format for frontend
        all_trades = []

        # Add trade history (most important)
        for trade in trade_history:
            formatted_trade = {
                "timestamp": trade["timestamp"],
                "symbol": trade["symbol"],
                "action": trade["side"],
                "quantity": trade["size"],
                "price": trade["price"],
                "total": trade["funds"],
                "status": "COMPLETED",
                "fees": trade["fee"],
                "trade_id": trade["trade_id"],
                "source": "kucoin_fills",
            }
            all_trades.append(formatted_trade)

        # Add order history
        for order in order_history:
            if order["deal_size"] > 0:  # Only orders that were actually filled
                formatted_trade = {
                    "timestamp": order["timestamp"],
                    "symbol": order["symbol"],
                    "action": order["side"],
                    "quantity": order["deal_size"],
                    "price": order["price"],
                    "total": order["deal_funds"],
                    "status": "COMPLETED",
                    "fees": order["fee"],
                    "trade_id": order["order_id"],
                    "source": "kucoin_orders",
                }
                all_trades.append(formatted_trade)

        # Sort by timestamp (newest first)
        all_trades.sort(key=lambda x: x["timestamp"], reverse=True)

        # Remove duplicates and limit results
        seen_trade_ids = set()
        unique_trades = []
        for trade in all_trades:
            if trade["trade_id"] not in seen_trade_ids:
                seen_trade_ids.add(trade["trade_id"])
                unique_trades.append(trade)
                if len(unique_trades) >= limit:
                    break

        result = {
            "trades": unique_trades,
            "last_updated": datetime.now().isoformat(),
            "total_trades": len(unique_trades),
            "source": "kucoin_api",
            "metadata": {
                "trade_history_count": len(trade_history),
                "order_history_count": len(order_history),
                "account_transactions_count": len(account_transactions),
            },
        }

        logger.info(f"✅ Compiled {len(unique_trades)} unique live trades from KuCoin")
        return result


# Global instance
kucoin_tracker = KuCoinTransactionTracker()


def get_kucoin_live_trades(limit: int = 50) -> Dict[str, Any]:
    """
    Main function to get live trades from KuCoin

    Args:
        limit: Number of trades to fetch

    Returns:
        Dictionary with live trades data
    """
    return kucoin_tracker.get_live_trades_data(limit)


if __name__ == "__main__":
    # Test the tracker
    print("🧪 Testing KuCoin Transaction Tracker")
    print("=" * 50)

    tracker = KuCoinTransactionTracker()

    # Test trade history
    trades = tracker.get_trade_history(limit=10)
    print(f"Trade History: {len(trades)} records")

    # Test live trades data
    live_data = tracker.get_live_trades_data(limit=20)
    print(f"Live Trades: {live_data['total_trades']} trades")
    print(f"Sources: {live_data['metadata']}")
