#!/usr/bin/env python3
"""
Simple server startup script to avoid import-time optimizations
"""

import uvicorn
import os

if __name__ == "__main__":
    # Get port from environment or default to 3005
    port = int(os.getenv("PORT", 3005))
    host = os.getenv("HOST", "0.0.0.0")
    
    print(f"🚀 Starting Alpha Predator Bot API server on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
