import re
import logging

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def clean_text(text: str) -> str:
    """
    Basic preprocessing to normalize text.
    - Lowercase
    - Strip whitespace
    - Remove URLs, punctuation, emojis
    """
    if not text:
        return ""
    text = text.lower().strip()
    text = re.sub(r"http\S+", "", text)  # remove URLs
    text = re.sub(r"[^\w\s]", "", text)  # remove punctuation
    text = re.sub(r"\s+", " ", text)  # normalize spaces
    text = re.sub(r"[\U00010000-\U0010ffff]", "", text)  # remove emojis
    return text.strip()

    return text.strip()


def is_spam_or_fud(content: str) -> bool:
    """Basic spam/fud detection using keyword rules."""
    if not content:
        return False
    content = content.lower()
    patterns = [
        r"join now", r"giveaway", r"airdrops?", r"1000x", r"moon soon", r"next gem",
        r"not financial advice", r"buy now", r"get rich", r"shill", r"telegram", r"discord.gg", r"free tokens",
        r"quick gains", r"pump group", r"buy alerts?", r"\$\w+\s+(up\s+)?\d{2,}x", r"\U0001f525", r"\U0001f680"
    ]
    count = sum(1 for p in patterns if re.search(p, content))
    return count / len(patterns) > 0.25

def is_relevant_news(text: str, min_length: int = 20) -> bool:

    """
    Determines if a piece of news is relevant and not spam or too short.

    Args:
        text (str): News text to evaluate.
        min_length (int): Minimum character length to consider.

    Returns:
        bool: True if the news is relevant and safe, False otherwise.
    """
    if not text:
        logger.debug("Empty news text received.")
        return False

    cleaned = clean_text(text)
    if len(cleaned) < min_length:
        logger.debug(f"Irrelevant news: too short after cleaning: {cleaned}")
        return False

    if is_spam_or_fud(cleaned):
        logger.info(f"Filtered spam/FUD news: {cleaned}")
        return False

    return True