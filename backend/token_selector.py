# SDK-based imports for optimal performance
from kucoin_sdk_migration import kucoin_sdk
from coingecko_sdk_migration import coingecko_sdk
from ai_clients_sdk_migration import get_ai_clients_status

import json
import logging
import os
import asyncio
import time
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from news_sentiment_fallback import get_combined_sentiment_score
from price_fetcher import get_price
from config import COINGECKO_API_KEY, TOKENMETRICS_API_KEY
from kucoin_data import fetch_kucoin_spike_tokens
from cache import get_cached_data, set_cached_data
from tokenmetrics_client import get_tokenmetrics_batch_data, TokenMetricsData

# 🎯 ALPHA PREDATOR TRADING FLOW CONFIGURATION
PARALLEL_WORKERS = 20  # 🚀 Increased for faster processing
BATCH_SIZE = 50
CACHE_TTL_TOKENS = 30  # 30 seconds for near real-time data
CACHE_TTL_PRICES = 10  # 10 seconds for live price updates
MAX_TOKENS_TO_ANALYZE = 200
API_RATE_LIMIT_DELAY = 0.1  # 100ms between API calls

# 📊 TRADING FLOW PARAMETERS
KUCOIN_TOP_TOKENS = 100  # Fetch top 100 KuCoin tokens
SELECTED_TOP_TOKENS = 50  # Select top 50 based on comprehensive analysis
MIN_VOLUME_THRESHOLD = 10000  # Minimum 24h volume for consideration (lowered)
MIN_MARKET_CAP = 1000000  # Minimum market cap for stability

logger = logging.getLogger(__name__)


@dataclass
class EnhancedTokenScore:
    """Enhanced token scoring with comprehensive metrics"""

    symbol: str
    score: float
    price: float
    volume_24h: float
    market_cap: float
    price_change_24h: float
    sentiment_score: float
    volume_ratio: float
    volatility: float
    trading_readiness: float
    reasoning: str
    timestamp: float


# Map of source credibility weights for news sentiment
sourceCredibilityMap = {
    "coindesk": 0.9,
    "cointelegraph": 0.9,
    "reddit": 0.6,
    "twitter": 0.5,
    "github": 0.8,
    "blog": 0.7,
    "unknown": 0.4,
}

# === Enhanced Constants & Setup for High-Frequency Trading ===
KUCOIN_URL = "https://api.kucoin.com/api/v1/market/allTickers"
COINGECKO_URL = "https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd"
CACHE_FILE = "backend/data/token_cache.json"
DISCOVER_FILE = "backend/data/discover_tokens.json"
DISCOVER_API_FILE = "backend/data/api_discover_response.json"
LOG_FILE = "backend/logs/token_selector.log"

# Enhanced configuration for high-frequency trading
TOP_LIMIT = 50  # Increased for more trading opportunities
VOLUME_THRESHOLD = 10_000  # Further lowered to allow more tokens (was 100k)
HIGH_VOLUME_THRESHOLD = 1_000_000  # High volume bonus threshold (lowered)
MIN_PRICE_CHANGE = -0.50  # Allow more price movement for opportunities
MAX_PRICE_CHANGE = 2.00  # Allow more upside for growth tokens
OPTIMAL_VOLATILITY_RANGE = (0.02, 0.20)  # 2-20% volatility range

# Enhanced scoring weights optimized for high-frequency trading
SCORING_WEIGHTS = {
    "volume": 0.35,  # Highest weight for liquidity
    "price_momentum": 0.25,  # Price change momentum
    "sentiment": 0.20,  # Market sentiment
    "market_cap": 0.12,  # Market cap stability
    "volatility": 0.08,  # Price volatility for opportunities
}

# Performance optimization settings
PARALLEL_PROCESSING = True
MAX_WORKERS = 8
CACHE_TTL = 180  # 3 minutes cache for faster updates

# Ensure logging directory exists
os.makedirs("backend/logs", exist_ok=True)
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)


def is_valid_token(symbol: str, token: Dict) -> bool:
    """Enhanced token validation with multiple criteria for high-frequency trading."""
    try:
        # Basic symbol validation
        if not symbol.endswith("-USDT"):
            return False

        # Skip leveraged and derivative tokens
        skip_patterns = [
            "3L-",
            "3S-",
            "UP-",
            "DOWN-",
            "BEAR",
            "BULL",
            "LEVERAGED",
            "SHORT",
            "LONG",
            "2X-",
            "3X-",
            "HALF",
            "HEDGE",
            "INVERSE",
        ]
        if any(pattern in symbol for pattern in skip_patterns):
            return False

        # Volume validation
        volume = float(token.get("volValue", 0))
        if volume < VOLUME_THRESHOLD:
            return False

        # Price validation
        price = float(token.get("last", 0))
        if price <= 0:
            return False

        # Price change validation (avoid major dumps and extreme pumps)
        change_rate = float(token.get("changeRate", 0))
        if change_rate < MIN_PRICE_CHANGE or change_rate > MAX_PRICE_CHANGE:
            return False

        return True

    except (ValueError, TypeError):
        return False


def calculate_token_metrics(token: Dict) -> Dict:
    """Calculate enhanced metrics for token scoring."""
    try:
        symbol = token.get("symbol", "").upper()
        volume = float(token.get("volValue", 0))
        change_rate = float(token.get("changeRate", 0))
        price = float(token.get("last", 0))

        # Calculate volatility estimate
        high = float(token.get("high", price))
        low = float(token.get("low", price))
        volatility = (high - low) / price if price > 0 else 0

        # Volume scoring
        volume_score = min(volume / HIGH_VOLUME_THRESHOLD, 2.0)

        # Momentum scoring
        momentum_score = abs(change_rate) * 10

        # Volatility scoring (optimal range gets higher score)
        vol_min, vol_max = OPTIMAL_VOLATILITY_RANGE
        if vol_min <= volatility <= vol_max:
            volatility_score = 1.0
        elif volatility < vol_min:
            volatility_score = volatility / vol_min
        else:
            volatility_score = max(0.1, vol_max / volatility)

        return {
            "symbol": symbol,
            "last": price,
            "volume": volume,
            "change_rate": change_rate,
            "volatility": volatility,
            "volume_score": volume_score,
            "momentum_score": momentum_score,
            "volatility_score": volatility_score,
            "high": high,
            "low": low,
        }

    except (ValueError, TypeError) as e:
        logging.debug(f"Error calculating metrics for token: {e}")
        return {}


def fetch_kucoin_token_data() -> List[Dict]:
    """
    Enhanced KuCoin token fetching with improved filtering and parallel processing.

    Returns:
        List of filtered token dictionaries with enhanced metrics.
    """
    try:
        logging.info("Fetching all KuCoin tokens using SDK...")
        # Use KuCoin SDK instead of custom HTTP client
        raw_tokens = kucoin_sdk.get_all_tickers()

        if not raw_tokens:
            logging.warning("No tokens received from KuCoin SDK")
            return []

        clean_tokens: List[Dict] = []
        processed_count = 0

        # Process tokens with enhanced filtering
        for token in raw_tokens:
            symbol = token.get("symbol", "").upper()
            processed_count += 1

            # Enhanced validation
            if not is_valid_token(symbol, token):
                continue

            # Skip price fetching for now to avoid async issues
            # Price will be fetched later in the pipeline
            price = float(token.get("last", 0))
            if price is None or price <= 0:
                continue

            # Calculate enhanced metrics
            enhanced_token = calculate_token_metrics(token)
            if not enhanced_token:
                continue

            # Update original token with enhanced data
            token.update(enhanced_token)
            clean_tokens.append(token)

            if processed_count % 100 == 0:
                logging.info(
                    f"Processed {processed_count} tokens, selected {len(clean_tokens)}"
                )

            # Dynamic limit based on quality - fetch more for better selection
            if len(clean_tokens) >= TOP_LIMIT * 2:
                break

        logging.info(
            f"Enhanced token selection: {len(clean_tokens)} valid tokens from {processed_count} processed"
        )

        # Sort by volume for initial quality filter
        clean_tokens.sort(key=lambda x: x.get("volume", 0), reverse=True)

        return clean_tokens

    except Exception as e:
        logging.error(f"KuCoin token fetch failed: {e}", exc_info=True)
        return []


def fetch_market_cap_data() -> List[Dict]:
    """
    Fetches market cap data from CoinGecko with optional API key.

    Returns:
        List of market cap data dictionaries.
    """
    try:
        logging.info("Fetching CoinGecko market cap data using SDK...")
        # Use CoinGecko SDK instead of custom HTTP client
        markets_data = coingecko_sdk.get_coins_markets(limit=250)
        return markets_data
    except Exception as e:
        logging.error(f"CoinGecko fetch failed: {e}", exc_info=True)
        return []


def build_sentiment_scores(token_list: List[Dict]) -> Dict[str, float]:
    """
    Builds a dictionary of sentiment scores keyed by token symbol.

    Args:
        token_list: List of token dictionaries.

    Returns:
        Dictionary mapping token symbols to sentiment scores.
    """
    logging.info("Building sentiment scores...")
    scores: Dict[str, float] = {}
    for idx, token in enumerate(token_list):
        symbol = token.get("symbol", "").upper()
        try:
            base_token = symbol.replace("-USDT", "")
            sentiment_result = get_combined_sentiment_score(base_token)
            # Handle both dict and float return types
            if isinstance(sentiment_result, dict):
                scores[symbol] = sentiment_result.get("score", 0.0)
            else:
                scores[symbol] = (
                    float(sentiment_result) if sentiment_result is not None else 0.0
                )
            logging.info(f"  → [{idx+1}/{len(token_list)}] {symbol}: {scores[symbol]}")
        except Exception as e:
            logging.warning(f"Sentiment fetch failed for {symbol}: {e}", exc_info=True)
            scores[symbol] = 0
    return scores


def score_token(
    token: Dict,
    market_caps: List[Dict],
    sentiment_scores: Dict[str, float],
    spike_ratios: Dict[str, float],
) -> Optional[Dict]:
    """
    Enhanced composite scoring for high-frequency trading optimization.

    Args:
        token: Token dictionary with enhanced metrics.
        market_caps: List of market cap data dictionaries.
        sentiment_scores: Dictionary of sentiment scores keyed by symbol.
        spike_ratios: Dictionary of volume spike ratios keyed by symbol.

    Returns:
        Dictionary with enhanced scoring metrics, or None on failure.
    """
    symbol = ""
    try:
        symbol = token.get("symbol", "").upper()
        volume = float(token.get("volume", token.get("volValue", 0)))
        change_rate = float(token.get("change_rate", token.get("changeRate", 0)))
        last_price = float(token.get("last", 0))
        volatility = float(token.get("volatility", 0))

        # Get pre-calculated scores
        volume_score = float(token.get("volume_score", 0))
        momentum_score = float(token.get("momentum_score", 0))
        volatility_score = float(token.get("volatility_score", 0))

        base_symbol = symbol.replace("-USDT", "")
        mc_score = 0

        # Market cap scoring with enhanced logic
        for mc in market_caps:
            if isinstance(mc, dict) and mc.get("symbol", "").upper() == base_symbol:
                try:
                    market_cap = float(mc.get("market_cap", 0))
                    # Logarithmic scaling for market cap to avoid bias toward large caps
                    if market_cap > 0:
                        mc_score = min(2.0, (market_cap / 1e9) ** 0.5)
                except Exception:
                    mc_score = 0
                break

        # Enhanced sentiment scoring
        sentiment_dict = sentiment_scores.get(symbol, {"score": 0})
        sentiment = (
            sentiment_dict.get("score", 0) if isinstance(sentiment_dict, dict) else 0
        )
        # Normalize sentiment to 0-2 range for better scoring
        sentiment_normalized = max(0, min(2.0, sentiment * 2))

        # Enhanced spike ratio scoring
        spike_ratio = float(spike_ratios.get(symbol, 1.0))
        spike_score = min(2.0, max(0, (spike_ratio - 1.0) * 2))  # Amplify spike impact

        # Calculate weighted composite score using enhanced weights
        score = (
            volume_score * SCORING_WEIGHTS["volume"]
            + momentum_score * SCORING_WEIGHTS["price_momentum"]
            + sentiment_normalized * SCORING_WEIGHTS["sentiment"]
            + mc_score * SCORING_WEIGHTS["market_cap"]
            + volatility_score * SCORING_WEIGHTS["volatility"]
        )

        # Add spike bonus
        score += spike_score * 0.1

        # Quality multipliers for high-frequency trading
        quality_multiplier = 1.0

        # High volume bonus
        if volume > HIGH_VOLUME_THRESHOLD:
            quality_multiplier *= 1.2

        # Optimal volatility bonus
        vol_min, vol_max = OPTIMAL_VOLATILITY_RANGE
        if vol_min <= volatility <= vol_max:
            quality_multiplier *= 1.15

        # Positive momentum bonus
        if change_rate > 0.02:  # 2% positive change
            quality_multiplier *= 1.1

        final_score = score * quality_multiplier

        result = {
            "symbol": symbol,
            "price": last_price,
            "score": round(final_score, 4),
            "volume": volume,
            "sentiment": sentiment,
            "volatility": volatility,
            "change_rate": change_rate,
            "volume_score": volume_score,
            "momentum_score": momentum_score,
            "volatility_score": volatility_score,
            "market_cap_score": mc_score,
            "spike_score": spike_score,
            "quality_multiplier": quality_multiplier,
        }

        logging.info(
            f"Enhanced scoring: {symbol} | Vol: {volume:.0f} | Change: {change_rate:.3f} | "
            f"Volatility: {volatility:.3f} | Sentiment: {sentiment:.2f} | Final Score: {final_score:.4f}"
        )
        return result

    except Exception as e:
        logging.error(f"Enhanced scoring failed for {symbol}: {e}", exc_info=True)
        return None


def save_token_cache(top_tokens: List[Dict]) -> None:
    """
    Saves top token list to JSON cache files.

    Args:
        top_tokens: List of top token dictionaries.
    """
    try:
        os.makedirs(os.path.dirname(CACHE_FILE), exist_ok=True)
        with open(CACHE_FILE, "w") as f:
            json.dump(top_tokens, f, indent=4)
        logging.info(f"Token cache saved to {CACHE_FILE}")
        with open(DISCOVER_FILE, "w") as f:
            json.dump(top_tokens, f, indent=2)
        logging.info(f"🎯 Top {len(top_tokens)} tokens written to {DISCOVER_FILE}")
    except Exception as e:
        logging.error(f"Failed to save token cache: {e}", exc_info=True)


from typing import Any
from cache import get_cached_data, set_cached_data


async def analyze_token_enhanced(
    token_data: Dict[str, Any],
) -> Optional[EnhancedTokenScore]:
    """Enhanced token analysis with comprehensive scoring."""
    try:
        symbol = token_data.get("symbol", "").upper()
        if not symbol:
            return None

        # Basic metrics
        price = float(token_data.get("last", 0))
        volume_24h = float(token_data.get("volValue", 0))
        price_change_24h = float(token_data.get("changeRate", 0))

        # Market cap estimation (simplified)
        market_cap = price * volume_24h * 100  # Rough estimation

        # Volume ratio analysis
        volume_ratio = await _calculate_volume_ratio(symbol, volume_24h)

        # Volatility calculation
        volatility = abs(price_change_24h) + (volume_ratio - 1.0) * 0.1

        # Sentiment analysis
        sentiment_score = await _get_sentiment_score_async(symbol)

        # Calculate comprehensive score
        score = _calculate_enhanced_score(
            price,
            volume_24h,
            market_cap,
            price_change_24h,
            sentiment_score,
            volume_ratio,
            volatility,
        )

        # Trading readiness assessment
        trading_readiness = _assess_trading_readiness(
            volume_24h, volatility, sentiment_score, price_change_24h
        )

        # Generate reasoning
        reasoning = _generate_token_reasoning(
            score, sentiment_score, volume_ratio, volatility, price_change_24h
        )

        return EnhancedTokenScore(
            symbol=symbol,
            score=score,
            price=price,
            volume_24h=volume_24h,
            market_cap=market_cap,
            price_change_24h=price_change_24h,
            sentiment_score=sentiment_score,
            volume_ratio=volume_ratio,
            volatility=volatility,
            trading_readiness=trading_readiness,
            reasoning=reasoning,
            timestamp=time.time(),
        )

    except Exception as e:
        logger.error(
            f"Error analyzing token {token_data.get('symbol', 'UNKNOWN')}: {e}"
        )
        return None


async def enhance_tokens_with_tokenmetrics(tokens: List[Dict]) -> List[Dict]:
    """Enhance token data with TokenMetrics information."""
    try:
        if not TOKENMETRICS_API_KEY:
            logger.info("TokenMetrics API key not configured, skipping enhancement")
            return tokens

        logger.info(f"🚀 Enhancing {len(tokens)} tokens with TokenMetrics data...")

        # Extract symbols for batch processing
        symbols = [token.get("symbol", "") for token in tokens if token.get("symbol")]

        # Get TokenMetrics data in batch
        tokenmetrics_data = await get_tokenmetrics_batch_data(symbols)

        # Create lookup dictionary
        tm_lookup = {tm.symbol: tm for tm in tokenmetrics_data}

        # Enhance tokens with TokenMetrics data
        enhanced_tokens = []
        for token in tokens:
            symbol = token.get("symbol", "")
            tm_data = tm_lookup.get(symbol)

            if tm_data:
                # Add TokenMetrics enhancements
                token["tm_grade"] = tm_data.tm_grade
                token["tm_score"] = tm_data.tm_score
                token["liquidity_score"] = tm_data.liquidity_score
                token["social_sentiment"] = tm_data.social_sentiment
                token["volatility"] = tm_data.volatility
                token["technical_analysis"] = tm_data.technical_analysis

                # Boost score based on TokenMetrics grade
                grade_multiplier = {
                    "A": 1.5,
                    "B": 1.2,
                    "C": 1.0,
                    "D": 0.8,
                    "F": 0.5,
                }.get(tm_data.tm_grade, 1.0)
                token["score"] = token.get("score", 0) * grade_multiplier

                logger.debug(
                    f"✅ Enhanced {symbol} with TokenMetrics (Grade: {tm_data.tm_grade}, Score: {tm_data.tm_score})"
                )

            enhanced_tokens.append(token)

        logger.info(
            f"✅ TokenMetrics enhancement complete: {len(tokenmetrics_data)} tokens enhanced"
        )
        return enhanced_tokens

    except Exception as e:
        logger.error(f"TokenMetrics enhancement failed: {e}")
        return tokens


async def get_enhanced_token_list_parallel(limit: int = 50) -> List[EnhancedTokenScore]:
    """Get enhanced token list with parallel processing - LIVE DATA ONLY."""
    try:
        # 🔴 LIVE MODE: Skip cache for real-time data
        logger.info(f"🔴 LIVE MODE: Fetching fresh token data for {limit} tokens")

        # Add small delay to respect API limits
        await asyncio.sleep(API_RATE_LIMIT_DELAY)

        # Fetch raw token data
        raw_tokens = await _fetch_raw_token_data()
        if not raw_tokens:
            logger.warning("No raw token data available")
            return []

        # Filter and limit tokens
        filtered_tokens = [
            token
            for token in raw_tokens
            if is_valid_token(token.get("symbol", ""), token)
        ][:MAX_TOKENS_TO_ANALYZE]

        # Parallel analysis
        logger.info(f"🔄 Analyzing {len(filtered_tokens)} tokens in parallel...")

        with ThreadPoolExecutor(max_workers=PARALLEL_WORKERS) as executor:
            loop = asyncio.get_event_loop()
            tasks = [
                loop.run_in_executor(
                    executor, lambda t=token: asyncio.run(analyze_token_enhanced(t))
                )
                for token in filtered_tokens
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter successful results
        enhanced_tokens = [
            result
            for result in results
            if isinstance(result, EnhancedTokenScore) and result is not None
        ]

        # Sort by score and trading readiness
        enhanced_tokens.sort(
            key=lambda x: (x.score * 0.7 + x.trading_readiness * 0.3), reverse=True
        )

        # Limit results
        top_tokens = enhanced_tokens[:limit]

        # Cache results
        cache_key = f"enhanced_tokens_{limit}"
        cache_data = [token.__dict__ for token in top_tokens]
        set_cached_data(cache_key, cache_data, CACHE_TTL_TOKENS, "high")

        logger.info(
            f"✅ Enhanced token analysis complete: {len(top_tokens)} tokens selected"
        )
        return top_tokens

    except Exception as e:
        logger.error(f"❌ Enhanced token list generation failed: {e}")
        return []


def generate_top_token_list(limit: int = 50) -> List[Dict[str, Any]]:
    """
    Synchronous wrapper for async token generation.
    """
    try:
        import asyncio

        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, create a new task
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(
                    asyncio.run, _generate_top_token_list_async(limit)
                )
                return future.result()
        else:
            return asyncio.run(_generate_top_token_list_async(limit))
    except Exception as e:
        logging.error(f"Token generation failed: {e}")
        return []


async def alpha_predator_trading_flow() -> Dict[str, Any]:
    """
    🎯 ALPHA PREDATOR COMPLETE TRADING FLOW

    Flow:
    1. Fetch top 100 KuCoin tokens
    2. Select top 50 based on comprehensive data analysis
    3. News searching and sentiment analysis
    4. AI decision making (BUY/SELL/HOLD)
    5. Auto-trading with TP/SL strategies
    """
    try:
        logger.info("🚀 ALPHA PREDATOR TRADING FLOW INITIATED")

        # PHASE 1: TOKEN DISCOVERY (Top 100 KuCoin)
        logger.info("📊 PHASE 1: Fetching top 100 KuCoin tokens...")
        kucoin_tokens = await _fetch_kucoin_top_tokens(KUCOIN_TOP_TOKENS)
        logger.info(f"✅ Fetched {len(kucoin_tokens)} KuCoin tokens")

        # PHASE 2: TOKEN SELECTION (Top 50 based on data points)
        logger.info(
            "🔍 PHASE 2: Selecting top 50 tokens based on comprehensive analysis..."
        )
        selected_tokens = await _select_top_tokens_with_analysis(
            kucoin_tokens, SELECTED_TOP_TOKENS
        )
        logger.info(f"✅ Selected {len(selected_tokens)} tokens for trading analysis")

        # PHASE 3: NEWS & SENTIMENT ANALYSIS
        logger.info("🧠 PHASE 3: Performing news and sentiment analysis...")
        analyzed_tokens = await _perform_news_sentiment_analysis(selected_tokens)
        logger.info(
            f"✅ Completed sentiment analysis for {len(analyzed_tokens)} tokens"
        )

        # PHASE 4: AI DECISION MAKING
        logger.info("🤖 PHASE 4: AI decision making for trading actions...")
        trading_decisions = await _make_ai_trading_decisions(analyzed_tokens)
        logger.info(f"✅ Generated {len(trading_decisions)} trading decisions")

        # PHASE 5: AUTO-TRADING EXECUTION
        logger.info("⚡ PHASE 5: Executing auto-trading with TP/SL strategies...")
        execution_results = await _execute_auto_trading(trading_decisions)
        logger.info(f"✅ Executed {len(execution_results)} trading actions")

        return {
            "status": "success",
            "kucoin_tokens_fetched": len(kucoin_tokens),
            "tokens_selected": len(selected_tokens),
            "tokens_analyzed": len(analyzed_tokens),
            "trading_decisions": len(trading_decisions),
            "trades_executed": len(execution_results),
            "execution_results": execution_results,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"❌ Alpha Predator trading flow failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
        }


async def _fetch_kucoin_top_tokens(limit: int = 100) -> List[Dict[str, Any]]:
    """PHASE 1: Fetch top tokens from KuCoin exchange"""
    try:
        logger.info(f"📊 Fetching top {limit} tokens from KuCoin...")

        # Use existing KuCoin data fetcher
        kucoin_data = fetch_kucoin_spike_tokens()

        if not kucoin_data:
            logger.warning("⚠️ No KuCoin data available, using fallback")
            return []

        # Filter and sort by volume
        filtered_tokens = []
        for token in kucoin_data[:limit]:
            if token.get("volume", 0) > MIN_VOLUME_THRESHOLD and token.get(
                "symbol", ""
            ).endswith("-USDT"):
                filtered_tokens.append(
                    {
                        "symbol": token.get("symbol", ""),
                        "price": float(token.get("price", 0)),
                        "volume": float(token.get("volume", 0)),
                        "change": float(token.get("change", 0)),
                        "market_cap": float(token.get("market_cap", 0)),
                        "source": "kucoin",
                        "phase": "discovery",
                    }
                )

        # Sort by volume (highest first)
        filtered_tokens.sort(key=lambda x: x.get("volume", 0), reverse=True)

        logger.info(
            f"✅ KuCoin Phase 1 complete: {len(filtered_tokens)} tokens fetched"
        )
        return filtered_tokens[:limit]

    except Exception as e:
        logger.error(f"❌ KuCoin token fetching failed: {e}")
        return []


async def _select_top_tokens_with_analysis(
    tokens: List[Dict], limit: int = 50
) -> List[Dict[str, Any]]:
    """PHASE 2: Select top tokens based on comprehensive data analysis"""
    try:
        logger.info(f"🔍 Analyzing {len(tokens)} tokens for selection...")

        # Enhance tokens with additional data
        enhanced_tokens = []

        for token in tokens:
            symbol = ""
            try:
                symbol = token.get("symbol", "")

                # Get price data
                from price_fetcher import get_enhanced_price_data

                price_data = await get_enhanced_price_data(symbol)

                if price_data:
                    token.update(
                        {
                            "enhanced_price": price_data.price,
                            "enhanced_volume": price_data.volume_24h,
                            "enhanced_market_cap": price_data.market_cap,
                            "price_change_24h": price_data.price_change_percentage_24h,
                            "data_confidence": price_data.confidence,
                        }
                    )

                # Calculate selection score
                selection_score = _calculate_selection_score(token)
                token["selection_score"] = selection_score

                enhanced_tokens.append(token)

            except Exception as e:
                logger.warning(f"Token enhancement failed for {symbol}: {e}")
                continue

        # Sort by selection score
        enhanced_tokens.sort(key=lambda x: x.get("selection_score", 0), reverse=True)

        # Select top tokens
        selected_tokens = enhanced_tokens[:limit]

        logger.info(f"✅ Phase 2 complete: Selected {len(selected_tokens)} top tokens")
        return selected_tokens

    except Exception as e:
        logger.error(f"❌ Token selection failed: {e}")
        return tokens[:limit]  # Fallback


def _calculate_selection_score(token: Dict) -> float:
    """Calculate selection score based on multiple factors"""
    try:
        score = 0.0

        # Volume score (40% weight)
        volume = token.get("enhanced_volume", token.get("volume", 0))
        if volume > 10000000:  # >10M
            score += 0.4
        elif volume > 1000000:  # >1M
            score += 0.3
        elif volume > 100000:  # >100K
            score += 0.2

        # Price change momentum (30% weight)
        price_change = token.get("price_change_24h", token.get("change", 0))
        if price_change > 5:  # Strong positive
            score += 0.3
        elif price_change > 0:  # Positive
            score += 0.15
        elif price_change > -5:  # Stable
            score += 0.1

        # Market cap stability (20% weight)
        market_cap = token.get("enhanced_market_cap", token.get("market_cap", 0))
        if market_cap > 1000000000:  # >1B
            score += 0.2
        elif market_cap > 100000000:  # >100M
            score += 0.15
        elif market_cap > 10000000:  # >10M
            score += 0.1

        # Data confidence (10% weight)
        confidence = token.get("data_confidence", 0.5)
        score += confidence * 0.1

        return min(1.0, score)

    except Exception:
        return 0.5


async def _perform_news_sentiment_analysis(tokens: List[Dict]) -> List[Dict[str, Any]]:
    """PHASE 3: Perform comprehensive news and sentiment analysis"""
    try:
        logger.info(
            f"🧠 Performing news & sentiment analysis for {len(tokens)} tokens..."
        )

        analyzed_tokens = []

        # Process tokens in batches for efficiency
        batch_size = 10
        for i in range(0, len(tokens), batch_size):
            batch = tokens[i : i + batch_size]

            # Process batch concurrently
            batch_tasks = [_analyze_token_sentiment(token) for token in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Collect successful results
            for result in batch_results:
                if isinstance(result, dict):
                    analyzed_tokens.append(result)

        logger.info(f"✅ Phase 3 complete: Analyzed {len(analyzed_tokens)} tokens")
        return analyzed_tokens

    except Exception as e:
        logger.error(f"❌ News & sentiment analysis failed: {e}")
        return tokens


async def _analyze_token_sentiment(token: Dict) -> Dict[str, Any]:
    """Analyze individual token sentiment with news data"""
    try:
        symbol = token.get("symbol", "").replace("-USDT", "")

        # Get comprehensive sentiment analysis
        from sentiment_engine import get_combined_sentiment

        sentiment_result = get_combined_sentiment(symbol)

        # Get news sentiment
        try:
            from news_sentiment import get_combined_sentiment_score

            news_data = get_combined_sentiment_score(symbol, [])
            news_score = (
                news_data.get("score", 0.5) if isinstance(news_data, dict) else 0.5
            )
        except Exception:
            news_score = 0.5

        # Get social sentiment
        try:
            from reddit_github_alpha import fetch_signal_sentiment

            social_score = fetch_signal_sentiment(symbol)
            # Normalize to 0-1 range
            social_score = (social_score + 1) / 2
        except Exception:
            social_score = 0.5

        # Calculate comprehensive sentiment score
        sentiment_weights = {"combined": 0.5, "news": 0.3, "social": 0.2}
        final_sentiment = (
            sentiment_result.get("combined_score", 0.5) * sentiment_weights["combined"]
            + news_score * sentiment_weights["news"]
            + social_score * sentiment_weights["social"]
        )

        # Update token with sentiment data
        token.update(
            {
                "sentiment_score": final_sentiment,
                "news_sentiment": news_score,
                "social_sentiment": social_score,
                "sentiment_confidence": sentiment_result.get("confidence", 0.5),
                "sentiment_breakdown": sentiment_result.get("breakdown", {}),
                "phase": "sentiment_analyzed",
            }
        )

        logger.debug(
            f"✅ Sentiment analysis complete for {symbol}: {final_sentiment:.3f}"
        )
        return token

    except Exception as e:
        logger.warning(
            f"Sentiment analysis failed for {token.get('symbol', 'UNKNOWN')}: {e}"
        )
        # Return token with default sentiment
        token.update(
            {
                "sentiment_score": 0.5,
                "news_sentiment": 0.5,
                "social_sentiment": 0.5,
                "sentiment_confidence": 0.1,
                "phase": "sentiment_failed",
            }
        )
        return token


async def _make_ai_trading_decisions(tokens: List[Dict]) -> List[Dict[str, Any]]:
    """PHASE 4: AI decision making for trading actions"""
    try:
        logger.info(f"🤖 Making AI trading decisions for {len(tokens)} tokens...")

        from ai_core import get_ai_engine

        ai_engine = get_ai_engine()

        trading_decisions = []

        # Process tokens for AI decisions
        for token in tokens:
            symbol = ""
            try:
                symbol = token.get("symbol", "")

                # Get AI decision
                decision = await ai_engine.get_profitable_decision(symbol)

                # Combine token data with AI decision
                trading_decision = {
                    **token,  # Include all token data
                    "ai_decision": decision.get("decision", "HOLD"),
                    "ai_confidence": decision.get("confidence", 0.5),
                    "ai_reasoning": decision.get("reasoning", ""),
                    "technical_score": decision.get("technical_score", 0.5),
                    "market_score": decision.get("market_score", 0.5),
                    "volume_score": decision.get("volume_score", 0.5),
                    "tokenmetrics_score": decision.get("tokenmetrics_score", 0.5),
                    "combined_ai_score": decision.get("combined_score", 0.5),
                    "data_points_used": decision.get("data_points_used", 0),
                    "analysis_breakdown": decision.get("analysis_breakdown", {}),
                    "phase": "ai_analyzed",
                }

                # Only include tokens with BUY decisions above confidence threshold
                if (
                    decision.get("decision") == "BUY"
                    and decision.get("confidence", 0) > 0.6
                ):
                    trading_decisions.append(trading_decision)
                    logger.info(
                        f"✅ BUY decision for {symbol} (confidence: {decision.get('confidence', 0):.2f})"
                    )

            except Exception as e:
                logger.warning(f"AI decision failed for {symbol}: {e}")
                continue

        logger.info(
            f"✅ Phase 4 complete: {len(trading_decisions)} BUY decisions generated"
        )
        return trading_decisions

    except Exception as e:
        logger.error(f"❌ AI decision making failed: {e}")
        return []


async def _execute_auto_trading(trading_decisions: List[Dict]) -> List[Dict[str, Any]]:
    """PHASE 5: Execute auto-trading with TP/SL strategies"""
    try:
        logger.info(
            f"⚡ Executing auto-trading for {len(trading_decisions)} decisions..."
        )

        from trade_engine import execute_trade_with_strategy

        execution_results = []

        for decision in trading_decisions:
            symbol = ""
            try:
                symbol = decision.get("symbol", "")
                ai_confidence = decision.get("ai_confidence", 0.5)

                # Calculate position size based on confidence and risk management
                position_size = _calculate_position_size(decision)

                # Determine TP/SL levels based on token type and volatility
                tp_sl_levels = _calculate_tp_sl_levels(decision)

                # Execute trade with strategy
                trade_result = await execute_trade_with_strategy(
                    symbol=symbol,
                    action="BUY",
                    amount=position_size,
                    take_profit=tp_sl_levels["take_profit"],
                    stop_loss=tp_sl_levels["stop_loss"],
                    strategy="alpha_predator",
                    metadata={
                        "ai_confidence": ai_confidence,
                        "sentiment_score": decision.get("sentiment_score", 0.5),
                        "data_points": decision.get("data_points_used", 0),
                        "reasoning": decision.get("ai_reasoning", ""),
                    },
                )

                execution_results.append(
                    {
                        "symbol": symbol,
                        "action": "BUY",
                        "amount": position_size,
                        "status": trade_result.get("status", "unknown"),
                        "trade_id": trade_result.get("trade_id", ""),
                        "take_profit": tp_sl_levels["take_profit"],
                        "stop_loss": tp_sl_levels["stop_loss"],
                        "ai_confidence": ai_confidence,
                        "timestamp": datetime.now().isoformat(),
                    }
                )

                logger.info(f"✅ Trade executed for {symbol}: {position_size} USDT")

            except Exception as e:
                logger.error(f"Trade execution failed for {symbol}: {e}")
                execution_results.append(
                    {
                        "symbol": symbol,
                        "action": "BUY",
                        "status": "failed",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat(),
                    }
                )

        logger.info(f"✅ Phase 5 complete: {len(execution_results)} trades processed")
        return execution_results

    except Exception as e:
        logger.error(f"❌ Auto-trading execution failed: {e}")
        return []


def _calculate_position_size(decision: Dict) -> float:
    """Calculate position size based on confidence and risk management"""
    try:
        from config import MAX_BUY

        ai_confidence = decision.get("ai_confidence", 0.5)
        sentiment_score = decision.get("sentiment_score", 0.5)

        # Base position size
        base_size = 100.0  # $100 default

        # Adjust based on confidence (0.5x to 1.5x)
        confidence_multiplier = 0.5 + (ai_confidence * 1.0)

        # Adjust based on sentiment (0.8x to 1.2x)
        sentiment_multiplier = 0.8 + (sentiment_score * 0.4)

        # Check if it's a meme coin (special handling)
        symbol = decision.get("symbol", "")
        is_meme_coin = any(
            meme in symbol.lower() for meme in ["doge", "shib", "pepe", "floki"]
        )

        if is_meme_coin:
            position_size = min(
                20.0, base_size * confidence_multiplier * sentiment_multiplier
            )
        else:
            position_size = min(
                float(MAX_BUY), base_size * confidence_multiplier * sentiment_multiplier
            )

        return round(position_size, 2)

    except Exception:
        return 100.0  # Default fallback


def _calculate_tp_sl_levels(decision: Dict) -> Dict[str, float]:
    """Calculate take profit and stop loss levels"""
    try:
        symbol = decision.get("symbol", "")
        current_price = decision.get("enhanced_price", decision.get("price", 0))
        volatility = decision.get("volatility", 0.1)

        # Check if it's a meme coin
        is_meme_coin = any(
            meme in symbol.lower() for meme in ["doge", "shib", "pepe", "floki"]
        )

        if is_meme_coin:
            # Meme coins: Higher volatility, different strategy
            take_profit = current_price * 1.30  # 30% TP
            stop_loss = current_price * 0.85  # 15% SL
        else:
            # Regular tokens: Standard strategy
            take_profit = current_price * 1.20  # 20% TP
            stop_loss = current_price * 0.90  # 10% SL

        # Adjust based on volatility
        if volatility > 0.2:  # High volatility
            take_profit *= 1.1  # Increase TP
            stop_loss *= 0.95  # Tighter SL

        return {"take_profit": round(take_profit, 8), "stop_loss": round(stop_loss, 8)}

    except Exception:
        return {"take_profit": 0.0, "stop_loss": 0.0}


async def _generate_top_token_list_async(limit: int = 50) -> List[Dict[str, Any]]:
    """
    Enhanced token generation with parallel processing and intelligent caching for high-frequency trading.

    Args:
        limit: Number of top tokens to return (default increased to 50).
    Returns:
        List of top scored token dictionaries optimized for high-frequency trading.
    """
    if limit <= 0:
        limit = 50

    # 🚀 LIVE DATA ONLY - Skip cache and static files for real-time data
    logging.info(
        f"🔴 LIVE MODE: Fetching fresh data for {limit} tokens (no cache/static files)"
    )

    # Enhanced API-based generation with parallel processing - ALWAYS LIVE
    try:
        start_time = time.time()
        logging.info(
            "🚀 Starting enhanced token generation with parallel processing..."
        )

        # Fetch data in parallel where possible
        kucoin_tokens = fetch_kucoin_token_data()
        if not kucoin_tokens:
            logging.warning("No tokens fetched from KuCoin")
            return []

        logging.info(
            f"📊 Processing {len(kucoin_tokens)} tokens for enhanced scoring..."
        )

        # Parallel data fetching
        with ThreadPoolExecutor(max_workers=3) as executor:
            market_cap_future = executor.submit(fetch_market_cap_data)
            sentiment_future = executor.submit(build_sentiment_scores, kucoin_tokens)
            spike_future = executor.submit(fetch_kucoin_spike_tokens)

            # Collect results
            market_caps = market_cap_future.result()
            sentiment_scores = sentiment_future.result()
            spike_data = spike_future.result()

        # Process spike data
        spike_ratios = {}
        for entry in spike_data:
            if "symbol" in entry:
                multiplier = entry.get("multiplier")
                if multiplier is None:
                    multiplier = entry.get("volume_ratio", 1.0)
                try:
                    spike_ratios[entry["symbol"]] = float(multiplier)
                except (ValueError, TypeError):
                    spike_ratios[entry["symbol"]] = 1.0

        # Enhanced parallel scoring
        scored_tokens: List[Dict] = []

        if PARALLEL_PROCESSING and len(kucoin_tokens) > 20:
            # Parallel scoring for large token sets
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                scoring_futures = [
                    executor.submit(
                        score_token, token, market_caps, sentiment_scores, spike_ratios
                    )
                    for token in kucoin_tokens
                ]

                for future in as_completed(scoring_futures):
                    result = future.result()
                    if result and result.get("score", 0) > 0:
                        scored_tokens.append(result)
        else:
            # Sequential scoring for smaller sets
            for token in kucoin_tokens:
                result = score_token(token, market_caps, sentiment_scores, spike_ratios)
                if result and result.get("score", 0) > 0:
                    scored_tokens.append(result)

        # 🚀 NEW: Enhance with TokenMetrics data before final sorting
        try:
            scored_tokens = await enhance_tokens_with_tokenmetrics(scored_tokens)
            logger.info(
                f"✅ TokenMetrics enhancement applied to {len(scored_tokens)} tokens"
            )
        except Exception as e:
            logger.warning(f"TokenMetrics enhancement failed, continuing without: {e}")

        # Enhanced sorting with multiple criteria (including TokenMetrics scores)
        sorted_tokens = sorted(
            scored_tokens,
            key=lambda x: (
                x.get("score", 0),
                x.get("tm_score", 0),  # Include TokenMetrics score
                x.get("volume", 0),
                x.get("quality_multiplier", 1.0),
            ),
            reverse=True,
        )

        # Apply quality filtering
        if len(sorted_tokens) > limit:
            # Use score threshold to ensure quality
            min_score_threshold = max(0.1, sorted_tokens[limit - 1]["score"] * 0.7)
            quality_tokens = [
                token
                for token in sorted_tokens
                if token["score"] >= min_score_threshold
            ][:limit]
        else:
            quality_tokens = sorted_tokens

        save_token_cache(quality_tokens)

        # Save API response for debugging
        try:
            with open(DISCOVER_API_FILE, "w") as f:
                json.dump(quality_tokens, f, indent=2)
            logging.info(f"📁 Enhanced API response saved to {DISCOVER_API_FILE}")
        except Exception as e:
            logging.error(f"Failed to write API discover response: {e}", exc_info=True)

        # Performance logging
        processing_time = time.time() - start_time
        top_score = quality_tokens[0]["score"] if quality_tokens else 0
        logging.info(
            f"⚡ Enhanced token generation completed in {processing_time:.2f}s | "
            f"Processed: {len(kucoin_tokens)} | Scored: {len(scored_tokens)} | "
            f"Final: {len(quality_tokens)} | Top Score: {top_score:.4f}"
        )

        # Log top tokens with enhanced metrics
        for i, token in enumerate(quality_tokens[:10]):  # Log top 10
            logging.info(
                f"🏆 #{i+1} {token['symbol']} | Score: {token['score']:.4f} | "
                f"Vol: {token['volume']:.0f} | Change: {token['change_rate']:.3f} | "
                f"Volatility: {token['volatility']:.3f}"
            )

        # Cache with shorter TTL for high-frequency updates
        cache_key = f"top_tokens_{limit}"
        set_cached_data(cache_key, quality_tokens, ttl=CACHE_TTL)

        return quality_tokens

    except Exception as e:
        logging.error(f"Enhanced token generation failed: {e}", exc_info=True)
        return []


def get_top_tokens_for_trading(limit: int = 20) -> List[Dict]:
    """
    Enhanced function to get top tokens optimized for high-frequency trading.

    Args:
        limit: Number of tokens to return for trading.

    Returns:
        List of top token dictionaries optimized for trading.
    """
    try:
        tokens = generate_top_token_list(limit)
        if not tokens:
            logging.warning("⚠️ No tokens found for trading - using fallback")
            # Try to get cached tokens as fallback
            fallback_tokens = get_cached_data("enhanced_tokens_20")
            if fallback_tokens:
                logging.info(f"📦 Using {len(fallback_tokens)} fallback tokens")
                return fallback_tokens[:limit]
            return []

        # Filter tokens for trading readiness
        trading_ready_tokens = []
        for token in tokens:
            # Additional trading readiness checks
            if (
                token.get("volume", 0) > VOLUME_THRESHOLD
                and token.get("score", 0) > 0.1
                and token.get("volatility", 0) > 0.01
            ):  # Minimum volatility for trading opportunities
                trading_ready_tokens.append(token)

        logging.info(
            f"🎯 {len(trading_ready_tokens)} tokens ready for high-frequency trading"
        )
        return trading_ready_tokens

    except Exception as e:
        logging.error(f"Error getting tokens for trading: {e}", exc_info=True)
        return []


def get_top_tokens_for_legacy(limit: int = 20) -> List[Dict]:
    """
    Alias function for backward compatibility.
    """
    return get_top_tokens_for_trading(limit)


def get_spike_tokens(limit: int = TOP_LIMIT) -> List[Dict]:
    """
    Returns top tokens with volume spikes from KuCoin.

    Args:
        limit: Number of spike tokens to fetch.

    Returns:
        List of spike token dictionaries.
    """
    cache_key = f"spike_tokens_{limit}"
    cached_data = get_cached_data(cache_key)
    if cached_data:
        logging.info(f"Returning cached spike tokens for limit {limit}")
        return cached_data

    try:
        spikes = fetch_kucoin_spike_tokens(limit=limit)
        logging.info(f"Fetched {len(spikes)} spike tokens")
        set_cached_data(cache_key, spikes, ttl=300)  # Cache for 5 minutes
        return spikes
    except Exception as e:
        logging.error(f"Failed to fetch spike tokens: {e}", exc_info=True)
        return []


def get_newly_listed_tokens(limit: int = TOP_LIMIT) -> List[Dict]:
    """
    Returns newest listed tokens. Using volume spike as proxy until listing timestamp is available.

    Args:
        limit: Number of newly listed tokens to fetch.

    Returns:
        List of newly listed token dictionaries.
    """
    cache_key = f"newly_listed_tokens_{limit}"
    cached_data = get_cached_data(cache_key)
    if cached_data:
        logging.info(f"Returning cached newly listed tokens for limit {limit}")
        return cached_data

    newly_listed = get_spike_tokens(limit=limit)
    set_cached_data(cache_key, newly_listed, ttl=300)  # Cache for 5 minutes
    return newly_listed


def get_top_tokens(limit: int = TOP_LIMIT) -> List[Dict]:
    """
    Alias for live_runner.py to fetch top N tokens using generate_top_token_list().

    Args:
        limit: Number of top tokens to fetch.

    Returns:
        List of top token dictionaries limited by the specified number.
    """
    tokens = generate_top_token_list()
    return tokens[:limit]


def discover_tokens(limit: int = TOP_LIMIT):
    """
    Discover tokens with optional limit parameter.

    Args:
        limit: Maximum number of tokens to return

    Returns:
        List of token dictionaries or error dict
    """
    try:
        # Try selected_tokens.json first
        try:
            with open("backend/data/selected_tokens.json", "r") as f:
                tokens = json.load(f)
            return (
                tokens[:limit]
                if isinstance(tokens, list)
                else {"status": "success", "tokens": tokens}
            )
        except FileNotFoundError:
            pass

        # Fallback to discover_tokens.json
        try:
            with open(DISCOVER_FILE, "r") as f:
                tokens = json.load(f)
            return tokens[:limit] if isinstance(tokens, list) else tokens
        except FileNotFoundError:
            pass

        # Generate new tokens if no cached data
        tokens = generate_top_token_list(limit=limit)
        return tokens

    except json.JSONDecodeError:
        return {"status": "error", "message": "Token data is malformed"}
    except Exception as e:
        logging.error(f"Error in discover_tokens: {e}")
        return {"status": "error", "message": str(e)}


def get_trending_tokens(limit: int = TOP_LIMIT):
    """
    Alias for discover_tokens to maintain backward compatibility.
    """
    return discover_tokens(limit=limit)


def select_tokens(limit: int = TOP_LIMIT):
    """
    Select tokens for trading - alias for get_top_tokens_for_trading
    """
    return get_top_tokens_for_trading(limit=limit)


def get_enhanced_discover_tokens(limit: int = TOP_LIMIT):
    """
    Get enhanced discover tokens - alias for discover_tokens
    """
    return discover_tokens(limit=limit)


if __name__ == "__main__":
    logging.info("✅ Running token selector...")
    tokens = generate_top_token_list()
    if not tokens:
        logging.warning("⚠️ No tokens selected.")
    else:
        try:
            with open(DISCOVER_FILE, "w") as f:
                json.dump(tokens, f, indent=2)
            logging.info(f"🎯 Top {len(tokens)} tokens written to {DISCOVER_FILE}")
        except Exception as e:
            logging.error(f"Failed to write discover_tokens.json: {e}", exc_info=True)


# Enhanced helper functions for parallel token analysis


async def _calculate_volume_ratio(symbol: str, current_volume: float) -> float:
    """Calculate volume ratio compared to historical average."""
    try:
        cache_key = f"volume_ratio_{symbol}"
        cached_ratio = get_cached_data(cache_key)
        if cached_ratio:
            return cached_ratio

        # Simplified volume ratio calculation
        base_ratio = 1.0 + (current_volume / 1000000) * 0.1  # Rough estimation
        ratio = min(3.0, max(0.1, base_ratio))  # Clamp between 0.1 and 3.0

        set_cached_data(cache_key, ratio, 300, "normal")  # Cache for 5 minutes
        return ratio

    except Exception:
        return 1.0


async def _get_sentiment_score_async(symbol: str) -> float:
    """Get sentiment score asynchronously using enhanced sentiment engine."""
    try:
        clean_symbol = symbol.replace("-USDT", "").replace("-USD", "")

        # 🔧 FIX: Use the enhanced sentiment engine
        from sentiment_engine import get_sentiment_score

        sentiment_score = get_sentiment_score(clean_symbol)

        # Ensure score is in valid range
        if isinstance(sentiment_score, (int, float)) and 0.0 <= sentiment_score <= 1.0:
            logger.debug(f"✅ Sentiment for {clean_symbol}: {sentiment_score:.3f}")
            return float(sentiment_score)
        else:
            logger.warning(
                f"⚠️ Invalid sentiment score for {clean_symbol}: {sentiment_score}"
            )
            return 0.5

    except Exception as e:
        logger.warning(f"Sentiment analysis failed for {symbol}: {e}")
        return 0.5


def _calculate_enhanced_score(
    price: float,
    volume_24h: float,
    market_cap: float,
    price_change_24h: float,
    sentiment_score: float,
    volume_ratio: float,
    volatility: float,
) -> float:
    """Calculate enhanced token score (0-100)."""
    try:
        score = 50.0  # Start with neutral score

        # Volume score (25% weight)
        if volume_24h > 10000000:  # $10M+
            score += 12.5
        elif volume_24h > 1000000:  # $1M+
            score += 7.5
        elif volume_24h > 100000:  # $100K+
            score += 2.5
        else:
            score -= 5.0  # Low volume penalty

        # Price momentum score (20% weight)
        if 0.02 <= price_change_24h <= 0.15:  # 2-15% gain
            score += 10
        elif -0.05 <= price_change_24h < 0.02:  # Small decline to small gain
            score += 5
        elif price_change_24h > 0.15:  # High pump - risky
            score -= 5
        elif price_change_24h < -0.10:  # Major decline
            score -= 10

        # Sentiment score (20% weight)
        sentiment_boost = (sentiment_score - 0.5) * 20  # Convert to -10 to +10 range
        score += sentiment_boost

        # Volume ratio score (15% weight)
        if volume_ratio > 2.0:  # High volume spike
            score += 7.5
        elif volume_ratio > 1.5:  # Moderate volume increase
            score += 5
        elif volume_ratio < 0.5:  # Low volume
            score -= 5

        # Market cap consideration (10% weight)
        if 100000000 <= market_cap <= 10000000000:  # $100M - $10B sweet spot
            score += 5
        elif market_cap > 50000000000:  # Too large
            score -= 2.5
        elif market_cap < 10000000:  # Too small
            score -= 5

        # Volatility adjustment (10% weight)
        if 0.02 <= volatility <= 0.08:  # Optimal volatility range
            score += 5
        elif volatility > 0.15:  # Too volatile
            score -= 5

        return max(0.0, min(100.0, score))

    except Exception:
        return 50.0


def _assess_trading_readiness(
    volume_24h: float,
    volatility: float,
    sentiment_score: float,
    price_change_24h: float,
) -> float:
    """Assess how ready a token is for trading (0-100)."""
    try:
        readiness = 50.0

        # Volume readiness (40% weight)
        if volume_24h > 5000000:  # $5M+
            readiness += 20
        elif volume_24h > 1000000:  # $1M+
            readiness += 10
        else:
            readiness -= 10

        # Volatility readiness (30% weight)
        if 0.03 <= volatility <= 0.10:  # Good volatility for trading
            readiness += 15
        elif volatility > 0.20:  # Too risky
            readiness -= 15

        # Sentiment readiness (20% weight)
        if sentiment_score > 0.6 or sentiment_score < 0.4:  # Strong sentiment
            readiness += 10
        else:
            readiness += 5  # Neutral is still tradeable

        # Price momentum readiness (10% weight)
        if abs(price_change_24h) > 0.02:  # Some movement
            readiness += 5

        return max(0.0, min(100.0, readiness))

    except Exception:
        return 50.0


def _generate_token_reasoning(
    score: float,
    sentiment_score: float,
    volume_ratio: float,
    volatility: float,
    price_change_24h: float,
) -> str:
    """Generate human-readable reasoning for token selection."""
    try:
        reasons = []

        if score > 70:
            reasons.append("High overall score")
        elif score < 30:
            reasons.append("Low overall score")

        if sentiment_score > 0.6:
            reasons.append("Positive sentiment")
        elif sentiment_score < 0.4:
            reasons.append("Negative sentiment")

        if volume_ratio > 2.0:
            reasons.append("Volume spike detected")
        elif volume_ratio < 0.5:
            reasons.append("Low volume warning")

        if price_change_24h > 0.10:
            reasons.append("Strong price momentum")
        elif price_change_24h < -0.05:
            reasons.append("Price decline")

        if volatility > 0.15:
            reasons.append("High volatility")
        elif volatility < 0.02:
            reasons.append("Low volatility")

        return "; ".join(reasons) if reasons else "Standard market conditions"

    except Exception:
        return "Analysis incomplete"


async def _fetch_raw_token_data() -> List[Dict[str, Any]]:
    """Fetch raw token data from multiple sources."""
    try:
        # Try KuCoin first
        kucoin_data = await _fetch_kucoin_data_async()
        if kucoin_data:
            return kucoin_data

        # Fallback to cached data
        cached_data = get_cached_data("raw_token_data")
        if cached_data:
            return cached_data

        return []

    except Exception as e:
        logger.error(f"Error fetching raw token data: {e}")
        return []


async def _fetch_kucoin_data_async() -> List[Dict[str, Any]]:
    """Fetch KuCoin data asynchronously."""
    try:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _fetch_kucoin_data_sync)

    except Exception:
        return []


def _fetch_kucoin_data_sync() -> List[Dict[str, Any]]:
    """Synchronous KuCoin data fetching."""
    try:
        # Use KuCoin SDK instead of custom HTTP client
        tickers = kucoin_sdk.get_all_tickers()
        if tickers:
            return tickers
        return []
    except Exception:
        return []
