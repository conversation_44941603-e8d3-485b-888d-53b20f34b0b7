"""
CoinGecko Integration Service for Alpha Predator
Combines CoinGecko MCP data with existing token selection and analysis
"""

import asyncio
import logging
import time
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from coingecko_mcp_client import coingecko_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CoinGeckoIntegration:
    """
    Integration service that combines CoinGecko data with Alpha Predator analysis
    """
    
    def __init__(self):
        self.cache_file = "data/coingecko_cache.json"
        self.cache_duration = 300  # 5 minutes cache
        self.top_coins_cache = {}
        self.trending_cache = {}
        self.last_update = 0
        
    def _load_cache(self) -> Dict[str, Any]:
        """Load cached CoinGecko data"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
                    if time.time() - cache_data.get("timestamp", 0) < self.cache_duration:
                        return cache_data
        except Exception as e:
            logger.warning(f"Failed to load CoinGecko cache: {e}")
        return {}
    
    def _save_cache(self, data: Dict[str, Any]):
        """Save CoinGecko data to cache"""
        try:
            os.makedirs("data", exist_ok=True)
            cache_data = {
                "timestamp": time.time(),
                "data": data
            }
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save CoinGecko cache: {e}")
    
    async def get_enhanced_token_data(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get enhanced token data combining CoinGecko with our analysis
        """
        try:
            # Check cache first
            cached_data = self._load_cache()
            if cached_data and "enhanced_tokens" in cached_data.get("data", {}):
                logger.info("✅ Using cached CoinGecko enhanced token data")
                return cached_data["data"]["enhanced_tokens"]
            
            # Fetch fresh data from CoinGecko
            logger.info("🔄 Fetching fresh CoinGecko market data...")
            market_data = await coingecko_client.get_coins_markets(limit=limit)
            
            if not market_data:
                logger.warning("No market data from CoinGecko")
                return []
            
            # Enhance with our scoring system
            enhanced_tokens = []
            for coin in market_data:
                try:
                    enhanced_token = self._enhance_token_data(coin)
                    if enhanced_token:
                        enhanced_tokens.append(enhanced_token)
                except Exception as e:
                    logger.warning(f"Failed to enhance token {coin.get('symbol', 'unknown')}: {e}")
            
            # Sort by our custom score
            enhanced_tokens.sort(key=lambda x: x.get("alpha_score", 0), reverse=True)
            
            # Cache the results
            cache_data = {"enhanced_tokens": enhanced_tokens}
            self._save_cache(cache_data)
            
            logger.info(f"✅ Enhanced {len(enhanced_tokens)} tokens with CoinGecko data")
            return enhanced_tokens
            
        except Exception as e:
            logger.error(f"Failed to get enhanced token data: {e}")
            return []
    
    def _enhance_token_data(self, coin: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Enhance CoinGecko coin data with Alpha Predator scoring
        """
        try:
            # Extract CoinGecko data
            symbol = coin.get("symbol", "").upper()
            name = coin.get("name", "")
            price = coin.get("current_price", 0)
            market_cap = coin.get("market_cap", 0)
            volume_24h = coin.get("total_volume", 0)
            price_change_24h = coin.get("price_change_percentage_24h", 0)
            market_cap_rank = coin.get("market_cap_rank", 999)
            
            # Skip if essential data is missing
            if not symbol or not price or not market_cap:
                return None
            
            # Calculate Alpha Predator score
            alpha_score = self._calculate_alpha_score(
                price=price,
                market_cap=market_cap,
                volume_24h=volume_24h,
                price_change_24h=price_change_24h,
                market_cap_rank=market_cap_rank
            )
            
            # Determine trading signal
            signal = self._get_trading_signal(alpha_score, price_change_24h, volume_24h, market_cap)
            
            # Create enhanced token data
            enhanced_token = {
                "symbol": f"{symbol}-USDT",  # Format for KuCoin
                "name": name,
                "price": price,
                "market_cap": market_cap,
                "volume": volume_24h,
                "price_change_24h": price_change_24h,
                "market_cap_rank": market_cap_rank,
                "alpha_score": alpha_score,
                "signal": signal,
                "source": "coingecko_mcp",
                "timestamp": time.time(),
                
                # Additional CoinGecko data
                "ath": coin.get("ath", 0),
                "ath_change_percentage": coin.get("ath_change_percentage", 0),
                "atl": coin.get("atl", 0),
                "atl_change_percentage": coin.get("atl_change_percentage", 0),
                "circulating_supply": coin.get("circulating_supply", 0),
                "total_supply": coin.get("total_supply", 0),
                "max_supply": coin.get("max_supply"),
                "fully_diluted_valuation": coin.get("fully_diluted_valuation", 0),
                
                # Metadata
                "image": coin.get("image", ""),
                "coingecko_id": coin.get("id", ""),
                "last_updated": coin.get("last_updated", "")
            }
            
            return enhanced_token
            
        except Exception as e:
            logger.error(f"Failed to enhance token data: {e}")
            return None
    
    def _calculate_alpha_score(self, price: float, market_cap: int, volume_24h: float, 
                              price_change_24h: float, market_cap_rank: int) -> float:
        """
        Calculate Alpha Predator score based on CoinGecko data
        """
        try:
            score = 0.0
            
            # Volume score (30% weight)
            if volume_24h > 0 and market_cap > 0:
                volume_ratio = volume_24h / market_cap
                if volume_ratio > 0.1:  # High volume
                    score += 0.3
                elif volume_ratio > 0.05:  # Medium volume
                    score += 0.2
                elif volume_ratio > 0.01:  # Low volume
                    score += 0.1
            
            # Price momentum score (25% weight)
            if price_change_24h > 5:  # Strong positive momentum
                score += 0.25
            elif price_change_24h > 2:  # Moderate positive momentum
                score += 0.15
            elif price_change_24h > -2:  # Stable
                score += 0.1
            elif price_change_24h < -10:  # Oversold potential
                score += 0.05
            
            # Market cap rank score (20% weight)
            if market_cap_rank <= 50:  # Top 50
                score += 0.2
            elif market_cap_rank <= 100:  # Top 100
                score += 0.15
            elif market_cap_rank <= 200:  # Top 200
                score += 0.1
            elif market_cap_rank <= 500:  # Top 500
                score += 0.05
            
            # Liquidity score (15% weight)
            if volume_24h > 50000000:  # $50M+ volume
                score += 0.15
            elif volume_24h > 10000000:  # $10M+ volume
                score += 0.1
            elif volume_24h > 1000000:  # $1M+ volume
                score += 0.05
            
            # Market cap score (10% weight)
            if 100000000 <= market_cap <= 10000000000:  # $100M - $10B sweet spot
                score += 0.1
            elif 50000000 <= market_cap <= 100000000000:  # $50M - $100B
                score += 0.05
            
            return min(score, 1.0)  # Cap at 1.0
            
        except Exception as e:
            logger.error(f"Failed to calculate alpha score: {e}")
            return 0.0
    
    def _get_trading_signal(self, alpha_score: float, price_change_24h: float, 
                           volume_24h: float, market_cap: int) -> str:
        """
        Generate trading signal based on analysis
        """
        try:
            # Strong buy conditions
            if (alpha_score > 0.7 and price_change_24h > 3 and 
                volume_24h > 10000000 and market_cap > 100000000):
                return "STRONG_BUY"
            
            # Buy conditions
            elif (alpha_score > 0.5 and price_change_24h > 1 and 
                  volume_24h > 1000000):
                return "BUY"
            
            # Hold conditions
            elif alpha_score > 0.3 and price_change_24h > -5:
                return "HOLD"
            
            # Sell conditions
            elif alpha_score < 0.2 or price_change_24h < -10:
                return "SELL"
            
            else:
                return "HOLD"
                
        except Exception as e:
            logger.error(f"Failed to get trading signal: {e}")
            return "HOLD"
    
    async def get_trending_analysis(self) -> Dict[str, Any]:
        """
        Get trending coins analysis from CoinGecko
        """
        try:
            trending_data = await coingecko_client.get_trending_coins()
            
            if not trending_data:
                return {"trending_coins": [], "analysis": "No trending data available"}
            
            # Extract trending coins
            trending_coins = []
            for coin_data in trending_data.get("coins", [])[:10]:  # Top 10
                coin = coin_data.get("item", {})
                trending_coins.append({
                    "name": coin.get("name", ""),
                    "symbol": coin.get("symbol", ""),
                    "market_cap_rank": coin.get("market_cap_rank", 999),
                    "price_btc": coin.get("price_btc", 0),
                    "score": coin.get("score", 0),
                    "thumb": coin.get("thumb", ""),
                    "slug": coin.get("slug", "")
                })
            
            # Generate analysis
            analysis = f"Found {len(trending_coins)} trending coins. "
            if trending_coins:
                top_coin = trending_coins[0]
                analysis += f"Top trending: {top_coin['name']} ({top_coin['symbol']}) "
                analysis += f"ranked #{top_coin['market_cap_rank']}"
            
            return {
                "trending_coins": trending_coins,
                "analysis": analysis,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Failed to get trending analysis: {e}")
            return {"trending_coins": [], "analysis": "Failed to fetch trending data"}
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """
        Get CoinGecko API usage summary
        """
        usage_stats = coingecko_client.get_usage_stats()
        
        return {
            "provider": "CoinGecko MCP",
            "plan": "Free Tier",
            "monthly_calls": usage_stats["monthly_calls"],
            "monthly_limit": usage_stats["monthly_limit"],
            "monthly_remaining": usage_stats["monthly_remaining"],
            "usage_percentage": usage_stats["usage_percentage"],
            "rate_limit_per_minute": usage_stats["rate_limit_per_minute"],
            "rate_limit_remaining": usage_stats["rate_limit_remaining"],
            "cost_per_call": 0.0,  # Free tier
            "estimated_monthly_cost": 0.0,  # Free tier
            "status": "active" if usage_stats["monthly_remaining"] > 0 else "limit_reached"
        }

# Global integration instance
coingecko_integration = CoinGeckoIntegration()
