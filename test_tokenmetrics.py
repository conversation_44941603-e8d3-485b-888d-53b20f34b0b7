#!/usr/bin/env python3
"""
Comprehensive test suite for the enhanced TokenMetrics API integration.
Tests all new endpoints and functionality.
"""

import asyncio
import logging
import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env files
load_dotenv('backend/.env.production')
load_dotenv('backend/.env')

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.tokenmetrics_api import TokenMetricsAPI
from backend.ai_core import run_ai_trade_async
from backend.enhanced_arbitrage import enhanced_arbitrage_engine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TokenMetricsTestSuite:
    """Comprehensive test suite for TokenMetrics API"""
    
    def __init__(self):
        self.api = TokenMetricsAPI()
        self.test_symbols = ["BTC", "ETH", "SOL", "ADA", "DOT"]
        
    def test_api_key_check(self):
        """Test API key availability"""
        logger.info("🔑 Testing API key availability...")
        
        has_key = self.api._check_api_key()
        if has_key:
            logger.info("✅ API key is available")
            return True
        else:
            logger.warning("⚠️ API key not available - tests will use fallback mode")
            return False
    
    def test_get_tokens(self):
        """Test the basic tokens endpoint"""
        logger.info("🪙 Testing get_tokens endpoint...")
        
        try:
            # Test basic token retrieval
            result = self.api.get_tokens(limit=10, page=1)
            
            if result.get("success"):
                data = result.get("data", [])
                logger.info(f"✅ Retrieved {len(data)} tokens")
                
                if data:
                    sample_token = data[0]
                    logger.info(f"📊 Sample token: {sample_token.get('TOKEN_SYMBOL')} - {sample_token.get('TOKEN_NAME')}")
                    return True
            else:
                logger.warning(f"⚠️ get_tokens failed: {result.get('message')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ get_tokens test failed: {e}")
            return False
    
    def test_get_token_info(self):
        """Test the token info endpoint for ID mapping"""
        logger.info("🗺️ Testing get_token_info endpoint...")
        
        try:
            result = self.api.get_token_info(limit=100)
            
            if result.get("success"):
                data = result.get("data", [])
                logger.info(f"✅ Retrieved token info for {len(data)} tokens")
                
                # Check if cache was updated
                if self.api._token_cache:
                    logger.info(f"📦 Token cache updated with {len(self.api._token_cache)} entries")
                    
                    # Show some cached symbols
                    sample_symbols = list(self.api._token_cache.keys())[:5]
                    logger.info(f"🔍 Sample cached symbols: {sample_symbols}")
                    return True
            else:
                logger.warning(f"⚠️ get_token_info failed: {result.get('message')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ get_token_info test failed: {e}")
            return False
    
    def test_resolve_token_ids(self):
        """Test symbol to TokenMetrics ID resolution"""
        logger.info("🔍 Testing resolve_token_ids...")
        
        try:
            # First ensure we have token info
            self.api.get_token_info(limit=500)
            
            # Test resolution
            result = self.api.resolve_token_ids(self.test_symbols)
            
            logger.info(f"📋 Token ID resolution results:")
            for symbol, token_id in result.items():
                if token_id:
                    logger.info(f"  ✅ {symbol} -> TokenMetrics ID: {token_id}")
                else:
                    logger.info(f"  ❌ {symbol} -> Not found")
            
            # Count successful resolutions
            successful = sum(1 for tid in result.values() if tid is not None)
            logger.info(f"📊 Successfully resolved {successful}/{len(self.test_symbols)} symbols")
            
            return successful > 0
            
        except Exception as e:
            logger.error(f"❌ resolve_token_ids test failed: {e}")
            return False
    
    def test_get_ai_reports(self):
        """Test the AI reports endpoint"""
        logger.info("🤖 Testing get_ai_reports endpoint...")
        
        try:
            # Get some token IDs first
            token_mapping = self.api.resolve_token_ids(["BTC", "ETH"])
            token_ids = [str(tid) for tid in token_mapping.values() if tid is not None]
            
            if not token_ids:
                logger.warning("⚠️ No token IDs available for AI reports test")
                return False
            
            # Test AI reports
            token_ids_str = ",".join(token_ids[:2])  # Test with first 2 tokens
            result = self.api.get_ai_reports(token_ids=token_ids_str, limit=5)
            
            if result.get("success"):
                data = result.get("data", [])
                logger.info(f"✅ Retrieved AI reports for {len(data)} tokens")
                
                if data:
                    sample_report = data[0]
                    logger.info(f"📊 Sample AI report keys: {list(sample_report.keys())}")
                    return True
            else:
                logger.warning(f"⚠️ get_ai_reports failed: {result.get('message')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ get_ai_reports test failed: {e}")
            return False
    
    def test_get_technical_indicators(self):
        """Test the technical indicators endpoint"""
        logger.info("📈 Testing get_technical_indicators endpoint...")
        
        try:
            # Get some token IDs first
            token_mapping = self.api.resolve_token_ids(["BTC", "ETH"])
            token_ids = [str(tid) for tid in token_mapping.values() if tid is not None]
            
            if not token_ids:
                logger.warning("⚠️ No token IDs available for technical indicators test")
                return False
            
            # Test technical indicators
            token_ids_str = ",".join(token_ids[:2])
            result = self.api.get_technical_indicators(token_ids=token_ids_str, limit=10)
            
            if result.get("success"):
                data = result.get("data", [])
                logger.info(f"✅ Retrieved technical indicators for {len(data)} entries")
                
                if data:
                    sample_indicator = data[0]
                    logger.info(f"📊 Sample technical indicator keys: {list(sample_indicator.keys())}")
                    return True
            else:
                logger.warning(f"⚠️ get_technical_indicators failed: {result.get('message')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ get_technical_indicators test failed: {e}")
            return False
    
    def test_get_comprehensive_analysis(self):
        """Test the comprehensive analysis method"""
        logger.info("🔬 Testing get_comprehensive_analysis...")
        
        try:
            test_symbol = "BTC"
            result = self.api.get_comprehensive_analysis(test_symbol)
            
            if result.get("available"):
                logger.info(f"✅ Comprehensive analysis available for {test_symbol}")
                
                # Check AI analysis
                ai_analysis = result.get("ai_analysis", {})
                if ai_analysis:
                    logger.info(f"🤖 AI Recommendation: {ai_analysis.get('recommendation')} (confidence: {ai_analysis.get('confidence')})")
                
                # Check technical analysis
                tech_analysis = result.get("technical_analysis", {})
                if tech_analysis:
                    logger.info(f"📈 Technical Signals: {tech_analysis.get('bullish_signals')} bullish, {tech_analysis.get('bearish_signals')} bearish")
                
                # Check combined signal
                combined_signal = result.get("combined_signal")
                combined_confidence = result.get("confidence")
                logger.info(f"🎯 Combined Signal: {combined_signal} (confidence: {combined_confidence:.2f})")
                
                return True
            else:
                logger.warning(f"⚠️ Comprehensive analysis not available: {result.get('message')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ get_comprehensive_analysis test failed: {e}")
            return False
    
    def test_legacy_compatibility(self):
        """Test backward compatibility with legacy get_trading_signals method"""
        logger.info("🔄 Testing legacy compatibility...")
        
        try:
            test_symbol = "BTC"
            result = self.api.get_trading_signals(test_symbol)
            
            if result:
                logger.info(f"✅ Legacy method works for {test_symbol}")
                logger.info(f"📊 Signal: {result.get('signal')}, Confidence: {result.get('confidence')}")
                return True
            else:
                logger.warning(f"⚠️ Legacy method returned empty result for {test_symbol}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Legacy compatibility test failed: {e}")
            return False
    
    async def test_ai_core_integration(self):
        """Test integration with AI core"""
        logger.info("🧠 Testing AI core integration...")
        
        try:
            test_symbol = "BTC-USDT"
            result = await run_ai_trade_async(test_symbol)
            
            if result and result.get("symbol") == test_symbol:
                decision = result.get("decision", "UNKNOWN")
                logger.info(f"✅ AI core integration works - Decision: {decision}")
                return True
            else:
                logger.warning("⚠️ AI core integration test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ AI core integration test failed: {e}")
            return False
    
    async def test_arbitrage_integration(self):
        """Test integration with enhanced arbitrage"""
        logger.info("⚖️ Testing arbitrage integration...")
        
        try:
            opportunities = await enhanced_arbitrage_engine.find_enhanced_arbitrage_opportunities(limit=5)
            
            if opportunities:
                logger.info(f"✅ Found {len(opportunities)} arbitrage opportunities")
                
                # Check if TokenMetrics data is included
                sample_opp = opportunities[0]
                tm_signals = sample_opp.get("tokenmetrics_signals", {})
                
                if tm_signals.get("available"):
                    logger.info(f"🎯 TokenMetrics integration working in arbitrage")
                    logger.info(f"📊 Sample signal: {tm_signals.get('signal')} (confidence: {tm_signals.get('confidence')})")
                    return True
                else:
                    logger.warning("⚠️ TokenMetrics data not available in arbitrage")
                    return False
            else:
                logger.warning("⚠️ No arbitrage opportunities found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Arbitrage integration test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests and return summary"""
        logger.info("🚀 Starting TokenMetrics API Test Suite")
        logger.info("=" * 60)
        
        tests = [
            ("API Key Check", self.test_api_key_check),
            ("Get Tokens", self.test_get_tokens),
            ("Get Token Info", self.test_get_token_info),
            ("Resolve Token IDs", self.test_resolve_token_ids),
            ("Get AI Reports", self.test_get_ai_reports),
            ("Get Technical Indicators", self.test_get_technical_indicators),
            ("Comprehensive Analysis", self.test_get_comprehensive_analysis),
            ("Legacy Compatibility", self.test_legacy_compatibility),
        ]
        
        async_tests = [
            ("AI Core Integration", self.test_ai_core_integration),
            ("Arbitrage Integration", self.test_arbitrage_integration),
        ]
        
        results = {}
        
        # Run synchronous tests
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running: {test_name}")
            try:
                results[test_name] = test_func()
            except Exception as e:
                logger.error(f"❌ {test_name} crashed: {e}")
                results[test_name] = False
        
        # Run asynchronous tests
        async def run_async_tests():
            for test_name, test_func in async_tests:
                logger.info(f"\n🧪 Running: {test_name}")
                try:
                    results[test_name] = await test_func()
                except Exception as e:
                    logger.error(f"❌ {test_name} crashed: {e}")
                    results[test_name] = False
        
        # Run async tests
        if async_tests:
            asyncio.run(run_async_tests())
        
        # Print summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{status} - {test_name}")
        
        logger.info(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            logger.info("🎉 All tests passed! TokenMetrics integration is working correctly.")
        else:
            logger.warning(f"⚠️ {total-passed} tests failed. Check the logs above for details.")
        
        return results

def main():
    """Main test runner"""
    test_suite = TokenMetricsTestSuite()
    results = test_suite.run_all_tests()
    
    # Exit with appropriate code
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    if passed == total:
        print("\n🎉 All TokenMetrics tests passed!")
        exit(0)
    else:
        print(f"\n❌ {total-passed} tests failed.")
        exit(1)

if __name__ == "__main__":
    main()
