#!/usr/bin/env python3
"""
🧪 COMPREHENSIVE INTEGRATION TEST
Tests TokenMetrics, Reddit/GitHub, Discord news, and frontend API endpoints
"""

import asyncio
import requests
import json
from datetime import datetime

def test_tokenmetrics_fallback():
    """Test TokenMetrics with fallback data"""
    print("🔧 TESTING TOKENMETRICS INTEGRATION")
    print("-" * 40)
    
    try:
        from tokenmetrics_client import TokenMetricsClient
        
        async def test_tokenmetrics():
            client = TokenMetricsClient()
            
            # Test with BTC
            btc_data = await client.get_token_data("BTC-USDT")
            print(f"✅ TokenMetrics BTC data: {btc_data.symbol if btc_data else 'None'}")
            print(f"   Source: {btc_data.source if btc_data else 'N/A'}")
            print(f"   Confidence: {btc_data.confidence if btc_data else 'N/A'}")
            
            # Test with ETH
            eth_data = await client.get_token_data("ETH-USDT")
            print(f"✅ TokenMetrics ETH data: {eth_data.symbol if eth_data else 'None'}")
            
            return btc_data is not None and eth_data is not None
        
        # Run async test
        result = asyncio.run(test_tokenmetrics())
        print(f"📊 TokenMetrics test: {'PASS' if result else 'FAIL'}")
        return result
        
    except Exception as e:
        print(f"❌ TokenMetrics test failed: {e}")
        return False

def test_reddit_github_integration():
    """Test Reddit/GitHub token-specific filtering"""
    print("\n🔗 TESTING REDDIT/GITHUB INTEGRATION")
    print("-" * 40)
    
    try:
        from reddit_github_alpha import fetch_reddit_signals_for_token, fetch_github_signals_for_token
        
        # Test Reddit signals for BTC
        btc_reddit = fetch_reddit_signals_for_token("BTC")
        print(f"✅ Reddit BTC signals: {len(btc_reddit)} items")
        
        if btc_reddit:
            sample = btc_reddit[0]
            print(f"   Sample: {sample.get('title', '')[:50]}...")
            print(f"   Relevance: {sample.get('relevance_score', 0):.2f}")
        
        # Test GitHub signals for ETH
        eth_github = fetch_github_signals_for_token("ETH")
        print(f"✅ GitHub ETH signals: {len(eth_github)} items")
        
        if eth_github:
            sample = eth_github[0]
            print(f"   Sample: {sample.get('title', '')[:50]}...")
            print(f"   Relevance: {sample.get('relevance_score', 0):.2f}")
        
        return len(btc_reddit) >= 0 and len(eth_github) >= 0  # Allow empty results
        
    except Exception as e:
        print(f"❌ Reddit/GitHub test failed: {e}")
        return False

def test_discord_news():
    """Test Discord news integration"""
    print("\n📱 TESTING DISCORD NEWS INTEGRATION")
    print("-" * 40)
    
    try:
        from discord_news_bot import get_latest_discord_news, add_discord_news, simulate_discord_news
        
        # Simulate some news first
        simulate_discord_news()
        
        # Test general news
        all_news = get_latest_discord_news(limit=5)
        print(f"✅ Discord all news: {len(all_news)} items")
        
        if all_news:
            sample = all_news[0]
            print(f"   Sample: {sample.get('title', '')[:50]}...")
            print(f"   Sentiment: {sample.get('sentiment', 'neutral')} ({sample.get('sentiment_score', 0):.2f})")
            print(f"   Tokens: {', '.join(sample.get('tokens_mentioned', []))}")
        
        # Test BTC-specific news
        btc_news = get_latest_discord_news(limit=3, token="BTC")
        print(f"✅ Discord BTC news: {len(btc_news)} items")
        
        return len(all_news) > 0
        
    except Exception as e:
        print(f"❌ Discord news test failed: {e}")
        return False

def test_enhanced_sentiment():
    """Test enhanced sentiment analysis"""
    print("\n🧠 TESTING ENHANCED SENTIMENT ANALYSIS")
    print("-" * 40)
    
    try:
        from sentiment_engine import get_combined_sentiment, get_discord_sentiment, get_reddit_sentiment, get_github_sentiment
        
        # Test individual sentiment sources
        discord_sentiment = get_discord_sentiment("BTC")
        reddit_sentiment = get_reddit_sentiment("BTC")
        github_sentiment = get_github_sentiment("BTC")
        
        print(f"✅ Discord sentiment for BTC: {discord_sentiment:.3f}")
        print(f"✅ Reddit sentiment for BTC: {reddit_sentiment:.3f}")
        print(f"✅ GitHub sentiment for BTC: {github_sentiment:.3f}")
        
        # Test combined sentiment
        combined = get_combined_sentiment("BTC")
        print(f"✅ Combined sentiment for BTC:")
        print(f"   Overall: {combined.get('combined_sentiment', 'neutral')}")
        print(f"   Score: {combined.get('combined_score', 0.5):.3f}")
        print(f"   Confidence: {combined.get('confidence', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced sentiment test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints (requires server to be running)"""
    print("\n🌐 TESTING API ENDPOINTS")
    print("-" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint: PASS")
            health_ok = True
        else:
            print(f"⚠️ Health endpoint: {response.status_code}")
            health_ok = False
    except Exception as e:
        print(f"❌ Health endpoint failed: {e}")
        health_ok = False
    
    if not health_ok:
        print("⚠️ Server not running, skipping API tests")
        return False
    
    # Test TokenMetrics endpoint (would need auth token in real scenario)
    try:
        # This would fail without auth, but we can check if endpoint exists
        response = requests.get(f"{base_url}/api/tokenmetrics/BTC", timeout=5)
        print(f"✅ TokenMetrics endpoint exists: {response.status_code}")
    except Exception as e:
        print(f"⚠️ TokenMetrics endpoint: {e}")
    
    # Test news endpoint
    try:
        response = requests.get(f"{base_url}/api/news/live", timeout=5)
        print(f"✅ News endpoint exists: {response.status_code}")
    except Exception as e:
        print(f"⚠️ News endpoint: {e}")
    
    return True

def main():
    """Run all integration tests"""
    print("🧪 COMPREHENSIVE INTEGRATION TEST SUITE")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now()}")
    print()
    
    # Run all tests
    tests = [
        ("TokenMetrics Integration", test_tokenmetrics_fallback),
        ("Reddit/GitHub Integration", test_reddit_github_integration),
        ("Discord News Integration", test_discord_news),
        ("Enhanced Sentiment Analysis", test_enhanced_sentiment),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{len(results)} tests passed ({(passed/len(results))*100:.1f}%)")
    
    if passed == len(results):
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ TokenMetrics fallback working")
        print("✅ Reddit/GitHub token filtering operational")
        print("✅ Discord news integration functional")
        print("✅ Enhanced sentiment analysis working")
        print("✅ API endpoints accessible")
    else:
        print("⚠️ Some integration tests failed")
        print("🔧 Check individual components for issues")
    
    print(f"\n⏰ Test completed at: {datetime.now()}")
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
