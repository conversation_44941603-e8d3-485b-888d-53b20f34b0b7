#!/usr/bin/env python3
"""
🧪 SDK TEST SCRIPT
Test all installed SDKs to ensure they're working
"""

import sys
import os

sys.path.append("backend")


def test_sdk_imports():
    """Test if all SDKs can be imported"""
    results = {}

    # Test KuCoin SDK
    try:
        from kucoin.client import Market, Trade, User

        results["kucoin"] = "✅ KuCoin SDK imported successfully"
    except ImportError as e:
        results["kucoin"] = f"❌ KuCoin SDK import failed: {e}"

    # Test CoinGecko SDK
    try:
        from pycoingecko import CoinGeckoAPI

        results["coingecko"] = "✅ CoinGecko SDK imported successfully"
    except ImportError as e:
        results["coingecko"] = f"❌ CoinGecko SDK import failed: {e}"

    # Test Anthropic SDK
    try:
        from anthropic import Anthropic

        results["anthropic"] = "✅ Anthropic SDK imported successfully"
    except ImportError as e:
        results["anthropic"] = f"❌ Anthropic SDK import failed: {e}"

    # Test Gemini SDK
    try:
        from google import genai

        results["gemini"] = "✅ Gemini SDK imported successfully"
    except ImportError as e:
        results["gemini"] = f"❌ Gemini SDK import failed: {e}"

    # Test OpenAI SDK
    try:
        from openai import OpenAI

        results["openai"] = "✅ OpenAI SDK imported successfully"
    except ImportError as e:
        results["openai"] = f"❌ OpenAI SDK import failed: {e}"

    # Test Telegram SDK
    try:
        from telegram import Bot

        results["telegram"] = "✅ Telegram SDK imported successfully"
    except ImportError as e:
        results["telegram"] = f"❌ Telegram SDK import failed: {e}"

    # Test Discord SDK
    try:
        import discord

        results["discord"] = "✅ Discord SDK imported successfully"
    except ImportError as e:
        results["discord"] = f"❌ Discord SDK import failed: {e}"

    return results


if __name__ == "__main__":
    print("🧪 TESTING SDK IMPORTS")
    print("=" * 40)

    results = test_sdk_imports()

    success_count = 0
    total_count = len(results)

    for sdk, result in results.items():
        print(f"{sdk.upper()}: {result}")
        if "✅" in result:
            success_count += 1

    print(f"\n📊 SUMMARY: {success_count}/{total_count} SDKs imported successfully")

    if success_count == total_count:
        print("🎉 All SDKs installed and working!")
    else:
        print("⚠️ Some SDKs failed to import - check installation")
