#!/usr/bin/env python3
"""
Simple Trade Test
Test the current paper trading system to ensure trades are being logged properly.
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')
load_dotenv('.env')

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.trade_executor import execute_trade, print_pnl_dashboard
from backend.price_fetcher import get_price

async def test_paper_trading():
    """Test the paper trading system"""
    
    print("📝 Testing Paper Trading System")
    print("=" * 50)
    
    # Test 1: Get current price
    print("\n💲 Test 1: Getting Current Price")
    print("-" * 30)
    
    btc_price = await get_price("BTC")
    if btc_price:
        print(f"✅ BTC Price: ${btc_price:,.2f}")
    else:
        print("❌ Failed to get BTC price")
        btc_price = 100000  # Fallback price for testing
    
    # Test 2: Execute a paper buy trade
    print("\n🛒 Test 2: Executing Paper BUY Trade")
    print("-" * 30)
    
    buy_result = execute_trade(
        token_symbol="BTC",
        side="BUY",
        amount_usd=50.0,
        price=btc_price,
        strategy="Test",
        reason="Testing paper trading system"
    )
    
    if buy_result.get("success"):
        print("✅ BUY trade executed successfully!")
        print(f"   Message: {buy_result.get('message')}")
        print(f"   P&L: ${buy_result.get('pnl', 0):.2f}")
    else:
        print(f"❌ BUY trade failed: {buy_result.get('message')}")
    
    # Test 3: Execute a paper sell trade
    print("\n💰 Test 3: Executing Paper SELL Trade")
    print("-" * 30)
    
    # Simulate a slightly higher price for profit
    sell_price = btc_price * 1.02  # 2% higher
    
    sell_result = execute_trade(
        token_symbol="BTC",
        side="SELL",
        amount_usd=25.0,  # Sell half
        price=sell_price,
        strategy="Test",
        reason="Testing paper trading system - taking profit"
    )
    
    if sell_result.get("success"):
        print("✅ SELL trade executed successfully!")
        print(f"   Message: {sell_result.get('message')}")
        print(f"   P&L: ${sell_result.get('pnl', 0):.2f}")
    else:
        print(f"❌ SELL trade failed: {sell_result.get('message')}")
    
    # Test 4: Show P&L Dashboard
    print("\n📊 Test 4: P&L Dashboard")
    print("-" * 30)
    
    try:
        print_pnl_dashboard()
        print("✅ P&L dashboard generated successfully!")
    except Exception as e:
        print(f"❌ P&L dashboard failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Paper Trading Test Complete!")
    print("\n💡 Key Points:")
    print("  • Paper trading system is working")
    print("  • Trades are being logged to CSV")
    print("  • Portfolio state is tracked in JSON")
    print("  • P&L calculations are functional")
    print("  • Ready for real trading when KuCoin is properly configured")

if __name__ == "__main__":
    asyncio.run(test_paper_trading())
