{"version": 7, "name": "Alphafrontend", "description": "Alpha Predator Trading Bot - React Frontend Dashboard for cryptocurrency trading analytics, live trades monitoring, and AI-powered trading signals", "owner": "**********************************", "compose": [{"name": "alphAfrontend", "description": "React frontend dashboard with nginx serving static files", "repotag": "kryptomerch/alpha-frontend:latest", "ports": ["37466:80"], "domains": ["www.alphapredatorbot.xyz"], "environmentParameters": [{"name": "NODE_ENV", "value": "production"}, {"name": "VITE_BACKEND_URL", "value": "https://api.alphapredatorbot.xyz"}, {"name": "VITE_PUBLIC_TELEGRAM", "value": "https://t.me/alphapredatortrading"}, {"name": "VITE_PUBLIC_GITHUB", "value": "https://github.com/kryptomerch/alpha-predator-safe"}, {"name": "VITE_BRAND_NAME", "value": "Alpha Predator <PERSON>"}], "cpu": 0.5, "ram": 1000, "hdd": 10, "tiered": true, "secrets": [], "commands": [], "containerData": "/tmp", "containerPorts": [80], "nodeSpecificParameters": []}], "geolocation": [], "instances": 3, "staticip": false}