---
type: "always_apply"
description: "Example description"
---
🔹 Code Quality & Structure
	•	✅ Follow modular architecture. Each module should serve one specific purpose.
	•	✅ Use type hints, docstrings, and proper comments in all Python functions.
	•	✅ Ensure all config values (API keys, thresholds, etc.) are loaded from .env files or a config class — never hardcode them.
	•	✅ All changes must be compatible with Docker and must pass local testing.

🔹 AI Decision Engine
	•	✅ Validate that structured prompts sent to Gemini, ChatGPT, and DeepSeek follow consistent logic (token, price, news, chart, sentiment).
	•	✅ Ensure consensus logic checks for at least 2/3 agreement before executing any trade.
	•	✅ All AI-generated decisions must include:
	•	Reason summary
	•	Confidence score (if possible)
	•	Timestamp
	•	Trade context (token, risk, trend)

🔹 Trading Logic
	•	✅ Respect trading constraints:
	•	Meme coins: max $20 buy, special exit logic.
	•	Others: $100 cap, 10% stop loss, 20% dynamic TP.
	•	✅ Execute trades only in TRADING_MODE=LIVE and simulate in TRADING_MODE=PAPER.
	•	✅ Implement cooldown logic (if recent losses > threshold, skip token for X hours).

🔹 Token Selection
	•	✅ Rank tokens using combined scores from:
	•	Volume, volatility
	•	Sentiment
	•	Community signals (Reddit, Discord)
	•	New listings on CoinGecko or KuCoin
	•	✅ Output top 50 tokens daily into token_list.json.

🔹 News & Sentiment
	•	✅ Parse and forward messages from:
	•	CoinTelegraph, CoinDesk, Reddit, Twitter, Discord
	•	✅ Save news articles/messages with:
	•	Sentiment label
	•	Tags (hack, listing, FUD, etc.)
	•	Timestamp and token mentions
	•	✅ Discard duplicate/spam/FUD messages before AI analysis.

🔹 Telegram Bot
	•	✅ Maintain:
	•	/summary command
	•	Inline buttons for trade confirmation
	•	Auto-execution after timeout (if no response in 60s)
	•	Daily PnL and trade alert messages

🔹 Logging & PnL
	•	✅ Log all trades to trades.csv with full metadata
	•	✅ Update portfolio JSON after every trade
	•	✅ Refresh pnl_dashboard.py to track:
	•	Realized vs Unrealized PnL
	•	Worst and best trades
	•	Token duration held
	•	Fees, slippage impact

🔹 Frontend API
	•	✅ Ensure /api/pnl, /api/trades/live, /api/tokens, /api/ai-logic routes work and return real data
	•	✅ Secure API endpoints and allow only whitelisted IPs (in production)
	•	✅ Format PnL and token data for React + Recharts display

⸻

🚫 DO NOTs for Augment

🔹 Code Quality
	•	❌ Do NOT use magic numbers or hardcoded config inside functions.
	•	❌ Do NOT generate overly long functions (>100 lines) — always modularize.
	•	❌ Do NOT remove logging or debugging output without proper replacement.

🔹 AI & Trading Logic
	•	❌ Do NOT let the bot make trades without proper AI consensus.
	•	❌ Do NOT send unstructured or inconsistent prompts to AI models.
	•	❌ Do NOT override trade limits (e.g., buying >$20 meme coin).
	•	❌ Do NOT reuse old AI decisions — every trade must use fresh data.

🔹 Token Filtering
	•	❌ Do NOT include tokens with low volume, zero liquidity, or low sentiment.
	•	❌ Do NOT allow repeated buys of the same token within the cooldown period.
	•	❌ Do NOT store invalid or incomplete token symbols (e.g., USDTUSDT).

🔹 News & Alerts
	•	❌ Do NOT fetch from broken URLs or stale RSS feeds.
	•	❌ Do NOT relay news to Telegram without sentiment analysis.
	•	❌ Do NOT allow unverified Twitter signals to trigger trades directly.

🔹 Security & Deployment
	•	❌ Do NOT commit .env or API keys to version control.
	•	❌ Do NOT expose admin dashboards or internal logs publicly.
	•	❌ Do NOT allow automatic trading in production without explicit flags or toggles.

⸻
