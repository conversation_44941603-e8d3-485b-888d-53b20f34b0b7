#!/bin/bash

# Quick Docker Logs - One-liner commands for Alpha Predator Trading Bot
# Usage: ./quick_logs.sh [command]

case "$1" in
    "backend"|"b")
        echo "=== Backend Logs (Last 50 lines) ==="
        ./docker_manager.sh logs backend 50
        ;;
    "frontend"|"f")
        echo "=== Frontend Logs (Last 50 lines) ==="
        ./docker_manager.sh logs frontend 50
        ;;
    "both"|"all"|"")
        echo "=== All Service Logs (Last 30 lines each) ==="
        ./docker_manager.sh logs all 30
        ;;
    "follow-backend"|"fb")
        echo "=== Following Backend Logs (Ctrl+C to stop) ==="
        ./docker_manager.sh follow backend
        ;;
    "follow-frontend"|"ff")
        echo "=== Following Frontend Logs (Ctrl+C to stop) ==="
        ./docker_manager.sh follow frontend
        ;;
    "status"|"s")
        echo "=== Container Status ==="
        ./docker_manager.sh status
        ;;
    "start")
        echo "=== Starting Services ==="
        ./docker_manager.sh start
        ;;
    "restart"|"r")
        echo "=== Restarting Services ==="
        ./docker_manager.sh restart
        ;;
    "errors"|"e")
        echo "=== Recent Errors ==="
        echo "Backend errors:"
        ./docker_manager.sh logs backend 100 | grep -i error | tail -5
        echo ""
        echo "Frontend errors:"
        ./docker_manager.sh logs frontend 100 | grep -i error | tail -5
        ;;
    "help"|"h"|*)
        echo "Quick Docker Logs Commands:"
        echo ""
        echo "  ./quick_logs.sh backend     (or b)  - Show backend logs"
        echo "  ./quick_logs.sh frontend    (or f)  - Show frontend logs"
        echo "  ./quick_logs.sh both        (or all) - Show both logs"
        echo "  ./quick_logs.sh follow-backend (fb) - Follow backend logs"
        echo "  ./quick_logs.sh follow-frontend (ff) - Follow frontend logs"
        echo "  ./quick_logs.sh status      (or s)  - Show container status"
        echo "  ./quick_logs.sh start              - Start services"
        echo "  ./quick_logs.sh restart     (or r)  - Restart services"
        echo "  ./quick_logs.sh errors      (or e)  - Show recent errors"
        echo "  ./quick_logs.sh help        (or h)  - Show this help"
        echo ""
        echo "Examples:"
        echo "  ./quick_logs.sh b           # Quick backend logs"
        echo "  ./quick_logs.sh fb          # Follow backend logs"
        echo "  ./quick_logs.sh e           # Check for errors"
        ;;
esac
