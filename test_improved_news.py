#!/usr/bin/env python3
"""
Test Improved News System with Fallback Mechanism
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from backend.news_ingestion.cryptopanic_news import fetch_cryptopanic_news, save_news_to_file

def test_improved_news_system():
    print('🧪 Testing Improved CryptoPanic News System')
    print('=' * 60)

    # Test fetching with fallback
    print('📡 Testing news fetching with fallback...')
    news_data = fetch_cryptopanic_news(['BTC', 'ETH', 'SOL'])

    if news_data:
        print(f'✅ Successfully fetched {len(news_data)} articles')
        print('📰 Sample articles:')
        for i, article in enumerate(news_data[:3]):
            title = article.get("title", "N/A")
            source = article.get("source", {})
            if isinstance(source, dict):
                source_name = source.get("title", "N/A")
            else:
                source_name = str(source)
            print(f'  {i+1}. {title[:80]}...')
            print(f'     Source: {source_name}')
    else:
        print('❌ No news data fetched')

    # Test saving functionality
    print('\n💾 Testing save functionality...')
    save_news_to_file(['BTC', 'ETH', 'SOL'])
    
    # Check if file was created/updated
    if os.path.exists('backend/data/cryptopanic.json'):
        import json
        with open('backend/data/cryptopanic.json', 'r') as f:
            saved_data = json.load(f)
        print(f'✅ News saved successfully: {len(saved_data)} total articles')
        
        # Show sample saved article
        if saved_data:
            sample = saved_data[-1]  # Latest article
            print(f'📄 Latest article: {sample.get("title", "N/A")[:60]}...')
            print(f'🎯 Sentiment: {sample.get("sentiment_score", "N/A")}')
    else:
        print('⚠️ News file not found')

    print('\n🎯 Improved News System Features:')
    print('✅ Fallback news generation when API fails')
    print('✅ Improved error handling and logging')
    print('✅ Better user agent for API requests')
    print('✅ Graceful degradation to mock data')
    print('✅ Sentiment scoring for all articles')

if __name__ == '__main__':
    test_improved_news_system()
