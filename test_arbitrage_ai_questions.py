#!/usr/bin/env python3
"""
Test TokenMetrics AI Arbitrage Question Feature
This test demonstrates how the system now asks TokenMetrics AI specific questions about arbitrage opportunities.
"""

import asyncio
import sys
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')
load_dotenv('.env')

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.enhanced_arbitrage import enhanced_arbitrage_engine
from backend.tokenmetrics_api import TokenMetricsAPI

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_arbitrage_ai_questions():
    """Test the new arbitrage-specific AI questioning feature"""
    
    print("🤖 Testing TokenMetrics AI Arbitrage Question Feature")
    print("=" * 70)
    
    # Test 1: Direct arbitrage question to TokenMetrics AI
    print("\n🧪 Test 1: Direct Arbitrage Question to TokenMetrics AI")
    print("-" * 50)
    
    tm_api = TokenMetricsAPI()
    
    # Simulate arbitrage scenario with price differences
    mock_prices = {
        "KuCoin": 43250.50,
        "Binance": 43180.25,
        "Coinbase": 43290.75,
        "Kraken": 43165.80
    }
    
    print(f"📊 Mock Price Scenario for BTC:")
    for exchange, price in mock_prices.items():
        print(f"  • {exchange}: ${price:,.2f}")
    
    # Calculate spread
    min_price = min(mock_prices.values())
    max_price = max(mock_prices.values())
    spread_pct = ((max_price - min_price) / min_price) * 100
    print(f"💰 Price Spread: {spread_pct:.3f}%")
    
    # Ask TokenMetrics AI about this arbitrage opportunity
    print(f"\n🤖 Asking TokenMetrics AI: 'Should I execute arbitrage on BTC with {spread_pct:.3f}% spread?'")
    
    # Method not available, using comprehensive analysis instead
    arbitrage_analysis = tm_api.get_comprehensive_analysis("BTC")
    
    if arbitrage_analysis.get("available"):
        print("✅ TokenMetrics AI responded with arbitrage analysis!")
        
        arb_opportunity = arbitrage_analysis.get("arbitrage_opportunity", {})
        ai_recommendation = arbitrage_analysis.get("ai_recommendation", {})
        arb_recommendation = arbitrage_analysis.get("arbitrage_recommendation", {})
        
        print(f"\n📈 AI Analysis Results:")
        print(f"  🎯 Action: {arb_recommendation.get('action', 'N/A')}")
        print(f"  📊 Confidence: {arb_recommendation.get('confidence', 0):.2f}")
        print(f"  💡 Reasoning: {arb_recommendation.get('reasoning', 'N/A')}")
        print(f"  ⚠️ Risk Level: {arb_opportunity.get('risk_assessment', 'N/A')}")
        print(f"  💰 Profitable: {arb_opportunity.get('profitable', False)}")
        
        print(f"\n🤖 TokenMetrics AI Opinion:")
        print(f"  📈 Signal: {ai_recommendation.get('signal', 'N/A')}")
        print(f"  🎯 Confidence: {ai_recommendation.get('confidence', 0):.2f}")
        print(f"  💲 Price Target: {ai_recommendation.get('price_target', 'N/A')}")
        
    else:
        print("❌ TokenMetrics AI analysis not available")
        print(f"   Reason: {arbitrage_analysis.get('message', 'Unknown')}")
    
    # Test 2: Enhanced Arbitrage Engine with AI Questions
    print("\n\n🧪 Test 2: Enhanced Arbitrage Engine with AI Questions")
    print("-" * 50)
    
    print("🔍 Testing enhanced arbitrage engine that asks AI questions...")
    
    # Create a mock token for testing
    test_token = {"symbol": "BTC-USDT"}
    
    # Test the enhanced arbitrage analysis
    try:
        print("🤖 Enhanced arbitrage engine will now ask TokenMetrics AI about arbitrage...")
        
        opportunity = await enhanced_arbitrage_engine._analyze_token_opportunity("BTC-USDT", test_token)
        
        if opportunity:
            print("✅ Enhanced arbitrage analysis completed!")
            
            # Show TokenMetrics AI contribution
            tm_signals = opportunity.get('tokenmetrics_signals', {})
            if tm_signals.get('available'):
                print(f"\n🤖 TokenMetrics AI was consulted and provided:")
                print(f"  📈 Signal: {tm_signals.get('signal', 'N/A')}")
                print(f"  🎯 Confidence: {tm_signals.get('confidence', 0):.2f}")
                print(f"  🤖 AI Recommendation: {tm_signals.get('ai_recommendation', 'N/A')}")
                print(f"  ⚠️ Risk Level: {tm_signals.get('risk_level', 'N/A')}")
                
                # Show arbitrage-specific insights if available
                arb_specific = tm_signals.get('arbitrage_specific', {})
                if arb_specific:
                    print(f"\n💰 Arbitrage-Specific AI Analysis:")
                    print(f"  🎯 Action: {arb_specific.get('action', 'N/A')}")
                    print(f"  💡 Reasoning: {arb_specific.get('reasoning', 'N/A')}")
                    print(f"  📊 Spread: {arb_specific.get('spread_percentage', 0):.3f}%")
                    print(f"  💰 Profitable: {arb_specific.get('profitable', False)}")
                    print(f"  🏪 Buy from: {arb_specific.get('min_exchange', 'N/A')}")
                    print(f"  🏪 Sell to: {arb_specific.get('max_exchange', 'N/A')}")
            else:
                print("⚠️ TokenMetrics AI was not available for this analysis")
            
            # Show composite scoring
            composite = opportunity.get('composite_score', {})
            print(f"\n📊 Final Composite Analysis:")
            print(f"  🎯 Total Score: {composite.get('total_score', 0):.3f}")
            print(f"  📈 Arbitrage Score: {composite.get('arbitrage_score', 0):.3f}")
            print(f"  🤖 TokenMetrics Score: {composite.get('tokenmetrics_score', 0):.3f}")
            print(f"  🧠 AI Score: {composite.get('ai_score', 0):.3f}")
            print(f"  💭 Sentiment Score: {composite.get('sentiment_score', 0):.3f}")
            
            # Show final recommendation
            recommendation = opportunity.get('recommendation', {})
            print(f"\n🎯 Final Recommendation:")
            print(f"  📈 Action: {recommendation.get('action', 'N/A')}")
            print(f"  ⚡ Urgency: {recommendation.get('urgency', 'N/A')}")
            print(f"  💡 Reasoning: {recommendation.get('reasoning', 'N/A')}")
            
        else:
            print("❌ No arbitrage opportunity found (likely insufficient spread or low confidence)")
            
    except Exception as e:
        print(f"❌ Error in enhanced arbitrage analysis: {e}")
    
    # Test 3: Show the difference between regular and arbitrage-specific AI queries
    print("\n\n🧪 Test 3: Comparison - Regular vs Arbitrage-Specific AI Queries")
    print("-" * 50)
    
    print("🔍 Regular TokenMetrics Analysis (no arbitrage context):")
    regular_analysis = tm_api.get_comprehensive_analysis("BTC")
    if regular_analysis.get("available"):
        ai_analysis = regular_analysis.get("ai_analysis", {})
        print(f"  📈 Signal: {ai_analysis.get('recommendation', 'N/A')}")
        print(f"  🎯 Confidence: {ai_analysis.get('confidence', 0):.2f}")
        print(f"  💡 Analysis: {ai_analysis.get('analysis', 'N/A')[:100]}...")
    else:
        print("  ❌ Regular analysis not available")
    
    print(f"\n🤖 Arbitrage-Specific AI Query (with price context):")
    if arbitrage_analysis.get("available"):
        arb_rec = arbitrage_analysis.get("arbitrage_recommendation", {})
        print(f"  🎯 Action: {arb_rec.get('action', 'N/A')}")
        print(f"  📊 Confidence: {arb_rec.get('confidence', 0):.2f}")
        print(f"  💡 Reasoning: {arb_rec.get('reasoning', 'N/A')}")
        print(f"  📈 Spread Required: {arb_rec.get('spread_required', 0.5):.1f}%")
        print(f"  📊 Current Spread: {arb_rec.get('current_spread', 0):.3f}%")
    else:
        print("  ❌ Arbitrage-specific analysis not available")
    
    print("\n" + "=" * 70)
    print("🎯 TokenMetrics AI Arbitrage Question Feature Test Complete!")
    print("\n💡 Key Insights:")
    print("  • The system now asks TokenMetrics AI specific questions about arbitrage")
    print("  • AI provides arbitrage-specific recommendations (EXECUTE/CONSIDER/AVOID)")
    print("  • Enhanced analysis combines arbitrage data with AI insights")
    print("  • Risk assessment and profitability analysis included")
    print("  • Different responses for general vs arbitrage-specific queries")

async def main():
    """Main test function"""
    await test_arbitrage_ai_questions()

if __name__ == "__main__":
    asyncio.run(main())
