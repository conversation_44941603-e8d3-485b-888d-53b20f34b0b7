# 🚀 Alpha Predator - Production-Grade Crypto Trading Bot

[![CI/CD Pipeline](https://github.com/your-username/alpha-predator/actions/workflows/ci-cd.yml/badge.svg)](https://github.com/your-username/alpha-predator/actions/workflows/ci-cd.yml)
[![Security Scan](https://github.com/your-username/alpha-predator/actions/workflows/security.yml/badge.svg)](https://github.com/your-username/alpha-predator/actions/workflows/security.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A sophisticated, production-ready cryptocurrency trading bot with AI-powered decision making, real-time market analysis, and comprehensive risk management.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   External APIs │
│   (React/Vite)  │◄──►│   (FastAPI)     │◄──►│   (KuCoin, etc) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   Database      │◄─────────────┘
                        │   (JSON/Redis)  │
                        └─────────────────┘
```

## ✨ Features

### 🤖 AI-Powered Trading
- **Multi-Model AI Integration**: OpenAI GPT, Google Gemini, DeepSeek
- **Sentiment Analysis**: Real-time news and social media sentiment
- **Technical Analysis**: 15+ trading strategies and indicators
- **Risk Management**: Dynamic stop-loss and take-profit optimization

### 📊 Real-Time Analytics
- **Live Market Data**: KuCoin, CoinGecko, CoinMarketCap integration
- **Performance Tracking**: P&L analysis, win rate, Sharpe ratio
- **Backtesting Engine**: Historical strategy validation
- **Portfolio Management**: Multi-asset position tracking

### 🔐 Enterprise Security
- **JWT Authentication**: Secure token-based auth with refresh
- **Google OAuth**: Single sign-on integration
- **Rate Limiting**: API protection and abuse prevention
- **Input Validation**: Comprehensive request sanitization
- **CORS Protection**: Secure cross-origin resource sharing

### 🚀 Production Infrastructure
- **Docker Containerization**: Multi-stage production builds
- **CI/CD Pipeline**: Automated testing, building, and deployment
- **Health Monitoring**: Prometheus metrics and Grafana dashboards
- **Structured Logging**: JSON logs with correlation IDs
- **Auto-scaling**: Kubernetes-ready deployment manifests

## 🛠️ Tech Stack

### Backend
- **Framework**: FastAPI (Python 3.11)
- **Authentication**: JWT + Google OAuth
- **Database**: JSON files + Redis caching
- **AI/ML**: OpenAI, Google Gemini, DeepSeek APIs
- **Trading**: KuCoin API integration
- **Monitoring**: Prometheus + Grafana

### Frontend
- **Framework**: React 18 + Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context + Hooks
- **HTTP Client**: Axios with interceptors
- **Build Tool**: Vite with optimizations

### DevOps
- **Containerization**: Docker + Docker Compose
- **CI/CD**: GitHub Actions
- **Orchestration**: Kubernetes (optional)
- **Monitoring**: Prometheus, Grafana, Health checks
- **Security**: Trivy vulnerability scanning

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### 1. Clone Repository
```bash
git clone https://github.com/your-username/alpha-predator.git
cd alpha-predator
```

### 2. Environment Setup
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp backend/.env.example backend/.env.production

# Edit environment variables
nano backend/.env.production
```

### 3. Production Deployment
```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Check health
curl http://localhost/health
curl http://localhost:8000/health
```

### 4. Access Application
- **Frontend**: http://localhost
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

## 📋 Environment Variables

### Required Variables
| Variable | Description | Example |
|----------|-------------|---------|
| `JWT_SECRET_KEY` | JWT signing secret (32+ chars) | `your-super-secure-jwt-secret-key` |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | `123456789.apps.googleusercontent.com` |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | `GOCSPX-xxxxxxxxxxxxx` |
| `ALLOWED_EMAILS` | Comma-separated allowed emails | `<EMAIL>,<EMAIL>` |

### Trading APIs
| Variable | Description | Required |
|----------|-------------|----------|
| `KUCOIN_API_KEY` | KuCoin API key | Yes |
| `KUCOIN_API_SECRET` | KuCoin API secret | Yes |
| `KUCOIN_API_PASSPHRASE` | KuCoin API passphrase | Yes |
| `OPENAI_API_KEY` | OpenAI API key | Optional |
| `GEMINI_API_KEY` | Google Gemini API key | Optional |

### External Services
| Variable | Description | Required |
|----------|-------------|----------|
| `TELEGRAM_BOT_TOKEN` | Telegram bot token | Optional |
| `DISCORD_BOT_TOKEN` | Discord bot token | Optional |
| `COINGECKO_API_KEY` | CoinGecko API key | Optional |

## 🔧 Development

### Local Development Setup
```bash
# Backend
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --port 8000

# Frontend
cd frontend
npm install
npm run dev
```

### Running Tests
```bash
# Backend tests
cd backend
pytest tests/ -v --cov=.

# Frontend tests
cd frontend
npm run test
```

### Code Quality
```bash
# Backend linting
cd backend
black .
isort .
flake8 .

# Frontend linting
cd frontend
npm run lint
npm run type-check
```

## 📊 API Documentation

### Authentication Endpoints
- `POST /api/login` - Email/password login
- `GET /api/auth/google` - Google OAuth login
- `POST /api/auth/refresh` - Refresh JWT token

### Trading Endpoints
- `POST /api/trades/live` - Execute manual trade
- `POST /api/trades/live/strategy-trade` - Execute AI strategy trade
- `GET /api/trades/summary` - Get trading summary

### Market Data Endpoints
- `GET /api/market/price/{symbol}` - Get current price
- `GET /api/market/sentiment/{symbol}` - Get sentiment analysis
- `GET /api/market/signals` - Get AI trading signals

## 🐳 Docker Deployment

### Production Deployment
```bash
# Start production stack
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --remove-orphans
```

### Monitoring Stack
```bash
# Start with monitoring
docker-compose -f docker-compose.prod.yml --profile monitoring up -d

# View metrics
open http://localhost:3000  # Grafana
open http://localhost:9090  # Prometheus
```

## ☸️ Kubernetes Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n alpha-predator

# View logs
kubectl logs -f deployment/alpha-predator-backend -n alpha-predator
```

## 📈 Monitoring & Observability

### Health Checks
- **Backend**: `GET /health`
- **Frontend**: `GET /health`
- **Database**: Connection status
- **External APIs**: Response time monitoring

### Metrics
- Request/response times
- Error rates and status codes
- Trading performance metrics
- System resource usage

### Logging
- Structured JSON logs in production
- Request correlation IDs
- Error tracking with stack traces
- Trading event audit logs

## 🔒 Security

### Authentication & Authorization
- JWT tokens with expiration
- Google OAuth integration
- Email-based access control
- Secure cookie handling

### API Security
- Rate limiting (10 req/s general, 5 req/m login)
- Input validation and sanitization
- CORS protection
- Security headers (CSP, HSTS, etc.)

### Infrastructure Security
- Non-root container users
- Secret management via environment variables
- Network isolation with Docker networks
- Regular security scanning with Trivy

## 🚨 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check logs
docker-compose logs backend

# Common fixes
docker-compose down
docker-compose up -d --force-recreate
```

#### Frontend Build Fails
```bash
# Clear cache and rebuild
cd frontend
rm -rf node_modules dist
npm install
npm run build
```

#### Database Connection Issues
```bash
# Check Redis connection
docker-compose exec redis redis-cli ping

# Reset data volumes
docker-compose down -v
docker-compose up -d
```

### Performance Optimization

#### Backend Performance
- Increase worker count: `--workers 4`
- Enable Redis caching
- Optimize database queries
- Use connection pooling

#### Frontend Performance
- Enable gzip compression
- Optimize bundle size
- Use CDN for static assets
- Implement code splitting

## 📝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 for Python code
- Use ESLint/Prettier for JavaScript
- Write tests for new features
- Update documentation
- Ensure CI/CD passes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) - Modern Python web framework
- [React](https://reactjs.org/) - Frontend library
- [KuCoin](https://www.kucoin.com/) - Cryptocurrency exchange
- [OpenAI](https://openai.com/) - AI/ML APIs
- [Docker](https://www.docker.com/) - Containerization platform

## 📞 Support

- **Documentation**: [Wiki](https://github.com/your-username/alpha-predator/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/alpha-predator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/alpha-predator/discussions)
- **Email**: <EMAIL>

---

**⚠️ Disclaimer**: This software is for educational purposes only. Cryptocurrency trading involves substantial risk of loss. Use at your own risk.
