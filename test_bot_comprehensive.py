#!/usr/bin/env python3
"""
Comprehensive Bot Testing Script
Tests all major functionality and API endpoints
"""

import requests
import json
import time
import sys
import os

# Add backend to path
sys.path.append('backend')

from backend.auth import create_access_token

class BotTester:
    def __init__(self, base_url="http://localhost:3005"):
        self.base_url = base_url
        self.token = None
        self.headers = {}
        self.results = {
            "passed": 0,
            "failed": 0,
            "tests": []
        }
    
    def authenticate(self):
        """Get authentication token"""
        try:
            # Create test token
            self.token = create_access_token(data={"sub": "<EMAIL>"})
            self.headers = {"Authorization": f"Bearer {self.token}"}
            print("✅ Authentication token created")
            return True
        except Exception as e:
            print(f"❌ Authentication failed: {e}")
            return False
    
    def test_endpoint(self, name, endpoint, method="GET", data=None, auth_required=True):
        """Test a single endpoint"""
        try:
            url = f"{self.base_url}{endpoint}"
            headers = self.headers if auth_required else {}
            
            response = None
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data, timeout=10)
            
            if response and response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"✅ {name}: {response.status_code} - {len(str(json_data))} chars")
                    self.results["passed"] += 1
                    self.results["tests"].append({
                        "name": name,
                        "status": "PASS",
                        "endpoint": endpoint,
                        "response_size": len(str(json_data))
                    })
                    return json_data
                except:
                    print(f"✅ {name}: {response.status_code} - Non-JSON response")
                    self.results["passed"] += 1
                    return response.text
            else:
                if response:
                    print(f"❌ {name}: {response.status_code} - {response.text[:100]}")
                    error_msg = f"{response.status_code}: {response.text[:100]}"
                else:
                    print(f"❌ {name}: No response received")
                    error_msg = "No response received"
                
                self.results["failed"] += 1
                self.results["tests"].append({
                    "name": name,
                    "status": "FAIL",
                    "endpoint": endpoint,
                    "error": error_msg
                })
                return None
                
        except Exception as e:
            print(f"❌ {name}: Exception - {str(e)}")
            self.results["failed"] += 1
            self.results["tests"].append({
                "name": name,
                "status": "ERROR",
                "endpoint": endpoint,
                "error": str(e)
            })
            return None
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🚀 Starting Comprehensive Bot Tests")
        print("=" * 60)
        
        # 1. Health checks (no auth required)
        print("\n🏥 HEALTH CHECKS")
        print("-" * 30)
        self.test_endpoint("Root Health", "/", auth_required=False)
        self.test_endpoint("Basic Health", "/health", auth_required=False)
        self.test_endpoint("API Health", "/api/health", auth_required=False)
        
        # 2. Authentication
        print("\n🔐 AUTHENTICATION")
        print("-" * 30)
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return
        
        # 3. Core trading endpoints
        print("\n💹 TRADING ENDPOINTS")
        print("-" * 30)
        self.test_endpoint("Tokens List", "/api/tokens?limit=5")
        self.test_endpoint("Spike Tokens", "/api/spike-tokens?limit=5")
        self.test_endpoint("Discover Tokens", "/api/discover?limit=5")
        self.test_endpoint("Portfolio Status", "/api/portfolio")
        self.test_endpoint("Trading Summary", "/api/summary")
        
        # 4. News and sentiment
        print("\n📰 NEWS & SENTIMENT")
        print("-" * 30)
        self.test_endpoint("Live News", "/api/news/live?limit=10")
        self.test_endpoint("Sentiment Feed", "/api/sentiment-feed")
        
        # 5. AI and analytics
        print("\n🤖 AI & ANALYTICS")
        print("-" * 30)
        self.test_endpoint("AI Logic", "/api/ai-logic")
        self.test_endpoint("Analytics", "/api/analytics")
        self.test_endpoint("PnL Data", "/api/pnl-data")
        
        # 6. Fast dashboard endpoints
        print("\n⚡ FAST DASHBOARD")
        print("-" * 30)
        self.test_endpoint("Summary Fast", "/api/dashboard/summary-fast", auth_required=False)
        self.test_endpoint("Trades Fast", "/api/dashboard/trades-fast", auth_required=False)
        self.test_endpoint("PnL Fast", "/api/dashboard/pnl-fast", auth_required=False)
        self.test_endpoint("All Dashboard Fast", "/api/dashboard/all-fast", auth_required=False)
        
        # 7. TokenMetrics endpoints
        print("\n💎 TOKENMETRICS")
        print("-" * 30)
        self.test_endpoint("TokenMetrics Top Tokens", "/api/tokenmetrics/top-tokens", auth_required=False)
        self.test_endpoint("TokenMetrics Moonshots", "/api/tokenmetrics/moonshots", auth_required=False)
        
        # 8. Bot management
        print("\n🤖 BOT MANAGEMENT")
        print("-" * 30)
        self.test_endpoint("Alpha Bot Status", "/api/alpha-bot/status")
        self.test_endpoint("Micro Bot Status", "/api/micro-bot/status")
        
        # 9. Arbitrage
        print("\n⚖️ ARBITRAGE")
        print("-" * 30)
        self.test_endpoint("Arbitrage Opportunities", "/api/arbitrage/opportunities", auth_required=False)
        self.test_endpoint("Enhanced Arbitrage", "/api/arbitrage/enhanced")
        
        # 10. CoinGecko integration
        print("\n🦎 COINGECKO")
        print("-" * 30)
        self.test_endpoint("CoinGecko Usage", "/api/coingecko/usage", auth_required=False)
        self.test_endpoint("CoinGecko Enhanced Tokens", "/api/coingecko/enhanced-tokens", auth_required=False)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("-" * 30)
        total_tests = self.results["passed"] + self.results["failed"]
        success_rate = (self.results["passed"] / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.results['passed']}")
        print(f"Failed: {self.results['failed']}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if self.results["failed"] > 0:
            print("\n❌ FAILED TESTS:")
            for test in self.results["tests"]:
                if test["status"] in ["FAIL", "ERROR"]:
                    print(f"   • {test['name']}: {test.get('error', 'Unknown error')}")
        
        # Save detailed results
        with open("comprehensive_test_results.json", "w") as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: comprehensive_test_results.json")
        
        if success_rate >= 80:
            print("🎉 Bot is functioning well!")
            return True
        else:
            print("⚠️ Bot has significant issues that need attention")
            return False

def main():
    """Main test runner"""
    tester = BotTester()
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:3005/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly")
            return False
    except:
        print("❌ Server is not running. Please start the bot first.")
        return False
    
    # Run comprehensive tests
    success = tester.run_comprehensive_tests()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
