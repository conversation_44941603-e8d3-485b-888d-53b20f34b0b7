#!/usr/bin/env python3
"""
🔧 Comprehensive System Fix for Alpha Predator Bot
Fixes all critical issues identified in the logs:
1. KUCOIN_SANDBOX import error
2. TokenMetrics API authentication
3. CoinGecko API key issues
4. Rate limiting optimization
5. System performance improvements
"""

import os
import sys
import logging
import json
import time
from pathlib import Path
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemFixer:
    """Comprehensive system fixer for Alpha Predator Bot"""
    
    def __init__(self):
        self.backend_dir = Path("backend")
        self.fixes_applied = []
        self.errors_found = []
        
    def run_all_fixes(self):
        """Run all system fixes"""
        logger.info("🔧 Starting comprehensive system fixes...")
        
        try:
            # Fix 1: KUCOIN_SANDBOX import issue
            self.fix_kucoin_sandbox_import()
            
            # Fix 2: TokenMetrics API authentication
            self.fix_tokenmetrics_auth()
            
            # Fix 3: CoinGecko API key configuration
            self.fix_coingecko_api_key()
            
            # Fix 4: Rate limiting optimization
            self.optimize_rate_limiting()
            
            # Fix 5: System performance improvements
            self.optimize_system_performance()
            
            # Fix 6: Create fallback systems
            self.create_fallback_systems()
            
            # Generate summary report
            self.generate_fix_report()
            
        except Exception as e:
            logger.error(f"❌ Critical error during system fixes: {e}")
            raise
    
    def fix_kucoin_sandbox_import(self):
        """Fix KUCOIN_SANDBOX import error in config.py"""
        logger.info("🔧 Fixing KUCOIN_SANDBOX import issue...")
        
        try:
            config_file = self.backend_dir / "config.py"
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    content = f.read()
                
                # Add KUCOIN_SANDBOX configuration
                kucoin_sandbox_config = '''
# KuCoin Sandbox Configuration
KUCOIN_SANDBOX = os.getenv("KUCOIN_SANDBOX", "false").lower() == "true"
KUCOIN_SANDBOX_BASE_URL = "https://openapi-sandbox.kucoin.com"
KUCOIN_PRODUCTION_BASE_URL = "https://api.kucoin.com"

# Use appropriate base URL based on sandbox setting
KUCOIN_BASE_URL = KUCOIN_SANDBOX_BASE_URL if KUCOIN_SANDBOX else KUCOIN_PRODUCTION_BASE_URL
'''
                
                # Find the right place to insert (after KUCOIN_BASE_URL line)
                if "KUCOIN_SANDBOX" not in content:
                    # Replace the existing KUCOIN_BASE_URL line
                    content = content.replace(
                        'KUCOIN_BASE_URL = os.getenv("KUCOIN_BASE_URL", "https://api.kucoin.com")',
                        kucoin_sandbox_config.strip()
                    )
                    
                    with open(config_file, 'w') as f:
                        f.write(content)
                    
                    self.fixes_applied.append("✅ Added KUCOIN_SANDBOX configuration to config.py")
                    logger.info("✅ KUCOIN_SANDBOX configuration added successfully")
                else:
                    logger.info("ℹ️ KUCOIN_SANDBOX already configured")
            
        except Exception as e:
            error_msg = f"❌ Failed to fix KUCOIN_SANDBOX import: {e}"
            self.errors_found.append(error_msg)
            logger.error(error_msg)
    
    def fix_tokenmetrics_auth(self):
        """Fix TokenMetrics API authentication issues"""
        logger.info("🔧 Fixing TokenMetrics API authentication...")
        
        try:
            # Update the existing tokenmetrics_api.py with better error handling
            tokenmetrics_api_file = self.backend_dir / "tokenmetrics_api.py"
            
            if tokenmetrics_api_file.exists():
                with open(tokenmetrics_api_file, 'r') as f:
                    content = f.read()
                
                # Add improved error handling for authentication
                auth_fix = '''
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """Make API request with improved authentication error handling"""
        if not self.api_key:
            logger.warning("⚠️ TokenMetrics API key not available")
            return {"success": False, "error": "API key not configured"}
        
        try:
            url = f"{self.base_url}{endpoint}"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "AlphaPredatorBot/1.0"
            }
            
            # Add timeout and retry logic
            for attempt in range(3):
                try:
                    response = requests.get(url, headers=headers, params=params, timeout=30)
                    
                    if response.status_code == 200:
                        data = response.json()
                        return {"success": True, "data": data}
                    elif response.status_code == 401:
                        logger.error("TokenMetrics API: Unauthorized - check API key or subscription")
                        return {"success": False, "error": "Unauthorized - invalid API key or insufficient subscription"}
                    elif response.status_code == 429:
                        logger.warning("TokenMetrics API: Rate limited")
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    else:
                        logger.warning(f"TokenMetrics API returned status {response.status_code}")
                        return {"success": False, "error": f"HTTP {response.status_code}"}
                        
                except requests.exceptions.Timeout:
                    logger.warning(f"TokenMetrics API timeout (attempt {attempt + 1}/3)")
                    if attempt == 2:
                        return {"success": False, "error": "Request timeout"}
                    time.sleep(1)
                    
                except requests.exceptions.RequestException as e:
                    logger.error(f"TokenMetrics API request error: {e}")
                    return {"success": False, "error": str(e)}
            
            return {"success": False, "error": "Max retries exceeded"}
            
        except Exception as e:
            logger.error(f"TokenMetrics API unexpected error: {e}")
            return {"success": False, "error": str(e)}
'''
                
                # Replace the existing _make_request method if it exists
                if "_make_request" in content:
                    # Find and replace the method
                    import re
                    pattern = r'def _make_request\(self.*?(?=\n    def|\nclass|\n$)'
                    content = re.sub(pattern, auth_fix.strip(), content, flags=re.DOTALL)
                    
                    with open(tokenmetrics_api_file, 'w') as f:
                        f.write(content)
                    
                    self.fixes_applied.append("✅ Improved TokenMetrics API authentication error handling")
                    logger.info("✅ TokenMetrics API authentication improved")
            
        except Exception as e:
            error_msg = f"❌ Failed to fix TokenMetrics authentication: {e}"
            self.errors_found.append(error_msg)
            logger.error(error_msg)
    
    def fix_coingecko_api_key(self):
        """Fix CoinGecko API key configuration"""
        logger.info("🔧 Fixing CoinGecko API key configuration...")
        
        try:
            # Update config.py to handle missing CoinGecko API key
            config_file = self.backend_dir / "config.py"
            
            if config_file.exists():
                with open(config_file, 'r') as f:
                    content = f.read()
                
                # Add fallback for CoinGecko API key
                coingecko_fix = '''
# CoinGecko API Configuration with fallback
COINGECKO_API_KEY = os.getenv("COINGECKO_API_KEY", "")
COINGECKO_USE_FREE_API = not COINGECKO_API_KEY or COINGECKO_API_KEY == "your_coingecko_api_key_here"

if COINGECKO_USE_FREE_API:
    COINGECKO_BASE_URL = "https://api.coingecko.com/api/v3"
    COINGECKO_RATE_LIMIT = 50  # Free API limit per minute
else:
    COINGECKO_BASE_URL = "https://pro-api.coingecko.com/api/v3"
    COINGECKO_RATE_LIMIT = 500  # Pro API limit per minute
'''
                
                # Replace existing CoinGecko configuration
                if "COINGECKO_USE_FREE_API" not in content:
                    content = content.replace(
                        'COINGECKO_API_KEY = os.getenv("COINGECKO_API_KEY", "")',
                        coingecko_fix.strip()
                    )
                    
                    with open(config_file, 'w') as f:
                        f.write(content)
                    
                    self.fixes_applied.append("✅ Added CoinGecko API fallback configuration")
                    logger.info("✅ CoinGecko API fallback configured")
            
        except Exception as e:
            error_msg = f"❌ Failed to fix CoinGecko API configuration: {e}"
            self.errors_found.append(error_msg)
            logger.error(error_msg)
    
    def optimize_rate_limiting(self):
        """Optimize rate limiting to reduce system slowdown"""
        logger.info("🔧 Optimizing rate limiting...")
        
        try:
            # Update tokenmetrics_usage_monitor.py with more reasonable limits
            usage_monitor_file = self.backend_dir / "tokenmetrics_usage_monitor.py"
            
            if usage_monitor_file.exists():
                with open(usage_monitor_file, 'r') as f:
                    content = f.read()
                
                # Replace aggressive rate limiting with more reasonable values
                optimizations = [
                    ('min_interval = 30  # 30 seconds between calls', 'min_interval = 5  # 5 seconds between calls'),
                    ('min_interval = 15  # 15 seconds for high priority', 'min_interval = 3  # 3 seconds for high priority'),
                    ('min_interval = 60  # 1 minute for low priority', 'min_interval = 10  # 10 seconds for low priority'),
                    ('self.hourly_limit = 25', 'self.hourly_limit = 100'),
                    ('self.minute_limit = 2', 'self.minute_limit = 10'),
                ]
                
                for old, new in optimizations:
                    if old in content:
                        content = content.replace(old, new)
                
                with open(usage_monitor_file, 'w') as f:
                    f.write(content)
                
                self.fixes_applied.append("✅ Optimized TokenMetrics rate limiting")
                logger.info("✅ Rate limiting optimized for better performance")
            
        except Exception as e:
            error_msg = f"❌ Failed to optimize rate limiting: {e}"
            self.errors_found.append(error_msg)
            logger.error(error_msg)
    
    def optimize_system_performance(self):
        """Optimize system performance"""
        logger.info("🔧 Optimizing system performance...")
        
        try:
            # Create performance optimization script
            perf_optimizer = '''#!/usr/bin/env python3
"""
⚡ System Performance Optimizer
Reduces API calls and improves response times
"""

import asyncio
import logging
from typing import Dict, Any
import time

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """System performance optimizer"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def cache_result(self, key: str, data: Any, ttl: int = None):
        """Cache API result with TTL"""
        ttl = ttl or self.cache_ttl
        self.cache[key] = {
            'data': data,
            'expires': time.time() + ttl
        }
        
        # Cleanup old entries periodically
        if time.time() - self.last_cleanup > 300:  # Every 5 minutes
            self.cleanup_cache()
    
    def get_cached_result(self, key: str) -> Any:
        """Get cached result if still valid"""
        if key in self.cache:
            entry = self.cache[key]
            if time.time() < entry['expires']:
                return entry['data']
            else:
                del self.cache[key]
        return None
    
    def cleanup_cache(self):
        """Remove expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time >= entry['expires']
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        self.last_cleanup = current_time
        logger.info(f"🧹 Cache cleanup: removed {len(expired_keys)} expired entries")

# Global optimizer instance
performance_optimizer = PerformanceOptimizer()
'''
            
            perf_file = self.backend_dir / "performance_optimizer.py"
            with open(perf_file, 'w') as f:
                f.write(perf_optimizer)
            
            self.fixes_applied.append("✅ Created performance optimizer")
            logger.info("✅ Performance optimizer created")
            
        except Exception as e:
            error_msg = f"❌ Failed to create performance optimizer: {e}"
            self.errors_found.append(error_msg)
            logger.error(error_msg)
    
    def create_fallback_systems(self):
        """Create fallback systems for API failures"""
        logger.info("🔧 Creating fallback systems...")
        
        try:
            # Create API fallback manager
            fallback_manager = '''#!/usr/bin/env python3
"""
🛡️ API Fallback Manager
Handles API failures gracefully with fallback data
"""

import logging
import json
import time
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class APIFallbackManager:
    """Manages API fallbacks and cached data"""
    
    def __init__(self):
        self.fallback_dir = Path("backend/data/fallbacks")
        self.fallback_dir.mkdir(parents=True, exist_ok=True)
        
        # Default fallback data
        self.default_trading_signals = [
            {
                "symbol": "BTC",
                "signal": "HOLD",
                "confidence": 0.7,
                "price_target": 45000,
                "timestamp": time.time()
            },
            {
                "symbol": "ETH", 
                "signal": "BUY",
                "confidence": 0.6,
                "price_target": 2800,
                "timestamp": time.time()
            }
        ]
        
        self.default_sentiment = {
            "overall_sentiment": "NEUTRAL",
            "fear_greed_index": 50,
            "market_trend": "SIDEWAYS",
            "timestamp": time.time()
        }
    
    def get_fallback_trading_signals(self) -> List[Dict[str, Any]]:
        """Get fallback trading signals"""
        try:
            fallback_file = self.fallback_dir / "trading_signals.json"
            
            if fallback_file.exists():
                with open(fallback_file, 'r') as f:
                    data = json.load(f)
                    
                # Check if data is recent (within 1 hour)
                if time.time() - data.get('timestamp', 0) < 3600:
                    return data.get('signals', self.default_trading_signals)
            
            return self.default_trading_signals
            
        except Exception as e:
            logger.error(f"Error loading fallback trading signals: {e}")
            return self.default_trading_signals
    
    def get_fallback_sentiment(self) -> Dict[str, Any]:
        """Get fallback market sentiment"""
        try:
            fallback_file = self.fallback_dir / "sentiment.json"
            
            if fallback_file.exists():
                with open(fallback_file, 'r') as f:
                    data = json.load(f)
                    
                # Check if data is recent (within 30 minutes)
                if time.time() - data.get('timestamp', 0) < 1800:
                    return data
            
            return self.default_sentiment
            
        except Exception as e:
            logger.error(f"Error loading fallback sentiment: {e}")
            return self.default_sentiment
    
    def save_api_data(self, data_type: str, data: Any):
        """Save API data for future fallback use"""
        try:
            fallback_file = self.fallback_dir / f"{data_type}.json"
            
            fallback_data = {
                'data': data,
                'timestamp': time.time()
            }
            
            with open(fallback_file, 'w') as f:
                json.dump(fallback_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving fallback data for {data_type}: {e}")

# Global fallback manager
fallback_manager = APIFallbackManager()
'''
            
            fallback_file = self.backend_dir / "api_fallback_system.py"
            with open(fallback_file, 'w') as f:
                f.write(fallback_manager)
            
            self.fixes_applied.append("✅ Created API fallback system")
            logger.info("✅ API fallback system created")
            
        except Exception as e:
            error_msg = f"❌ Failed to create fallback systems: {e}"
            self.errors_found.append(error_msg)
            logger.error(error_msg)
    
    def generate_fix_report(self):
        """Generate comprehensive fix report"""
        logger.info("📊 Generating fix report...")
        
        report = {
            "timestamp": time.time(),
            "fixes_applied": self.fixes_applied,
            "errors_found": self.errors_found,
            "system_status": "IMPROVED" if len(self.fixes_applied) > len(self.errors_found) else "NEEDS_ATTENTION",
            "recommendations": [
                "🔄 Restart the backend server to apply configuration changes",
                "🧪 Test TokenMetrics API connection with new authentication",
                "📊 Monitor system performance after rate limiting optimization",
                "🛡️ Verify fallback systems are working correctly",
                "⚙️ Update environment variables if needed"
            ]
        }
        
        # Save report
        report_file = Path("SYSTEM_FIX_REPORT.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        logger.info("=" * 60)
        logger.info("🔧 SYSTEM FIX SUMMARY")
        logger.info("=" * 60)
        
        logger.info(f"✅ Fixes Applied: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            logger.info(f"   {fix}")
        
        if self.errors_found:
            logger.info(f"❌ Errors Found: {len(self.errors_found)}")
            for error in self.errors_found:
                logger.info(f"   {error}")
        
        logger.info(f"📊 System Status: {report['system_status']}")
        
        logger.info("\n🎯 Next Steps:")
        for rec in report["recommendations"]:
            logger.info(f"   {rec}")
        
        logger.info("=" * 60)

def main():
    """Main function to run all system fixes"""
    try:
        fixer = SystemFixer()
        fixer.run_all_fixes()
        return True
    except Exception as e:
        logger.error(f"❌ System fix failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
