#!/usr/bin/env python3
"""
Test TokenMetrics integration with Enhanced Arbitrage System
"""

import asyncio
import sys
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')
load_dotenv('.env')

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.enhanced_arbitrage import enhanced_arbitrage_engine
from backend.tokenmetrics_api import TokenMetricsAPI

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_tokenmetrics_arbitrage_integration():
    """Test TokenMetrics integration with arbitrage system"""
    
    print("🚀 Testing TokenMetrics + Arbitrage Integration")
    print("=" * 60)
    
    # Test 1: TokenMetrics API direct test
    print("\n🧪 Test 1: TokenMetrics API Direct Test")
    tm_api = TokenMetricsAPI()
    
    # Test with BTC (should work)
    btc_analysis = tm_api.get_comprehensive_analysis("BTC")
    print(f"📊 BTC Analysis Available: {btc_analysis.get('available', False)}")
    if btc_analysis.get('available'):
        print(f"🤖 AI Signal: {btc_analysis.get('combined_signal', 'N/A')}")
        print(f"📈 Confidence: {btc_analysis.get('confidence', 0):.2f}")
    
    # Test 2: Enhanced Arbitrage TokenMetrics Integration
    print("\n🧪 Test 2: Enhanced Arbitrage TokenMetrics Integration")
    
    # Test the TokenMetrics signal method directly
    tm_signals = await enhanced_arbitrage_engine._get_tokenmetrics_signals("BTC")
    print(f"📊 TokenMetrics Signals Available: {tm_signals.get('available', False)}")
    if tm_signals.get('available'):
        print(f"🎯 Signal: {tm_signals.get('signal', 'N/A')}")
        print(f"📊 Confidence: {tm_signals.get('confidence', 0):.2f}")
        print(f"🤖 AI Recommendation: {tm_signals.get('ai_recommendation', 'N/A')}")
        print(f"⚠️ Risk Level: {tm_signals.get('risk_level', 'N/A')}")
    
    # Test 3: Full Arbitrage Analysis with TokenMetrics
    print("\n🧪 Test 3: Full Arbitrage Analysis with TokenMetrics")
    
    # Create a mock token for testing
    test_token = {"symbol": "BTC-USDT"}
    
    try:
        opportunity = await enhanced_arbitrage_engine._analyze_token_opportunity("BTC-USDT", test_token)
        
        if opportunity:
            print("✅ Full analysis completed successfully!")
            print(f"📊 Total Score: {opportunity.get('total_score', 0):.3f}")
            print(f"🎯 Recommendation: {opportunity.get('recommendation', {}).get('action', 'N/A')}")
            
            # Show TokenMetrics contribution
            tm_data = opportunity.get('tokenmetrics_signals', {})
            if tm_data.get('available'):
                print(f"🤖 TokenMetrics Signal: {tm_data.get('signal', 'N/A')}")
                print(f"📈 TokenMetrics Confidence: {tm_data.get('confidence', 0):.2f}")
            else:
                print("⚠️ TokenMetrics data not available for BTC-USDT")
            
            # Show composite scoring
            composite = opportunity.get('composite_score', {})
            print(f"📊 Scoring Breakdown:")
            print(f"  • Arbitrage Score: {composite.get('arbitrage_score', 0):.3f}")
            print(f"  • TokenMetrics Score: {composite.get('tokenmetrics_score', 0):.3f}")
            print(f"  • AI Score: {composite.get('ai_score', 0):.3f}")
            print(f"  • Sentiment Score: {composite.get('sentiment_score', 0):.3f}")
            
        else:
            print("❌ No opportunity found (likely no arbitrage available)")
            
    except Exception as e:
        print(f"❌ Error in full analysis: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 TokenMetrics + Arbitrage Integration Test Complete!")

async def main():
    """Main test function"""
    await test_tokenmetrics_arbitrage_integration()

if __name__ == "__main__":
    asyncio.run(main())
