// src/services/kucoinApi.js
import axios from 'axios';

const DEFAULT_LIMIT = 5;

/**
 * Fetch your top scored tokens.
 */
export const getTopTokens = (limit = DEFAULT_LIMIT) =>
  axios.get(`/api/top-tokens?limit=${limit}`);

/**
 * Fetch tokens with volume spikes.
 */
export const getSpikeTokens = (limit = DEFAULT_LIMIT) =>
  axiosInstance.get(`/api/spike-tokens?limit=${limit}`);

/**
 * Fetch newly listed tokens (uses spike proxy).
 */
export const getNewlyListed = (limit = DEFAULT_LIMIT) =>
  axiosInstance.get(`/api/newly-listed?limit=${limit}`);