import React, { useState, useEffect } from 'react';
import Pn<PERSON>hart from '../components/PnLChart';
import TokenListItem from '../components/TokenListItem';
import { getSpikeTokens } from '../services/kucoinApi';
import { getPnLData, getPortfolioTokens } from '../services/dashboardApi';

export default function HomeDashboard() {
  const [pnlData, setPnlData] = useState([]);
  const [totalTrades, setTotalTrades] = useState(0);
  const [totalProfit, setTotalProfit] = useState(0);
  const [isLoadingPnL, setIsLoadingPnL] = useState(true);
  const [hasErrorPnL, setHasErrorPnL] = useState(false);
  const [volumeMovers, setVolumeMovers] = useState([]);
  const [tokenList, setTokenList] = useState([]);

  useEffect(() => {
    // Fetch PnL data
    getPnLData()
      .then(res => {
        if (res.data && res.data.tokens) {
          const formattedData = Object.entries(res.data.tokens).map(([token, profit]) => ({
            token,
            profit
          }));
          setPnlData(formattedData);
          setTotalTrades(res.data.total_trades || 0);
          setTotalProfit(res.data.total_profit || 0);
        } else {
          setPnlData([]);
        }
        setIsLoadingPnL(false);
      })
      .catch(err => {
        console.error("Failed to fetch PnL data:", err);
        setHasErrorPnL(true);
        setIsLoadingPnL(false);
      });

    // Fetch top volume spike tokens
    getSpikeTokens(5)
      .then(res => setVolumeMovers(res.data))
      .catch(err => console.error('Spike fetch error:', err));

    // Fetch portfolio tokens
    getPortfolioTokens()
      .then(res => setTokenList(res.data))
      .catch(err => console.error('Token fetch error:', err));
  }, []);

  return (
    <div className="p-6 grid grid-cols-12 gap-6">
      {/* Left: PnL Overview */}
      <div className="col-span-12 md:col-span-8 bg-white p-4 rounded shadow">
        <h3 className="text-xl font-semibold mb-2">📈 PnL Overview (Last 7 Days)</h3>
        <PnLChart data={pnlData} totalTrades={totalTrades} totalProfit={totalProfit} isLoading={isLoadingPnL} hasError={hasErrorPnL} />
      </div>

      {/* Right: Volume Movers + Portfolio */}
      <div className="col-span-12 md:col-span-4 bg-white p-4 rounded shadow space-y-4">
        <div>
          <h3 className="text-xl font-semibold mb-2">Top Volume Movers 🔥</h3>
          {volumeMovers.length === 0 ? (
            <p className="text-gray-500">No spikes right now.</p>
          ) : (
            volumeMovers.map(tok => (
              <div key={tok.symbol} className="flex justify-between py-1">
                <span>{tok.symbol}</span>
                <span>{tok.volume_ratio.toFixed(1)}×</span>
              </div>
            ))
          )}
        </div>

        <div>
          <h3 className="text-xl font-semibold mb-2">📊 Live Portfolio</h3>
          {tokenList.length === 0 ? (
            <p className="text-gray-500">No tokens in portfolio.</p>
          ) : (
            tokenList.map(token => (
              <TokenListItem key={token.symbol} token={token} />
            ))
          )}
        </div>
      </div>
    </div>
  );
}
