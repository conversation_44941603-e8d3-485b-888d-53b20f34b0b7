// src/screens/SignalDetailModal.jsx
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
// Adjust this import to wherever your Modal/Dialog component lives:
import Modal from '../components/Modal'; 
import { getNewsSources } from '../services/newsApi';

export default function SignalDetailModal({ isOpen, onClose, signal }) {
  const [sourceWeights, setSourceWeights] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isOpen) return;
    setLoading(true);
    setError('');
    getNewsSources()
      .then(res => {
        const map = {};
        res.data.forEach(({ source, weight }) => {
          map[source] = weight;
        });
        setSourceWeights(map);
      })
      .catch(err => {
        console.error('Error fetching news source weights:', err);
        setError('Failed to load source weights.');
      })
      .finally(() => setLoading(false));
  }, [isOpen]);

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="p-6">
        {signal && Object.keys(signal).length > 0 && signal.symbol ? (
          <>
            <h2 className="text-2xl font-semibold mb-4">
              Trade Signal Breakdown: {signal.symbol}
            </h2>
            <div className="mb-6 space-y-1">
              <p><strong>Score:</strong> {signal.score !== undefined && signal.score !== null ? signal.score.toFixed(2) : 'N/A'}</p>
              <p><strong>Price:</strong> {signal.price !== undefined && signal.price !== null ? `$${signal.price.toFixed(4)}` : 'N/A'}</p>
              <p><strong>Volume:</strong> {signal.volume !== undefined && signal.volume !== null ? signal.volume.toLocaleString() : 'N/A'}</p>
            </div>
            <div className="mb-6 space-y-1">
              <p title="Aggregated AI consensus (e.g. BUY or SELL)"><strong>Final Decision:</strong> {signal.final_decision || 'N/A'}</p>
              <p title="How confident the AI is about the trade decision"><strong>Confidence Score:</strong> {(signal.confidence !== undefined && signal.confidence !== null) ? (signal.confidence * 100).toFixed(1) + '%' : 'N/A'}</p>
            </div>
            {signal && (
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">AI Model Opinions</h3>
                <ul className="space-y-1">
                  <li><strong>OpenAI:</strong> {signal.openai || 'N/A'}</li>
                  <li><strong>Gemini:</strong> {signal.gemini || 'N/A'}</li>
                  <li><strong>DeepSeek:</strong> {signal.deepseek || 'N/A'}</li>
                </ul>
                <div className="mb-4 mt-2">
                  <p className="text-sm text-gray-600"><strong>OpenAI Reason:</strong> {signal.openai_reason || 'No reason provided.'}</p>
                  <p className="text-sm text-gray-600"><strong>Gemini Reason:</strong> {signal.gemini_reason || 'No reason provided.'}</p>
                  <p className="text-sm text-gray-600"><strong>DeepSeek Reason:</strong> {signal.deepseek_reason || 'No reason provided.'}</p>
                </div>
              </div>
            )}
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-2">Reasoning</h3>
              <p className="text-gray-800 bg-gray-50 border rounded p-3 text-sm leading-relaxed">
                {signal.reasoning || 'Detailed reasoning not available.'}
              </p>
            </div>
          </>
        ) : (
          <p className="text-gray-500">No signal selected.</p>
        )}

        <div>
          <h3 className="text-xl font-semibold mb-2">Source Impact Weights</h3>
          {loading ? (
            <p className="text-gray-500">Loading source weights…</p>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : Object.keys(sourceWeights).length === 0 ? (
            <p className="text-gray-500">No data available.</p>
          ) : (
            Object.entries(sourceWeights).map(([src, wt]) => (
              <div key={src} className="flex items-center justify-between py-1">
                <div className="flex items-center space-x-2">
                  <img
                    src={`/logos/${src.toLowerCase()}.png`}
                    alt={src}
                    className="w-6 h-6 rounded"
                    title={`Source: ${src}`}
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = '/logos/default.png';
                    }}
                  />
                  <span title={`Impact weight of ${src}`}>{src}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 h-2 rounded overflow-hidden" title="Visual representation of this source's impact weight">
                    <div className="h-2 bg-blue-500" style={{ width: `${wt * 100}%` }} />
                  </div>
                  <span className="text-sm text-gray-700" title="Weight as a percent of impact">+{(wt * 100).toFixed(0)}%</span>
                </div>
              </div>
            ))
          )}
          <button
            onClick={() => {
              // Placeholder for reset logic: currently does nothing but logs to console.
              console.log('Reset weights to default');
            }}
            className="mt-4 px-3 py-1 bg-gray-100 rounded text-sm hover:bg-gray-200"
            title="Reset all weights to default 50%"
          >
            🔄 Reset Weights
          </button>
        </div>
      </div>
    </Modal>
  );
}

SignalDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  signal: PropTypes.shape({
    symbol: PropTypes.string,
    score: PropTypes.number,
    price: PropTypes.number,
    volume: PropTypes.number,
    final_decision: PropTypes.string,
    confidence: PropTypes.number,
    openai: PropTypes.string,
    gemini: PropTypes.string,
    deepseek: PropTypes.string,
    reasoning: PropTypes.string,
    openai_reason: PropTypes.string,
    gemini_reason: PropTypes.string,
    deepseek_reason: PropTypes.string,
  }),
};

SignalDetailModal.defaultProps = {
  signal: {}
};