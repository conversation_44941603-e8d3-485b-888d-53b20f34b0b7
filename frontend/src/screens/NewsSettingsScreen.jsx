// src/services/newsApi.js
const baseUrl = import.meta.env.VITE_NEWS_API_URL || "http://localhost:8000";

export async function getNewsSources() {
  const response = await fetch(`${baseUrl}/news-sources`);
  if (!response.ok) {
    throw new Error('Failed to fetch news sources');
  }
  return response.json();
}

export async function updateNewsSourceWeight(source, weight) {
  const response = await fetch(`${baseUrl}/news-sources/${source}/weight`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ weight }),
  });
  if (!response.ok) {
    throw new Error('Failed to update news source weight');
  }
  return response.json();
}

function NewsSettingsScreen() {
  // Component implementation here
}

export default NewsSettingsScreen;