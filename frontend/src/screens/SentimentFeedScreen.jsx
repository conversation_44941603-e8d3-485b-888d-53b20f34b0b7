import React, { useEffect, useState } from "react";
import axios from "axios";

const SentimentFeedScreen = () => {
  const [feed, setFeed] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 🔁 Fetch sentiment feed data
  const fetchFeed = async () => {
    try {
      const res = await axiosInstance.get(`/api/sentiment`);
      setFeed(res.data || []);
    } catch (err) {
      setError("Failed to load sentiment feed.");
    } finally {
      setLoading(false);
    }
  };

  // 📡 Auto-refresh every 5 mins
  useEffect(() => {
    fetchFeed();
    const interval = setInterval(fetchFeed, 300000); // every 5 min
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-6 text-white">
      <h2 className="text-2xl font-bold mb-4">📡 Token Sentiment Feed</h2>

      {loading ? (
        <p className="text-gray-400">Loading...</p>
      ) : error ? (
        <p className="text-red-400">{error}</p>
      ) : feed.length === 0 ? (
        <p className="text-yellow-400">No recent sentiment signals available.</p>
      ) : (
        feed.map((item, index) => (
          <div
            key={index}
            className="bg-gray-800 p-4 rounded-md mb-4 shadow"
          >
            <div className="flex justify-between">
              <h3 className="text-lg font-semibold">{item.token.toUpperCase()}</h3>
              <span
                className={`text-sm font-bold ${
                  item.sentiment === "positive"
                    ? "text-green-400"
                    : item.sentiment === "negative"
                    ? "text-red-400"
                    : "text-yellow-400"
                }`}
              >
                {item.sentiment === "positive"
                  ? "🟢 POSITIVE"
                  : item.sentiment === "negative"
                  ? "🔴 NEGATIVE"
                  : "🟡 NEUTRAL"}
              </span>
            </div>

            <p className="text-sm text-gray-300 mt-1">
              {item.preview?.length > 200
                ? `${item.preview.slice(0, 200)}...`
                : item.preview}
            </p>

            {item.confidence !== undefined && (
              <p className="text-xs text-blue-300 mt-1">
                Confidence: {(item.confidence * 100).toFixed(1)}%
              </p>
            )}

            <p className="text-xs text-gray-500 mt-1 italic">
              Source: {item.source} |{" "}
              {new Date(item.timestamp).toLocaleString()}
            </p>
          </div>
        ))
      )}

      {/* 🔘 Manual refresh button */}
      <button
        onClick={fetchFeed}
        className="mt-6 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white"
      >
        🔄 Refresh Now
      </button>
    </div>
  );
};

export default SentimentFeedScreen;
