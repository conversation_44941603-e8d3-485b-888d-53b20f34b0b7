import React, { useState, useEffect } from 'react';
import axiosInstance from './axiosInstance';

const MicroBotScreen = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axiosInstance.get('/api/micro-bot/status');
      setStatus(response.data);
    } catch (e) {
      console.error('Error fetching micro-bot status:', e);
      setError(e.response?.data?.detail || e.message || 'Failed to fetch micro-bot status');
    } finally {
      setLoading(false);
    }
  };

  const toggleBot = async (action) => {
    setLoading(true);
    setError(null);
    try {
      await axiosInstance.post(`/api/micro-bot/${action}`);
      await fetchStatus(); // Refresh status after action
    } catch (e) {
      console.error(`Error ${action}ing micro-bot:`, e);
      setError(e.response?.data?.detail || e.message || `Failed to ${action} micro-bot`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, 5000); // Poll every 5 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading && !status) {
    return <div className="text-white p-4">Loading micro-bot status...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">Error: {error}</div>;
  }

  return (
    <div className="p-4 text-white">
      <h2 className="text-2xl font-bold mb-4">Micro-Bot Control Panel</h2>
      {status && (
        <div className="bg-gray-800 p-6 rounded-lg shadow-md">
          <p className="mb-2"><strong>Status:</strong> {status.is_running ? 'Running' : 'Stopped'}</p>
          <p className="mb-2"><strong>Trades Today:</strong> {status.trades_today}</p>
          <p className="mb-2"><strong>PnL Today:</strong> ${status.pnl_today ? status.pnl_today.toFixed(2) : '0.00'}</p>
          <p className="mb-2"><strong>Last Trade Day:</strong> {status.last_trade_day || 'N/A'}</p>
          <p className="mb-4"><strong>Portfolio Size:</strong> {status.portfolio_size}</p>
          <div className="space-x-4">
            <button
              onClick={() => toggleBot('start')}
              disabled={status.is_running || loading}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
            >
              Start Bot
            </button>
            <button
              onClick={() => toggleBot('stop')}
              disabled={!status.is_running || loading}
              className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
            >
              Stop Bot
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MicroBotScreen;
