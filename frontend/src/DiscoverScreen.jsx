import React, { useEffect, useState, useCallback, useMemo } from 'react';
import axios from 'axios';
import { useAuth } from './contexts/AuthContext';
import { useRealTimeData } from './hooks/useRealTimeData.jsx';
import ErrorBoundary from './components/ErrorBoundary.jsx';

const DiscoverScreen = () => {
  const [filters, setFilters] = useState({
    volumeSurge: false,
    priceAction: false,
    newListing: false,
  });

  // Real-time data fetching with AI-optimized intervals
  const {
    data: rawTokens,
    loading,
    error,
    lastUpdated,
    isRefreshing,
    refresh
  } = useRealTimeData('/api/discover', {
    refreshInterval: 300000, // 5 minutes for token discovery (slow process)
    dataImportance: 'medium',
    onError: (err) => console.error('❌ Error fetching tokens:', err)
  });

  // Memoized filtered tokens for better performance
  const tokens = useMemo(() => {
    if (!Array.isArray(rawTokens)) return [];

    let filteredData = [...rawTokens];

    // Apply filters
    if (filters.volumeSurge) {
      filteredData = filteredData.filter((token) => token.volume_change_24h > 30);
    }
    if (filters.priceAction) {
      filteredData = filteredData.filter((token) => Math.abs(token.price_change_24h) > 10);
    }
    if (filters.newListing) {
      filteredData = filteredData.filter((token) => token.is_new);
    }

    return filteredData;
  }, [rawTokens, filters]);

  const handleFilterChange = useCallback((filterName) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: !prev[filterName],
    }));
  }, []);

  return (
    <ErrorBoundary>
      <div className="p-6 bg-gray-900 text-white min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
            🔍 Token Discovery
          </h1>
          <div className="text-sm text-gray-400">
            {isRefreshing && <span className="text-yellow-400">Refreshing...</span>}
            {lastUpdated && <span>Updated: {new Date(lastUpdated).toLocaleTimeString()}</span>}
          </div>
        </div>

        {/* Enhanced Filter Controls */}
        <div className="bg-gray-800 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-3 text-blue-400">🔧 Filters</h3>
          <div className="flex flex-wrap gap-4">
            <label title="Tokens with 24h volume increase > 30%" className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.volumeSurge}
                onChange={() => handleFilterChange('volumeSurge')}
                className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
              />
              <span className="text-green-400">📈 Volume Surge</span>
            </label>
            <label title="Tokens with price move > 10% in 24h" className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.priceAction}
                onChange={() => handleFilterChange('priceAction')}
                className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
              />
              <span className="text-yellow-400">⚡ Price Action</span>
            </label>
            <label title="Newly listed tokens on KuCoin" className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.newListing}
                onChange={() => handleFilterChange('newListing')}
                className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
              />
              <span className="text-purple-400">🆕 New Listing</span>
            </label>
          </div>
        </div>

        <div className="flex justify-between items-center mb-6">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
            onClick={refresh}
            disabled={isRefreshing}
          >
            <span>{isRefreshing ? '⏳' : '🔄'}</span>
            <span>{isRefreshing ? 'Refreshing...' : 'Refresh Tokens'}</span>
          </button>
          <select
            className="bg-gray-700 text-white px-2 py-1 rounded ml-4"
            onChange={(e) => {
              const sorted = [...tokens].sort((a, b) => {
                if (e.target.value === 'volume') return (b.volume || 0) - (a.volume || 0);
                if (e.target.value === 'price') return (b.price || 0) - (a.price || 0);
                if (e.target.value === 'score') return (b.score || 0) - (a.score || 0);
                return 0;
              });
              setTokens(sorted);
            }}
          >
            <option value="">Sort Tokens</option>
            <option value="volume">Volume</option>
            <option value="price">Price</option>
            <option value="score">Score</option>
          </select>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {loading ? (
            <div className="text-white text-center col-span-full animate-pulse py-10">
              🔄 Fetching latest token data...
            </div>
          ) : tokens.length === 0 ? (
            <div className="text-white text-center col-span-full py-8">
              No tokens found for selected filters.
              <br />
              <button onClick={refresh} className="mt-2 underline text-blue-400 hover:text-blue-200">
                Retry Fetch
              </button>
            </div>
          ) : (
            tokens.map((token) => (
              <div key={token.symbol} className="bg-gray-800 p-4 rounded shadow hover:shadow-lg transition">
                <div className="text-lg font-semibold text-yellow-400">{token.symbol}</div>
                <div className="text-sm text-white">
                  Price: ${token.price ? token.price.toLocaleString('en-US', { minimumFractionDigits: 4, maximumFractionDigits: 4 }) : 'N/A'}
                </div>
                <div className="text-sm text-green-300">
                  Volume: {token.volume ? token.volume.toLocaleString('en-US') : 'N/A'}
                </div>
                <div className="text-sm text-purple-400">
                  Score: {token.score ? token.score.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : 'N/A'}
                </div>
                {token.sentiment !== undefined && (
                  <div className={`text-xs mt-1 ${token.sentiment > 0 ? 'text-green-400' : token.sentiment < 0 ? 'text-red-400' : 'text-gray-400'}`}>
                    Sentiment: {token.sentiment.toFixed(2)}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default DiscoverScreen;
