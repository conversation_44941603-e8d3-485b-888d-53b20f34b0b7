import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import LiveTradesScreen from './LiveTradesScreen';

// Mock axios
vi.mock('axios');
import axios from 'axios';

const mockTrades = [
  {
    id: '1',
    symbol: 'BTC/USDT',
    token: 'BTC',
    side: 'buy',
    action: 'BUY',
    price: 50000,
    amount: 0.1,
    quantity: 0.1,
    timestamp: new Date().toISOString(),
    status: 'open',
    usd_value: 5000,
    total: 5000,
    pnl: 150.50
  },
  {
    id: '2',
    symbol: 'ETH/USDT',
    token: 'ETH',
    side: 'sell',
    action: 'SELL',
    price: 3000,
    amount: 1.5,
    quantity: 1.5,
    timestamp: new Date().toISOString(),
    status: 'closed',
    usd_value: 4500,
    total: 4500,
    pnl: -75.25
  }
];

describe('LiveTradesScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    axios.get.mockResolvedValue({
      data: mockTrades
    });
  });

  it('renders live trades screen title', async () => {
    render(
      <BrowserRouter>
        <LiveTradesScreen />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/live trade feed/i)).toBeInTheDocument();
    });
  });

  it('displays trade list', async () => {
    render(
      <BrowserRouter>
        <LiveTradesScreen />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('BTC')).toBeInTheDocument();
      expect(screen.getByText('ETH')).toBeInTheDocument();
    });
  });

  it('fetches trades data on mount', async () => {
    render(
      <BrowserRouter>
        <LiveTradesScreen />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(axios.get).toHaveBeenCalledWith(
        'https://api.alphapredatorbot.xyz/trades/live'
      );
    });
  });

  it('handles error state', async () => {
    axios.get.mockRejectedValueOnce(new Error('Failed to fetch'));

    render(
      <BrowserRouter>
        <LiveTradesScreen />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/failed to load live trades/i)).toBeInTheDocument();
    });
  });

  it('displays loading state initially', () => {
    render(
      <BrowserRouter>
        <LiveTradesScreen />
      </BrowserRouter>
    );

    expect(screen.getByText(/fetching live trades/i)).toBeInTheDocument();
  });

  it('shows refresh button when trades are loaded', async () => {
    render(
      <BrowserRouter>
        <LiveTradesScreen />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/refresh now/i)).toBeInTheDocument();
    });
  });
});
