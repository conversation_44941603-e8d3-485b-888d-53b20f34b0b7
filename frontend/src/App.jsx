import React from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import LoginScreen from './LoginScreen';
import LogicScreen from './LogicScreen';
import DashboardScreen from './DashboardScreen';
import AnalyticsScreen from './AnalyticsScreen';
import DiscoverScreen from './DiscoverScreen';
import LiveTradesScreen from './LiveTradesScreen';
import MicroBotScreen from './MicroBotScreen';
import ArbitrageScreen from './ArbitrageScreen';
import NewsScreen from './NewsScreen';
import TokenMetricsScreen from './TokenMetricsScreen';
import CostMonitoringScreen from './CostMonitoringScreen';
import ManualTrading from './components/ManualTrading';
import NavHeader from './components/NavHeader';
import ProtectedRoute from './components/ProtectedRoute';
// import { PreloaderProvider } from './contexts/PreloaderContext';
// import PreloaderStatus from './components/PreloaderStatus';

/**
 * App
 * ----
 * Top‑level route configuration.
 * `GoogleOAuthProvider` is now handled in `main.jsx`,
 * so we do NOT wrap the app in it again here.
 */
function App() {
  const location = useLocation();
  const isLoginPage = location.pathname === '/' || location.pathname === '/login';

  return (
    // <PreloaderProvider>
    <div className="min-h-screen bg-gradient-to-br from-[#0f2027] via-[#203a43] to-[#2c5364]">
      {!isLoginPage && <NavHeader />}
      {/* {!isLoginPage && <PreloaderStatus />} */}
      <Routes>
        {/* Login routes */}
        <Route path="/" element={<LoginScreen />} />
        <Route path="/login" element={<LoginScreen />} />

        {/* Authenticated screens */}
        <Route path="/dashboard" element={<ProtectedRoute><DashboardScreen /></ProtectedRoute>} />
        <Route path="/logic" element={<ProtectedRoute><LogicScreen /></ProtectedRoute>} />
        <Route path="/analytics" element={<ProtectedRoute><AnalyticsScreen /></ProtectedRoute>} />
        <Route path="/discover" element={<ProtectedRoute><DiscoverScreen /></ProtectedRoute>} />
        <Route path="/live-trades" element={<ProtectedRoute><LiveTradesScreen /></ProtectedRoute>} />
        <Route path="/microbot" element={<ProtectedRoute><MicroBotScreen /></ProtectedRoute>} />
        <Route path="/arbitrage" element={<ProtectedRoute><ArbitrageScreen /></ProtectedRoute>} />
        <Route path="/news" element={<ProtectedRoute><NewsScreen /></ProtectedRoute>} />
        <Route path="/tokenmetrics" element={<ProtectedRoute><TokenMetricsScreen /></ProtectedRoute>} />
        <Route path="/cost-monitoring" element={<ProtectedRoute><CostMonitoringScreen /></ProtectedRoute>} />
        <Route path="/manual-trading" element={<ProtectedRoute><ManualTrading /></ProtectedRoute>} />

        {/* Catch‑all → /dashboard */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </div>
    // </PreloaderProvider >
  );
}

export default App;
