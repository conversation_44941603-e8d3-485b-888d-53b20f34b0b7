import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import axiosInstance from '../axiosInstance';

/**
 * 🚀 PRELOADER CONTEXT
 * Preloads all dashboard screens in the background for instant navigation
 */

const PreloaderContext = createContext();

export const usePreloader = () => {
  const context = useContext(PreloaderContext);
  if (!context) {
    throw new Error('usePreloader must be used within a PreloaderProvider');
  }
  return context;
};

export const PreloaderProvider = ({ children }) => {
  const [preloadedData, setPreloadedData] = useState({});
  const [preloadStatus, setPreloadStatus] = useState({});
  const [isPreloading, setIsPreloading] = useState(false);

  // Define all endpoints to preload
  const endpoints = {
    dashboard: '/api/dashboard/all-fast',
    discover: '/api/dashboard/discover-fast',
    trades: '/api/dashboard/trades-fast',
    pnl: '/api/dashboard/pnl-fast',
    aiSignals: '/api/dashboard/ai-signals-fast',
    news: '/api/news/live',
    tokenmetrics: '/api/tokenmetrics/top-tokens',
    costMonitoring: '/api/cost-monitoring',
    analytics: '/api/analytics/summary',
    arbitrage: '/api/arbitrage/opportunities',
  };

  // Preload a single endpoint
  const preloadEndpoint = useCallback(async (key, endpoint) => {
    try {
      setPreloadStatus(prev => ({ ...prev, [key]: 'loading' }));
      
      const response = await axiosInstance.get(endpoint);
      
      setPreloadedData(prev => ({
        ...prev,
        [key]: {
          data: response.data,
          timestamp: Date.now(),
          status: 'success'
        }
      }));
      
      setPreloadStatus(prev => ({ ...prev, [key]: 'success' }));
      
      console.log(`✅ Preloaded ${key} data`);
      
    } catch (error) {
      console.error(`❌ Failed to preload ${key}:`, error);
      
      setPreloadedData(prev => ({
        ...prev,
        [key]: {
          data: null,
          timestamp: Date.now(),
          status: 'error',
          error: error.message
        }
      }));
      
      setPreloadStatus(prev => ({ ...prev, [key]: 'error' }));
    }
  }, []);

  // Preload all endpoints
  const preloadAll = useCallback(async () => {
    console.log('🚀 Starting background preload of all screens...');
    setIsPreloading(true);

    // Preload in priority order (most important first)
    const priorityOrder = [
      'dashboard',
      'discover', 
      'trades',
      'aiSignals',
      'pnl',
      'news',
      'tokenmetrics',
      'analytics',
      'costMonitoring',
      'arbitrage'
    ];

    // Preload high priority items first (parallel)
    const highPriority = priorityOrder.slice(0, 4);
    await Promise.allSettled(
      highPriority.map(key => preloadEndpoint(key, endpoints[key]))
    );

    // Then preload medium priority items
    const mediumPriority = priorityOrder.slice(4, 7);
    await Promise.allSettled(
      mediumPriority.map(key => preloadEndpoint(key, endpoints[key]))
    );

    // Finally preload low priority items
    const lowPriority = priorityOrder.slice(7);
    await Promise.allSettled(
      lowPriority.map(key => preloadEndpoint(key, endpoints[key]))
    );

    setIsPreloading(false);
    console.log('✅ Background preload completed');
  }, [preloadEndpoint, endpoints]);

  // Get preloaded data for a screen
  const getPreloadedData = useCallback((key) => {
    const cached = preloadedData[key];
    
    if (!cached) return null;
    
    // Check if data is still fresh (5 minutes)
    const isStale = Date.now() - cached.timestamp > 300000;
    
    if (isStale) {
      // Refresh stale data in background
      preloadEndpoint(key, endpoints[key]);
    }
    
    return cached;
  }, [preloadedData, preloadEndpoint, endpoints]);

  // Refresh specific screen data
  const refreshScreen = useCallback(async (key) => {
    if (endpoints[key]) {
      await preloadEndpoint(key, endpoints[key]);
    }
  }, [preloadEndpoint, endpoints]);

  // Refresh all screen data
  const refreshAll = useCallback(() => {
    preloadAll();
  }, [preloadAll]);

  // Start preloading on mount
  useEffect(() => {
    // Start preloading after a short delay to not block initial render
    const timer = setTimeout(() => {
      preloadAll();
    }, 1000);

    return () => clearTimeout(timer);
  }, [preloadAll]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('🔄 Auto-refreshing preloaded data...');
      preloadAll();
    }, 300000); // 5 minutes

    return () => clearInterval(interval);
  }, [preloadAll]);

  const value = {
    preloadedData,
    preloadStatus,
    isPreloading,
    getPreloadedData,
    refreshScreen,
    refreshAll,
    endpoints
  };

  return (
    <PreloaderContext.Provider value={value}>
      {children}
    </PreloaderContext.Provider>
  );
};

/**
 * 🎯 PRELOADER HOOK FOR SCREENS
 * Use this in screen components to get preloaded data
 */
export const useScreenData = (screenKey, fallbackEndpoint) => {
  const { getPreloadedData, refreshScreen } = usePreloader();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const preloaded = getPreloadedData(screenKey);
    
    if (preloaded) {
      if (preloaded.status === 'success') {
        setData(preloaded.data);
        setError(null);
      } else if (preloaded.status === 'error') {
        setError(preloaded.error);
      }
      setLoading(false);
    } else {
      // Fallback to direct API call if no preloaded data
      if (fallbackEndpoint) {
        axiosInstance.get(fallbackEndpoint)
          .then(response => {
            setData(response.data);
            setError(null);
          })
          .catch(err => {
            setError(err.message);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [screenKey, fallbackEndpoint, getPreloadedData]);

  const refresh = useCallback(() => {
    refreshScreen(screenKey);
  }, [screenKey, refreshScreen]);

  return { data, loading, error, refresh };
};

export default PreloaderContext;
