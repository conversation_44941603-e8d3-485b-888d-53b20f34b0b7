import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [token, setToken] = useState(null);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Get API base URL
  const getBaseURL = () => {
    // Use environment variable if available, otherwise fallback to production URL
    const backendUrl = import.meta.env.VITE_BACKEND_URL;
    console.log('Using VITE_BACKEND_URL:', backendUrl);
    return backendUrl || "https://api.alphapredatorbot.xyz";
  };

  // Initialize auth state from localStorage
  useEffect(() => {
    const storedToken = localStorage.getItem('auth_token');
    const storedUser = localStorage.getItem('auth_user');
    
    if (storedToken && storedUser) {
      setToken(storedToken);
      setUser(JSON.parse(storedUser));
      setupAxiosInterceptor(storedToken);
    }
    setLoading(false);
  }, []);

  // Setup axios interceptor to include token in all requests
  const setupAxiosInterceptor = (authToken) => {
    // Clear existing interceptors
    axios.interceptors.request.handlers = [];
    axios.interceptors.response.handlers = [];
    
    axios.interceptors.request.use(
      (config) => {
        if (authToken) {
          config.headers.Authorization = `Bearer ${authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token expiration
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          logout();
        }
        return Promise.reject(error);
      }
    );
  };

  const login = async (email, password) => {
    try {
      const response = await axios.post(`${getBaseURL()}/api/login`, {
        email,
        password
      });

      const { access_token, token_type } = response.data;
      const userData = { email };

      // Store in state
      setToken(access_token);
      setUser(userData);

      // Store in localStorage
      localStorage.setItem('auth_token', access_token);
      localStorage.setItem('auth_user', JSON.stringify(userData));

      // Setup axios interceptor
      setupAxiosInterceptor(access_token);

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Login failed' 
      };
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    
    // Clear axios interceptors
    axios.interceptors.request.handlers = [];
    axios.interceptors.response.handlers = [];
  };

  const isAuthenticated = () => {
    return !!token;
  };

  const value = {
    token,
    user,
    loading,
    login,
    logout,
    isAuthenticated,
    getBaseURL
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
