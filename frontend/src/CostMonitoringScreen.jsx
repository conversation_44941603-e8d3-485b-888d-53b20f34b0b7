import React, { useState } from 'react';
import { useRealTimeData, LastUpdatedIndicator } from './hooks/useRealTimeData.jsx';

const CostMonitoringScreen = () => {
  const [timeRange, setTimeRange] = useState('month');
  const [activeTab, setActiveTab] = useState('overview');

  // Real-time cost data fetching with AI-optimized refresh
  const {
    data: costData,
    loading,
    error,
    lastUpdated,
    isRefreshing,
    refresh
  } = useRealTimeData('/api/cost-monitoring', {
    refreshInterval: 180000, // 3 minutes - cost data changes slowly
    dataImportance: 'low', // Cost monitoring is not critical for real-time
    onSuccess: (data) => {
      console.log('✅ Real-time cost data updated:', data);
    },
    onError: (err) => {
      console.error('❌ Cost monitoring data fetch failed:', err);
    }
  });

  // AI optimization data fetching
  const {
    data: aiOptimizationData,
    loading: aiLoading,
    error: aiError,
    refresh: refreshAI
  } = useRealTimeData('/api/ai-optimization', {
    refreshInterval: 300000, // 5 minutes for AI optimization data
    dataImportance: 'low', // AI optimization is background data
    onSuccess: (data) => {
      console.log('✅ AI optimization data updated:', data);
    },
    onError: (err) => {
      console.error('❌ AI optimization data fetch failed:', err);
    }
  });

  // Get usage stats from cost data
  const usageStats = costData?.daily_usage || {};
  const aiOptimization = aiOptimizationData || {};

  // API usage analytics
  const getAPIUsageData = () => {
    if (!costData) return [];

    const apiServices = [
      { name: 'OpenAI', calls: usageStats.openai_requests || 0, cost: costData.monthly_costs?.ai_providers?.openai || 0, color: 'bg-blue-500' },
      { name: 'Claude', calls: usageStats.claude_requests || 0, cost: costData.monthly_costs?.ai_providers?.claude || 0, color: 'bg-purple-500' },
      { name: 'DeepSeek', calls: usageStats.deepseek_requests || 0, cost: costData.monthly_costs?.ai_providers?.deepseek || 0, color: 'bg-green-500' },
      { name: 'Gemini', calls: usageStats.gemini_requests || 0, cost: costData.monthly_costs?.ai_providers?.gemini || 0, color: 'bg-red-500' },
      { name: 'TokenMetrics', calls: usageStats.tokenmetrics_calls || 0, cost: costData.monthly_costs?.data_apis?.tokenmetrics || 0, color: 'bg-yellow-500' },
      { name: 'CoinGecko', calls: usageStats.coingecko_requests || 0, cost: costData.monthly_costs?.data_apis?.coingecko || 0, color: 'bg-orange-500' },
      { name: 'KuCoin', calls: usageStats.kucoin_requests || 0, cost: costData.monthly_costs?.data_apis?.kucoin || 0, color: 'bg-teal-500' },
    ];

    return apiServices.filter(service => service.calls > 0 || service.cost > 0);
  };

  const runAIOptimization = async () => {
    try {
      const response = await fetch('/api/ai-optimization/run', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        refreshAI();
        refresh();
      }
    } catch (error) {
      console.error('Failed to run AI optimization:', error);
    }
  };

  const getTotalCosts = () => {
    if (!costData) return 0;
    const aiCosts = Object.values(costData.monthly_costs.ai_providers).reduce((a, b) => a + b, 0);
    const dataCosts = Object.values(costData.monthly_costs.data_apis).reduce((a, b) => a + b, 0);
    const infraCosts = Object.values(costData.monthly_costs.infrastructure).reduce((a, b) => a + b, 0);
    return aiCosts + dataCosts + infraCosts;
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'text-red-400';
    if (percentage >= 75) return 'text-yellow-400';
    if (percentage >= 50) return 'text-blue-400';
    return 'text-green-400';
  };

  const getProgressBarColor = (percentage) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    if (percentage >= 50) return 'bg-blue-500';
    return 'bg-green-500';
  };

  if (loading && !costData) {
    return (
      <div className="p-4 text-white">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p>Loading cost monitoring data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 text-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-3xl font-bold">💰 Cost Monitoring Dashboard</h2>
        <div className="flex gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>

      {/* Real-time Status Indicator */}
      <div className="mb-6">
        <LastUpdatedIndicator
          lastUpdated={lastUpdated}
          isRefreshing={isRefreshing}
          error={error}
          onRefresh={refresh}
          className="justify-end"
        />
      </div>

      {error && (
        <div className="bg-red-900 border border-red-600 text-red-200 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6">
        {[
          { id: 'overview', label: '📊 Overview', icon: '📊' },
          { id: 'api-usage', label: '🔌 API Usage', icon: '🔌' },
          { id: 'ai-optimization', label: '🤖 AI Optimization', icon: '🤖' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${activeTab === tab.id
              ? 'bg-blue-600 text-white'
              : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <>
          {/* Cost Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold text-green-400 mb-2">Total Monthly Cost</h3>
              <p className="text-3xl font-bold">${getTotalCosts().toFixed(2)}</p>
              <p className="text-sm text-gray-400 mt-2">All services combined</p>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold text-blue-400 mb-2">AI Providers</h3>
              <p className="text-3xl font-bold">
                ${costData ? Object.values(costData.monthly_costs.ai_providers).reduce((a, b) => a + b, 0).toFixed(2) : '0.00'}
              </p>
              <p className="text-sm text-gray-400 mt-2">OpenAI, DeepSeek, Gemini, Claude</p>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold text-purple-400 mb-2">Data APIs</h3>
              <p className="text-3xl font-bold">
                ${costData ? Object.values(costData.monthly_costs.data_apis).reduce((a, b) => a + b, 0).toFixed(2) : '0.00'}
              </p>
              <p className="text-sm text-gray-400 mt-2">TokenMetrics, CryptoPanic</p>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-lg font-semibold text-yellow-400 mb-2">Infrastructure</h3>
              <p className="text-3xl font-bold">
                ${costData ? Object.values(costData.monthly_costs.infrastructure).reduce((a, b) => a + b, 0).toFixed(2) : '0.00'}
              </p>
              <p className="text-sm text-gray-400 mt-2">Hosting, Database, Monitoring</p>
            </div>
          </div>

          {/* Usage Alerts */}
          {costData?.alerts && costData.alerts.length > 0 && (
            <div className="bg-yellow-900 border border-yellow-600 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-yellow-200 mb-3">⚠️ Usage Alerts</h3>
              <div className="space-y-2">
                {costData.alerts.map((alert, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-yellow-100">{alert.service}</span>
                    <span className={`font-semibold ${getUsageColor(alert.usage)}`}>
                      {alert.usage.toFixed(1)}% of limit used
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* API Usage Limits */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <h3 className="col-span-full text-2xl font-bold mb-4">📊 API Usage Limits</h3>
            {costData?.limits && Object.entries(costData.limits).map(([service, data]) => (
              <div key={service} className="bg-gray-800 p-6 rounded-lg shadow-lg">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-lg font-semibold capitalize">{service}</h4>
                  <span className={`font-bold ${getUsageColor(data.percentage)}`}>
                    {data.percentage.toFixed(1)}%
                  </span>
                </div>

                <div className="mb-3">
                  <div className="flex justify-between text-sm text-gray-400 mb-1">
                    <span>{data.used.toLocaleString()} used</span>
                    <span>{data.limit.toLocaleString()} limit</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getProgressBarColor(data.percentage)}`}
                      style={{ width: `${Math.min(data.percentage, 100)}%` }}
                    ></div>
                  </div>
                </div>

                <div className="text-sm text-gray-400">
                  {data.limit - data.used > 0 ?
                    `${(data.limit - data.used).toLocaleString()} requests remaining` :
                    'Limit exceeded!'
                  }
                </div>
              </div>
            ))}
          </div>

          {/* Detailed Cost Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* AI Providers */}
            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold text-blue-400 mb-4">🤖 AI Providers</h3>
              <div className="space-y-3">
                {costData?.monthly_costs.ai_providers && Object.entries(costData.monthly_costs.ai_providers).map(([provider, cost]) => (
                  <div key={provider} className="flex justify-between items-center">
                    <span className="capitalize">{provider}</span>
                    <span className="font-semibold">${cost.toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Data APIs */}
            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold text-purple-400 mb-4">📡 Data APIs</h3>
              <div className="space-y-3">
                {costData?.monthly_costs.data_apis && Object.entries(costData.monthly_costs.data_apis).map(([api, cost]) => (
                  <div key={api} className="flex justify-between items-center">
                    <span className="capitalize">{api}</span>
                    <span className="font-semibold">
                      {cost === 0 ? 'Free' : `$${cost.toFixed(2)}`}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Infrastructure */}
            <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold text-yellow-400 mb-4">🏗️ Infrastructure</h3>
              <div className="space-y-3">
                {costData?.monthly_costs.infrastructure && Object.entries(costData.monthly_costs.infrastructure).map(([service, cost]) => (
                  <div key={service} className="flex justify-between items-center">
                    <span className="capitalize">{service}</span>
                    <span className="font-semibold">${cost.toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Daily Usage Stats */}
          <div className="mt-8 bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-green-400 mb-4">📈 Daily Usage Statistics</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {usageStats && Object.entries(usageStats).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {typeof value === 'number' ? value.toLocaleString() : value}
                  </div>
                  <div className="text-sm text-gray-400 capitalize">
                    {key.replace(/_/g, ' ')}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Cost Optimization Tips */}
          <div className="mt-8 bg-blue-900 border border-blue-600 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-blue-200 mb-4">💡 Cost Optimization Tips</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-100">
              <div>
                <h4 className="font-semibold mb-2">AI Provider Optimization:</h4>
                <ul className="text-sm space-y-1">
                  <li>• Use DeepSeek for cost-effective analysis</li>
                  <li>• Reserve OpenAI for complex decisions</li>
                  <li>• Implement request caching</li>
                  <li>• Batch similar requests</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">API Usage Optimization:</h4>
                <ul className="text-sm space-y-1">
                  <li>• Cache TokenMetrics data for 5+ minutes</li>
                  <li>• Use free APIs when possible</li>
                  <li>• Implement rate limiting</li>
                  <li>• Monitor usage patterns</li>
                </ul>
              </div>
            </div>
          </div>
        </>
      )}

      {/* API Usage Tab */}
      {activeTab === 'api-usage' && (
        <div className="space-y-6">
          <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-blue-400 mb-4">🔌 Daily API Usage by Service</h3>
            <div className="space-y-4">
              {getAPIUsageData().map((service, index) => (
                <div key={service.name} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`w-4 h-4 rounded-full ${service.color}`}></div>
                    <div>
                      <div className="font-semibold">{service.name}</div>
                      <div className="text-sm text-gray-400">API Calls Today</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-400">{service.calls.toLocaleString()}</div>
                    <div className="text-sm text-gray-400">${service.cost.toFixed(2)} monthly</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-green-400 mb-4">📊 API Usage Analytics</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400">{usageStats.total_api_calls || 0}</div>
                <div className="text-gray-400">Total API Calls Today</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400">
                  {costData?.recent_activity?.last_hour_requests || 0}
                </div>
                <div className="text-gray-400">Calls Last Hour</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400">
                  {costData?.recent_activity?.avg_requests_per_minute || 0}
                </div>
                <div className="text-gray-400">Avg Calls/Minute</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Optimization Tab */}
      {activeTab === 'ai-optimization' && (
        <div className="space-y-6">
          <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-purple-400">🤖 AI API Optimization Status</h3>
              <button
                onClick={runAIOptimization}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors"
                disabled={aiLoading}
              >
                {aiLoading ? 'Running...' : 'Run Optimization'}
              </button>
            </div>

            {aiOptimization.current_intervals && (
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-300">Current Optimized Intervals:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {Object.entries(aiOptimization.current_intervals).map(([endpoint, interval]) => (
                    <div key={endpoint} className="bg-gray-700 p-4 rounded-lg">
                      <div className="font-medium text-blue-400 capitalize">
                        {endpoint.replace(/-/g, ' ').replace(/\//g, ' ')}
                      </div>
                      <div className="text-2xl font-bold text-white">{interval}s</div>
                      <div className="text-sm text-gray-400">Refresh Interval</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-green-400 mb-4">📈 Recent Activity</h3>
            {aiOptimization.recent_activity && (
              <div className="space-y-4">
                {Object.entries(aiOptimization.recent_activity).map(([endpoint, activity]) => (
                  <div key={endpoint} className="flex justify-between items-center p-3 bg-gray-700 rounded">
                    <div className="font-medium capitalize">
                      {endpoint.replace(/-/g, ' ').replace(/\//g, ' ')}
                    </div>
                    <div className="text-right">
                      <div className="text-blue-400 font-bold">{activity.calls_last_5min} calls</div>
                      <div className="text-sm text-gray-400">Last 5 minutes</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="bg-blue-900 border border-blue-600 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-blue-200 mb-4">🧠 AI Optimization Benefits</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-100">
              <div>
                <h4 className="font-semibold mb-2">Automatic Adjustments:</h4>
                <ul className="text-sm space-y-1">
                  <li>• Reduces calls when data doesn't change</li>
                  <li>• Increases intervals during high error rates</li>
                  <li>• Optimizes based on response times</li>
                  <li>• Learns from usage patterns</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Cost Savings:</h4>
                <ul className="text-sm space-y-1">
                  <li>• Up to 40% reduction in API calls</li>
                  <li>• Smart caching based on data freshness</li>
                  <li>• Error-based backoff prevents waste</li>
                  <li>• Real-time performance monitoring</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CostMonitoringScreen;
