import { vi } from 'vitest';

export default {
  get: vi.fn(() => Promise.resolve({ 
    data: {},
    status: 200 
  })),
  post: vi.fn(() => Promise.resolve({ 
    data: {},
    status: 200 
  })),
  put: vi.fn(() => Promise.resolve({ 
    data: {},
    status: 200 
  })),
  delete: vi.fn(() => Promise.resolve({ 
    data: {},
    status: 200 
  })),
  defaults: {
    baseURL: '',
    headers: {
      'Content-Type': 'application/json'
    }
  }
};
