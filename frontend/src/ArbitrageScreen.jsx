import React, { useState, useEffect } from 'react';
import axiosInstance from './axiosInstance';

const ArbitrageScreen = () => {
  const [suggestions, setSuggestions] = useState([]);
  const [enhancedOpportunities, setEnhancedOpportunities] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('enhanced');

  const fetchSuggestions = async () => {
    try {
      const response = await axiosInstance.get('/api/arbitrage/suggestions');
      setSuggestions(response.data);
    } catch (e) {
      console.error('Error fetching basic arbitrage:', e);
    }
  };

  const fetchEnhancedOpportunities = async () => {
    try {
      const response = await axiosInstance.get('/api/arbitrage/enhanced');
      setEnhancedOpportunities(response.data);
    } catch (e) {
      console.error('Error fetching enhanced arbitrage:', e);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axiosInstance.get('/api/arbitrage/stats');
      setStats(response.data);
    } catch (e) {
      console.error('Error fetching arbitrage stats:', e);
    }
  };

  const fetchAllData = async () => {
    setLoading(true);
    setError(null);
    try {
      await Promise.all([
        fetchSuggestions(),
        fetchEnhancedOpportunities(),
        fetchStats()
      ]);
    } catch (e) {
      setError('Failed to fetch arbitrage data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
    const interval = setInterval(fetchAllData, 15000);
    return () => clearInterval(interval);
  }, []);

  if (loading && suggestions.length === 0 && enhancedOpportunities.length === 0) {
    return <div className="text-white p-4">Loading arbitrage data...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">Error: {error}</div>;
  }

  return (
    <div className="p-4 text-white">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-3xl font-bold">🔄 Enhanced Arbitrage Engine</h2>
        <button
          onClick={fetchAllData}
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg"
        >
          🔄 Refresh
        </button>
      </div>

      {/* Performance Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-green-400">Success Rate</h3>
            <p className="text-2xl">{stats.success_rate || 0}%</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-400">Total Profit</h3>
            <p className="text-2xl">${stats.total_profit || 0}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-purple-400">Avg Profit</h3>
            <p className="text-2xl">${stats.avg_profit || 0}</p>
          </div>
          <div className="bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-400">Total Trades</h3>
            <p className="text-2xl">{stats.total_trades || 0}</p>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex mb-6 border-b border-gray-700">
        <button
          onClick={() => setActiveTab('enhanced')}
          className={`px-6 py-3 font-semibold ${
            activeTab === 'enhanced'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          🚀 Enhanced ({enhancedOpportunities.length})
        </button>
        <button
          onClick={() => setActiveTab('basic')}
          className={`px-6 py-3 font-semibold ${
            activeTab === 'basic'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          📊 Basic ({suggestions.length})
        </button>
      </div>

      {/* Enhanced Opportunities */}
      {activeTab === 'enhanced' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {enhancedOpportunities.length === 0 ? (
            <div className="col-span-full text-gray-400 text-center py-8">
              No enhanced arbitrage opportunities found.
            </div>
          ) : (
            enhancedOpportunities.map((opportunity, index) => (
              <div key={index} className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-bold text-yellow-400">{opportunity.symbol}</h3>
                  {opportunity.tokenmetrics_grade && (
                    <span className={`px-2 py-1 rounded text-sm font-bold ${
                      opportunity.tokenmetrics_grade === 'A' ? 'bg-green-600' :
                      opportunity.tokenmetrics_grade === 'B' ? 'bg-blue-600' :
                      opportunity.tokenmetrics_grade === 'C' ? 'bg-yellow-600' : 'bg-red-600'
                    }`}>
                      Grade: {opportunity.tokenmetrics_grade}
                    </span>
                  )}
                </div>
                
                <div className="space-y-2 mb-4">
                  <p><strong>Buy:</strong> {opportunity.buy_exchange} at ${opportunity.buy_price?.toFixed(6)}</p>
                  <p><strong>Sell:</strong> {opportunity.sell_exchange} at ${opportunity.sell_price?.toFixed(6)}</p>
                  <p className="text-green-400">
                    <strong>Profit:</strong> {opportunity.percentage_diff?.toFixed(4)}% (${opportunity.profit_usd?.toFixed(2)})
                  </p>
                  {opportunity.ai_analysis_score && (
                    <p className="text-purple-400">
                      <strong>AI Score:</strong> {opportunity.ai_analysis_score}/100
                    </p>
                  )}
                </div>

                <button
                  onClick={() => alert('Execute feature coming soon!')}
                  className="w-full py-2 px-4 rounded-lg font-semibold bg-green-600 hover:bg-green-700"
                >
                  🚀 Execute Trade
                </button>
              </div>
            ))
          )}
        </div>
      )}

      {/* Basic Opportunities */}
      {activeTab === 'basic' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {suggestions.length === 0 ? (
            <div className="col-span-full text-gray-400 text-center py-8">
              No basic arbitrage opportunities found.
            </div>
          ) : (
            suggestions.map((opportunity, index) => (
              <div key={index} className="bg-gray-800 p-4 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold mb-2">{opportunity.symbol}</h3>
                <p><strong>Buy on:</strong> {opportunity.buy_exchange} at ${opportunity.buy_price?.toFixed(6)}</p>
                <p><strong>Sell on:</strong> {opportunity.sell_exchange} at ${opportunity.sell_price?.toFixed(6)}</p>
                <p><strong>Potential Profit:</strong> {opportunity.percentage_diff?.toFixed(4)}% (${opportunity.profit_usd?.toFixed(2)})</p>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default ArbitrageScreen;
