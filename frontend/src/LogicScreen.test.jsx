import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import LogicScreen from './LogicScreen';

// Mock axiosInstance
vi.mock('./axiosInstance');
import axiosInstance from './axiosInstance';

// Mock react-tooltip
vi.mock('react-tooltip', () => ({
  Tooltip: () => null
}));

const mockLogicData = {
  logic: [
    {
      symbol: 'BTC/USDT',
      final_decision: 'BUY',
      confidence: 0.85,
      deepseek: 'BUY',
      deepseek_reason: 'Strong upward trend',
      gemini: 'BUY',
      gemini_reason: 'Positive market sentiment',
      openai: 'BUY',
      openai_reason: 'Technical indicators positive',
      reasoning: 'Multiple indicators suggest upward momentum',
      timestamp: new Date().toISOString(),
      volume_surge: true,
      price_action: true
    },
    {
      symbol: 'ETH/USDT',
      final_decision: 'SELL',
      confidence: 0.75,
      deepseek: 'SELL',
      deepseek_reason: 'Bearish pattern forming',
      gemini: 'SELL',
      gemini_reason: 'Negative market outlook',
      openai: 'SELL',
      openai_reason: 'Technical resistance reached',
      reasoning: 'Multiple indicators suggest downward pressure',
      timestamp: new Date().toISOString(),
      volume_surge: false,
      price_action: true
    }
  ]
};

const mockPnlData = {
  total_trades: 100,
  net_profit: 5000.50,
  best_performing: {
    symbol: 'BTC',
    profit: 15.5
  },
  worst_performing: {
    symbol: 'ETH',
    profit: -8.2
  }
};

describe('LogicScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock both API calls
    axiosInstance.get.mockImplementation((url) => {
      if (url.includes('/ai-logic')) {
        return Promise.resolve({ data: mockLogicData });
      }
      if (url.includes('/pnl-data')) { // Changed from /api/trades/summary to /pnl-data
        return Promise.resolve({ data: mockPnlData });
      }
      return Promise.reject(new Error('Not found'));
    });
  });

  it('renders trading logic title', async () => {
    render(
      <MemoryRouter>
        <LogicScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /alpha ai decision logic/i })).toBeInTheDocument();
    });
  });

  it('fetches and displays AI logic data', async () => {
    render(
      <MemoryRouter>
        <LogicScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(axiosInstance.get).toHaveBeenCalledWith('/ai-logic');
      expect(screen.getByText('BTC/USDT')).toBeInTheDocument();
      expect(screen.getByText('ETH/USDT')).toBeInTheDocument();
    });
  });

  it('fetches and displays PnL data', async () => {
    render(
      <MemoryRouter>
        <LogicScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(axiosInstance.get).toHaveBeenCalledWith('/pnl-data'); // Changed from /api/trades/summary to /pnl-data
      expect(screen.getByText('100')).toBeInTheDocument(); // total trades
      expect(screen.getByText('$5,000.50')).toBeInTheDocument(); // net profit
    });
  });

  it('handles filter toggles', async () => {
    render(
      <MemoryRouter>
        <LogicScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      const volumeSurgeCheckbox = screen.getByLabelText('Volume Surge'); // Changed query
      fireEvent.click(volumeSurgeCheckbox);
      // After filtering, only BTC/USDT should be visible (has volume_surge: true)
      expect(screen.getByText('BTC/USDT')).toBeInTheDocument();
      expect(screen.queryByText('ETH/USDT')).not.toBeInTheDocument();
    });
  });

  it('handles error state for AI logic', async () => {
    axiosInstance.get.mockImplementation((url) => {
      if (url.includes('/ai-logic')) {
        return Promise.reject(new Error('Failed to fetch'));
      }
      return Promise.resolve({ data: {} });
    });

    render(
      <MemoryRouter>
        <LogicScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/failed to fetch ai decision logic/i)).toBeInTheDocument();
    });
  });

  it('handles error state for PnL data', async () => {
    axiosInstance.get.mockImplementation((url) => {
      if (url.includes('/pnl-data')) { // Changed from /api/trades/summary to /pnl-data
        return Promise.reject(new Error('Failed to fetch'));
      }
      return Promise.resolve({ data: mockLogicData });
    });

    render(
      <MemoryRouter>
        <LogicScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/failed to fetch pnl data/i)).toBeInTheDocument();
    });
  });
});