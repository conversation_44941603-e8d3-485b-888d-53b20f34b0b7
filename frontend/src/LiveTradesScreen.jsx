import React, { useEffect, useState } from "react";
import axiosInstance from "./axiosInstance";

export default function LiveTradesScreen() {
  const [trades, setTrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchTrades = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(`/api/trades/live`);
      setTrades(response.data || []);
      setLastUpdated(new Date());
    } catch (err) {
      console.error("Error fetching live trades:", err);
      setError("Failed to load live trades.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrades();
    const interval = setInterval(fetchTrades, 300000); // 5-minute refresh
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-semibold mb-4">📈 Live Trade Feed</h1>

      {loading && (
        <div className="flex items-center justify-center py-4">
          <span className="loader border-4 border-blue-500 border-t-transparent rounded-full w-6 h-6 animate-spin"></span>
          <p className="ml-2 text-blue-600 text-sm">Fetching live trades...</p>
        </div>
      )}
      {error && <p className="text-red-500">{error}</p>}
      {!loading && trades.length === 0 && (
        <div className="text-center py-6 text-gray-500 italic">No trades yet. Our bot is watching the market 👀</div>
      )}

      {!loading && trades.length > 0 && (
        <>
          <div className="mb-2 flex items-center justify-between">
            <p className="text-sm text-gray-500">
              Last updated:{" "}
              {lastUpdated ? new Date(lastUpdated).toLocaleTimeString() : "Fetching..."}
            </p>
            <button
              onClick={fetchTrades}
              className="ml-4 px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded shadow"
            >
              🔄 Refresh Now
            </button>
          </div>
          <table className="min-w-full bg-white rounded shadow-md overflow-hidden">
            <thead>
              <tr className="bg-gray-100 text-left text-sm font-medium text-gray-600">
                <th className="px-4 py-2">⏱️ Time</th>
                <th className="px-4 py-2">🔹 Token</th>
                <th className="px-4 py-2">💼 Action</th>
                <th className="px-4 py-2">💰 Price</th>
                <th className="px-4 py-2">📦 Quantity</th>
                <th className="px-4 py-2">📊 Value</th>
                <th className="px-4 py-2">📈 PnL</th>
              </tr>
            </thead>
            <tbody>
              {trades.map((trade, index) => (
                <tr key={index} className="border-t text-sm hover:bg-gray-50">
                  <td className="px-4 py-2">
                    {trade?.timestamp
                      ? new Date(trade.timestamp).toLocaleString("en-US", {
                          hour: "2-digit",
                          minute: "2-digit",
                          second: "2-digit",
                          hour12: true,
                        })
                      : "—"}
                  </td>
                  <td className="px-4 py-2">{(trade?.token || trade?.symbol || "—").toUpperCase()}</td>
                  <td
                    className={`px-4 py-2 font-semibold ${
                      (trade?.action || trade?.side) === "BUY"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {(trade?.action || trade?.side || "—").toUpperCase()}
                  </td>
                  <td className="px-4 py-2">
                    {trade?.price
                      ? `$${parseFloat(trade.price).toLocaleString("en-US", { minimumFractionDigits: 4, maximumFractionDigits: 4 })}`
                      : "—"}
                  </td>
                  <td className="px-4 py-2">
                    {trade?.amount || trade?.quantity
                      ? parseFloat(trade.amount || trade.quantity).toLocaleString("en-US", { minimumFractionDigits: 4, maximumFractionDigits: 4 })
                      : "—"}
                  </td>
                  <td className="px-4 py-2">
                    {trade?.usd_value || trade?.total
                      ? `$${parseFloat(trade.usd_value || trade.total).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                      : "—"}
                  </td>
                  <td className="px-4 py-2">
                    {trade?.pnl !== undefined ? (
                      <span
                        className={
                          parseFloat(trade.pnl) >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        ${parseFloat(trade.pnl).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </span>
                    ) : (
                      "—"
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </>
      )}
    </div>
  );
}
