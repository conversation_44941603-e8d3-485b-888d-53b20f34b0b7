import { useState, useEffect, useCallback, useRef } from 'react';
import axiosInstance from '../axiosInstance';
import { getOptimalInterval, API_USAGE_TRACKER } from '../config/refreshIntervals';

/**
 * 🚀 REAL-TIME DATA HOOK
 * Provides fast, real-time data fetching with last updated timestamps
 */
export const useRealTimeData = (
  endpoint,
  options = {}
) => {
  const {
    refreshInterval = 60000, // Default 1 minute (more reasonable)
    params = {},
    enabled = true,
    onSuccess,
    onError,
    transform = (data) => data,
    userActivity = 'active',
    performanceMode = 'balanced',
    dataImportance = 'medium'
  } = options;

  // Calculate optimal refresh interval
  const optimalInterval = getOptimalInterval(
    refreshInterval,
    userActivity,
    performanceMode,
    dataImportance
  );

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const intervalRef = useRef(null);
  const mountedRef = useRef(true);

  // Stabilize callback references to prevent infinite loops
  const stableOnSuccess = useRef(onSuccess);
  const stableOnError = useRef(onError);
  const stableTransform = useRef(transform);
  const stableParams = useRef(params);

  // Update refs when values change
  useEffect(() => {
    stableOnSuccess.current = onSuccess;
    stableOnError.current = onError;
    stableTransform.current = transform;
    stableParams.current = params;
  });

  const fetchData = useCallback(async (isInitial = false) => {
    if (!enabled) return;

    try {
      if (isInitial) {
        setLoading(true);
      } else {
        setIsRefreshing(true);
      }

      setError(null);

      const response = await axiosInstance.get(endpoint, { params: stableParams.current });

      // Track API usage for optimization
      API_USAGE_TRACKER.recordCall(endpoint);

      if (mountedRef.current) {
        const transformedData = stableTransform.current(response.data);
        setData(transformedData);
        setLastUpdated(new Date());

        if (stableOnSuccess.current) {
          stableOnSuccess.current(transformedData);
        }
      }
    } catch (err) {
      if (mountedRef.current) {
        console.error(`❌ Real-time data fetch failed for ${endpoint}:`, err);
        setError(err.response?.data?.error || err.message || 'Failed to fetch data');

        if (stableOnError.current) {
          stableOnError.current(err);
        }
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
        setIsRefreshing(false);
      }
    }
  }, [endpoint, enabled]); // Only depend on endpoint and enabled

  const refresh = useCallback(() => {
    fetchData(false);
  }, [fetchData]);

  useEffect(() => {
    mountedRef.current = true;

    // Clear any existing interval first
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (enabled) {
      // Initial fetch
      fetchData(true);

      // Set up interval for real-time updates with optimal interval
      if (optimalInterval > 0) {
        intervalRef.current = setInterval(() => {
          fetchData(false);
        }, optimalInterval);
      }
    }

    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [fetchData, optimalInterval, enabled]);

  // Format last updated time
  const getLastUpdatedText = () => {
    if (!lastUpdated) return 'Never';

    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 10) return 'Just now';
    if (diffSeconds < 60) return `${diffSeconds}s ago`;

    const diffMinutes = Math.floor(diffSeconds / 60);
    if (diffMinutes < 60) return `${diffMinutes}m ago`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    return lastUpdated.toLocaleDateString();
  };

  const getStatusColor = () => {
    if (error) return 'text-red-400';
    if (isRefreshing) return 'text-yellow-400';
    if (!lastUpdated) return 'text-gray-400';

    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 30) return 'text-green-400';
    if (diffSeconds < 120) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getStatusIcon = () => {
    if (error) return '❌';
    if (isRefreshing) return '🔄';
    if (!lastUpdated) return '⏳';

    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 30) return '🟢';
    if (diffSeconds < 120) return '🟡';
    return '🔴';
  };

  return {
    data,
    loading,
    error,
    lastUpdated,
    isRefreshing,
    refresh,
    getLastUpdatedText,
    getStatusColor,
    getStatusIcon
  };
};

/**
 * 🕒 LAST UPDATED COMPONENT
 * Reusable component to show last updated status
 */
export const LastUpdatedIndicator = ({
  lastUpdated,
  isRefreshing,
  error,
  onRefresh,
  className = ""
}) => {
  const getLastUpdatedText = () => {
    if (!lastUpdated) return 'Never';

    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 10) return 'Just now';
    if (diffSeconds < 60) return `${diffSeconds}s ago`;

    const diffMinutes = Math.floor(diffSeconds / 60);
    if (diffMinutes < 60) return `${diffMinutes}m ago`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    return lastUpdated.toLocaleDateString();
  };

  const getStatusColor = () => {
    if (error) return 'text-red-400';
    if (isRefreshing) return 'text-yellow-400';
    if (!lastUpdated) return 'text-gray-400';

    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 30) return 'text-green-400';
    if (diffSeconds < 120) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getStatusIcon = () => {
    if (error) return '❌';
    if (isRefreshing) return '🔄';
    if (!lastUpdated) return '⏳';

    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffSeconds = Math.floor(diffMs / 1000);

    if (diffSeconds < 30) return '🟢';
    if (diffSeconds < 120) return '🟡';
    return '🔴';
  };

  return (
    <div className={`flex items-center space-x-2 text-sm ${className}`}>
      <span className={getStatusColor()}>
        {getStatusIcon()} Last updated: {getLastUpdatedText()}
      </span>
      {onRefresh && (
        <button
          onClick={onRefresh}
          disabled={isRefreshing}
          className="px-2 py-1 text-xs bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded transition-colors"
        >
          {isRefreshing ? '🔄' : '🔄'} Refresh
        </button>
      )}
      {error && (
        <span className="text-red-400 text-xs">
          ⚠️ {error}
        </span>
      )}
    </div>
  );
};

export default useRealTimeData;
