import React, { useEffect, useState, useMemo } from "react";
// This screen fetches and displays real-time analytics from the backend
import axiosInstance from "./axiosInstance";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

function AnalyticsScreen() {
  const [analytics, setAnalytics] = useState(null);
  const [error, setError] = useState(null);

  // Fetches real-time analytics data from backend API
  useEffect(() => {
    let intervalId;

    async function fetchAnalytics() {
      try {
        const res = await axiosInstance.get("/api/analytics");
        setAnalytics(res.data);
      } catch (err) {
        console.error("Failed to fetch analytics:", err);
        setError("Unable to load analytics data.");
      }
    }

    fetchAnalytics();
    intervalId = setInterval(fetchAnalytics, 30000); // Refresh every 30 seconds

    return () => clearInterval(intervalId);
  }, []);

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (!analytics) {
    return (
      <div className="text-gray-300">
        <span className="animate-spin">⏳</span> Loading analytics...
      </div>
    );
  }

  return (
    <div className="p-6 space-y-4 text-white bg-gray-900 min-h-screen">
      <h1 className="text-3xl font-bold mb-4">Real-Time AI Analytics Dashboard</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Total Trades</h2>
          <p className="text-xl">{analytics.trade_counts?.total ?? 0}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Win Rate (%)</h2>
          <p className="text-sm text-gray-400">Calculated from successful trades / total trades</p>
          <p className="text-xl">{analytics.win_loss?.win_percent?.toFixed(2) ?? "0.00"}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Loss Rate (%)</h2>
          <p className="text-sm text-gray-400">Inverse of win rate</p>
          <p className="text-xl">{analytics.win_loss?.loss_percent?.toFixed(2) ?? "0.00"}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Largest Win</h2>
          <p className="text-sm text-gray-400">Largest profit on a single trade</p>
          <p className="text-xl">${analytics.profit_loss?.largest_win?.toFixed(2) ?? "0.00"}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Largest Loss</h2>
          <p className="text-sm text-gray-400">Largest loss on a single trade</p>
          <p className="text-xl">${analytics.profit_loss?.largest_loss?.toFixed(2) ?? "0.00"}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Current Portfolio Value</h2>
          <p className="text-xl">${analytics.portfolio?.current_value?.toFixed(2) ?? "0.00"}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">Portfolio Change</h2>
          <p className="text-sm text-gray-400">Since last trade update</p>
          <p className="text-xl">${analytics.portfolio?.change?.toFixed(2) ?? "0.00"}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">AI Decisions (Buy)</h2>
          <p className="text-xl">{analytics.ai_decisions?.buy ?? 0}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">AI Decisions (Sell)</h2>
          <p className="text-xl">{analytics.ai_decisions?.sell ?? 0}</p>
        </div>
        <div className="bg-gray-800 p-4 rounded shadow">
          <h2 className="text-lg font-semibold">AI Decisions (Hold)</h2>
          <p className="text-xl">{analytics.ai_decisions?.hold ?? 0}</p>
        </div>

        {/* Strategy Metrics */}
        {analytics.strategy_metrics && (
          <div className="bg-gray-700 p-4 rounded shadow col-span-full">
            <h2 className="text-2xl font-bold mb-2">Strategy-Level Metrics</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(analytics.strategy_metrics).map(([strategy, data]) => (
                <div key={strategy} className="bg-gray-800 p-3 rounded">
                  <h3 className="text-lg font-semibold">{strategy}</h3>
                  <p className="text-sm text-gray-400">Total Trades: {data.total_trades}</p>
                  <p className="text-sm text-gray-400">Win %: {data.win_rate?.toFixed(2)}</p>
                  <p className="text-sm text-gray-400">Avg PnL: ${data.avg_pnl?.toFixed(2)}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Token Metrics */}
        {analytics.token_metrics && (
          <div className="bg-gray-700 p-4 rounded shadow col-span-full">
            <h2 className="text-2xl font-bold mb-2">Token-Level Metrics</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(analytics.token_metrics).map(([symbol, data]) => (
                <div key={symbol} className="bg-gray-800 p-3 rounded">
                  <h3 className="text-lg font-semibold">{symbol}</h3>
                  <p className="text-sm text-gray-400">Trades: {data.trades}</p>
                  <p className="text-sm text-gray-400">Win %: {data.win_rate?.toFixed(2)}</p>
                  <p className="text-sm text-gray-400">Avg Return: {data.avg_return?.toFixed(2)}%</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Reasoning Quality Metrics */}
        {analytics.ai_reasoning && (
          <div className="bg-gray-700 p-4 rounded shadow col-span-full">
            <h2 className="text-2xl font-bold mb-2">AI Reasoning Insights</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(analytics.ai_reasoning).map(([model, data]) => (
                <div key={model} className="bg-gray-800 p-3 rounded">
                  <h3 className="text-lg font-semibold">{model}</h3>
                  <p className="text-sm text-gray-400">Avg Length: {data.avg_length?.toFixed(2)}</p>
                  <p className="text-sm text-gray-400">Clarity Score: {data.clarity_score?.toFixed(2)}</p>
                  <p className="text-sm text-gray-400">Keyword Coverage: {data.keyword_coverage?.toFixed(2)}%</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {Object.keys(analytics).length === 0 && (
        <p className="text-red-400 text-sm mt-4">⚠️ Analytics data not fully loaded or empty.</p>
      )}
    </div>
  );
}

export default AnalyticsScreen;
