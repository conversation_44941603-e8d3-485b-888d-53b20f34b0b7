import React, { useState } from 'react';
import { useRealTimeData, LastUpdatedIndicator } from './hooks/useRealTimeData.jsx';

const NewsScreen = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  // Real-time news data with AI-optimized 60-second refresh
  const {
    data: newsData,
    loading,
    error,
    lastUpdated,
    isRefreshing,
    refresh
  } = useRealTimeData('/api/news/live', {
    refreshInterval: 180000, // 3 minutes for news (news doesn't change rapidly)
    dataImportance: 'medium',
    params: { limit: 50 },
    transform: (data) => {
      const liveNews = data.news || data || [];

      // Extract sentiment analysis from news data
      const sentimentData = {};
      liveNews.forEach(article => {
        if (article.tokens_mentioned) {
          article.tokens_mentioned.forEach(token => {
            if (!sentimentData[token]) {
              sentimentData[token] = {
                sentiment_score: 0,
                article_count: 0
              };
            }
            sentimentData[token].sentiment_score += (article.sentiment_score || 0);
            sentimentData[token].article_count += 1;
          });
        }
      });

      // Calculate average sentiment per token
      Object.keys(sentimentData).forEach(token => {
        const data = sentimentData[token];
        data.sentiment_score = data.article_count > 0 ?
          data.sentiment_score / data.article_count : 0;
      });

      return {
        news: liveNews,
        sentimentAnalysis: sentimentData
      };
    },
    onSuccess: (data) => {
      console.log('✅ Real-time news data updated:', data.news.length, 'articles');
    }
  });

  const news = newsData?.news || [];
  const sentimentAnalysis = newsData?.sentimentAnalysis || {};



  const getSentimentColor = (sentiment) => {
    if (sentiment > 0.1) return 'text-green-400';
    if (sentiment < -0.1) return 'text-red-400';
    return 'text-yellow-400';
  };

  const getSentimentLabel = (sentiment) => {
    if (sentiment > 0.1) return 'Bullish';
    if (sentiment < -0.1) return 'Bearish';
    return 'Neutral';
  };

  const filteredNews = news.filter(article => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'bullish') return article.sentiment_score > 0.1;
    if (activeFilter === 'bearish') return article.sentiment_score < -0.1;
    if (activeFilter === 'signals') return article.trading_signal && article.trading_signal !== 'HOLD';
    return true;
  });

  if (loading) {
    return <div className="text-white p-4">Loading news and sentiment data...</div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">Error: {error}</div>;
  }

  return (
    <div className="p-4 text-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-3xl font-bold">📰 News & Sentiment Analysis</h2>
      </div>

      {/* Real-time Status Indicator */}
      <div className="mb-6">
        <LastUpdatedIndicator
          lastUpdated={lastUpdated}
          isRefreshing={isRefreshing}
          error={error}
          onRefresh={refresh}
          className="justify-end"
        />
      </div>

      {/* Market Sentiment Overview */}
      {sentimentAnalysis.overall_sentiment && (
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h3 className="text-xl font-bold mb-4">📊 Market Sentiment Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <h4 className="text-lg font-semibold">Overall Sentiment</h4>
              <p className={`text-2xl font-bold ${getSentimentColor(sentimentAnalysis.overall_sentiment)}`}>
                {getSentimentLabel(sentimentAnalysis.overall_sentiment)}
              </p>
              <p className="text-sm text-gray-400">
                Score: {sentimentAnalysis.overall_sentiment?.toFixed(3)}
              </p>
            </div>
            <div className="text-center">
              <h4 className="text-lg font-semibold">News Volume</h4>
              <p className="text-2xl font-bold text-blue-400">
                {sentimentAnalysis.news_count || news.length}
              </p>
              <p className="text-sm text-gray-400">Articles analyzed</p>
            </div>
            <div className="text-center">
              <h4 className="text-lg font-semibold">Signal Strength</h4>
              <p className="text-2xl font-bold text-purple-400">
                {sentimentAnalysis.signal_strength || 'Medium'}
              </p>
              <p className="text-sm text-gray-400">Confidence level</p>
            </div>
          </div>
        </div>
      )}

      {/* Filter Tabs */}
      <div className="flex mb-6 border-b border-gray-700">
        {[
          { key: 'all', label: `All News (${news.length})` },
          { key: 'bullish', label: `Bullish (${news.filter(a => a.sentiment_score > 0.1).length})` },
          { key: 'bearish', label: `Bearish (${news.filter(a => a.sentiment_score < -0.1).length})` },
          { key: 'signals', label: `Trading Signals (${news.filter(a => a.trading_signal && a.trading_signal !== 'HOLD').length})` }
        ].map(filter => (
          <button
            key={filter.key}
            onClick={() => setActiveFilter(filter.key)}
            className={`px-6 py-3 font-semibold ${activeFilter === filter.key
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
              }`}
          >
            {filter.label}
          </button>
        ))}
      </div>

      {/* News Feed */}
      <div className="space-y-4">
        {filteredNews.length === 0 ? (
          <div className="text-gray-400 text-center py-8">
            No news articles found for the selected filter.
          </div>
        ) : (
          filteredNews.map((article, index) => (
            <div key={index} className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-white flex-1 mr-4">
                  {article.title}
                </h3>
                <div className="flex flex-col items-end space-y-2">
                  {article.sentiment_score !== undefined && (
                    <span className={`px-3 py-1 rounded-full text-sm font-bold ${article.sentiment_score > 0.1 ? 'bg-green-600' :
                      article.sentiment_score < -0.1 ? 'bg-red-600' : 'bg-yellow-600'
                      }`}>
                      {getSentimentLabel(article.sentiment_score)}
                    </span>
                  )}
                  {article.trading_signal && article.trading_signal !== 'HOLD' && (
                    <span className={`px-3 py-1 rounded-full text-sm font-bold ${article.trading_signal === 'BUY' ? 'bg-green-600' : 'bg-red-600'
                      }`}>
                      {article.trading_signal}
                    </span>
                  )}
                </div>
              </div>

              {article.summary && (
                <p className="text-gray-300 mb-4">{article.summary}</p>
              )}

              <div className="flex justify-between items-center text-sm text-gray-400">
                <span>{article.source || 'Unknown Source'}</span>
                <span>{article.published_at || 'Recent'}</span>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default NewsScreen;
