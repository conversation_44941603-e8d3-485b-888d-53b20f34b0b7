import axios from "axios";

// Get API base URL
const getBaseURL = () => {
  const hostname = window.location.hostname;
  
  // Priority 1: Check for explicit backend URL environment variable
  if (import.meta.env.VITE_BACKEND_URL) {
    console.log('Using VITE_BACKEND_URL:', import.meta.env.VITE_BACKEND_URL);
    return import.meta.env.VITE_BACKEND_URL;
  }
  
  // Priority 2: For localhost development, check if we should use mainnet backend
  if (hostname === "localhost") {
    // If VITE_USE_MAINNET is true, use production backend
    if (import.meta.env.VITE_USE_MAINNET === 'true') {
      console.log('Using mainnet backend for localhost');
      return "https://api.alphapredatorbot.xyz";
    }
    console.log('Using local backend for localhost');
    return `http://${hostname}:3005`;
  } else {
    // Priority 3: Fallback for production without environment variable
    console.log('Using production fallback');
    return "https://api.alphapredatorbot.xyz";
  }
};

const axiosInstance = axios.create({
  baseURL: getBaseURL(),
  headers: {
    'Content-Type': 'application/json'
  }
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token expiration
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid - redirect to login
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
