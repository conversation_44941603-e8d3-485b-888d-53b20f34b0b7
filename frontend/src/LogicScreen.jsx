import React, { useEffect, useState } from "react";
import axiosInstance from "./axiosInstance";
import SignalDetailModal from "./screens/SignalDetailModal.jsx";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { formatNumber } from "./utils/formatters";
import Loader from "./components/Loader";

const LogicScreen = () => {
  const [logicData, setLogicData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [pnlData, setPnlData] = useState({});
  const [pnlLoading, setPnlLoading] = useState(true);
  const [pnlError, setPnlError] = useState(null);

  const [volumeSurge, setVolumeSurge] = useState(false);
  const [priceAction, setPriceAction] = useState(false);
  const [newListing, setNewListing] = useState(false);

  const [selectedEntry, setSelectedEntry] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);

  const [selectedSymbols, setSelectedSymbols] = useState([]);

  useEffect(() => {
    const fetchLogicData = async () => {
      try {
        const response = await axiosInstance.get("/api/ai-logic");
        if (response && response.data) {
          let logicArray = [];
          if (Array.isArray(response.data.logic)) {
            logicArray = response.data.logic;
          } else if (Array.isArray(response.data)) {
            logicArray = response.data;
          }
          setLogicData(logicArray);
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchLogicData();
  }, []);

  useEffect(() => {
    const fetchPnlData = async () => {
      try {
        const response = await axiosInstance.get("/api/pnl-data");
        setPnlData(response.data);
      } catch (err) {
        setPnlError(err.message);
      } finally {
        setPnlLoading(false);
      }
    };
    fetchPnlData();
  }, []);

  console.log("✅ AI Logic Data:", logicData);

  if (loading) return <Loader />;
  if (error) return <div className="text-red-500">{error}</div>;

  // Extract unique symbols from logicData for dynamic symbol filters
  const uniqueSymbols = Array.from(new Set(logicData.map(entry => entry?.symbol)))
    .filter(Boolean)
    .sort();

  const toggleSymbol = (symbol) => {
    if (selectedSymbols.includes(symbol)) {
      setSelectedSymbols(selectedSymbols.filter(s => s !== symbol));
    } else {
      setSelectedSymbols([...selectedSymbols, symbol]);
    }
  };

  const filteredLogicData = logicData.filter(entry => {
    if (!entry || typeof entry !== 'object') return false;
    if (volumeSurge && !entry.volume_surge) return false;
    if (priceAction && !entry.price_action) return false;
    if (newListing && !entry.new_listing) return false;
    if (selectedSymbols.length > 0 && !selectedSymbols.includes(entry.symbol)) return false;
    return true;
  });

  return (
    <div className="container mx-auto p-6 text-white">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Alpha AI Decision Logic</h2>
        <div className="text-sm text-gray-400">
          Last updated: {new Date().toLocaleString()}
        </div>
      </div>
      {/* Filters UI */}
      <section aria-label="Filters" className="bg-gray-800 p-4 rounded-md mb-4">
        <h3 className="text-lg font-semibold mb-2">Filters</h3>
        <label className="mr-4">
          <input type="checkbox" className="mr-2" checked={volumeSurge} onChange={() => setVolumeSurge(!volumeSurge)} />
          Volume Surge
        </label>
        <label className="mr-4">
          <input type="checkbox" className="mr-2" checked={priceAction} onChange={() => setPriceAction(!priceAction)} />
          Price Action
        </label>
        <label className="mr-4">
          <input type="checkbox" className="mr-2" checked={newListing} onChange={() => setNewListing(!newListing)} />
          New Listing
        </label>
        <button
          onClick={() => {
            setVolumeSurge(false);
            setPriceAction(false);
            setNewListing(false);
            setSelectedSymbols([]);
          }}
          className="mt-2 px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition"
        >
          Reset Filters
        </button>
      </section>
      {/* Dynamic Symbol Filters */}
      {uniqueSymbols.length > 0 && (
        <section aria-label="Symbol Filters" className="bg-gray-800 p-4 rounded-md mb-4">
          <h3 className="text-lg font-semibold mb-2">Symbols</h3>
          {uniqueSymbols.map(symbol => (
            <label key={symbol} className="mr-4">
              <input
                type="checkbox"
                className="mr-2"
                checked={selectedSymbols.includes(symbol)}
                onChange={() => toggleSymbol(symbol)}
              />
              {symbol}
            </label>
          ))}
        </section>
      )}
      <section aria-label="AI Logic Results">
        {Array.isArray(filteredLogicData) && filteredLogicData.length > 0 ? (
          filteredLogicData.map((entry, idx) => (
            <button
              key={idx}
              onClick={() => {
                setSelectedEntry(entry);
                setModalOpen(true);
              }}
              className="w-full text-left border border-gray-700 p-4 rounded-md bg-gray-800 hover:bg-gray-700 transition mb-4"
            >
              <p data-tip="Ticker symbol of the asset"><strong>Symbol:</strong> {entry?.symbol || "N/A"}</p>
              <p data-tip="Final combined decision from DeepSeek, Gemini, and OpenAI"><strong>Final Decision:</strong> <span className="font-bold text-green-400">{entry?.final_decision || "N/A"}</span></p>
              <p data-tip="Confidence level of the AI decision"><strong>Confidence:</strong> {
                entry?.confidence !== undefined && entry?.confidence !== null ? (
                  <span className={`font-bold ${
                    entry.confidence > 0.7 ? 'text-green-400' :
                    entry.confidence > 0.4 ? 'text-yellow-400' :
                    'text-red-400'
                  }`}>
                    {formatNumber(entry.confidence * 100, 1)}%
                  </span>
                ) : "N/A"
              }</p>

              {/* Strategy Breakdown */}
              <div className="mt-2 text-sm text-gray-300 space-y-1">
                <div>
                  <p data-tip="DeepSeek strategy output"><strong>DeepSeek:</strong> {entry.deepseek || "N/A"}</p>
                  <p className="ml-2 text-gray-400 italic" data-tip="Reasoning behind DeepSeek's decision">{entry.deepseek_reason || "Reasoning not available. Please check AI source for details."}</p>
                </div>
                <div>
                  <p data-tip="Gemini strategy output"><strong>Gemini:</strong> {entry.gemini || "N/A"}</p>
                  <p className="ml-2 text-gray-400 italic" data-tip="Reasoning behind Gemini's decision">{entry.gemini_reason || "Reasoning not available. Please check AI source for details."}</p>
                </div>
                <div>
                  <p data-tip="OpenAI strategy output"><strong>OpenAI:</strong> {entry.openai || "N/A"}</p>
                  <p className="ml-2 text-gray-400 italic" data-tip="Reasoning behind OpenAI's decision">{entry.openai_reason || "Reasoning not available. Please check AI source for details."}</p>
                </div>
              </div>

              {entry?.reasoning && (
                <p className="text-sm text-gray-400 mt-2" data-tip="Overall reasoning for the AI decision">
                  <strong>Reason:</strong> {entry.reasoning}
                </p>
              )}
              {entry?.timestamp && (
                <p className="text-sm text-gray-500 mt-1">Last Updated: {new Date(entry.timestamp).toLocaleString()}</p>
              )}
            </button>
          ))
        ) : (
          <div className="text-yellow-400 flex items-center space-x-2 mt-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M12 18.5a6.5 6.5 0 100-13 6.5 6.5 0 000 13z" />
            </svg>
            <span>⚠️ No matching signals found. Try removing filters or wait for updated AI decisions.</span>
          </div>
        )}
      </section>
      {/* Charts Placeholder */}
      <div className="bg-gray-800 p-4 rounded-md mt-6">
        <h3 className="text-lg font-semibold mb-2">Charts (Coming Soon)</h3>
        <p className="text-gray-400">Visualizations like token trends and volumes will appear here.</p>
      </div>
      {/* PnL Dashboard */}
      <section aria-label="PnL Dashboard" className="bg-gray-800 p-4 rounded-md mt-6">
        <h3 className="text-lg font-semibold mb-2">PnL Dashboard</h3>
        {pnlLoading ? (
          <div className="text-gray-400 flex items-center space-x-2">
            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-gray-400"></div>
            <span>Loading PnL data...</span>
          </div>
        ) : pnlError ? (
          <p className="text-red-400">{pnlError}</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-700 p-3 rounded-md">
              <p data-tip="Total number of trades executed" className="text-sm text-gray-400">Total Trades</p>
              <p className="text-xl font-bold">{formatNumber(pnlData.total_trades ?? 0, 0)}</p>
            </div>
            <div className="bg-gray-700 p-3 rounded-md">
              <p data-tip="Net profit from all trades" className="text-sm text-gray-400">Net Profit</p>
              <p className="text-xl font-bold text-green-400">${formatNumber(pnlData.net_profit ?? 0)}</p>
            </div>
            <div className="bg-gray-700 p-3 rounded-md">
              <p data-tip="Token with the highest profit" className="text-sm text-gray-400">Best Performing Token</p>
              <p className="text-lg font-semibold">{pnlData.best_performing?.symbol ?? "N/A"}</p>
              <p className="text-green-300">
                {pnlData.best_performing?.profit !== undefined && pnlData.best_performing?.profit !== null
                  ? `${formatNumber(pnlData.best_performing.profit, 1)}% gain`
                  : "N/A"}
              </p>
            </div>
            <div className="bg-gray-700 p-3 rounded-md">
              <p data-tip="Token with the largest loss" className="text-sm text-gray-400">Worst Performing Token</p>
              <p className="text-lg font-semibold">{pnlData.worst_performing?.symbol ?? "N/A"}</p>
              <p className="text-red-300">
                {pnlData.worst_performing?.profit !== undefined && pnlData.worst_performing?.profit !== null
                  ? `${formatNumber(pnlData.worst_performing.profit, 1)}% loss`
                  : "N/A"}
              </p>
            </div>
          </div>
        )}
      </section>
      {modalOpen && selectedEntry && (
        <SignalDetailModal
          signal={selectedEntry}
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          showReasoning={true}
        />
      )}
      <ReactTooltip effect="solid" />
    </div>
  );
};

export default LogicScreen;
