import React from 'react';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { AuthProvider } from './contexts/AuthContext';

// Mock the actual AuthContext
vi.mock('./contexts/AuthContext', () => ({
  AuthProvider: ({ children }) => {
    const mockAuthValue = {
      user: { email: '<EMAIL>' },
      login: vi.fn().mockResolvedValue(true),
      logout: vi.fn(),
      loading: false
    };

    const AuthContext = React.createContext(mockAuthValue);
    
    return (
      <AuthContext.Provider value={mockAuthValue}>
        {children}
      </AuthContext.Provider>
    );
  },
  useAuth: () => ({
    user: { email: '<EMAIL>' },
    login: vi.fn().mockResolvedValue(true),
    logout: vi.fn(),
    loading: false
  })
}));

// Mock AuthProvider for tests
const MockAuthProvider = ({ children }) => {
  return <AuthProvider>{children}</AuthProvider>;
};

// Custom render function that includes providers
export const renderWithProviders = (ui, options = {}) => {
  const { initialEntries = ['/'], ...renderOptions } = options;

  const Wrapper = ({ children }) => (
    <MemoryRouter initialEntries={initialEntries}>
      <MockAuthProvider>
        {children}
      </MockAuthProvider>
    </MemoryRouter>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { renderWithProviders as render };
