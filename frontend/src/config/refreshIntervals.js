/**
 * 🎯 OPTIMIZED REFRESH INTERVALS
 * Balanced approach for performance and real-time data
 */

export const REFRESH_INTERVALS = {
  // CRITICAL DATA (High frequency, but not excessive)
  LIVE_TRADES: 30000,        // 30 seconds - trades need to be current
  AI_SIGNALS: 60000,         // 1 minute - AI decisions don't change rapidly
  DASHBOARD_SUMMARY: 45000,  // 45 seconds - main dashboard data
  
  // IMPORTANT DATA (Medium frequency)
  PNL_DATA: 120000,          // 2 minutes - PnL changes gradually
  PORTFOLIO: 120000,         // 2 minutes - portfolio updates
  BOT_STATUS: 90000,         // 1.5 minutes - bot status
  
  // BACKGROUND DATA (Lower frequency)
  DISCOVER_TOKENS: 300000,   // 5 minutes - token discovery is slow process
  NEWS_FEED: 180000,         // 3 minutes - news doesn't change rapidly
  ANALYTICS: 300000,         // 5 minutes - analytics are historical
  
  // EXPENSIVE DATA (Very low frequency)
  COST_MONITORING: 180000,   // 3 minutes - cost data changes slowly
  TOKENMETRICS: 240000,      // 4 minutes - expensive API calls
  ARBITRAGE: 300000,         // 5 minutes - arbitrage opportunities
  
  // STATIC DATA (Very low frequency)
  SOURCE_WEIGHTS: 600000,    // 10 minutes - configuration data
  AI_OPTIMIZATION: 300000,   // 5 minutes - optimization data
};

/**
 * 🚀 ADAPTIVE REFRESH INTERVALS
 * Adjusts intervals based on user activity and data importance
 */
export const ADAPTIVE_INTERVALS = {
  // When user is actively viewing the screen
  ACTIVE: {
    LIVE_TRADES: 15000,      // 15 seconds when actively viewing
    AI_SIGNALS: 30000,       // 30 seconds when actively viewing
    DASHBOARD_SUMMARY: 30000, // 30 seconds when actively viewing
  },
  
  // When user is on different screen (background updates)
  BACKGROUND: {
    LIVE_TRADES: 60000,      // 1 minute in background
    AI_SIGNALS: 120000,      // 2 minutes in background
    DASHBOARD_SUMMARY: 90000, // 1.5 minutes in background
  },
  
  // When user is idle (very slow updates)
  IDLE: {
    LIVE_TRADES: 300000,     // 5 minutes when idle
    AI_SIGNALS: 300000,      // 5 minutes when idle
    DASHBOARD_SUMMARY: 300000, // 5 minutes when idle
  }
};

/**
 * 🎛️ PERFORMANCE MODES
 * Different refresh strategies based on system performance
 */
export const PERFORMANCE_MODES = {
  HIGH_PERFORMANCE: {
    // For powerful systems - more frequent updates
    multiplier: 0.7,  // 30% faster than normal
    maxConcurrent: 5, // Allow more concurrent requests
  },
  
  BALANCED: {
    // Default mode - balanced performance
    multiplier: 1.0,  // Normal intervals
    maxConcurrent: 3, // Standard concurrent requests
  },
  
  POWER_SAVER: {
    // For slower systems or mobile - less frequent updates
    multiplier: 2.0,  // 2x slower than normal
    maxConcurrent: 2, // Fewer concurrent requests
  }
};

/**
 * 🔄 SMART REFRESH LOGIC
 * Determines optimal refresh interval based on multiple factors
 */
export const getOptimalInterval = (
  baseInterval,
  userActivity = 'active',
  performanceMode = 'balanced',
  dataImportance = 'medium'
) => {
  let interval = baseInterval;
  
  // Apply user activity multiplier
  switch (userActivity) {
    case 'active':
      interval *= 1.0;
      break;
    case 'background':
      interval *= 1.5;
      break;
    case 'idle':
      interval *= 3.0;
      break;
  }
  
  // Apply performance mode multiplier
  interval *= PERFORMANCE_MODES[performanceMode.toUpperCase()]?.multiplier || 1.0;
  
  // Apply data importance multiplier
  switch (dataImportance) {
    case 'critical':
      interval *= 0.8;  // 20% faster for critical data
      break;
    case 'high':
      interval *= 0.9;  // 10% faster for high importance
      break;
    case 'medium':
      interval *= 1.0;  // Normal for medium importance
      break;
    case 'low':
      interval *= 1.5;  // 50% slower for low importance
      break;
  }
  
  // Ensure minimum and maximum bounds
  const MIN_INTERVAL = 10000;  // 10 seconds minimum
  const MAX_INTERVAL = 600000; // 10 minutes maximum
  
  return Math.max(MIN_INTERVAL, Math.min(MAX_INTERVAL, interval));
};

/**
 * 📊 USAGE ANALYTICS
 * Track API call patterns for optimization
 */
export const API_USAGE_TRACKER = {
  calls: {},
  
  recordCall: (endpoint) => {
    const now = Date.now();
    if (!API_USAGE_TRACKER.calls[endpoint]) {
      API_USAGE_TRACKER.calls[endpoint] = [];
    }
    API_USAGE_TRACKER.calls[endpoint].push(now);
    
    // Keep only last 100 calls per endpoint
    if (API_USAGE_TRACKER.calls[endpoint].length > 100) {
      API_USAGE_TRACKER.calls[endpoint] = API_USAGE_TRACKER.calls[endpoint].slice(-100);
    }
  },
  
  getCallFrequency: (endpoint, timeWindow = 300000) => {
    const now = Date.now();
    const calls = API_USAGE_TRACKER.calls[endpoint] || [];
    const recentCalls = calls.filter(time => now - time < timeWindow);
    return recentCalls.length;
  },
  
  getRecommendedInterval: (endpoint) => {
    const frequency = API_USAGE_TRACKER.getCallFrequency(endpoint);
    
    // If too many calls, increase interval
    if (frequency > 20) return 180000; // 3 minutes
    if (frequency > 10) return 120000; // 2 minutes
    if (frequency > 5) return 60000;   // 1 minute
    
    return REFRESH_INTERVALS[endpoint] || 60000; // Default 1 minute
  }
};

export default REFRESH_INTERVALS;
