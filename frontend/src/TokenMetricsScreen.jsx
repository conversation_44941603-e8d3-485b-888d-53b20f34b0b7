import React, { useState, useEffect, useCallback, useMemo } from 'react';
import axiosInstance from './axiosInstance';
import { useRealTimeData, LastUpdatedIndicator } from './hooks/useRealTimeData.jsx';
import ErrorBoundary from './components/ErrorBoundary.jsx';

const TokenMetricsScreen = () => {
  const [selectedToken, setSelectedToken] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterGrade, setFilterGrade] = useState('all');
  const [sortBy, setSortBy] = useState('grade');
  const [activeTab, setActiveTab] = useState('tokens'); // 'tokens' or 'moonshots'

  // Real-time TokenMetrics data with AI-optimized intervals
  const {
    data: tokenMetricsData,
    loading,
    error,
    lastUpdated,
    isRefreshing,
    refresh
  } = useRealTimeData('/api/tokenmetrics/top-tokens', {
    refreshInterval: 180000, // 3 minutes for live price accuracy
    dataImportance: 'high',
    transform: (data) => {
      console.log('🔍 TokenMetrics data received:', data);
      return data?.tokens || data || [];
    },
    onError: (err) => console.error('❌ TokenMetrics fetch failed:', err)
  });

  // Real-time Moonshots data with advanced features
  const {
    data: moonshotsData,
    loading: moonshotsLoading,
    error: moonshotsError,
    lastUpdated: moonshotsLastUpdated,
    isRefreshing: moonshotsRefreshing,
    refresh: refreshMoonshots
  } = useRealTimeData('/api/tokenmetrics/moonshots', {
    refreshInterval: 240000, // 4 minutes for moonshots with advanced analysis
    dataImportance: 'high',
    enabled: activeTab === 'moonshots', // Only fetch when moonshots tab is active
    transform: (data) => {
      console.log('🚀 Moonshots data received:', data);
      return data?.moonshots || data || [];
    },
    onError: (err) => console.error('❌ Moonshots fetch failed:', err)
  });



  // Token selection handler
  const handleTokenClick = useCallback((token) => {
    setSelectedToken(token);
  }, []);

  // Advanced features status checker
  const getAdvancedFeaturesStatus = useCallback((data) => {
    if (!data || !Array.isArray(data)) return { available: false, features: [] };

    const sample = data[0];
    if (!sample) return { available: false, features: [] };

    const features = [];
    if (sample.tokenmetrics_available) features.push('TokenMetrics API');
    if (sample.advanced_features_used) features.push('Advanced Analysis');
    if (sample.price_source) features.push(`Price Source: ${sample.price_source}`);
    if (sample.validation) features.push('Price Validation');

    return {
      available: sample.tokenmetrics_available || false,
      features,
      source: sample.source || 'unknown'
    };
  }, []);

  // Get current advanced features status
  const advancedStatus = getAdvancedFeaturesStatus(tokenMetricsData);
  const moonshotsStatus = getAdvancedFeaturesStatus(moonshotsData);

  // Moonshot category color mapping
  const getMoonshotCategoryColor = useCallback((category) => {
    switch (category) {
      case 'High Potential': return 'text-green-400 bg-green-900';
      case 'Moderate Potential': return 'text-blue-400 bg-blue-900';
      case 'Speculative': return 'text-yellow-400 bg-yellow-900';
      default: return 'text-gray-400 bg-gray-700';
    }
  }, []);

  // Risk level color mapping
  const getRiskLevelColor = useCallback((riskLevel) => {
    switch (riskLevel) {
      case 'Low': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'High': return 'text-red-400';
      default: return 'text-gray-400';
    }
  }, []);

  // Grade color mapping
  const getGradeColor = useCallback((grade) => {
    switch (grade) {
      case 'A+': case 'A': case 'A-': return 'text-green-400 bg-green-900';
      case 'B+': case 'B': case 'B-': return 'text-blue-400 bg-blue-900';
      case 'C+': case 'C': case 'C-': return 'text-yellow-400 bg-yellow-900';
      case 'D+': case 'D': return 'text-orange-400 bg-orange-900';
      case 'F': return 'text-red-400 bg-red-900';
      default: return 'text-gray-400 bg-gray-700';
    }
  }, []);

  // Recommendation color mapping
  const getRecommendationColor = useCallback((recommendation) => {
    const rec = recommendation?.toUpperCase();
    switch (rec) {
      case 'BUY':
      case 'STRONG BUY':
        return 'text-green-400 bg-green-900';
      case 'SELL':
      case 'STRONG SELL':
        return 'text-red-400 bg-red-900';
      case 'HOLD':
      case 'NEUTRAL':
        return 'text-yellow-400 bg-yellow-900';
      default:
        return 'text-gray-400 bg-gray-700';
    }
  }, []);

  // Memoized filtered and sorted tokens for better performance
  const filteredTokens = useMemo(() => {
    if (!Array.isArray(tokenMetricsData)) return [];

    let filtered = tokenMetricsData.filter(token => {
      // Search filter
      if (searchTerm && !token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !token.name?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Grade filter
      if (filterGrade !== 'all') {
        if (filterGrade === 'A' && !['A+', 'A', 'A-'].includes(token.tokenmetrics_grade)) return false;
        if (filterGrade === 'B' && !['B+', 'B', 'B-'].includes(token.tokenmetrics_grade)) return false;
        if (filterGrade === 'C' && !['C+', 'C', 'C-'].includes(token.tokenmetrics_grade)) return false;
        if (filterGrade === 'D+' && !['D+', 'D', 'F'].includes(token.tokenmetrics_grade)) return false;
        if (filterGrade === 'available' && !token.tokenmetrics_available) return false;
      }

      return true;
    });

    // Sort tokens
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'grade':
          const gradeOrder = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F', 'N/A'];
          return gradeOrder.indexOf(a.tokenmetrics_grade) - gradeOrder.indexOf(b.tokenmetrics_grade);
        case 'confidence':
          return (b.confidence || 0) - (a.confidence || 0);
        case 'score':
          return (b.tokenmetrics_score || 0) - (a.tokenmetrics_score || 0);
        case 'price_change':
          return (b.price_change_24h || 0) - (a.price_change_24h || 0);
        case 'volume':
          return (b.volume_24h || 0) - (a.volume_24h || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tokenMetricsData, searchTerm, filterGrade, sortBy]);

  // Render moonshot card
  const renderMoonshotCard = useCallback((moonshot, index) => (
    <div
      key={`${moonshot.symbol}-${index}`}
      className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 hover:border-yellow-500 cursor-pointer transition-all transform hover:scale-105"
      onClick={() => handleTokenClick(moonshot)}
    >
      {/* Moonshot Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-bold text-yellow-400">{moonshot.symbol}</h3>
          <div className="text-sm text-gray-400">{moonshot.name}</div>
        </div>
        <div className="flex flex-col items-end space-y-1">
          <span className={`px-3 py-1 rounded-full text-xs font-bold ${getMoonshotCategoryColor(moonshot.moonshot_category)}`}>
            🌙 {moonshot.moonshot_category}
          </span>
          <span className={`text-xs font-medium ${getRiskLevelColor(moonshot.risk_level)}`}>
            Risk: {moonshot.risk_level}
          </span>
        </div>
      </div>

      {/* Moonshot Score */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-400">Moonshot Score</span>
          <span className="text-yellow-400 font-bold text-lg">{moonshot.moonshot_score?.toFixed(1)}/100</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${moonshot.moonshot_score || 0}%` }}
          ></div>
        </div>
      </div>

      {/* Price and Potential Return */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-gray-400">Current Price</div>
          <div className="text-lg font-semibold text-white">
            ${moonshot.current_price?.toFixed(6) || 'N/A'}
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-400">Potential Return</div>
          <div className="text-lg font-semibold text-green-400">
            +{moonshot.potential_return?.toFixed(1) || 0}%
          </div>
        </div>
      </div>

      {/* Trading Signal */}
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-400">Trading Signal</span>
          <span className={`px-2 py-1 rounded text-xs font-bold ${moonshot.signal_type === 'BUY' ? 'bg-green-900 text-green-400' :
            moonshot.signal_type === 'SELL' ? 'bg-red-900 text-red-400' :
              'bg-gray-700 text-gray-400'
            }`}>
            {moonshot.signal_type} ({moonshot.signal_strength?.toFixed(0) || 0}%)
          </span>
        </div>
      </div>

      {/* TokenMetrics Grades */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-gray-400">TM Grade</div>
          <span className={`px-2 py-1 rounded text-xs font-bold ${getGradeColor(moonshot.tm_grade)}`}>
            {moonshot.tm_grade}
          </span>
        </div>
        <div>
          <div className="text-sm text-gray-400">AI Confidence</div>
          <div className="text-purple-400 font-semibold">{moonshot.ai_confidence?.toFixed(0) || 0}%</div>
        </div>
      </div>

      {/* Entry and Target Prices */}
      {moonshot.entry_price && moonshot.target_price && (
        <div className="grid grid-cols-2 gap-4 mb-4 text-xs">
          <div>
            <div className="text-gray-500">Entry</div>
            <div className="text-blue-400">${moonshot.entry_price?.toFixed(6)}</div>
          </div>
          <div>
            <div className="text-gray-500">Target</div>
            <div className="text-green-400">${moonshot.target_price?.toFixed(6)}</div>
          </div>
        </div>
      )}

      {/* AI Summary */}
      {moonshot.ai_report_summary && (
        <div className="mt-4 p-3 bg-gray-700 rounded-lg">
          <div className="text-xs text-gray-400 mb-1">AI Analysis</div>
          <div className="text-sm text-gray-300 line-clamp-2">
            {moonshot.ai_report_summary}
          </div>
        </div>
      )}
    </div>
  ), [handleTokenClick, getMoonshotCategoryColor, getRiskLevelColor, getGradeColor]);

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-6xl mb-4">❌</div>
          <div className="text-white text-xl mb-4">TokenMetrics Error</div>
          <div className="text-gray-400 mb-6">{error}</div>
          <button
            onClick={refresh}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            🔄 Retry
          </button>
        </div>
      </div>
    );
  }

  // Show loading state only if all critical data is loading
  if (loading && !tokenMetricsData) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-white text-lg">Loading TokenMetrics data...</div>
          <div className="text-gray-400 text-sm mt-2">This may take a moment...</div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="p-6 bg-gray-900 text-white min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-500 bg-clip-text text-transparent">
            📊 TokenMetrics Advanced Analysis
          </h1>
          <div className="text-sm text-gray-400">
            {(activeTab === 'tokens' ? isRefreshing : moonshotsRefreshing) && <span className="text-yellow-400">Refreshing...</span>}
            {(activeTab === 'tokens' ? lastUpdated : moonshotsLastUpdated) &&
              <span>Updated: {new Date(activeTab === 'tokens' ? lastUpdated : moonshotsLastUpdated).toLocaleTimeString()}</span>
            }
          </div>
        </div>

        {/* Advanced Features Status */}
        <div className="mb-6 p-4 bg-gray-800 rounded-lg border border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-purple-400">💎 Advanced Membership Status</h3>
            <div className="flex items-center space-x-2">
              {advancedStatus.available ? (
                <span className="px-3 py-1 bg-green-900 text-green-400 rounded-full text-sm font-bold">
                  ✅ ACTIVE
                </span>
              ) : (
                <span className="px-3 py-1 bg-yellow-900 text-yellow-400 rounded-full text-sm font-bold">
                  📊 FALLBACK
                </span>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gray-700 p-3 rounded">
              <div className="text-sm text-gray-400">Data Source</div>
              <div className="text-white font-semibold">
                {advancedStatus.source === 'tokenmetrics_advanced' ? '💎 TokenMetrics API' :
                  advancedStatus.source === 'fallback_live_data' ? '📊 Live KuCoin' :
                    '🔄 Mixed Sources'}
              </div>
            </div>

            <div className="bg-gray-700 p-3 rounded">
              <div className="text-sm text-gray-400">Price Accuracy</div>
              <div className="text-white font-semibold">
                {advancedStatus.available ? '🎯 Multi-Source Validated' : '📈 Live Market Data'}
              </div>
            </div>

            <div className="bg-gray-700 p-3 rounded">
              <div className="text-sm text-gray-400">Features Active</div>
              <div className="text-white font-semibold">
                {advancedStatus.features.length} / 11 Premium
              </div>
            </div>

            <div className="bg-gray-700 p-3 rounded">
              <div className="text-sm text-gray-400">Update Frequency</div>
              <div className="text-white font-semibold">
                {advancedStatus.available ? '⚡ 3 min' : '🔄 3 min (Live)'}
              </div>
            </div>
          </div>

          {advancedStatus.available && (
            <div className="mt-3 p-3 bg-green-900/20 border border-green-700 rounded">
              <div className="text-sm text-green-400 font-semibold mb-1">
                🚀 Advanced Features Enabled:
              </div>
              <div className="text-xs text-green-300">
                AI Reports • Trading Signals • Sentiment Analysis • Quantmetrics •
                Scenario Analysis • Resistance/Support • Correlation • Investor Grades •
                Market Metrics • Price Validation • Moonshot Analysis
              </div>
            </div>
          )}

          {!advancedStatus.available && (
            <div className="mt-3 p-3 bg-blue-900/20 border border-blue-700 rounded">
              <div className="text-sm text-blue-400 font-semibold mb-1">
                📊 Using Live Market Data:
              </div>
              <div className="text-xs text-blue-300">
                Real-time prices from KuCoin • Live volume data • 24h change tracking •
                Market cap calculations • Liquidity filtering • Technical indicators
              </div>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="bg-gray-800 rounded-lg mb-6">
          <div className="flex border-b border-gray-700">
            <button
              onClick={() => setActiveTab('tokens')}
              className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${activeTab === 'tokens'
                ? 'bg-purple-600 text-white border-b-2 border-purple-400'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
            >
              📊 All Tokens ({tokenMetricsData?.length || 0})
            </button>
            <button
              onClick={() => setActiveTab('moonshots')}
              className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${activeTab === 'moonshots'
                ? 'bg-purple-600 text-white border-b-2 border-purple-400'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
            >
              🌙 Moonshots ({moonshotsData?.length || 0})
            </button>
          </div>
        </div>

        {/* Enhanced Status Indicator */}
        <div className="bg-gray-800 p-4 rounded-lg mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {activeTab === 'tokens' ? (
                <>
                  <div className="text-lg font-semibold">
                    📈 {filteredTokens.length} KuCoin Tokens with TokenMetrics Analysis
                  </div>
                  <div className="text-sm text-gray-400">
                    {tokenMetricsData?.filter(t => t.tokenmetrics_available).length || 0} with TokenMetrics data
                  </div>
                </>
              ) : (
                <>
                  <div className="text-lg font-semibold">
                    🌙 {moonshotsData?.length || 0} High-Potential Moonshot Tokens
                  </div>
                  <div className="text-sm text-gray-400">
                    AI-analyzed for breakout potential
                  </div>
                </>
              )}
            </div>
            <button
              onClick={activeTab === 'tokens' ? refresh : refreshMoonshots}
              disabled={activeTab === 'tokens' ? isRefreshing : moonshotsRefreshing}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <span>{(activeTab === 'tokens' ? isRefreshing : moonshotsRefreshing) ? '⏳' : '🔄'}</span>
              <span>{(activeTab === 'tokens' ? isRefreshing : moonshotsRefreshing) ? 'Refreshing...' : 'Refresh Data'}</span>
            </button>
          </div>
        </div>

        {(activeTab === 'tokens' ? error : moonshotsError) && (
          <div className="bg-red-900 border border-red-600 text-red-200 px-4 py-3 rounded-lg mb-6">
            <div className="flex items-center space-x-2">
              <span>⚠️</span>
              <span>{activeTab === 'tokens' ? error : moonshotsError}</span>
            </div>
          </div>
        )}

        {/* Enhanced Filters and Search - Only show for tokens tab */}
        {activeTab === 'tokens' && (
          <div className="bg-gray-800 p-4 rounded-lg mb-6">
            <h3 className="text-lg font-semibold mb-4 text-purple-400">🔧 Advanced Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Search Token</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by symbol or name..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Filter by Grade</label>
                <select
                  value={filterGrade}
                  onChange={(e) => setFilterGrade(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 text-white"
                >
                  <option value="all">All Grades</option>
                  <option value="A">Grade A (A+, A, A-)</option>
                  <option value="B">Grade B (B+, B, B-)</option>
                  <option value="C">Grade C (C+, C, C-)</option>
                  <option value="D+">Grade D+ & Below</option>
                  <option value="available">TokenMetrics Available</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Sort By</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 text-white"
                >
                  <option value="grade">TokenMetrics Grade</option>
                  <option value="confidence">Confidence Score</option>
                  <option value="score">TokenMetrics Score</option>
                  <option value="price_change">24h Price Change</option>
                  <option value="volume">24h Volume</option>
                </select>
              </div>
              <div className="flex items-end">
                <div className="text-sm text-gray-400">
                  Showing {filteredTokens.length} of {tokenMetricsData?.length || 0} tokens
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Moonshots Info Panel - Only show for moonshots tab */}
        {activeTab === 'moonshots' && (
          <div className="bg-gray-800 p-4 rounded-lg mb-6">
            <h3 className="text-lg font-semibold mb-4 text-yellow-400">🌙 Moonshots Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{moonshotsData?.filter(m => m.moonshot_category === 'High Potential').length || 0}</div>
                <div className="text-sm text-gray-400">High Potential</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{moonshotsData?.filter(m => m.moonshot_category === 'Moderate Potential').length || 0}</div>
                <div className="text-sm text-gray-400">Moderate Potential</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{moonshotsData?.filter(m => m.moonshot_category === 'Speculative').length || 0}</div>
                <div className="text-sm text-gray-400">Speculative</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-400 text-center">
              Moonshots are analyzed using AI reports, trading signals, and TokenMetrics grades for breakout potential
            </div>
          </div>
        )}

        {/* Enhanced Token/Moonshots Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {activeTab === 'tokens' ? (
            // Tokens Tab Content
            filteredTokens.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <div className="text-6xl mb-4">📊</div>
                <div className="text-xl text-gray-400 mb-2">
                  {!tokenMetricsData || tokenMetricsData.length === 0 ?
                    'No TokenMetrics data available' :
                    'No tokens match your filters'
                  }
                </div>
                <div className="text-sm text-gray-500">
                  {!tokenMetricsData || tokenMetricsData.length === 0 ?
                    'Please check API configuration or try refreshing' :
                    'Try adjusting your search or filter criteria'
                  }
                </div>
              </div>
            ) : (
              filteredTokens.map((token, index) => (
                <div
                  key={`${token.symbol}-${index}`}
                  className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 hover:border-purple-500 cursor-pointer transition-all transform hover:scale-105"
                  onClick={() => handleTokenClick(token)}
                >
                  {/* Token Header */}
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-yellow-400">{token.symbol}</h3>
                      <div className="text-sm text-gray-400">{token.name}</div>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-3 py-1 rounded-full text-xs font-bold ${getGradeColor(token.tokenmetrics_grade)}`}>
                        {token.tokenmetrics_grade}
                      </span>
                      {token.tokenmetrics_available && (
                        <span className="text-xs text-green-400">✓ TokenMetrics</span>
                      )}
                    </div>
                  </div>

                  {/* Price and Market Data */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="text-sm text-gray-400">Price</div>
                      <div className="text-lg font-semibold text-white">
                        ${token.price?.toFixed(6) || 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">24h Change</div>
                      <div className={`text-lg font-semibold ${(token.price_change_24h || 0) >= 0 ? 'text-green-400' : 'text-red-400'
                        }`}>
                        {(token.price_change_24h || 0).toFixed(2)}%
                      </div>
                    </div>
                  </div>

                  {/* TokenMetrics Analysis */}
                  {token.tokenmetrics_available ? (
                    <div className="space-y-3 mb-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-400">AI Score</span>
                        <span className="text-purple-400 font-semibold">{token.tokenmetrics_score}/100</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-400">Confidence</span>
                        <span className="text-blue-400 font-semibold">{(token.confidence * 100).toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-400">Recommendation</span>
                        <span className={`px-2 py-1 rounded text-xs font-bold ${getRecommendationColor(token.ai_recommendation)}`}>
                          {token.ai_recommendation}
                        </span>
                      </div>
                      {token.price_target && (
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-400">Price Target</span>
                          <span className="text-green-400 font-semibold">${token.price_target.toFixed(6)}</span>
                        </div>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-400">Risk Level</span>
                        <span className={`text-xs font-semibold ${token.risk_level === 'LOW' ? 'text-green-400' :
                          token.risk_level === 'MEDIUM' ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                          {token.risk_level}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <div className="text-sm">TokenMetrics data not available</div>
                      <div className="text-xs">KuCoin data only</div>
                    </div>
                  )}

                  {/* Action Button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTokenClick(token);
                    }}
                    className="w-full py-2 px-4 rounded-lg font-semibold bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 transition-all"
                  >
                    📊 View Analysis
                  </button>
                </div>
              ))
            )
          ) : (
            // Moonshots Tab Content
            moonshotsLoading ? (
              <div className="col-span-full text-center py-12">
                <div className="text-6xl mb-4">🌙</div>
                <div className="text-xl text-gray-400 mb-2">Analyzing moonshot potential...</div>
                <div className="text-sm text-gray-500">This may take a moment</div>
              </div>
            ) : !moonshotsData || moonshotsData.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <div className="text-6xl mb-4">🌙</div>
                <div className="text-xl text-gray-400 mb-2">No moonshot tokens found</div>
                <div className="text-sm text-gray-500">
                  {moonshotsError ? 'Error loading moonshots data' : 'Try refreshing to analyze current market conditions'}
                </div>
              </div>
            ) : (
              moonshotsData.map((moonshot, index) => renderMoonshotCard(moonshot, index))
            )
          )}
        </div>

        {/* Token Details Modal */}
        {selectedToken && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-2xl font-bold text-yellow-400">{selectedToken.symbol}</h3>
                <button
                  onClick={() => setSelectedToken(null)}
                  className="text-gray-400 hover:text-white text-xl"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-lg font-semibold mb-2">AI Analysis</h4>
                  {selectedToken.ai_analysis ? (
                    <div className="space-y-2">
                      <p><strong>Score:</strong> {selectedToken.ai_analysis.score}/100</p>
                      <p><strong>Recommendation:</strong>
                        <span className={getRecommendationColor(selectedToken.ai_analysis.recommendation)}>
                          {selectedToken.ai_analysis.recommendation}
                        </span>
                      </p>
                      <p><strong>Confidence:</strong> {(selectedToken.confidence * 100).toFixed(1)}%</p>
                    </div>
                  ) : (
                    <p className="text-gray-400">No AI analysis available</p>
                  )}
                </div>

                <div>
                  <h4 className="text-lg font-semibold mb-2">Technical Indicators</h4>
                  {selectedToken.technical_indicators ? (
                    <div className="space-y-2">
                      <p><strong>RSI:</strong> {selectedToken.technical_indicators.rsi}</p>
                      <p><strong>MACD:</strong> {selectedToken.technical_indicators.macd}</p>
                      <p><strong>Moving Avg:</strong> {selectedToken.technical_indicators.moving_average}</p>
                    </div>
                  ) : (
                    <p className="text-gray-400">No technical indicators available</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
};

export default TokenMetricsScreen;
