import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import './index.css';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { AuthProvider } from './contexts/AuthContext';

// ========= MAIN.JSX LOADED =========
console.info(
  "%c[Main.jsx] bundle loaded • React entrypoint",
  "background: #1e90ff; color: #fff; padding:2px 6px; border-radius:4px;"
);
// ===================================


const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
// --- TEMP DEBUG ---
console.log("CLIENT_ID:", clientId);
console.log("PAGE ORIGIN:", window.location.origin);
// ------------------

if (!clientId) {
  console.error("Missing VITE_GOOGLE_CLIENT_ID in environment variables.");
}

const root = ReactDOM.createRoot(document.getElementById('root'));

// Debug cookie presence on app load
console.log("Cookies on app load:", document.cookie);

const RootApp = () => (
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        {clientId ? (
          <GoogleOAuthProvider clientId={clientId}>
            <App />
          </GoogleOAuthProvider>
        ) : (
          <App />
        )}
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>
);

root.render(<RootApp />);
