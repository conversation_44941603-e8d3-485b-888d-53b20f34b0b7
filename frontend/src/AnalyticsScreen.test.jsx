import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import AnalyticsScreen from './AnalyticsScreen';

vi.mock('./axiosInstance');
import axiosInstance from './axiosInstance';

describe('AnalyticsScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    axiosInstance.get.mockResolvedValue({
      data: {
        trade_counts: { total: 200 },
        win_loss: { win_percent: 75.0, loss_percent: 25.0 },
        profit_loss: { largest_win: 1500.50, largest_loss: -200.25 },
        portfolio: { current_value: 10000.00, change: 150.75 },
        ai_decisions: { buy: 50, sell: 30, hold: 120 },
        strategy_metrics: {
          'momentum': { total_trades: 100, win_rate: 80.0, avg_pnl: 25.50 }
        },
        token_metrics: {
          'BTC': { trades: 50, win_rate: 85.0, avg_return: 5.2 }
        },
        ai_reasoning: {
          'openai': { avg_length: 150.5, clarity_score: 8.5, keyword_coverage: 85.0 }
        }
      }
    });
  });

  it('renders analytics screen title', async () => {
    render(
      <MemoryRouter>
        <AnalyticsScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/analytics/i)).toBeInTheDocument();
    });
  });

  it('displays performance metrics', async () => {
    render(
      <MemoryRouter>
        <AnalyticsScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /total trades/i })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: /win rate/i })).toBeInTheDocument();
    });
  });

  it('fetches analytics data on mount', async () => {
    render(
      <MemoryRouter>
        <AnalyticsScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(axiosInstance.get).toHaveBeenCalledWith('/analytics');
    });
  });

  it('handles loading state', () => {
    render(
      <MemoryRouter>
        <AnalyticsScreen />
      </MemoryRouter>
    );

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('handles error state', async () => {
    axiosInstance.get.mockRejectedValueOnce(new Error('Failed to fetch'));

    render(
      <MemoryRouter>
        <AnalyticsScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/unable to load analytics data/i)).toBeInTheDocument();
    });
  });
});