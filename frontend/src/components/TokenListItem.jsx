// src/components/TokenListItem.js
import React from 'react';

/**
 * Renders a single token row with optional spike badge.
 * Props:
 *   - token: { symbol, price, volume_ratio, is_spike }
 */
export default function TokenListItem({ token }) {
  return (
    <div className="flex items-center justify-between px-4 py-2 border-b hover:bg-gray-50">
      <span className="font-medium">{token.symbol}</span>
      <div className="flex items-center space-x-3">
        <span>${Number(token.price).toFixed(4)}</span>
        {token.is_spike && (
          <span title="Volume Spike!" className="text-red-500 animate-pulse">
            🔥
          </span>
        )}
      </div>
    </div>
  );
}