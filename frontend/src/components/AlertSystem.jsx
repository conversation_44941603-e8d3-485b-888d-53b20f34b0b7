import React, { useState, useEffect } from 'react';

const AlertSystem = () => {
  const [alerts, setAlerts] = useState([]);
  const [newAlert, setNewAlert] = useState({
    symbol: '',
    type: 'price',
    condition: 'above',
    value: '',
    enabled: true
  });
  const [notifications, setNotifications] = useState([]);

  // Mock alerts data
  const mockAlerts = [
    { id: 1, symbol: 'BTC', type: 'price', condition: 'above', value: 50000, enabled: true, triggered: false },
    { id: 2, symbol: 'ETH', type: 'price', condition: 'below', value: 3000, enabled: true, triggered: false },
    { id: 3, symbol: 'ADA', type: 'volume', condition: 'above', value: 1000000, enabled: false, triggered: false },
    { id: 4, symbol: 'SOL', type: 'change', condition: 'above', value: 5, enabled: true, triggered: true },
  ];

  useEffect(() => {
    setAlerts(mockAlerts);
    
    // Simulate real-time price updates and alert checking
    const interval = setInterval(() => {
      checkAlerts();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const checkAlerts = () => {
    // Mock price data
    const currentPrices = {
      BTC: 48500 + Math.random() * 3000,
      ETH: 2900 + Math.random() * 200,
      ADA: 0.45 + Math.random() * 0.1,
      SOL: 95 + Math.random() * 10
    };

    setAlerts(prevAlerts => 
      prevAlerts.map(alert => {
        if (!alert.enabled || alert.triggered) return alert;

        const currentPrice = currentPrices[alert.symbol];
        let shouldTrigger = false;

        if (alert.type === 'price') {
          if (alert.condition === 'above' && currentPrice > alert.value) {
            shouldTrigger = true;
          } else if (alert.condition === 'below' && currentPrice < alert.value) {
            shouldTrigger = true;
          }
        }

        if (shouldTrigger) {
          triggerNotification(alert, currentPrice);
          return { ...alert, triggered: true };
        }

        return alert;
      })
    );
  };

  const triggerNotification = (alert, currentValue) => {
    const notification = {
      id: Date.now(),
      title: `${alert.symbol} Alert Triggered`,
      message: `${alert.symbol} is ${alert.condition} ${alert.value} (Current: ${currentValue.toFixed(2)})`,
      type: 'alert',
      timestamp: new Date().toLocaleTimeString()
    };

    setNotifications(prev => [notification, ...prev.slice(0, 9)]);

    // Browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico'
      });
    }
  };

  const addAlert = () => {
    if (!newAlert.symbol || !newAlert.value) return;

    const alert = {
      id: Date.now(),
      ...newAlert,
      value: parseFloat(newAlert.value),
      triggered: false
    };

    setAlerts(prev => [...prev, alert]);
    setNewAlert({ symbol: '', type: 'price', condition: 'above', value: '', enabled: true });
  };

  const toggleAlert = (id) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === id ? { ...alert, enabled: !alert.enabled, triggered: false } : alert
      )
    );
  };

  const deleteAlert = (id) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id));
  };

  const requestNotificationPermission = () => {
    if ('Notification' in window) {
      Notification.requestPermission();
    }
  };

  return (
    <div className="space-y-6">
      {/* Notification Permission */}
      {Notification.permission === 'default' && (
        <div className="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-yellow-200 font-medium">Enable Notifications</h3>
              <p className="text-yellow-300 text-sm">Get real-time alerts when your conditions are met</p>
            </div>
            <button
              onClick={requestNotificationPermission}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded"
            >
              Enable
            </button>
          </div>
        </div>
      )}

      {/* Recent Notifications */}
      {notifications.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-white mb-3">Recent Alerts</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {notifications.map(notification => (
              <div key={notification.id} className="bg-gray-700 rounded p-3 border-l-4 border-red-500">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-white font-medium">{notification.title}</h4>
                    <p className="text-gray-300 text-sm">{notification.message}</p>
                  </div>
                  <span className="text-gray-400 text-xs">{notification.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add New Alert */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-4">Create New Alert</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <input
            type="text"
            placeholder="Symbol (e.g., BTC)"
            value={newAlert.symbol}
            onChange={(e) => setNewAlert({...newAlert, symbol: e.target.value.toUpperCase()})}
            className="bg-gray-700 text-white px-3 py-2 rounded"
          />
          <select
            value={newAlert.type}
            onChange={(e) => setNewAlert({...newAlert, type: e.target.value})}
            className="bg-gray-700 text-white px-3 py-2 rounded"
          >
            <option value="price">Price</option>
            <option value="volume">Volume</option>
            <option value="change">% Change</option>
          </select>
          <select
            value={newAlert.condition}
            onChange={(e) => setNewAlert({...newAlert, condition: e.target.value})}
            className="bg-gray-700 text-white px-3 py-2 rounded"
          >
            <option value="above">Above</option>
            <option value="below">Below</option>
          </select>
          <input
            type="number"
            placeholder="Value"
            value={newAlert.value}
            onChange={(e) => setNewAlert({...newAlert, value: e.target.value})}
            className="bg-gray-700 text-white px-3 py-2 rounded"
          />
          <button
            onClick={addAlert}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Add Alert
          </button>
        </div>
      </div>

      {/* Active Alerts */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-4">Active Alerts</h3>
        <div className="space-y-3">
          {alerts.map(alert => (
            <div key={alert.id} className={`bg-gray-700 rounded-lg p-4 border-l-4 ${
              alert.triggered ? 'border-red-500' : alert.enabled ? 'border-green-500' : 'border-gray-500'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-white font-medium">{alert.symbol}</span>
                    <span className="text-gray-300">{alert.type}</span>
                    <span className="text-gray-300">{alert.condition}</span>
                    <span className="text-white">{alert.value}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {alert.triggered && (
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs">
                        TRIGGERED
                      </span>
                    )}
                    <span className={`px-2 py-1 rounded text-xs ${
                      alert.enabled ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
                    }`}>
                      {alert.enabled ? 'ACTIVE' : 'DISABLED'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => toggleAlert(alert.id)}
                    className={`px-3 py-1 rounded text-sm ${
                      alert.enabled 
                        ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    {alert.enabled ? 'Disable' : 'Enable'}
                  </button>
                  <button
                    onClick={() => deleteAlert(alert.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
          {alerts.length === 0 && (
            <div className="text-center text-gray-400 py-8">
              No alerts configured. Create your first alert above.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AlertSystem;
