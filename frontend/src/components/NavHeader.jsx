import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const NavHeader = () => {
  const location = useLocation();

  const isActive = (path) => location.pathname === path;

  const navItems = [
    { path: '/dashboard', label: 'Dashboard' },
    { path: '/logic', label: 'AI Logic' },
    { path: '/analytics', label: 'Analytics' },
    { path: '/discover', label: 'Discover' },
    { path: '/live-trades', label: 'Live Trades' },
    { path: '/manual-trading', label: '🎯 Manual Trading' },
    { path: '/microbot', label: 'Micro Bot' },
    { path: '/arbitrage', label: 'Arbitrage' },
    { path: '/news', label: 'News' },
    { path: '/tokenmetrics', label: 'TokenMetrics' },
    { path: '/cost-monitoring', label: 'Cost Monitor' },
  ];

  return (
    <nav className="bg-gray-900 text-white p-4 shadow-lg">
      <div className="container mx-auto flex flex-col sm:flex-row items-center justify-between">
        <div className="flex items-center mb-4 sm:mb-0">
          <Link to="/dashboard" className="text-xl font-bold">
            Alpha Predator Bot
          </Link>
        </div>

        <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
          {navItems.map(({ path, label }) => (
            <Link
              key={path}
              to={path}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors
                ${isActive(path)
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
            >
              {label}
            </Link>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default NavHeader;
