import React from 'react';

const SimpleChart = ({ symbol, data, timeframe = '1h' }) => {
  // Mock chart data
  const chartData = data || [
    { time: '00:00', price: 42000, volume: 1250000 },
    { time: '01:00', price: 43800, volume: 980000 },
    { time: '02:00', price: 44200, volume: 1100000 },
    { time: '03:00', price: 44800, volume: 1350000 },
    { time: '04:00', price: 44600, volume: 890000 },
    { time: '05:00', price: 45200, volume: 1200000 },
    { time: '06:00', price: 45800, volume: 1450000 },
    { time: '07:00', price: 45600, volume: 1050000 },
    { time: '08:00', price: 46200, volume: 1300000 },
    { time: '09:00', price: 46800, volume: 1600000 },
  ];

  const maxPrice = Math.max(...chartData.map(d => d.price));
  const minPrice = Math.min(...chartData.map(d => d.price));
  const maxVolume = Math.max(...chartData.map(d => d.volume));

  return (
    <div className="bg-gray-900 p-4 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-white">{symbol} Chart</h3>
        <div className="flex gap-2">
          <select className="bg-gray-800 text-white px-2 py-1 rounded text-sm">
            <option>1m</option>
            <option>5m</option>
            <option>15m</option>
            <option selected>1h</option>
            <option>4h</option>
            <option>1d</option>
          </select>
          <select className="bg-gray-800 text-white px-2 py-1 rounded text-sm">
            <option selected>Candlestick</option>
            <option>Line</option>
            <option>Area</option>
          </select>
        </div>
      </div>

      {/* Price Chart */}
      <div className="relative h-64 bg-gray-800 rounded mb-4 overflow-hidden">
        <svg width="100%" height="100%" className="absolute inset-0">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Price line */}
          <polyline
            fill="none"
            stroke="#00aaff"
            strokeWidth="2"
            points={chartData.map((point, index) => {
              const x = (index / (chartData.length - 1)) * 100;
              const y = 100 - ((point.price - minPrice) / (maxPrice - minPrice)) * 80;
              return `${x}%,${y}%`;
            }).join(' ')}
          />
          
          {/* Price points */}
          {chartData.map((point, index) => {
            const x = (index / (chartData.length - 1)) * 100;
            const y = 100 - ((point.price - minPrice) / (maxPrice - minPrice)) * 80;
            return (
              <circle
                key={index}
                cx={`${x}%`}
                cy={`${y}%`}
                r="3"
                fill="#00aaff"
                className="hover:r-5 transition-all cursor-pointer"
              />
            );
          })}
        </svg>
        
        {/* Price labels */}
        <div className="absolute right-2 top-2 text-xs text-gray-400">
          ${maxPrice.toLocaleString()}
        </div>
        <div className="absolute right-2 bottom-2 text-xs text-gray-400">
          ${minPrice.toLocaleString()}
        </div>
      </div>

      {/* Volume Chart */}
      <div className="relative h-16 bg-gray-800 rounded">
        <svg width="100%" height="100%" className="absolute inset-0">
          {chartData.map((point, index) => {
            const x = (index / chartData.length) * 100;
            const width = 100 / chartData.length * 0.8;
            const height = (point.volume / maxVolume) * 80;
            return (
              <rect
                key={index}
                x={`${x}%`}
                y={`${100 - height}%`}
                width={`${width}%`}
                height={`${height}%`}
                fill="rgba(0, 170, 255, 0.3)"
                className="hover:fill-blue-400 transition-colors cursor-pointer"
              />
            );
          })}
        </svg>
        
        {/* Time labels */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between px-2 text-xs text-gray-400">
          {chartData.filter((_, i) => i % 2 === 0).map((point, index) => (
            <span key={index}>{point.time}</span>
          ))}
        </div>
      </div>

      {/* Chart controls */}
      <div className="flex justify-between items-center mt-4 text-sm">
        <div className="flex gap-4">
          <label className="flex items-center text-gray-300">
            <input type="checkbox" className="mr-2" defaultChecked />
            Volume
          </label>
          <label className="flex items-center text-gray-300">
            <input type="checkbox" className="mr-2" />
            RSI
          </label>
          <label className="flex items-center text-gray-300">
            <input type="checkbox" className="mr-2" />
            MACD
          </label>
          <label className="flex items-center text-gray-300">
            <input type="checkbox" className="mr-2" />
            Bollinger Bands
          </label>
        </div>
        
        <div className="text-gray-400">
          Current: ${chartData[chartData.length - 1]?.price.toLocaleString()}
        </div>
      </div>
    </div>
  );
};

export default SimpleChart;
