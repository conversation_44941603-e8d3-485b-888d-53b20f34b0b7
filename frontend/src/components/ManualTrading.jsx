import React, { useState, useEffect, useRef } from 'react';
import './ManualTrading.css';

const ManualTrading = () => {
  const [command, setCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [tradeHistory, setTradeHistory] = useState([]);
  const [suggestions, setSuggestions] = useState([
    'buy 5 usdt of btc',
    'buy 10 dollars of ethereum',
    'sell all btc',
    'sell 50% of eth',
    'get balance',
    'btc price',
    'buy 0.001 btc',
    'sell 0.5 eth'
  ]);
  const [balance, setBalance] = useState(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);
  const chatEndRef = useRef(null);

  useEffect(() => {
    // Load trade history from localStorage
    const savedHistory = localStorage.getItem('manualTradeHistory');
    if (savedHistory) {
      setTradeHistory(JSON.parse(savedHistory));
    }
    
    // Auto-load balance on component mount
    loadBalance();
  }, []);

  useEffect(() => {
    // Scroll to bottom when new messages are added
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [tradeHistory]);

  const saveTradeHistory = (newHistory) => {
    localStorage.setItem('manualTradeHistory', JSON.stringify(newHistory));
  };

  const addToHistory = (entry) => {
    const newHistory = [...tradeHistory, {
      ...entry,
      id: Date.now(),
      timestamp: new Date().toISOString()
    }];
    setTradeHistory(newHistory);
    saveTradeHistory(newHistory);
  };

  const loadBalance = async () => {
    setIsLoadingBalance(true);
    try {
      const response = await fetch('/api/manual-trading/execute-command', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command: 'get balance' })
      });

      const data = await response.json();
      
      if (data.success && data.result.success) {
        setBalance(data.result.balances);
      }
    } catch (error) {
      console.error('Error loading balance:', error);
    } finally {
      setIsLoadingBalance(false);
    }
  };

  const executeCommand = async () => {
    if (!command.trim() || isExecuting) return;

    setIsExecuting(true);
    
    // Add user command to history
    addToHistory({
      type: 'user',
      content: command,
      command: command
    });

    try {
      const response = await fetch('/api/manual-trading/execute-command', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command: command.trim() })
      });

      const data = await response.json();
      
      // Add response to history
      addToHistory({
        type: 'system',
        content: data,
        success: data.success,
        command: command
      });

      // If balance command was executed, update balance state
      if (data.success && data.result?.action === 'balance') {
        setBalance(data.result.balances);
      }

      // Clear command input
      setCommand('');

    } catch (error) {
      addToHistory({
        type: 'error',
        content: { error: error.message },
        success: false,
        command: command
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      executeCommand();
    }
  };

  const useSuggestion = (suggestion) => {
    setCommand(suggestion);
  };

  const clearHistory = () => {
    setTradeHistory([]);
    localStorage.removeItem('manualTradeHistory');
  };

  const formatBalance = (balances) => {
    if (!balances) return null;
    
    return Object.entries(balances).map(([currency, data]) => (
      <div key={currency} className="balance-item">
        <span className="currency">{currency}</span>
        <span className="amount">{data.available.toFixed(8)}</span>
        {data.hold > 0 && <span className="hold">(Hold: {data.hold.toFixed(8)})</span>}
      </div>
    ));
  };

  const formatTradeResult = (result) => {
    if (!result.success) {
      return (
        <div className="trade-error">
          <span className="error-icon">❌</span>
          <span className="error-message">{result.error}</span>
        </div>
      );
    }

    const { action, symbol, message } = result;
    
    if (action === 'buy') {
      return (
        <div className="trade-success">
          <span className="success-icon">✅</span>
          <div className="trade-details">
            <div className="trade-message">{message}</div>
            <div className="trade-meta">
              Order ID: {result.order_id}
              {result.funds_used && <span> | Funds: ${result.funds_used}</span>}
              {result.quantity && <span> | Quantity: {result.quantity}</span>}
            </div>
          </div>
        </div>
      );
    }

    if (action === 'sell') {
      return (
        <div className="trade-success">
          <span className="success-icon">✅</span>
          <div className="trade-details">
            <div className="trade-message">{message}</div>
            <div className="trade-meta">
              Order ID: {result.order_id} | Sold: {result.quantity_sold}
            </div>
          </div>
        </div>
      );
    }

    if (action === 'price') {
      return (
        <div className="price-info">
          <span className="price-icon">💰</span>
          <div className="price-details">
            <div className="price-main">{symbol}: ${result.price.toFixed(2)}</div>
            <div className="price-meta">
              24h Change: {result.change_24h > 0 ? '+' : ''}{result.change_24h.toFixed(2)}%
              | Volume: {result.volume_24h.toFixed(2)}
            </div>
          </div>
        </div>
      );
    }

    if (action === 'balance') {
      return (
        <div className="balance-info">
          <span className="balance-icon">💼</span>
          <div className="balance-details">
            <div className="balance-title">Account Balance</div>
            <div className="balance-list">
              {formatBalance(result.balances)}
            </div>
            <div className="balance-total">
              Estimated Total: ${result.estimated_total_usdt?.toFixed(2) || '0.00'} USDT
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="trade-info">
        <span className="info-icon">ℹ️</span>
        <span>{JSON.stringify(result, null, 2)}</span>
      </div>
    );
  };

  return (
    <div className="manual-trading">
      <div className="trading-header">
        <h2>🎯 Manual Trading Interface</h2>
        <div className="header-actions">
          <button 
            onClick={loadBalance} 
            disabled={isLoadingBalance}
            className="balance-btn"
          >
            {isLoadingBalance ? '⏳' : '💼'} Balance
          </button>
          <button onClick={clearHistory} className="clear-btn">
            🗑️ Clear
          </button>
        </div>
      </div>

      {balance && (
        <div className="current-balance">
          <h3>💼 Current Balance</h3>
          <div className="balance-grid">
            {formatBalance(balance)}
          </div>
        </div>
      )}

      <div className="chat-container">
        <div className="chat-messages">
          {tradeHistory.length === 0 && (
            <div className="welcome-message">
              <h3>🚀 Welcome to Manual Trading!</h3>
              <p>Type commands like:</p>
              <ul>
                <li>"buy 5 usdt of btc" - Buy $5 worth of Bitcoin</li>
                <li>"sell all eth" - Sell all Ethereum</li>
                <li>"get balance" - Check your balance</li>
                <li>"btc price" - Get Bitcoin price</li>
              </ul>
            </div>
          )}
          
          {tradeHistory.map((entry) => (
            <div key={entry.id} className={`message ${entry.type}`}>
              <div className="message-header">
                <span className="message-type">
                  {entry.type === 'user' ? '👤 You' : 
                   entry.type === 'error' ? '❌ Error' : '🤖 System'}
                </span>
                <span className="message-time">
                  {new Date(entry.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="message-content">
                {entry.type === 'user' ? (
                  <div className="user-command">{entry.content}</div>
                ) : entry.type === 'error' ? (
                  <div className="error-content">{entry.content.error}</div>
                ) : (
                  formatTradeResult(entry.content.result)
                )}
              </div>
            </div>
          ))}
          <div ref={chatEndRef} />
        </div>
      </div>

      <div className="suggestions">
        <h4>💡 Quick Commands:</h4>
        <div className="suggestion-buttons">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => useSuggestion(suggestion)}
              className="suggestion-btn"
              disabled={isExecuting}
            >
              {suggestion}
            </button>
          ))}
        </div>
      </div>

      <div className="command-input">
        <div className="input-container">
          <input
            type="text"
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your trading command... (e.g., 'buy 5 usdt of btc')"
            disabled={isExecuting}
            className="command-field"
          />
          <button
            onClick={executeCommand}
            disabled={!command.trim() || isExecuting}
            className="execute-btn"
          >
            {isExecuting ? '⏳' : '🚀'} Execute
          </button>
        </div>
        <div className="input-help">
          Press Enter to execute • Shift+Enter for new line
        </div>
      </div>
    </div>
  );
};

export default ManualTrading;
