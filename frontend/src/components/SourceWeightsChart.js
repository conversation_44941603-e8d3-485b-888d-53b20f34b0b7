import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from "recharts";
import PropTypes from 'prop-types';

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#8dd1e1", "#d0ed57", "#a4de6c"];

function SourceWeightsChart({ weights }) {
  return (
    <div className="bg-gray-800 p-4 rounded-lg shadow text-white w-full" style={{ height: "400px" }}>
      <h2 className="text-xl font-semibold mb-4">🧠 AI Source Weights</h2>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={weights}
            dataKey="weight"
            nameKey="source"
            cx="50%"
            cy="50%"
            outerRadius={120}
            fill="#8884d8"
            label
          >
            {weights.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

SourceWeightsChart.propTypes = {
  weights: PropTypes.arrayOf(PropTypes.shape({
    source: PropTypes.string.isRequired,
    weight: PropTypes.number.isRequired,
  })).isRequired,
};

export default SourceWeightsChart;