import React, { useState, useEffect } from 'react';

const AIInsights = () => {
  const [insights, setInsights] = useState([]);
  const [predictions, setPredictions] = useState([]);
  const [marketAnalysis, setMarketAnalysis] = useState({});
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');

  // Mock AI insights data
  const mockInsights = [
    {
      id: 1,
      type: 'price_prediction',
      symbol: 'BTC',
      title: 'Bitcoin Price Prediction',
      content: 'AI models suggest BTC could reach $52,000 within 7 days based on current momentum and technical indicators.',
      confidence: 85,
      timestamp: '2 hours ago',
      impact: 'high'
    },
    {
      id: 2,
      type: 'sentiment_analysis',
      symbol: 'ETH',
      title: 'Ethereum Sentiment Shift',
      content: 'Social sentiment for ETH has improved 23% in the last 24h. Positive news about network upgrades driving optimism.',
      confidence: 78,
      timestamp: '4 hours ago',
      impact: 'medium'
    },
    {
      id: 3,
      type: 'market_pattern',
      symbol: 'ADA',
      title: 'Cardano Pattern Recognition',
      content: 'Historical pattern analysis indicates ADA may be forming a bullish flag pattern with potential 15% upside.',
      confidence: 72,
      timestamp: '6 hours ago',
      impact: 'medium'
    },
    {
      id: 4,
      type: 'risk_alert',
      symbol: 'SOL',
      title: 'Solana Risk Assessment',
      content: 'Elevated volatility detected. Risk models suggest reducing position size by 20% until market stabilizes.',
      confidence: 91,
      timestamp: '8 hours ago',
      impact: 'high'
    }
  ];

  const mockPredictions = [
    { symbol: 'BTC', current: 48500, predicted: 52000, change: 7.2, timeframe: '7d', confidence: 85 },
    { symbol: 'ETH', current: 3100, predicted: 3350, change: 8.1, timeframe: '5d', confidence: 78 },
    { symbol: 'ADA', current: 0.48, predicted: 0.55, change: 14.6, timeframe: '10d', confidence: 72 },
    { symbol: 'SOL', current: 102, predicted: 95, change: -6.9, timeframe: '3d', confidence: 81 }
  ];

  const mockMarketAnalysis = {
    overall_sentiment: 'bullish',
    market_phase: 'accumulation',
    volatility_index: 'medium',
    fear_greed_index: 68,
    ai_confidence: 82
  };

  useEffect(() => {
    setInsights(mockInsights);
    setPredictions(mockPredictions);
    setMarketAnalysis(mockMarketAnalysis);
  }, []);

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'high': return 'border-red-500 bg-red-900/20';
      case 'medium': return 'border-yellow-500 bg-yellow-900/20';
      default: return 'border-green-500 bg-green-900/20';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'price_prediction': return '🎯';
      case 'sentiment_analysis': return '📊';
      case 'market_pattern': return '📈';
      case 'risk_alert': return '⚠️';
      default: return '🤖';
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Market Overview */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-white mb-6">AI Market Analysis</h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <h3 className="text-gray-400 text-sm mb-2">Overall Sentiment</h3>
            <p className="text-green-400 text-lg font-bold capitalize">{marketAnalysis.overall_sentiment}</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <h3 className="text-gray-400 text-sm mb-2">Market Phase</h3>
            <p className="text-blue-400 text-lg font-bold capitalize">{marketAnalysis.market_phase}</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <h3 className="text-gray-400 text-sm mb-2">Volatility</h3>
            <p className="text-yellow-400 text-lg font-bold capitalize">{marketAnalysis.volatility_index}</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <h3 className="text-gray-400 text-sm mb-2">Fear & Greed</h3>
            <p className="text-white text-lg font-bold">{marketAnalysis.fear_greed_index}</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <h3 className="text-gray-400 text-sm mb-2">AI Confidence</h3>
            <p className="text-purple-400 text-lg font-bold">{marketAnalysis.ai_confidence}%</p>
          </div>
        </div>
      </div>

      {/* AI Predictions */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">AI Price Predictions</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-gray-400 text-sm">
                <th className="text-left py-2">Asset</th>
                <th className="text-right py-2">Current</th>
                <th className="text-right py-2">Predicted</th>
                <th className="text-right py-2">Change</th>
                <th className="text-right py-2">Timeframe</th>
                <th className="text-right py-2">Confidence</th>
              </tr>
            </thead>
            <tbody>
              {predictions.map(pred => (
                <tr key={pred.symbol} className="border-t border-gray-700">
                  <td className="py-3 text-white font-medium">{pred.symbol}</td>
                  <td className="text-right py-3 text-white">${pred.current.toLocaleString()}</td>
                  <td className="text-right py-3 text-white">${pred.predicted.toLocaleString()}</td>
                  <td className={`text-right py-3 ${pred.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {pred.change >= 0 ? '+' : ''}{pred.change.toFixed(1)}%
                  </td>
                  <td className="text-right py-3 text-gray-300">{pred.timeframe}</td>
                  <td className="text-right py-3 text-purple-400">{pred.confidence}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* AI Insights Feed */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Latest AI Insights</h3>
        <div className="space-y-4">
          {insights.map(insight => (
            <div key={insight.id} className={`rounded-lg p-4 border-l-4 ${getImpactColor(insight.impact)}`}>
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-xl">{getTypeIcon(insight.type)}</span>
                  <h4 className="text-white font-medium">{insight.title}</h4>
                  <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                    {insight.symbol}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-purple-400 text-sm">{insight.confidence}% confidence</span>
                  <span className="text-gray-400 text-sm">{insight.timestamp}</span>
                </div>
              </div>
              <p className="text-gray-300 mb-3">{insight.content}</p>
              <div className="flex items-center justify-between">
                <span className={`px-2 py-1 rounded text-xs ${
                  insight.impact === 'high' ? 'bg-red-600 text-white' :
                  insight.impact === 'medium' ? 'bg-yellow-600 text-white' :
                  'bg-green-600 text-white'
                }`}>
                  {insight.impact.toUpperCase()} IMPACT
                </span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">
                  View Details →
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AIInsights;
