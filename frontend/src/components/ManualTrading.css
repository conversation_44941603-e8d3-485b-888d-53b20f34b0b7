.manual-trading {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 15px;
  color: #ffffff;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
}

.trading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #333;
}

.trading-header h2 {
  margin: 0;
  color: #00d4ff;
  font-size: 1.8rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.balance-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.balance-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.balance-btn:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
}

.clear-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
}

.clear-btn:hover {
  background: linear-gradient(135deg, #d32f2f, #b71c1c);
  transform: translateY(-2px);
}

.current-balance {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.current-balance h3 {
  margin: 0 0 15px 0;
  color: #00d4ff;
  font-size: 1.2rem;
}

.balance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.balance-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4px solid #00d4ff;
}

.balance-item .currency {
  font-weight: 600;
  color: #00d4ff;
}

.balance-item .amount {
  font-family: 'Courier New', monospace;
  color: #4CAF50;
}

.balance-item .hold {
  font-size: 0.8rem;
  color: #ff9800;
}

.chat-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.chat-messages {
  height: 400px;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #00d4ff;
  border-radius: 4px;
}

.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: #ccc;
}

.welcome-message h3 {
  color: #00d4ff;
  margin-bottom: 20px;
}

.welcome-message ul {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.welcome-message li {
  margin-bottom: 8px;
  color: #aaa;
}

.message {
  border-radius: 12px;
  padding: 15px;
  max-width: 80%;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  align-self: flex-end;
  margin-left: auto;
}

.message.system {
  background: rgba(255, 255, 255, 0.1);
  align-self: flex-start;
}

.message.error {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  align-self: flex-start;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  opacity: 0.8;
}

.message-type {
  font-weight: 600;
}

.message-time {
  font-size: 0.8rem;
}

.user-command {
  font-size: 1.1rem;
  font-weight: 500;
}

.trade-success, .trade-error, .trade-info, .price-info, .balance-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.success-icon, .error-icon, .info-icon, .price-icon, .balance-icon {
  font-size: 1.2rem;
  margin-top: 2px;
}

.trade-details, .price-details, .balance-details {
  flex: 1;
}

.trade-message, .price-main, .balance-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: #4CAF50;
}

.trade-meta, .price-meta {
  font-size: 0.9rem;
  color: #ccc;
  font-family: 'Courier New', monospace;
}

.balance-list {
  margin: 10px 0;
}

.balance-total {
  font-weight: 600;
  color: #00d4ff;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.error-content {
  color: #ffcdd2;
  font-weight: 500;
}

.suggestions {
  margin-bottom: 20px;
}

.suggestions h4 {
  margin: 0 0 15px 0;
  color: #00d4ff;
  font-size: 1.1rem;
}

.suggestion-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.suggestion-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
  transform: translateY(-2px);
}

.suggestion-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.command-input {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.input-container {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.command-field {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.command-field:focus {
  outline: none;
  border-color: #00d4ff;
  background: rgba(255, 255, 255, 0.15);
}

.command-field::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.execute-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.execute-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.execute-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  transform: none;
}

.input-help {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .manual-trading {
    padding: 15px;
  }
  
  .trading-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .balance-grid {
    grid-template-columns: 1fr;
  }
  
  .chat-messages {
    height: 300px;
  }
  
  .message {
    max-width: 95%;
  }
  
  .suggestion-buttons {
    justify-content: center;
  }
  
  .input-container {
    flex-direction: column;
  }
  
  .execute-btn {
    width: 100%;
  }
}
