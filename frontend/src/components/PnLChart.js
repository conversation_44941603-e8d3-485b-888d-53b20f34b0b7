import React from 'react';
import {
  <PERSON><PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer
} from 'recharts';
import PropTypes from 'prop-types';

function PnLChart({ data, totalTrades, totalProfit, isLoading, hasError }) {
  const formatNumber = (num) =>
    new Intl.NumberFormat("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(num);

  if (isLoading) {
    return <div className="text-white">🔄 Loading PnL data...</div>;
  }

  if (hasError) {
    return <div className="text-red-500">❌ Failed to load PnL chart data.</div>;
  }

  return (
    <div className="bg-gray-800 p-4 rounded-lg shadow w-full" style={{ height: "400px" }}>
      <h2 className="text-xl font-semibold mb-2 text-white">📈 PnL Overview (Last 7 Days)</h2>
      <div className="text-white mb-4">
        <p>💰 Total Profit: ${formatNumber(totalProfit)}</p>
        <p>📊 Total Trades: {new Intl.NumberFormat("en-US").format(totalTrades)}</p>
      </div>
      <ResponsiveContainer width="100%" height="80%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#555" />
          <XAxis dataKey="token" stroke="#ccc" />
          <YAxis stroke="#ccc" label={{ value: 'Profit ($)', angle: -90, position: 'insideLeft', fill: '#ccc' }} />
          {/* Future: Add dynamic % gain/loss or color-coded profit bar tooltips */}
          <Tooltip
            formatter={(value, name) => [`$${formatNumber(value)} profit`, name]}
            labelFormatter={(label) => `🪙 Token: ${label}`}
          />
          <Legend verticalAlign="top" height={36} />
          <Line type="monotone" dataKey="profit" stroke="#00FF00" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

PnLChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    token: PropTypes.string.isRequired,
    profit: PropTypes.number.isRequired,
  })).isRequired,
  totalTrades: PropTypes.number.isRequired,
  totalProfit: PropTypes.number.isRequired,
  isLoading: PropTypes.bool.isRequired,
  hasError: PropTypes.bool.isRequired,
};

export default PnLChart;