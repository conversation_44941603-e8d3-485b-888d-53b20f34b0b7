import React, { useState } from 'react';
import { usePreloader } from '../contexts/PreloaderContext';

/**
 * 🚀 PRELOADER STATUS COMPONENT
 * Shows the status of background preloading for all screens
 */
const PreloaderStatus = () => {
  const { preloadStatus, isPreloading, refreshAll } = usePreloader();
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'loading': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'loading': return 'text-yellow-400';
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const screenNames = {
    dashboard: 'Dashboard',
    discover: 'Discover',
    trades: 'Live Trades',
    aiSignals: 'AI Signals',
    pnl: 'PnL Analytics',
    news: 'News Feed',
    tokenmetrics: 'TokenMetrics',
    analytics: 'Analytics',
    costMonitoring: 'Cost Monitor',
    arbitrage: 'Arbitrage'
  };

  const totalScreens = Object.keys(screenNames).length;
  const loadedScreens = Object.values(preloadStatus).filter(status => status === 'success').length;
  const errorScreens = Object.values(preloadStatus).filter(status => status === 'error').length;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Compact Status Indicator */}
      <div 
        className={`bg-gray-800 border border-gray-600 rounded-lg p-3 cursor-pointer transition-all duration-300 ${
          isExpanded ? 'w-80' : 'w-48'
        }`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isPreloading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'}`} />
            <span className="text-sm text-white font-medium">
              Preloader
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-300">
              {loadedScreens}/{totalScreens}
            </span>
            <button
              onClick={(e) => {
                e.stopPropagation();
                refreshAll();
              }}
              className="text-blue-400 hover:text-blue-300 text-xs"
              title="Refresh all screens"
            >
              🔄
            </button>
            <span className="text-gray-400 text-xs">
              {isExpanded ? '▼' : '▶'}
            </span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-2 w-full bg-gray-700 rounded-full h-1">
          <div 
            className={`h-1 rounded-full transition-all duration-300 ${
              errorScreens > 0 ? 'bg-red-400' : 'bg-green-400'
            }`}
            style={{ width: `${(loadedScreens / totalScreens) * 100}%` }}
          />
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="mt-3 space-y-1 max-h-60 overflow-y-auto">
            <div className="text-xs text-gray-400 mb-2">
              Screen Preload Status:
            </div>
            {Object.entries(screenNames).map(([key, name]) => {
              const status = preloadStatus[key] || 'pending';
              return (
                <div key={key} className="flex items-center justify-between text-xs">
                  <span className="text-gray-300">{name}</span>
                  <span className={`flex items-center space-x-1 ${getStatusColor(status)}`}>
                    <span>{getStatusIcon(status)}</span>
                    <span className="capitalize">{status}</span>
                  </span>
                </div>
              );
            })}
            
            {/* Summary */}
            <div className="mt-3 pt-2 border-t border-gray-600 text-xs">
              <div className="flex justify-between text-gray-400">
                <span>Total:</span>
                <span>{totalScreens} screens</span>
              </div>
              <div className="flex justify-between text-green-400">
                <span>Loaded:</span>
                <span>{loadedScreens} screens</span>
              </div>
              {errorScreens > 0 && (
                <div className="flex justify-between text-red-400">
                  <span>Errors:</span>
                  <span>{errorScreens} screens</span>
                </div>
              )}
              <div className="flex justify-between text-gray-400 mt-1">
                <span>Status:</span>
                <span className={isPreloading ? 'text-yellow-400' : 'text-green-400'}>
                  {isPreloading ? 'Loading...' : 'Ready'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 🎯 SCREEN PRELOAD INDICATOR
 * Shows individual screen preload status
 */
export const ScreenPreloadIndicator = ({ screenKey, className = "" }) => {
  const { preloadStatus } = usePreloader();
  const status = preloadStatus[screenKey] || 'pending';

  const getStatusIcon = (status) => {
    switch (status) {
      case 'loading': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'loading': return 'text-yellow-400';
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className={`flex items-center space-x-1 text-xs ${getStatusColor(status)} ${className}`}>
      <span>{getStatusIcon(status)}</span>
      <span className="capitalize">{status === 'success' ? 'Preloaded' : status}</span>
    </div>
  );
};

export default PreloaderStatus;
