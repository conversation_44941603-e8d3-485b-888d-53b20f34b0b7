import React, { useState, useEffect } from 'react';

const PortfolioIntegration = () => {
  const [portfolio, setPortfolio] = useState([]);
  const [totalValue, setTotalValue] = useState(0);
  const [totalPnL, setTotalPnL] = useState(0);
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');

  // Mock portfolio data
  const mockPortfolio = [
    {
      symbol: 'BTC',
      name: 'Bitcoin',
      amount: 0.5,
      avgBuyPrice: 45000,
      currentPrice: 48500,
      value: 24250,
      pnl: 1750,
      pnlPercent: 7.78,
      allocation: 45.2
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      amount: 8.2,
      avgBuyPrice: 2800,
      currentPrice: 3100,
      value: 25420,
      pnl: 2460,
      pnlPercent: 10.71,
      allocation: 47.3
    },
    {
      symbol: 'ADA',
      name: 'Cardano',
      amount: 5000,
      avgBuyPrice: 0.42,
      currentPrice: 0.48,
      value: 2400,
      pnl: 300,
      pnlPercent: 14.29,
      allocation: 4.5
    },
    {
      symbol: 'SOL',
      name: '<PERSON><PERSON>',
      amount: 15,
      avgBuyPrice: 95,
      currentPrice: 102,
      value: 1530,
      pnl: 105,
      pnlPercent: 7.37,
      allocation: 2.8
    }
  ];

  const [trades, setTrades] = useState([
    {
      id: 1,
      symbol: 'BTC',
      type: 'buy',
      amount: 0.1,
      price: 47500,
      timestamp: '2024-01-10T10:30:00Z',
      status: 'completed'
    },
    {
      id: 2,
      symbol: 'ETH',
      type: 'sell',
      amount: 1.5,
      price: 3050,
      timestamp: '2024-01-10T09:15:00Z',
      status: 'completed'
    },
    {
      id: 3,
      symbol: 'ADA',
      type: 'buy',
      amount: 1000,
      price: 0.47,
      timestamp: '2024-01-10T08:45:00Z',
      status: 'pending'
    }
  ]);

  useEffect(() => {
    setPortfolio(mockPortfolio);
    calculateTotals(mockPortfolio);
  }, []);

  const calculateTotals = (portfolioData) => {
    const total = portfolioData.reduce((sum, asset) => sum + asset.value, 0);
    const pnl = portfolioData.reduce((sum, asset) => sum + asset.pnl, 0);
    setTotalValue(total);
    setTotalPnL(pnl);
  };

  const executeQuickTrade = (symbol, type) => {
    const newTrade = {
      id: Date.now(),
      symbol,
      type,
      amount: type === 'buy' ? 100 : 50, // Mock amounts
      price: portfolio.find(p => p.symbol === symbol)?.currentPrice || 0,
      timestamp: new Date().toISOString(),
      status: 'pending'
    };
    
    setTrades(prev => [newTrade, ...prev]);
    
    // Simulate trade execution
    setTimeout(() => {
      setTrades(prev => 
        prev.map(trade => 
          trade.id === newTrade.id 
            ? { ...trade, status: 'completed' }
            : trade
        )
      );
    }, 2000);
  };

  const rebalancePortfolio = () => {
    // Mock rebalancing logic
    alert('Portfolio rebalancing initiated. This would redistribute assets according to your target allocation.');
  };

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">Portfolio Overview</h2>
          <div className="flex gap-2">
            {['24h', '7d', '30d', '1y'].map(timeframe => (
              <button
                key={timeframe}
                onClick={() => setSelectedTimeframe(timeframe)}
                className={`px-3 py-1 rounded text-sm ${
                  selectedTimeframe === timeframe
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {timeframe}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-gray-400 text-sm mb-2">Total Portfolio Value</h3>
            <p className="text-2xl font-bold text-white">${totalValue.toLocaleString()}</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-gray-400 text-sm mb-2">Total P&L</h3>
            <p className={`text-2xl font-bold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {totalPnL >= 0 ? '+' : ''}${totalPnL.toLocaleString()}
            </p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-gray-400 text-sm mb-2">P&L Percentage</h3>
            <p className={`text-2xl font-bold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {totalPnL >= 0 ? '+' : ''}{((totalPnL / (totalValue - totalPnL)) * 100).toFixed(2)}%
            </p>
          </div>
        </div>

        <button
          onClick={rebalancePortfolio}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mb-6"
        >
          Rebalance Portfolio
        </button>
      </div>

      {/* Holdings */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Holdings</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-gray-400 text-sm">
                <th className="text-left py-2">Asset</th>
                <th className="text-right py-2">Amount</th>
                <th className="text-right py-2">Avg Price</th>
                <th className="text-right py-2">Current Price</th>
                <th className="text-right py-2">Value</th>
                <th className="text-right py-2">P&L</th>
                <th className="text-right py-2">Allocation</th>
                <th className="text-center py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {portfolio.map(asset => (
                <tr key={asset.symbol} className="border-t border-gray-700">
                  <td className="py-3">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-xs font-bold">{asset.symbol.slice(0, 2)}</span>
                      </div>
                      <div>
                        <p className="text-white font-medium">{asset.symbol}</p>
                        <p className="text-gray-400 text-sm">{asset.name}</p>
                      </div>
                    </div>
                  </td>
                  <td className="text-right py-3 text-white">{asset.amount}</td>
                  <td className="text-right py-3 text-white">${asset.avgBuyPrice.toLocaleString()}</td>
                  <td className="text-right py-3 text-white">${asset.currentPrice.toLocaleString()}</td>
                  <td className="text-right py-3 text-white">${asset.value.toLocaleString()}</td>
                  <td className={`text-right py-3 ${asset.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {asset.pnl >= 0 ? '+' : ''}${asset.pnl.toLocaleString()} ({asset.pnlPercent.toFixed(2)}%)
                  </td>
                  <td className="text-right py-3 text-white">{asset.allocation}%</td>
                  <td className="text-center py-3">
                    <div className="flex justify-center gap-2">
                      <button
                        onClick={() => executeQuickTrade(asset.symbol, 'buy')}
                        className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs"
                      >
                        Buy
                      </button>
                      <button
                        onClick={() => executeQuickTrade(asset.symbol, 'sell')}
                        className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                      >
                        Sell
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Trades */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Recent Trades</h3>
        <div className="space-y-3">
          {trades.slice(0, 5).map(trade => (
            <div key={trade.id} className="bg-gray-700 rounded-lg p-4 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${
                  trade.status === 'completed' ? 'bg-green-500' : 
                  trade.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <div>
                  <p className="text-white font-medium">
                    {trade.type.toUpperCase()} {trade.symbol}
                  </p>
                  <p className="text-gray-400 text-sm">
                    {trade.amount} @ ${trade.price.toLocaleString()}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white">${(trade.amount * trade.price).toLocaleString()}</p>
                <p className="text-gray-400 text-sm">
                  {new Date(trade.timestamp).toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Portfolio Allocation Chart */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Portfolio Allocation</h3>
        <div className="flex items-center justify-center">
          <div className="relative w-64 h-64">
            <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
              {portfolio.map((asset, index) => {
                const startAngle = portfolio.slice(0, index).reduce((sum, a) => sum + (a.allocation * 3.6), 0);
                const endAngle = startAngle + (asset.allocation * 3.6);
                const largeArcFlag = asset.allocation > 50 ? 1 : 0;
                
                const x1 = 50 + 40 * Math.cos((startAngle * Math.PI) / 180);
                const y1 = 50 + 40 * Math.sin((startAngle * Math.PI) / 180);
                const x2 = 50 + 40 * Math.cos((endAngle * Math.PI) / 180);
                const y2 = 50 + 40 * Math.sin((endAngle * Math.PI) / 180);
                
                const pathData = [
                  `M 50 50`,
                  `L ${x1} ${y1}`,
                  `A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  'Z'
                ].join(' ');
                
                const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
                
                return (
                  <path
                    key={asset.symbol}
                    d={pathData}
                    fill={colors[index % colors.length]}
                    className="hover:opacity-80 transition-opacity"
                  />
                );
              })}
            </svg>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mt-6">
          {portfolio.map((asset, index) => {
            const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
            return (
              <div key={asset.symbol} className="flex items-center">
                <div 
                  className="w-4 h-4 rounded mr-3"
                  style={{ backgroundColor: colors[index % colors.length] }}
                ></div>
                <span className="text-white">{asset.symbol}: {asset.allocation}%</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PortfolioIntegration;
