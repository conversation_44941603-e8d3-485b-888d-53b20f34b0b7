import React, { useState, useEffect } from 'react';

const SocialFeatures = () => {
  const [discussions, setDiscussions] = useState([]);
  const [sentiment, setSentiment] = useState({});
  const [newPost, setNewPost] = useState('');
  const [selectedToken, setSelectedToken] = useState('BTC');

  // Mock social data
  const mockDiscussions = [
    {
      id: 1,
      user: 'CryptoTrader123',
      avatar: '👤',
      token: 'BTC',
      content: 'Bitcoin looking strong above $48k resistance. Could see a move to $52k if volume picks up.',
      timestamp: '2 hours ago',
      likes: 24,
      replies: 8,
      sentiment: 'bullish'
    },
    {
      id: 2,
      user: 'AltcoinAnalyst',
      avatar: '📊',
      token: 'ETH',
      content: 'Ethereum merge upgrade showing positive effects on network efficiency. Long-term bullish.',
      timestamp: '4 hours ago',
      likes: 18,
      replies: 12,
      sentiment: 'bullish'
    },
    {
      id: 3,
      user: 'MarketWatcher',
      avatar: '👁️',
      token: 'ADA',
      content: 'Cardano development activity increasing. New partnerships could drive adoption.',
      timestamp: '6 hours ago',
      likes: 15,
      replies: 6,
      sentiment: 'neutral'
    },
    {
      id: 4,
      user: 'TechnicalTrader',
      avatar: '📈',
      token: 'SOL',
      content: 'Solana network congestion issues still a concern. Waiting for technical improvements.',
      timestamp: '8 hours ago',
      likes: 9,
      replies: 15,
      sentiment: 'bearish'
    }
  ];

  const mockSentiment = {
    BTC: { bullish: 65, neutral: 25, bearish: 10, total: 1250 },
    ETH: { bullish: 58, neutral: 30, bearish: 12, total: 890 },
    ADA: { bullish: 45, neutral: 40, bearish: 15, total: 420 },
    SOL: { bullish: 35, neutral: 35, bearish: 30, total: 380 }
  };

  const [trendingTopics, setTrendingTopics] = useState([
    { tag: '#Bitcoin', mentions: 2450, change: '+12%' },
    { tag: '#Ethereum', mentions: 1890, change: '+8%' },
    { tag: '#DeFi', mentions: 1200, change: '+15%' },
    { tag: '#NFT', mentions: 980, change: '-5%' },
    { tag: '#Altcoins', mentions: 750, change: '+22%' }
  ]);

  useEffect(() => {
    setDiscussions(mockDiscussions);
    setSentiment(mockSentiment);
  }, []);

  const postMessage = () => {
    if (!newPost.trim()) return;

    const post = {
      id: Date.now(),
      user: 'You',
      avatar: '🚀',
      token: selectedToken,
      content: newPost,
      timestamp: 'Just now',
      likes: 0,
      replies: 0,
      sentiment: 'neutral'
    };

    setDiscussions(prev => [post, ...prev]);
    setNewPost('');
  };

  const likePost = (id) => {
    setDiscussions(prev =>
      prev.map(post =>
        post.id === id ? { ...post, likes: post.likes + 1 } : post
      )
    );
  };

  const getSentimentColor = (sentiment) => {
    switch (sentiment) {
      case 'bullish': return 'text-green-400';
      case 'bearish': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getSentimentIcon = (sentiment) => {
    switch (sentiment) {
      case 'bullish': return '📈';
      case 'bearish': return '📉';
      default: return '➡️';
    }
  };

  return (
    <div className="space-y-6">
      {/* Community Sentiment Overview */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-white mb-6">Community Sentiment</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(sentiment).map(([token, data]) => (
            <div key={token} className="bg-gray-700 rounded-lg p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-white font-semibold">{token}</h3>
                <span className="text-gray-400 text-sm">{data.total} votes</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-green-400">Bullish</span>
                  <span className="text-green-400">{data.bullish}%</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-green-400 h-2 rounded-full" 
                    style={{ width: `${data.bullish}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-yellow-400">Neutral</span>
                  <span className="text-yellow-400">{data.neutral}%</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-yellow-400 h-2 rounded-full" 
                    style={{ width: `${data.neutral}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-red-400">Bearish</span>
                  <span className="text-red-400">{data.bearish}%</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-red-400 h-2 rounded-full" 
                    style={{ width: `${data.bearish}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Trending Topics */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Trending Topics</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {trendingTopics.map((topic, index) => (
            <div key={index} className="bg-gray-700 rounded-lg p-4 text-center">
              <h4 className="text-blue-400 font-medium mb-2">{topic.tag}</h4>
              <p className="text-white text-lg font-bold">{topic.mentions}</p>
              <p className={`text-sm ${topic.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                {topic.change}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Post New Message */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Share Your Thoughts</h3>
        <div className="space-y-4">
          <div className="flex gap-4">
            <select
              value={selectedToken}
              onChange={(e) => setSelectedToken(e.target.value)}
              className="bg-gray-700 text-white px-3 py-2 rounded"
            >
              <option value="BTC">BTC</option>
              <option value="ETH">ETH</option>
              <option value="ADA">ADA</option>
              <option value="SOL">SOL</option>
            </select>
            <textarea
              value={newPost}
              onChange={(e) => setNewPost(e.target.value)}
              placeholder="What's your take on the market?"
              className="flex-1 bg-gray-700 text-white px-3 py-2 rounded resize-none"
              rows="3"
            />
          </div>
          <button
            onClick={postMessage}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Post Message
          </button>
        </div>
      </div>

      {/* Community Discussions */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Community Discussions</h3>
        <div className="space-y-4">
          {discussions.map(post => (
            <div key={post.id} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">{post.avatar}</div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-white font-medium">{post.user}</span>
                    <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                      {post.token}
                    </span>
                    <span className={`${getSentimentColor(post.sentiment)} text-xs`}>
                      {getSentimentIcon(post.sentiment)} {post.sentiment}
                    </span>
                    <span className="text-gray-400 text-sm">{post.timestamp}</span>
                  </div>
                  <p className="text-gray-300 mb-3">{post.content}</p>
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => likePost(post.id)}
                      className="flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors"
                    >
                      <span>❤️</span>
                      <span>{post.likes}</span>
                    </button>
                    <button className="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors">
                      <span>💬</span>
                      <span>{post.replies}</span>
                    </button>
                    <button className="text-gray-400 hover:text-green-400 transition-colors">
                      🔄 Share
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SocialFeatures;
