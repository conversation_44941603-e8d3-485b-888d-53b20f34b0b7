import React, { useState, useEffect, useRef } from 'react';

const TradingChart = ({ symbol, data, timeframe = '1h' }) => {
  const canvasRef = useRef(null);
  const [chartType, setChartType] = useState('candlestick');
  const [indicators, setIndicators] = useState(['volume']);

  // Mock chart data
  const mockChartData = data || [
    { time: '00:00', open: 42000, high: 43500, low: 41800, close: 43200, volume: 1250000 },
    { time: '01:00', open: 43200, high: 44100, low: 42900, close: 43800, volume: 980000 },
    { time: '02:00', open: 43800, high: 44500, low: 43600, close: 44200, volume: 1100000 },
    { time: '03:00', open: 44200, high: 45000, low: 44000, close: 44800, volume: 1350000 },
    { time: '04:00', open: 44800, high: 45200, low: 44300, close: 44600, volume: 890000 },
    { time: '05:00', open: 44600, high: 45500, low: 44400, close: 45200, volume: 1200000 },
    { time: '06:00', open: 45200, high: 46000, low: 45000, close: 45800, volume: 1450000 },
    { time: '07:00', open: 45800, high: 46200, low: 45300, close: 45600, volume: 1050000 },
    { time: '08:00', open: 45600, high: 46500, low: 45400, close: 46200, volume: 1300000 },
    { time: '09:00', open: 46200, high: 47000, low: 46000, close: 46800, volume: 1600000 },
  ];

  const chartTypes = ['candlestick', 'line', 'area'];
  const availableIndicators = ['volume', 'rsi', 'macd', 'bollinger', 'ema20', 'ema50'];

  useEffect(() => {
    drawChart();
  }, [chartType, indicators]);

  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const { width, height } = canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Set up chart dimensions
    const padding = 60;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // Calculate price range
    const prices = mockChartData.flatMap(d => [d.high, d.low]);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;
    
    // Draw background
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, width, height);
    
    // Draw grid
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight * i) / 5;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(padding + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = padding + (chartWidth * i) / 10;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, padding + chartHeight);
      ctx.stroke();
    }

    // Draw price chart based on type
    if (chartType === 'candlestick') {
      drawCandlesticks(ctx, mockChartData, padding, chartWidth, chartHeight, minPrice, priceRange);
    } else if (chartType === 'line') {
      drawLineChart(ctx, mockChartData, padding, chartWidth, chartHeight, minPrice, priceRange);
    } else if (chartType === 'area') {
      drawAreaChart(ctx, mockChartData, padding, chartWidth, chartHeight, minPrice, priceRange);
    }

    // Draw volume bars if enabled
    if (indicators.includes('volume')) {
      drawVolumeBars(ctx, mockChartData, padding, chartWidth, chartHeight);
    }

    // Draw labels
    drawLabels(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice, mockChartData);
  };

  const drawCandlesticks = (ctx, data, padding, chartWidth, chartHeight, minPrice, priceRange) => {
    const candleWidth = (chartWidth / data.length) * 0.7;
    
    data.forEach((candle, index) => {
      const x = padding + (chartWidth * index) / data.length + (chartWidth / data.length - candleWidth) / 2;
      const openY = padding + chartHeight - ((candle.open - minPrice) / priceRange) * chartHeight;
      const closeY = padding + chartHeight - ((candle.close - minPrice) / priceRange) * chartHeight;
      const highY = padding + chartHeight - ((candle.high - minPrice) / priceRange) * chartHeight;
      const lowY = padding + chartHeight - ((candle.low - minPrice) / priceRange) * chartHeight;
      
      const isGreen = candle.close > candle.open;
      ctx.fillStyle = isGreen ? '#00ff88' : '#ff4444';
      ctx.strokeStyle = isGreen ? '#00ff88' : '#ff4444';
      ctx.lineWidth = 1;
      
      // Draw wick
      ctx.beginPath();
      ctx.moveTo(x + candleWidth / 2, highY);
      ctx.lineTo(x + candleWidth / 2, lowY);
      ctx.stroke();
      
      // Draw body
      const bodyTop = Math.min(openY, closeY);
      const bodyHeight = Math.abs(closeY - openY) || 1;
      ctx.fillRect(x, bodyTop, candleWidth, bodyHeight);
    });
  };

  const drawLineChart = (ctx, data, padding, chartWidth, chartHeight, minPrice, priceRange) => {
    ctx.strokeStyle = '#00aaff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    data.forEach((point, index) => {
      const x = padding + (chartWidth * index) / (data.length - 1);
      const y = padding + chartHeight - ((point.close - minPrice) / priceRange) * chartHeight;
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  };

  const drawAreaChart = (ctx, data, padding, chartWidth, chartHeight, minPrice, priceRange) => {
    ctx.fillStyle = 'rgba(0, 170, 255, 0.2)';
    ctx.strokeStyle = '#00aaff';
    ctx.lineWidth = 2;
    
    // Create area path
    ctx.beginPath();
    
    // Start from bottom left
    const firstX = padding;
    const bottomY = padding + chartHeight;
    ctx.moveTo(firstX, bottomY);
    
    // Draw line to first point
    const firstPointY = padding + chartHeight - ((data[0].close - minPrice) / priceRange) * chartHeight;
    ctx.lineTo(firstX, firstPointY);
    
    // Draw the price line
    data.forEach((point, index) => {
      const x = padding + (chartWidth * index) / (data.length - 1);
      const y = padding + chartHeight - ((point.close - minPrice) / priceRange) * chartHeight;
      ctx.lineTo(x, y);
    });
    
    // Close the area by going to bottom right and back to start
    const lastX = padding + chartWidth;
    ctx.lineTo(lastX, bottomY);
    ctx.lineTo(firstX, bottomY);
    
    // Fill the area
    ctx.fill();
    
    // Draw the line on top
    ctx.beginPath();
    data.forEach((point, index) => {
      const x = padding + (chartWidth * index) / (data.length - 1);
      const y = padding + chartHeight - ((point.close - minPrice) / priceRange) * chartHeight;
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  };

  const drawVolumeBars = (ctx, data, padding, chartWidth, chartHeight) => {
    const maxVolume = Math.max(...data.map(d => d.volume));
    const volumeHeight = chartHeight * 0.2; // Use 20% of chart height for volume
    const volumeY = padding + chartHeight - volumeHeight;
    
    ctx.fillStyle = 'rgba(100, 100, 100, 0.5)';
    
    data.forEach((bar, index) => {
      const barWidth = (chartWidth / data.length) * 0.8;
      const x = padding + (chartWidth * index) / data.length + (chartWidth / data.length - barWidth) / 2;
      const barHeight = (bar.volume / maxVolume) * volumeHeight;
      const y = volumeY + volumeHeight - barHeight;
      
      ctx.fillRect(x, y, barWidth, barHeight);
    });
  };

  const drawLabels = (ctx, padding, chartWidth, chartHeight, minPrice, maxPrice, data) => {
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    
    // Price labels
    for (let i = 0; i <= 5; i++) {
      const price = minPrice + (maxPrice - minPrice) * (1 - i / 5);
      const y = padding + (chartHeight * i) / 5;
      ctx.fillText(price.toFixed(0), 10, y + 4);
    }
    
    // Time labels
    const timeStep = Math.ceil(data.length / 6);
    for (let i = 0; i < data.length; i += timeStep) {
      const x = padding + (chartWidth * i) / data.length;
      ctx.fillText(data[i].time, x - 15, chartHeight + padding + 20);
    }
  };

  const toggleIndicator = (indicator) => {
    setIndicators(prev => 
      prev.includes(indicator) 
        ? prev.filter(i => i !== indicator)
        : [...prev, indicator]
    );
  };

  return (
    <div className="bg-gray-900 rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-4">
          <h3 className="text-white text-lg font-semibold">
            {symbol || 'BTC/USDT'} - {timeframe}
          </h3>
          
          {/* Chart Type Selector */}
          <div className="flex space-x-2">
            {chartTypes.map(type => (
              <button
                key={type}
                onClick={() => setChartType(type)}
                className={`px-3 py-1 rounded text-sm ${
                  chartType === type 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>
        
        {/* Indicators */}
        <div className="flex space-x-2">
          {availableIndicators.map(indicator => (
            <button
              key={indicator}
              onClick={() => toggleIndicator(indicator)}
              className={`px-2 py-1 rounded text-xs ${
                indicators.includes(indicator)
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {indicator.toUpperCase()}
            </button>
          ))}
        </div>
      </div>
      
      <canvas
        ref={canvasRef}
        width={800}
        height={400}
        className="w-full border border-gray-700 rounded"
      />
      
      {/* Chart Info */}
      <div className="mt-4 grid grid-cols-4 gap-4 text-sm">
        <div className="text-gray-400">
          <span className="block">Open</span>
          <span className="text-white">${mockChartData[0]?.open.toLocaleString()}</span>
        </div>
        <div className="text-gray-400">
          <span className="block">High</span>
          <span className="text-green-400">${Math.max(...mockChartData.map(d => d.high)).toLocaleString()}</span>
        </div>
        <div className="text-gray-400">
          <span className="block">Low</span>
          <span className="text-red-400">${Math.min(...mockChartData.map(d => d.low)).toLocaleString()}</span>
        </div>
        <div className="text-gray-400">
          <span className="block">Volume</span>
          <span className="text-white">{mockChartData.reduce((sum, d) => sum + d.volume, 0).toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
};

export default TradingChart;
