import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import LoginScreen from './LoginScreen';

// Mock the useNavigate hook
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock the AuthContext
const mockLogin = vi.fn();
vi.mock('./contexts/AuthContext', () => ({
  useAuth: () => ({
    login: mockLogin,
  }),
}));

describe('LoginScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderLoginScreen = () => {
    return render(
      <MemoryRouter>
        <LoginScreen />
      </MemoryRouter>
    );
  };

  it('renders login form', () => {
    renderLoginScreen();
    
    expect(screen.getByText('Alpha Predator Bot')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /log in/i })).toBeInTheDocument();
  });

  it('handles successful login', async () => {
    mockLogin.mockResolvedValueOnce({ success: true });

    renderLoginScreen();
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'password123' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('handles login failure', async () => {
    mockLogin.mockResolvedValueOnce({ success: false, error: 'Invalid credentials' });

    renderLoginScreen();
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'wrongpassword' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword');
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
    });
  });

  it('handles login exception', async () => {
    mockLogin.mockRejectedValueOnce(new Error('Network error'));

    renderLoginScreen();
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'password123' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));
    
    await waitFor(() => {
      expect(screen.getByText('Login failed. Please try again.')).toBeInTheDocument();
    });
  });
});
