import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, fireEvent, act, within } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import DashboardScreen from './DashboardScreen';
import axiosInstance from './axiosInstance';

// Mock axiosInstance
vi.mock('./axiosInstance');

// Mock data - matches what DashboardScreen expects
const mockAiSignals = [
  {
    symbol: 'BTC/USDT',
    confidence: 0.85,
    deepseek: 'BUY',
    gemini: 'BUY',
    openai: 'BUY',
    final_decision: 'BUY'
  }
];

const mockLiveTrades = {
  trades: [
    {
      time: '2024-01-01T12:34:56Z',
      token: 'BTC',
      type: 'buy',
      quantity: 0.1,
      price: 50000,
      profit: 150.5
    }
  ]
};

const mockSummary = {
  totalTrades: 150,
  netProfit: 2500.75,
  topToken: {
    symbol: 'BTC',
    gainPercent: 15.5
  },
  worstToken: {
    symbol: 'ETH',
    lossPercent: 8.2
  }
};

describe('DashboardScreen', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Setup axiosInstance mock responses
    axiosInstance.get.mockImplementation((url) => {
      switch (url) {
        case '/api/alpha-bot/status':
          return Promise.resolve({ data: { status: 'stopped' } });
        case '/api/micro-bot/status':
          return Promise.resolve({ data: { status: 'stopped', stats: null } });
        case '/api/ai-decisions':
          return Promise.resolve({ data: mockAiSignals });
        case '/api/trades/summary':
          return Promise.resolve({ data: mockSummary });
        case '/api/trades/live':
          return Promise.resolve({ data: mockLiveTrades });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });
  });

  it('renders dashboard title', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );
    
    await waitFor(() => {
      expect(screen.getByText('Dashboard Summary')).toBeInTheDocument();
    });
  });

  it('fetches and displays summary data', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument();
      expect(screen.getByText('$2500.75')).toBeInTheDocument();
      expect(screen.getByText('15.5% gain')).toBeInTheDocument();
    });
  });

  it('fetches and displays live trades', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Live Trades')).toBeInTheDocument();
      expect(screen.getByText('buy')).toBeInTheDocument();
      expect(screen.getByText('50000.0000')).toBeInTheDocument(); // Removed $ sign
      expect(screen.getByText('07:34:56')).toBeInTheDocument(); // Changed from 12:34:56 to 07:34:56
    });
  });

  it('fetches and displays AI signals', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('🤖 AI Trade Summary')).toBeInTheDocument();
      expect(screen.getByText('BTC/USDT')).toBeInTheDocument();
    });
  });

  it('handles loading state', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );
    
    expect(screen.getByText('Loading dashboard summary...')).toBeInTheDocument();
  });

  it('handles error state for live trades', async () => {
    axiosInstance.get.mockImplementation((url) => {
      switch (url) {
        case '/api/alpha-bot/status':
          return Promise.resolve({ data: { status: 'stopped' } });
        case '/api/micro-bot/status':
          return Promise.resolve({ data: { status: 'stopped', stats: null } });
        case '/api/ai-decisions':
          return Promise.resolve({ data: mockAiSignals });
        case '/api/trades/summary':
          return Promise.resolve({ data: mockSummary });
        case '/api/trades/live':
          return Promise.reject(new Error('Failed to fetch'));
        default:
          return Promise.reject(new Error('Not found'));
      }
    });

    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load live trades.')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('displays no trades message when trades array is empty', async () => {
    axiosInstance.get.mockImplementation((url) => {
      switch (url) {
        case '/api/alpha-bot/status':
          return Promise.resolve({ data: { status: 'stopped' } });
        case '/api/micro-bot/status':
          return Promise.resolve({ data: { status: 'stopped', stats: null } });
        case '/api/ai-decisions':
          return Promise.resolve({ data: mockAiSignals });
        case '/api/trades/summary':
          return Promise.resolve({ data: mockSummary });
        case '/api/trades/live':
          return Promise.resolve({ data: { trades: [] } });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });

    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('No trades yet.')).toBeInTheDocument();
    }, { timeout: 3000 });
  });
});

describe('DashboardScreen Modal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    axiosInstance.get.mockImplementation((url) => {
      switch (url) {
        case '/alpha-bot/status':
          return Promise.resolve({ data: { status: 'stopped' } });
        case '/api/ai-decisions':
          return Promise.resolve({ data: mockAiSignals });
        case '/api/trades/summary':
          return Promise.resolve({ data: mockSummary });
        case '/api/trades/live':
          return Promise.resolve({ data: mockLiveTrades });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });
  });

  it('opens modal with signal details when clicking a signal', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('BTC/USDT')).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(screen.getByText('BTC/USDT').closest('div'));
    });

    expect(screen.getByText('Trade Signal Breakdown: BTC/USDT')).toBeInTheDocument();
    expect(screen.getByText(/Confidence: 85%/)).toBeInTheDocument();
  });

  it('closes modal when clicking close button', async () => {
    render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('BTC/USDT')).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.click(screen.getByText('BTC/USDT').closest('div'));
    });

    expect(screen.getByText('Trade Signal Breakdown: BTC/USDT')).toBeInTheDocument();

    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: /close/i }));
    });

    expect(screen.queryByText('Trade Signal Breakdown: BTC/USDT')).not.toBeInTheDocument();
  });
});

describe('DashboardScreen Auto-refresh', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    axiosInstance.get.mockImplementation((url) => {
      switch (url) {
        case '/alpha-bot/status':
          return Promise.resolve({ data: { status: 'stopped' } });
        case '/api/ai-decisions':
          return Promise.resolve({ data: mockAiSignals });
        case '/api/trades/summary':
          return Promise.resolve({ data: mockSummary });
        case '/api/trades/live':
          return Promise.resolve({ data: mockLiveTrades });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });
  });

  it('sets up intervals for auto-refresh', async () => {
    const setIntervalSpy = vi.spyOn(global, 'setInterval');
    
    const { unmount } = render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    // Wait for component to mount and set up intervals
    await waitFor(() => {
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 5000); // Alpha bot status
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 60000); // AI signals
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 30000); // Live trades
    });

    unmount();
    setIntervalSpy.mockRestore();
  });

  it('cleans up intervals on unmount', async () => {
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    
    const { unmount } = render(
      <MemoryRouter>
        <DashboardScreen />
      </MemoryRouter>
    );

    // Wait for component to mount
    await waitFor(() => {
      expect(screen.getByText('Dashboard Summary')).toBeInTheDocument();
    });

    // Unmount component
    unmount();

    // Verify intervals were cleared
    expect(clearIntervalSpy).toHaveBeenCalled();
    
    clearIntervalSpy.mockRestore();
  });
});
