{"$schema": "https://json.schemastore.org/package.json", "name": "my-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite --open", "build": "vite build", "preview": "vite preview", "start": "vite preview --port 3000", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@react-oauth/google": "^0.12.2", "@vitest/coverage-v8": "^3.2.2", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jsdom-global": "^3.0.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "react-tooltip": "^5.28.1", "recharts": "^2.15.3"}, "devDependencies": {"@testing-library/react": "^15.0.0", "@types/chart.js": "^2.9.41", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "happy-dom": "^17.6.3", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "vite": "^7.0.0", "vitest": "^3.2.2", "dotenv": "^16.4.5"}}