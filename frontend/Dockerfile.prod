# Multi-stage production Dockerfile for Alpha Predator Frontend
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (needed for build process)
RUN npm install

# Copy source code
COPY . .

# Accept build arguments
ARG VITE_BACKEND_URL=https://api.alphapredatorbot.xyz
ARG VITE_PUBLIC_TELEGRAM=https://t.me/alphapredatortrading
ARG VITE_PUBLIC_GITHUB=https://github.com/kryptomerch/alpha-predator-safe
ARG VITE_BRAND_NAME="Alpha Predator Bot"

# Set environment variables for build
ENV NODE_ENV=production
ENV VITE_BACKEND_URL=${VITE_BACKEND_URL}
ENV VITE_PUBLIC_TELEGRAM=${VITE_PUBLIC_TELEGRAM}
ENV VITE_PUBLIC_GITHUB=${VITE_PUBLIC_GITHUB}
ENV VITE_BRAND_NAME=${VITE_BRAND_NAME}
ENV VITE_GOOGLE_CLIENT_ID=450152610705-sicd663g2b2dehgqgdi1i1mj5m85hvqs.apps.googleusercontent.com
ENV VITE_GOOGLE_REDIRECT_URI=/auth/callback
ENV VITE_JWT_SECRET_KEY=super-secret
ENV VITE_GITHUB_OAUTH_TOKEN=****************************************
ENV VITE_USE_REAL_NEWS=True
ENV VITE_FALLBACK_TO_OPENAI=True
ENV VITE_TRADE_INTERVAL_SECONDS=60
ENV VITE_MAX_BUY=5
ENV VITE_STOP_LOSS=0.10
ENV VITE_TAKE_PROFIT=0.20
ENV VITE_MIN_24H_VOLUME_USD=1000000
ENV VITE_TRADING_MODE=VIEW_ONLY
ENV VITE_KUCOIN_API_KEY=682f92ad92fc6e0001dc1581
ENV VITE_KUCOIN_API_SECRET=b7863776-ab5b-450e-b330-0aea310a9ace
ENV VITE_API_BASE_URL=/api

# Build the application
RUN npm run build

# Production stage with Nginx
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx configuration
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Create nginx directories and set permissions
RUN mkdir -p /var/cache/nginx/client_temp && \
    mkdir -p /var/cache/nginx/proxy_temp && \
    mkdir -p /var/cache/nginx/fastcgi_temp && \
    mkdir -p /var/cache/nginx/uwsgi_temp && \
    mkdir -p /var/cache/nginx/scgi_temp && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chmod -R 755 /usr/share/nginx/html

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start nginx (run as root to bind to port 80)
CMD ["nginx", "-g", "daemon off;"]
