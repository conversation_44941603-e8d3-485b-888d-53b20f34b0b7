# Build Stage
FROM node:20-alpine AS build

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install ALL dependencies (including dev dependencies needed for build)
RUN npm ci --legacy-peer-deps

# Copy configuration files
COPY vite.config.js ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY index.html ./

# Copy source code
COPY src/ ./src/
COPY public/ ./public/

# Set build environment and API URL
ARG NODE_ENV=production
ARG VITE_API_URL=/api
ENV NODE_ENV=$NODE_ENV
ENV VITE_API_URL=$VITE_API_URL

# Build the application
RUN npm run build

# Production Stage
FROM nginx:alpine

# Copy custom nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built files to nginx public directory
COPY --from=build /app/dist /usr/share/nginx/html

# Create error page
RUN echo '<html><head><title>Server Error</title></head><body><h1>Server Error</h1><p>The server encountered a temporary error. Please try again later.</p></body></html>' > /usr/share/nginx/html/50x.html

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
