#!/usr/bin/env python3
"""
Comprehensive Test for All AI Providers
Tests <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and DeepSeek integration
"""

import sys
import os
sys.path.append('backend')

from backend.ai_clients.openai_client import call_openai
from backend.ai_clients.claude_client import call_claude, get_claude_cost_estimate
from backend.ai_clients.real_gemini_client import call_gemini, get_gemini_cost_estimate
from backend.ai_clients.real_deepseek_client import call_deepseek, get_deepseek_cost_estimate
from backend.ai_clients.model_selector import TaskType, Urgency, Complexity

def test_individual_providers():
    """Test each AI provider individually"""
    print("🤖 Testing Individual AI Providers")
    print("=" * 60)
    
    test_prompt = """
    Bitcoin Analysis:
    - Price: $45,000 (up 5% today)
    - RSI: 65 (approaching overbought)
    - Volume: High (2x average)
    - News: Positive institutional adoption
    - Support: $43,000
    - Resistance: $47,000
    
    Should I buy, sell, or hold Bitcoin?
    """
    
    providers = [
        ("OpenAI GPT-4o-mini", call_openai, lambda: 0.0003),  # Estimated cost
        ("Claude 3.5 Haiku", call_claude, lambda: get_claude_cost_estimate(200, 100, "haiku")),
        ("Gemini 1.5 Flash", call_gemini, lambda: get_gemini_cost_estimate(200, 100, "flash")),
        ("DeepSeek V3", call_deepseek, lambda: get_deepseek_cost_estimate(200, 100)),
    ]
    
    results = []
    total_cost = 0.0
    
    for name, call_fn, cost_fn in providers:
        print(f"\n🔍 Testing {name}")
        print("-" * 40)
        
        try:
            # Test the provider
            result = call_fn(test_prompt, TaskType.TRADE_DECISION, Urgency.MEDIUM, Complexity.SIMPLE)
            cost = cost_fn()
            total_cost += cost
            
            if isinstance(result, dict):
                decision = result.get("decision", "UNKNOWN")
                confidence = result.get("confidence", 0.0)
                reason = result.get("reason", "No reason provided")[:100]
                
                status = "✅ SUCCESS" if decision in ["BUY", "SELL", "HOLD"] else "⚠️ PARTIAL"
                
                print(f"Status: {status}")
                print(f"Decision: {decision}")
                print(f"Confidence: {confidence}%")
                print(f"Reason: {reason}...")
                print(f"Cost: ${cost:.6f}")
                
                results.append({
                    "provider": name,
                    "status": "success" if "SUCCESS" in status else "partial",
                    "decision": decision,
                    "confidence": confidence,
                    "cost": cost
                })
            else:
                print(f"❌ FAILED - Invalid response format: {type(result)}")
                results.append({
                    "provider": name,
                    "status": "failed",
                    "decision": "ERROR",
                    "confidence": 0.0,
                    "cost": cost
                })
                
        except Exception as e:
            print(f"❌ FAILED - Exception: {e}")
            results.append({
                "provider": name,
                "status": "failed",
                "decision": "ERROR",
                "confidence": 0.0,
                "cost": 0.0
            })
    
    print(f"\n💰 Total Cost for All Providers: ${total_cost:.6f}")
    return results

def test_cost_comparison():
    """Compare costs between all providers"""
    print("\n💸 Cost Comparison Analysis")
    print("=" * 60)
    
    # Simulate daily usage
    daily_calls = 1000
    
    costs = {
        "OpenAI (gpt-4o-mini)": 0.0003,
        "Claude 3.5 Haiku": get_claude_cost_estimate(200, 100, "haiku"),
        "Gemini 1.5 Flash": get_gemini_cost_estimate(200, 100, "flash"),
        "DeepSeek V3": get_deepseek_cost_estimate(200, 100),
    }
    
    print(f"📊 Cost per call (200 input + 100 output tokens):")
    for provider, cost in costs.items():
        daily_cost = cost * daily_calls
        monthly_cost = daily_cost * 30
        print(f"  {provider}: ${cost:.6f} per call")
        print(f"    Daily ({daily_calls} calls): ${daily_cost:.2f}")
        print(f"    Monthly: ${monthly_cost:.2f}")
        print()
    
    # Find cheapest
    cheapest = min(costs.items(), key=lambda x: x[1])
    most_expensive = max(costs.items(), key=lambda x: x[1])
    
    savings = (most_expensive[1] - cheapest[1]) * daily_calls * 30
    savings_pct = ((most_expensive[1] - cheapest[1]) / most_expensive[1]) * 100
    
    print(f"🏆 Cheapest: {cheapest[0]} (${cheapest[1]:.6f})")
    print(f"💸 Most Expensive: {most_expensive[0]} (${most_expensive[1]:.6f})")
    print(f"💵 Monthly Savings: ${savings:.2f} ({savings_pct:.1f}%)")

def test_provider_diversity():
    """Test how different providers respond to the same prompt"""
    print("\n🎯 Provider Diversity Analysis")
    print("=" * 60)
    
    test_scenarios = [
        {
            "name": "Bullish Scenario",
            "prompt": "Bitcoin broke above $50k resistance with massive volume. RSI at 75. Should I buy?",
            "expected": "BUY"
        },
        {
            "name": "Bearish Scenario", 
            "prompt": "Bitcoin crashed below $40k support. RSI at 25. High selling pressure. Should I sell?",
            "expected": "SELL"
        },
        {
            "name": "Neutral Scenario",
            "prompt": "Bitcoin trading sideways at $45k. Low volume. Mixed signals. What should I do?",
            "expected": "HOLD"
        }
    ]
    
    providers = [
        ("OpenAI", call_openai),
        ("Claude", call_claude),
        ("Gemini", call_gemini),
        ("DeepSeek", call_deepseek),
    ]
    
    for scenario in test_scenarios:
        print(f"\n📈 {scenario['name']}")
        print(f"Prompt: {scenario['prompt']}")
        print(f"Expected: {scenario['expected']}")
        print("-" * 40)
        
        decisions = []
        for provider_name, call_fn in providers:
            try:
                result = call_fn(scenario['prompt'], TaskType.TRADE_DECISION, Urgency.MEDIUM, Complexity.SIMPLE)
                decision = result.get("decision", "UNKNOWN") if isinstance(result, dict) else "ERROR"
                confidence = result.get("confidence", 0.0) if isinstance(result, dict) else 0.0
                
                decisions.append(decision)
                match = "✅" if decision == scenario['expected'] else "❌"
                
                print(f"  {provider_name}: {decision} ({confidence:.1f}%) {match}")
                
            except Exception as e:
                print(f"  {provider_name}: ERROR - {e}")
                decisions.append("ERROR")
        
        # Calculate consensus
        unique_decisions = set(d for d in decisions if d != "ERROR")
        consensus = len(unique_decisions) == 1 if unique_decisions else False
        
        print(f"Consensus: {'✅ YES' if consensus else '❌ NO'} ({len(unique_decisions)} different decisions)")

def main():
    """Run all tests"""
    print("🚀 Comprehensive AI Provider Test Suite")
    print("=" * 80)
    
    try:
        # Test individual providers
        results = test_individual_providers()
        
        # Test cost comparison
        test_cost_comparison()
        
        # Test provider diversity
        test_provider_diversity()
        
        # Summary
        print("\n📊 Test Summary")
        print("=" * 60)
        
        successful = sum(1 for r in results if r['status'] == 'success')
        total = len(results)
        
        print(f"✅ Successful Providers: {successful}/{total}")
        print(f"💰 Total Test Cost: ${sum(r['cost'] for r in results):.6f}")
        
        if successful >= 2:
            print("🎯 Multi-provider AI system is ready for production!")
        else:
            print("⚠️ Need to configure more API keys for redundancy")
            
        # Show which providers need API keys
        print("\n🔑 API Key Requirements:")
        print("  OPENAI_API_KEY - OpenAI GPT models")
        print("  ANTHROPIC_API_KEY - Claude models") 
        print("  GOOGLE_API_KEY or GEMINI_API_KEY - Gemini models")
        print("  DEEPSEEK_API_KEY - DeepSeek models")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
