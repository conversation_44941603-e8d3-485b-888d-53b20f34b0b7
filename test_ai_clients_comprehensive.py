#!/usr/bin/env python3
"""
Comprehensive AI Clients Test
Tests all AI providers to ensure they're working correctly and not just falling back
"""

import sys
import os
sys.path.append('./backend')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('./backend/.env')

import asyncio
import logging
from backend.ai_clients.ai_request_manager import get_ai_decision
from backend.ai_clients.openai_client import call_openai
from backend.ai_clients.real_gemini_client import call_gemini
from backend.ai_clients.real_deepseek_client import call_deepseek
from backend.ai_clients.claude_client import call_claude

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_individual_ai_clients():
    """Test each AI client individually"""
    print("🧠 TESTING INDIVIDUAL AI CLIENTS")
    print("=" * 60)
    
    test_prompt = """
    Analyze BTC-USDT trading opportunity:
    - Current Price: $45,000
    - RSI: 65 (slightly overbought)
    - MACD: Bullish crossover
    - Volume: 20% above average
    - News Sentiment: Positive (institutional adoption)
    
    Provide JSON response with decision (BUY/SELL/HOLD), confidence (0-100), and reason.
    """
    
    clients = [
        ("OpenAI", call_openai),
        ("Gemini", call_gemini),
        ("Claude", call_claude),
        ("DeepSeek", call_deepseek)
    ]
    
    results = {}
    
    for name, client_fn in clients:
        print(f"\n🔍 Testing {name}...")
        try:
            result = client_fn(test_prompt)
            
            if result and isinstance(result, dict):
                decision = result.get("decision", "UNKNOWN")
                confidence = result.get("confidence", 0)
                reason = result.get("reason", "No reason provided")[:100]
                
                if decision == "ERROR":
                    print(f"❌ {name}: ERROR - {reason}")
                    results[name] = "FAILED"
                elif decision in ["BUY", "SELL", "HOLD"]:
                    print(f"✅ {name}: {decision} (confidence: {confidence}%) - {reason}")
                    results[name] = "SUCCESS"
                else:
                    print(f"⚠️ {name}: Invalid decision format - {decision}")
                    results[name] = "INVALID"
            else:
                print(f"❌ {name}: Invalid response format")
                results[name] = "FAILED"
                
        except Exception as e:
            print(f"❌ {name}: Exception - {str(e)}")
            results[name] = "EXCEPTION"
    
    return results

def test_ai_request_manager():
    """Test the AI request manager with multiple providers"""
    print("\n🤖 TESTING AI REQUEST MANAGER")
    print("=" * 60)
    
    test_prompt = """
    TOKEN: ETH-USDT
    CURRENT PRICE: $2,800 USDT
    PRICE CHANGE (24H): +5.2%
    MARKET SENTIMENT SCORE: 75% bullish
    VOLUME SURGE: 1.8x average
    CHART ANALYSIS: BULLISH (breakout pattern)
    
    TRADITIONAL STRATEGY SIGNALS:
    MA Crossover: BUY
    RSI: 58 (neutral)
    MACD: BUY
    Bollinger Bands: BUY
    
    Provide trading decision with confidence score.
    """
    
    try:
        result = get_ai_decision(test_prompt)
        
        if result and isinstance(result, dict):
            provider = result.get("provider", "Unknown")
            response = result.get("response", {})
            breakdown = result.get("breakdown", [])
            
            decision = response.get("decision", "UNKNOWN")
            confidence = response.get("confidence", 0)
            reason = response.get("reason", "No reason provided")
            
            print(f"🎯 Final Decision: {decision}")
            print(f"📊 Confidence: {confidence}%")
            print(f"🤖 Provider: {provider}")
            print(f"💭 Reason: {reason}")
            
            if breakdown:
                print(f"\n📋 Provider Breakdown ({len(breakdown)} providers responded):")
                for item in breakdown:
                    prov = item.get("provider", "Unknown")
                    dec = item.get("decision", "UNKNOWN")
                    conf = item.get("confidence", 0)
                    weight = item.get("weight", 0)
                    print(f"  • {prov}: {dec} (confidence: {conf}%, weight: {weight})")
                
                return len(breakdown) > 0  # Success if at least one provider responded
            else:
                print("⚠️ No provider breakdown available")
                return decision != "HOLD" or confidence > 0
        else:
            print("❌ Invalid response from AI request manager")
            return False
            
    except Exception as e:
        print(f"❌ AI Request Manager Exception: {e}")
        return False

def check_api_keys():
    """Check if API keys are properly configured"""
    print("\n🔑 CHECKING API KEY CONFIGURATION")
    print("=" * 60)
    
    api_keys = {
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
        "GEMINI_API_KEY": os.getenv("GEMINI_API_KEY"),
        "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
        "DEEPSEEK_API_KEY": os.getenv("DEEPSEEK_API_KEY")
    }
    
    configured_keys = 0
    
    for key_name, key_value in api_keys.items():
        if key_value and not key_value.startswith("YOUR_") and len(key_value) > 10:
            print(f"✅ {key_name}: Configured ({key_value[:10]}...)")
            configured_keys += 1
        else:
            print(f"❌ {key_name}: Not configured or invalid")
    
    print(f"\n📊 Summary: {configured_keys}/4 API keys properly configured")
    return configured_keys

def main():
    """Run comprehensive AI clients test"""
    print("🚀 COMPREHENSIVE AI CLIENTS TEST")
    print("=" * 80)
    
    # Check API keys first
    configured_keys = check_api_keys()
    
    # Test individual clients
    individual_results = test_individual_ai_clients()
    
    # Test AI request manager
    manager_success = test_ai_request_manager()
    
    # Summary
    print("\n📊 FINAL SUMMARY")
    print("=" * 60)
    
    successful_clients = sum(1 for result in individual_results.values() if result == "SUCCESS")
    total_clients = len(individual_results)
    
    print(f"🔑 API Keys Configured: {configured_keys}/4")
    print(f"✅ Individual Clients Working: {successful_clients}/{total_clients}")
    print(f"🤖 AI Request Manager: {'✅ Working' if manager_success else '❌ Failed'}")
    
    print("\n📋 Individual Client Results:")
    for client, status in individual_results.items():
        status_emoji = "✅" if status == "SUCCESS" else "❌"
        print(f"  {status_emoji} {client}: {status}")
    
    # Overall assessment
    if successful_clients >= 2 and manager_success:
        print("\n🎉 OVERALL STATUS: GOOD - Multiple AI providers working")
        return True
    elif successful_clients >= 1:
        print("\n⚠️ OVERALL STATUS: PARTIAL - Some AI providers working")
        return True
    else:
        print("\n❌ OVERALL STATUS: FAILED - No AI providers working properly")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
