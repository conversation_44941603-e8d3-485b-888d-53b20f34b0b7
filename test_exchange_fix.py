#!/usr/bin/env python3
"""
Test script to verify the new exchange availability checking mechanism.
This will test the fix for the Binance API errors.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.exchange_data import (
    check_token_on_binance_via_coingecko,
    is_token_available_on_exchange,
    fetch_binance_price,
    get_prices_from_all_exchanges
)
import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_exchange_mechanism():
    """Test the new exchange availability checking mechanism."""
    
    # Test tokens - some should be on Binance, some shouldn't
    test_tokens = [
        "BTC-USDT",    # Should be available on Binance
        "ETH-USDT",    # Should be available on Binance
        "NODE-USDT",   # Should NOT be available on Binance (this was causing errors)
        "MC-USDT",     # Should NOT be available on Binance
        "ZEND-USDT",   # Should NOT be available on Binance
    ]
    
    logger.info("🧪 Testing exchange availability checking mechanism...")
    
    for token in test_tokens:
        logger.info(f"\n--- Testing {token} ---")
        
        # Test Binance availability check
        binance_available = check_token_on_binance_via_coingecko(token)
        logger.info(f"Binance availability for {token}: {binance_available}")
        
        # Test KuCoin availability (should always be True)
        kucoin_available = is_token_available_on_exchange(token, "KuCoin")
        logger.info(f"KuCoin availability for {token}: {kucoin_available}")
        
        # Test Binance price fetch (should only attempt if available)
        binance_price = fetch_binance_price(token)
        logger.info(f"Binance price for {token}: {binance_price}")
        
        # Test unified price fetcher
        all_prices = await get_prices_from_all_exchanges(token)
        logger.info(f"All exchange prices for {token}: {all_prices}")
        
        logger.info("-" * 50)

if __name__ == "__main__":
    logger.info("🚀 Starting exchange mechanism test...")
    asyncio.run(test_exchange_mechanism())
    logger.info("✅ Test completed!")
