version: '3.9'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.amd64
      args:
        - NODE_ENV=production
        - VITE_API_URL=/api
      platforms:
        - linux/amd64
    image: kryptomerch/alpha-frontend:amd64
    container_name: alpha_frontend_amd64
    ports:
      - "39882:80"  # maps to Flux port 39882
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - alpha-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    image: kryptomerch/alpha-backend:latest
    container_name: alpha_backend_amd64
    ports:
      - "33903:3005"  # maps to Flux port 33903
    environment:
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - PORT=3005
      - HOST=0.0.0.0
      - CORS_ORIGINS=https://alphapredatorbot.xyz,https://www.alphapredatorbot.xyz,https://alphabot_39882.app.runonflux.io,http://alphabot_39882.app.runonflux.io
    volumes:
      - ./backend/data:/app/data
    restart: unless-stopped
    networks:
      - alpha-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  alpha-network:
    driver: bridge
