#!/usr/bin/env python3
"""
Advanced Rate Limiting Test
Tests the enhanced API optimization manager with sophisticated rate limiting,
precise timing controls, and automatic waiting mechanisms.
"""

import asyncio
import logging
import sys
import os
import time

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.api_optimization_manager import api_optimizer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_precise_rate_limiting():
    """Test precise rate limiting with actual API limits"""
    
    print("🔧 Testing Advanced Rate Limiting")
    print("=" * 50)
    
    # Test CoinGecko rate limiting (10 calls per minute)
    print("\n1. Testing CoinGecko Rate Limiting (10 calls/minute)...")
    
    api_name = 'coingecko'
    test_symbol = 'BTC-USDT'
    
    # Make rapid calls to test rate limiting
    for i in range(15):  # Try to make 15 calls (should hit limit at 10)
        start_time = time.time()
        
        # Check if we can make the call
        can_call = api_optimizer.can_make_api_call(api_name, test_symbol)
        should_skip = api_optimizer.should_skip_api_call(api_name, test_symbol)
        wait_time = api_optimizer.get_time_until_next_call(api_name)
        
        print(f"  Call {i+1:2d}: can_call={can_call}, should_skip={should_skip}, wait_time={wait_time:.1f}s")
        
        if not should_skip:
            # Record the API call
            api_optimizer.record_api_call(api_name)
            optimal_delay = api_optimizer.get_optimal_delay(api_name)
            print(f"    ✅ Call made, optimal_delay={optimal_delay}s")
        else:
            print(f"    ❌ Call skipped due to rate limiting")
            break
        
        # Small delay between attempts
        await asyncio.sleep(0.1)
    
    print(f"\n2. Testing Wait Mechanism...")
    
    # Test the wait mechanism
    wait_time = api_optimizer.get_time_until_next_call(api_name)
    if wait_time > 0:
        print(f"  Need to wait {wait_time:.1f}s for next call")
        
        # Test waiting with timeout
        print("  Testing wait_for_api_availability with 10s timeout...")
        start_wait = time.time()
        can_wait = await api_optimizer.wait_for_api_availability(api_name, max_wait=10.0)
        actual_wait = time.time() - start_wait
        
        print(f"  Wait result: {can_wait}, actual wait time: {actual_wait:.1f}s")
        
        if can_wait:
            # Try making a call after waiting
            can_call_after_wait = api_optimizer.can_make_api_call(api_name, test_symbol)
            print(f"  Can make call after waiting: {can_call_after_wait}")

async def test_different_api_limits():
    """Test different APIs with different rate limits"""
    
    print("\n3. Testing Different API Rate Limits...")
    print("-" * 40)
    
    apis_to_test = ['coingecko', 'kucoin', 'tokenmetrics']
    
    for api_name in apis_to_test:
        print(f"\n  Testing {api_name}:")
        
        # Get API configuration
        rate_config = api_optimizer.rate_limits.get(api_name, {})
        calls_per_minute = rate_config.get('calls_per_minute', 'Unknown')
        delay = rate_config.get('delay', 'Unknown')
        reset_window = rate_config.get('reset_window', 'Unknown')
        
        print(f"    Config: {calls_per_minute} calls/min, {delay}s delay, {reset_window}s window")
        
        # Test a few calls
        for i in range(3):
            can_call = api_optimizer.can_make_api_call(api_name)
            if can_call:
                api_optimizer.record_api_call(api_name)
                print(f"    Call {i+1}: ✅ Success")
            else:
                wait_time = api_optimizer.get_time_until_next_call(api_name)
                print(f"    Call {i+1}: ❌ Rate limited, wait {wait_time:.1f}s")
            
            await asyncio.sleep(0.1)

async def test_token_filtering_integration():
    """Test token filtering integration with rate limiting"""
    
    print("\n4. Testing Token Filtering + Rate Limiting Integration...")
    print("-" * 50)
    
    # Test with mixed valid/invalid tokens
    test_tokens = [
        "BTC-USDT",
        "ETH-USDT", 
        "INVALID-USDT",
        "S-USDT",
        "ADA-USDT"
    ]
    
    print(f"  Testing tokens: {test_tokens}")
    
    for token in test_tokens:
        is_supported = api_optimizer.is_token_supported(token)
        is_failed = api_optimizer.is_token_failed(token)
        can_call = api_optimizer.can_make_api_call('coingecko', token)
        
        print(f"    {token}: supported={is_supported}, failed={is_failed}, can_call={can_call}")
        
        if can_call:
            api_optimizer.record_api_call('coingecko')

async def test_realistic_scenario():
    """Test a realistic trading scenario with multiple API calls"""
    
    print("\n5. Testing Realistic Trading Scenario...")
    print("-" * 40)
    
    # Simulate a trading bot making various API calls
    trading_tokens = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
    
    print(f"  Simulating trading analysis for: {trading_tokens}")
    
    for round_num in range(3):
        print(f"\n  Round {round_num + 1}:")
        
        for token in trading_tokens:
            # Check if we can make CoinGecko call
            if api_optimizer.can_make_api_call('coingecko', token):
                api_optimizer.record_api_call('coingecko')
                print(f"    ✅ CoinGecko data fetched for {token}")
            else:
                wait_time = api_optimizer.get_time_until_next_call('coingecko')
                print(f"    ⏳ CoinGecko rate limited for {token}, wait {wait_time:.1f}s")
            
            # Check if we can make KuCoin call
            if api_optimizer.can_make_api_call('kucoin'):
                api_optimizer.record_api_call('kucoin')
                print(f"    ✅ KuCoin data fetched for {token}")
            else:
                wait_time = api_optimizer.get_time_until_next_call('kucoin')
                print(f"    ⏳ KuCoin rate limited for {token}, wait {wait_time:.1f}s")
            
            await asyncio.sleep(0.5)  # Simulate processing time

async def main():
    """Run all advanced rate limiting tests"""
    
    print("🧪 Advanced Rate Limiting Test Suite")
    print("=" * 60)
    print("Testing sophisticated rate limiting with precise timing controls")
    print("and automatic waiting mechanisms based on actual API limits.")
    print()
    
    try:
        # Test precise rate limiting
        await test_precise_rate_limiting()
        
        # Test different API limits
        await test_different_api_limits()
        
        # Test token filtering integration
        await test_token_filtering_integration()
        
        # Test realistic scenario
        await test_realistic_scenario()
        
        print("\n🎉 All advanced rate limiting tests completed!")
        print("\nKey Features Demonstrated:")
        print("✅ Precise rate limiting based on actual API limits")
        print("✅ Automatic waiting mechanisms with timeout controls")
        print("✅ Token filtering integration to prevent unnecessary calls")
        print("✅ Different rate limits for different APIs")
        print("✅ Realistic trading scenario simulation")
        print("\nThe system now properly handles API rate limits by:")
        print("- Tracking exact call counts within sliding time windows")
        print("- Calculating precise wait times until next allowed call")
        print("- Automatically waiting for API availability with timeouts")
        print("- Filtering unsupported tokens before making API calls")
        print("- Using different rate limits for different API providers")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
