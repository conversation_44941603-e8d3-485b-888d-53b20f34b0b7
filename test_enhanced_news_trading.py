#!/usr/bin/env python3
"""
Test Enhanced News-to-Trading Pipeline with Breaking News Priority
"""

import asyncio
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

async def test_enhanced_news_trading_pipeline():
    """Test the complete enhanced news-to-trading pipeline"""
    print("🚀 Testing Enhanced News-to-Trading Pipeline with Breaking News Priority\n")
    
    # Import the enhanced news sentiment system
    try:
        from backend.enhanced_news_sentiment import analyze_breaking_news  # type: ignore
        from backend.telegram_utils import send_telegram_message  # type: ignore
    except ImportError:
        print("❌ Import error: Running from backend directory")
        # Fallback imports
        import enhanced_news_sentiment  # type: ignore
        import telegram_utils  # type: ignore
        analyze_breaking_news = enhanced_news_sentiment.analyze_breaking_news
        send_telegram_message = telegram_utils.send_telegram_message
    
    # Critical breaking news scenarios that should trigger immediate trading signals
    breaking_news_scenarios = [
        {
            'headline': 'BREAKING: Nuclear war threat escalates as military conflict spreads globally',
            'content': 'Global markets panic as war fears grip investors, flight to safety assets begins',
            'expected_impact': 'CRITICAL',
            'expected_sentiment': 'BEARISH'
        },
        {
            'headline': 'SEC announces emergency cryptocurrency ban across all US exchanges',
            'content': 'Regulatory crackdown forces immediate compliance, major exchanges halt trading',
            'expected_impact': 'CRITICAL',
            'expected_sentiment': 'BEARISH'
        },
        {
            'headline': 'Federal Reserve emergency meeting: Interest rates crash to zero amid banking crisis',
            'content': 'Emergency monetary policy response as major banks face liquidity crisis',
            'expected_impact': 'CRITICAL',
            'expected_sentiment': 'BEARISH'
        },
        {
            'headline': 'Bitcoin ETF approved by SEC, institutional adoption surge begins',
            'content': 'Major corporations announce treasury allocation to crypto, mainstream adoption accelerates',
            'expected_impact': 'HIGH',
            'expected_sentiment': 'BULLISH'
        },
        {
            'headline': 'NASDAQ tech rally drives crypto correlation, risk-on sentiment returns',
            'content': 'Stock market gains fuel crypto momentum as growth assets rally together',
            'expected_impact': 'HIGH',
            'expected_sentiment': 'BULLISH'
        }
    ]
    
    print("=" * 80)
    print("📰 ENHANCED NEWS SENTIMENT ANALYSIS")
    print("=" * 80)
    
    trading_signals = []
    critical_alerts = []
    
    for i, scenario in enumerate(breaking_news_scenarios, 1):
        print(f"\n🚨 BREAKING NEWS SCENARIO {i}:")
        print(f"Headline: {scenario['headline']}")
        print(f"Content: {scenario['content'][:100]}...")
        
        # Analyze breaking news
        result = await analyze_breaking_news(scenario['headline'], scenario['content'])
        
        # Display analysis results
        sentiment_data = result['sentiment_analysis']
        urgency_data = result['urgency_analysis']
        correlation_data = sentiment_data['correlation_data']
        
        print(f"\n📊 ANALYSIS RESULTS:")
        print(f"   🎯 Sentiment: {sentiment_data['sentiment']}")
        print(f"   📈 Confidence: {sentiment_data['confidence']:.1%}")
        print(f"   🚨 Urgency Level: {sentiment_data['urgency_data']['urgency_level']}")
        print(f"   ⚡ Priority: {urgency_data['priority']}")
        print(f"   📊 Impact Weight: {sentiment_data['urgency_data']['impact_weight']:.1f}x")
        print(f"   🔗 Market Correlation: {correlation_data['correlation_type']}")
        print(f"   📈 Trade Eligible: {urgency_data['trade_eligible']}")
        print(f"   🎯 Urgency Score: {urgency_data['urgency_score']:.2f}")
        
        # Generate trading signal if eligible
        if urgency_data['trade_eligible']:
            # Determine trade action based on sentiment
            if sentiment_data['sentiment'] == 'BULLISH':
                trade_action = 'BUY'
                signal_emoji = '🚀'
            elif sentiment_data['sentiment'] == 'BEARISH':
                trade_action = 'SELL'
                signal_emoji = '📉'
            else:
                trade_action = 'HOLD'
                signal_emoji = '📊'
            
            # Create trading signal
            trading_signal = {
                'symbol': 'BTC-USDT',  # Default to BTC for breaking news
                'action': trade_action,
                'signal_strength': urgency_data['urgency_score'],
                'confidence': sentiment_data['confidence'],
                'priority': urgency_data['priority'],
                'news_headline': scenario['headline'][:100],
                'urgency_level': sentiment_data['urgency_data']['urgency_level'],
                'correlation_type': correlation_data['correlation_type'],
                'timestamp': datetime.now().isoformat()
            }
            
            trading_signals.append(trading_signal)
            
            print(f"\n{signal_emoji} TRADING SIGNAL GENERATED:")
            print(f"   Symbol: {trading_signal['symbol']}")
            print(f"   Action: {trading_signal['action']}")
            print(f"   Signal Strength: {trading_signal['signal_strength']:.2f}")
            print(f"   Priority: {trading_signal['priority']}")
            
            # Mark as critical alert if immediate priority
            if urgency_data['priority'] == 'IMMEDIATE':
                critical_alerts.append(trading_signal)
                print(f"   🚨 CRITICAL ALERT: Immediate action required!")
        
        print("-" * 80)
    
    # Generate comprehensive trading signals summary
    print("\n" + "=" * 80)
    print("📈 TRADING SIGNALS SUMMARY")
    print("=" * 80)
    
    if trading_signals:
        print(f"✅ Total Signals Generated: {len(trading_signals)}")
        print(f"🚨 Critical Alerts: {len(critical_alerts)}")
        
        # Count by action type
        buy_signals = sum(1 for s in trading_signals if s['action'] == 'BUY')
        sell_signals = sum(1 for s in trading_signals if s['action'] == 'SELL')
        hold_signals = sum(1 for s in trading_signals if s['action'] == 'HOLD')
        
        print(f"🚀 BUY Signals: {buy_signals}")
        print(f"📉 SELL Signals: {sell_signals}")
        print(f"📊 HOLD Signals: {hold_signals}")
        
        # Calculate average signal strength
        avg_strength = sum(s['signal_strength'] for s in trading_signals) / len(trading_signals)
        print(f"📊 Average Signal Strength: {avg_strength:.2f}")
        
        # Send Telegram alert for trading signals
        print(f"\n📱 Sending Telegram Trading Alerts...")
        
        # Create comprehensive alert message
        alert_message = f"""
🚨 **ENHANCED NEWS TRADING ALERTS**

**Analysis Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Breaking News Analyzed**: {len(breaking_news_scenarios)}
**Trading Signals Generated**: {len(trading_signals)}
**Critical Alerts**: {len(critical_alerts)}

**Signal Breakdown**:
🚀 BUY Signals: {buy_signals}
📉 SELL Signals: {sell_signals}
📊 Average Strength: {avg_strength:.2f}

**Key Features**:
✅ Breaking news priority weighting
✅ Market correlation analysis  
✅ Political/war/crisis detection
✅ Crypto regulation monitoring
✅ Stock market correlation tracking

#BreakingNews #TradingSignals #EnhancedAnalysis
"""
        
        result = await send_telegram_message(alert_message)
        print(f"📱 Telegram alert sent: {'SUCCESS' if result else 'FAILED'}")
    else:
        print("ℹ️ No trading signals generated")
    
    print(f"\n🎯 Enhanced news-to-trading pipeline test completed!")
    return True

if __name__ == "__main__":
    asyncio.run(test_enhanced_news_trading_pipeline())
