name: Build and Push Frontend Docker Image

on:
  push:
    paths:
      - 'frontend/**'
      - '.github/workflows/frontend.yml'
    branches:
      - main

jobs:
  build-and-push:
    name: Build and Push Frontend
    runs-on: ubuntu-latest

    steps:
      - name: ⬇️ Checkout repository
        uses: actions/checkout@v3

      - name: 🔐 Log in to Docker Hub via CLI
        run: echo "${{ secrets.DOCKERHUB_TOKEN }}" | docker login -u "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 📦 Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-frontend-build-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-frontend-build-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: kryptomerch/alpha-frontend:latest
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      - name: ✅ Build completed
        run: echo "Frontend image pushed successfully!"