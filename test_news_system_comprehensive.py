#!/usr/bin/env python3
"""
Comprehensive News System Test
"""

import asyncio
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Import modules
from dotenv import load_dotenv
import requests
import json

# Load environment variables
load_dotenv('backend/.env')

async def test_cryptopanic_news():
    """Test CryptoPanic news fetching"""
    print("📰 Testing CryptoPanic News API...")
    try:
        # Direct API test
        api_key = os.getenv("CRYPTOPANIC_API_KEY")
        if not api_key:
            print("❌ CryptoPanic API key not found")
            return False
        
        url = f"https://cryptopanic.com/api/v1/posts/?auth_token={api_key}&public=true&kind=news&filter=hot"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            news_items = data.get('results', [])
            print(f"✅ Fetched {len(news_items)} news articles")
            
            # Show first 3 articles
            for i, article in enumerate(news_items[:3], 1):
                title = article.get('title', 'No title')
                print(f"   {i}. {title[:80]}...")
            
            return news_items
        else:
            print(f"❌ API request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ CryptoPanic test failed: {e}")
        return False

async def test_sentiment_analysis(news_text):
    """Test sentiment analysis"""
    print("\n🧠 Testing Sentiment Analysis...")
    try:
        # Import sentiment analysis
        try:
            from openai import OpenAI  # type: ignore
        except ImportError:
            print("❌ OpenAI library not installed")
            return False
        
        # Get OpenAI API key
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ OpenAI API key not found")
            return False
        
        client = OpenAI(api_key=api_key)
        
        prompt = f"""
        Analyze the sentiment of this crypto news headline and provide a JSON response:
        
        Headline: "{news_text}"
        
        Respond with JSON format:
        {{
            "sentiment": "BULLISH|BEARISH|NEUTRAL",
            "confidence": 0.0-1.0,
            "reasoning": "brief explanation"
        }}
        """
        
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.1
        )
        
        result_text = response.choices[0].message.content.strip()
        
        # Try to parse JSON
        try:
            sentiment_data = json.loads(result_text)
            print(f"✅ Sentiment: {sentiment_data.get('sentiment', 'UNKNOWN')}")
            print(f"   Confidence: {sentiment_data.get('confidence', 0):.2f}")
            print(f"   Reasoning: {sentiment_data.get('reasoning', 'No reasoning')}")
            return sentiment_data
        except json.JSONDecodeError:
            print(f"✅ Raw sentiment result: {result_text}")
            return {"sentiment": "NEUTRAL", "confidence": 0.5, "reasoning": result_text}
            
    except Exception as e:
        print(f"❌ Sentiment analysis failed: {e}")
        return False

async def test_telegram_news_notification(headline, sentiment_data):
    """Test Telegram news notification"""
    print("\n📱 Testing Telegram News Notification...")
    try:
        from telegram import Bot
        
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        channel_id = os.getenv("TELEGRAM_CHANNEL_ID")
        
        if not token or not channel_id:
            print("❌ Telegram configuration missing")
            return False
        
        bot = Bot(token=token)
        
        # Create news alert message
        sentiment = sentiment_data.get('sentiment', 'NEUTRAL')
        confidence = sentiment_data.get('confidence', 0.5)
        reasoning = sentiment_data.get('reasoning', 'No analysis available')
        
        # Determine emoji based on sentiment
        emoji = "🚀" if sentiment == "BULLISH" else "📉" if sentiment == "BEARISH" else "📊"
        
        message = f"""
{emoji} **CRYPTO NEWS ALERT**

**Headline**: {headline[:150]}...

**Sentiment**: {sentiment}
**Confidence**: {confidence:.1%}
**Analysis**: {reasoning}

**Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

#CryptoNews #{sentiment.lower()}
"""
        
        await bot.send_message(
            chat_id=channel_id,
            text=message,
            parse_mode="Markdown"
        )
        
        print("✅ Telegram news notification sent successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Telegram notification failed: {e}")
        return False

async def test_cointelegraph_news():
    """Test Cointelegraph news scraping"""
    print("\n📰 Testing Cointelegraph News...")
    try:
        # Simple web scraping test
        import requests
        from bs4 import BeautifulSoup
        
        url = "https://cointelegraph.com/rss"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'xml')
            items = soup.find_all('item')[:5]
            
            print(f"✅ Fetched {len(items)} articles from Cointelegraph RSS")
            
            articles = []
            for i, item in enumerate(items, 1):
                title = item.title.text if item.title else "No title"
                link = item.link.text if item.link else "No link"
                articles.append({"title": title, "link": link})
                print(f"   {i}. {title[:80]}...")
            
            return articles
        else:
            print(f"❌ Cointelegraph RSS failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cointelegraph test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Comprehensive News System Test\n")
    
    # Test 1: CryptoPanic News
    news_data = await test_cryptopanic_news()
    sentiment_result = None
    
    if news_data:
        # Get first article for sentiment analysis
        first_article = news_data[0]
        headline = first_article.get('title', 'Bitcoin price analysis shows bullish momentum')
        
        # Test 2: Sentiment Analysis
        sentiment_result = await test_sentiment_analysis(headline)
        
        if sentiment_result:
            # Test 3: Telegram Notification
            await test_telegram_news_notification(headline, sentiment_result)
    
    # Test 4: Alternative news source
    await test_cointelegraph_news()
    
    print("\n📊 News System Test Summary:")
    print(f"✅ CryptoPanic API: {'WORKING' if news_data else 'FAILED'}")
    print(f"✅ Sentiment Analysis: {'WORKING' if sentiment_result else 'FAILED'}")
    print(f"✅ Telegram Integration: TESTED")
    print(f"✅ Alternative Sources: TESTED")
    
    print("\n🎯 News system testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
