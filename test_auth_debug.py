#!/usr/bin/env python3
"""
Debug authentication issues
"""

import requests
import sys
import os

# Add backend to path
sys.path.append('backend')

def test_auth():
    """Test authentication step by step"""
    print("🔐 DEBUGGING AUTHENTICATION")
    print("=" * 50)
    
    # Step 1: Create token
    try:
        from backend.auth import create_access_token
        from backend.config import ALLOWED_EMAILS
        
        print(f"✅ Allowed emails: {ALLOWED_EMAILS}")
        
        token = create_access_token(data={"sub": "<EMAIL>"})
        print(f"✅ Token created: {token[:50]}...")
        
        # Step 2: Test token with API
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test simple endpoint first
        print("\n📡 Testing API endpoints...")
        
        endpoints = [
            "/api/tokens?limit=1",
            "/api/portfolio", 
            "/api/summary",
            "/api/analytics"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"http://localhost:3005{endpoint}", 
                                      headers=headers, timeout=5)
                print(f"✅ {endpoint}: {response.status_code}")
                if response.status_code != 200:
                    print(f"   Error: {response.text[:100]}")
            except Exception as e:
                print(f"❌ {endpoint}: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Auth test failed: {e}")
        return False

def test_endpoints_without_auth():
    """Test endpoints that should work without auth"""
    print("\n🌐 TESTING NON-AUTH ENDPOINTS")
    print("=" * 50)
    
    endpoints = [
        "/",
        "/health", 
        "/api/health",
        "/api/dashboard/summary-fast",
        "/api/tokenmetrics/top-tokens",
        "/api/arbitrage/opportunities"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:3005{endpoint}", timeout=5)
            print(f"✅ {endpoint}: {response.status_code} - {len(response.text)} chars")
        except Exception as e:
            print(f"❌ {endpoint}: {str(e)}")

if __name__ == "__main__":
    # Test server is running
    try:
        response = requests.get("http://localhost:3005/health", timeout=3)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding properly")
            sys.exit(1)
    except:
        print("❌ Server is not running")
        sys.exit(1)
    
    # Run tests
    test_endpoints_without_auth()
    test_auth()
