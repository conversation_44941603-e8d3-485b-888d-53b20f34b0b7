{"version": 4, "name": "alpha-predator-backend", "description": "Alpha Predator Trading Bot - FastAPI Backend with AI-powered trading algorithms, real-time market analysis, and automated trading execution", "owner": "18mPQugRJ19PP4gtUAAkYMuGQXYGwNRCDC", "compose": [{"name": "alpha-predator-backend", "description": "FastAPI backend with AI trading algorithms and market analysis", "repotag": "kryptomerch/alpha-backend:latest", "ports": ["37467:3005"], "domains": ["api.alphapredatorbot.xyz"], "environmentParameters": ["ENV=production", "PORT=3005", "BACKEND_BASE_URL=https://alphapredatorbot.xyz", "TELEGRAM_CHANNEL_ID=-*************", "KRYPTONEWS_CHANNEL_ID=1324277704471740516", "WEBHOOK_URL=https://alphapredatorbot.xyz/webhook", "RSS_FEEDS=https://cointelegraph.com/rss,https://cryptopotato.com/feed/,https://cryptopanic.com/news/rss,https://coinmarketcap.com/headlines/news/feed/", "KUCOIN_BASE_URL=https://api.kucoin.com", "GOOGLE_REDIRECT_URI=https://alphapredatorbot.xyz/auth/callback", "FIRESTORE_CREDENTIALS_PATH=./config/firebase-service-account.json", "FIREBASE_PROJECT_ID=alpha-predator-bot", "FIREBASE_CLIENT_ID=115105595890262943788", "FIREBASE_PRIVATE_KEY_ID=823632c4881c129156ee2925fce6699c75f34f7d", "GOOGLE_APPLICATION_CREDENTIALS=./config/firebase-service-account.json", "ALLOWED_EMAILS=<EMAIL>,<EMAIL>", "ADMIN_EMAIL=<EMAIL>", "TRADE_INTERVAL_SECONDS=60", "MAX_BUY=0", "STOP_LOSS=0.10", "TAKE_PROFIT=0.20", "MIN_24H_VOLUME_USD=1000000", "TRADING_MODE=LIVE", "USE_REAL_NEWS=true", "FALLBACK_TO_OPENAI=true", "JWT_ALGORITHM=HS256", "DISCORD_BOT_TOKEN=MTM3NTQxNDEyMTM1MjQwMDk3Ng.GfEX08.FGWNUO_BgbJ7xkajihC99nJl6LZ8KN_2ulF4oY", "TELEGRAM_BOT_TOKEN=**********:AAEr2r4xzu_kggrzs6ihUhp7JRQWeJ2wvok", "KUCOIN_API_KEY=685f7eff680d7c0001035740", "KUCOIN_API_SECRET=53c3cd5b-f5a3-401f-a5a5-8678d5a9e41f", "KUCOIN_API_PASSPHRASE=Kcheharmata@94", "COINDESK_API_KEY=****************************************************************", "COINMARKETCAL_API_KEY=29Au8SZ2ky5EgwybVeYKO8FzSyyQKq7zaojgzmr7", "COINGECKO_API_KEY=CG-XG3NnYwE44nQu8ZhZVaeuJbp", "OPENAI_API_KEY=********************************************************************************************************************************************************************", "GEMINI_API_KEY=AIzaSyAI_F7rIYrHrSy3dKhBBfynPr0sz7AQ5y8", "TOGETHER_API_KEY=tgp_v1_DvgPzzB07_-QYcwJtBjl31bIhGIT9xqn2QLz0ClvHlk", "GOOGLE_CLIENT_ID=450152610705-sicd663g2b2dehgqgdi1i1mj5m85hvqs.apps.googleusercontent.com", "GOOGLE_CLIENT_SECRET=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", "JWT_SECRET_KEY=super-secret-jwt-key-needs-to-be-32-chars-minimum-for-production-security", "ADMIN_PASSWORD=9824444830", "GITHUB_OAUTH_TOKEN=****************************************"], "cpu": 2, "ram": 4000, "hdd": 50, "tiered": true, "secrets": [], "commands": [], "containerData": "", "containerPorts": [], "nodeSpecificParameters": []}], "geolocation": [], "instances": 3, "staticip": false}