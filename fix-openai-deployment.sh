#!/bin/bash

# Fix OpenAI Import Error - Rebuild and Redeploy Backend
echo "🔧 Fixing OpenAI import error and redeploying backend..."

# Step 1: Build new Docker image with updated requirements
echo "📦 Building new Docker image with updated OpenAI dependency..."
cd backend
docker build -f Dockerfile.prod -t kryptomerch/alpha-backend:latest .

# Step 2: Push to Docker Hub
echo "🚀 Pushing updated image to Docker Hub..."
docker push kryptomerch/alpha-backend:latest

# Step 3: Update Flux deployment to force restart
echo "🔄 Updating Flux deployment to trigger restart..."
cd ..

# Create a temporary deployment file with version bump to force update
cp flux-backend-deployment-fixed.json flux-backend-deployment-temp.json

# Update version to force Flux to pull new image
sed -i 's/"version": 7/"version": 8/' flux-backend-deployment-temp.json

# Deploy to Flux
echo "🌐 Deploying to Flux..."
curl -X POST \
  -H "Content-Type: application/json" \
  -d @flux-backend-deployment-temp.json \
  "https://api.runonflux.io/apps/globalappspecs"

# Clean up temp file
rm flux-backend-deployment-temp.json

echo "✅ Deployment complete! The backend should restart with the fixed OpenAI dependency."
echo "🔍 Monitor the logs at: https://api.alphapredatorbot.xyz/health"
echo "📊 Check deployment status on Flux dashboard"

# Optional: Test the deployment
echo "🧪 Testing deployment in 30 seconds..."
sleep 30
curl -f https://api.alphapredatorbot.xyz/health && echo "✅ Backend is healthy!" || echo "❌ Backend health check failed"
