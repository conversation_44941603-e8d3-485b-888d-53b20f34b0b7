# Alpha Predator Bot - Deployment Guide

## Problem Summary
The frontend container was incorrectly running the backend FastAPI server instead of serving the React frontend, causing a 503 error and preventing the frontend from loading.

## Root Cause Analysis
1. **Build Dependencies**: The original Dockerfile used `npm ci --only=production` which excluded dev dependencies needed for the Vite build process
2. **File Conflicts**: Duplicate PostCSS configuration files (both .js and .cjs versions)
3. **Platform Compatibility**: Missing AMD64 platform specification for Flux deployment
4. **Build Verification**: No verification steps to ensure the build process completed successfully

## Solutions Implemented

### 1. Updated Frontend Dockerfile
- **Fixed**: Changed from `npm ci --only=production` to `npm ci` to include all dependencies
- **Added**: Build verification steps with `ls -la` commands
- **Improved**: Cleaner file copying structure
- **Enhanced**: Better error handling and debugging

### 2. Platform-Specific Builds
- **Created**: `Dockerfile.amd64` with explicit `--platform=linux/amd64` for Flux compatibility
- **Added**: `docker-compose.amd64.yml` for AMD64-specific deployments
- **Included**: Health checks for both services

### 3. Deployment Automation
- **Created**: `deploy.sh` script with comprehensive testing and deployment workflow
- **Added**: Local testing before deployment
- **Included**: Automatic cleanup and build verification

### 4. Configuration Cleanup
- **Removed**: Duplicate `postcss.config.cjs` file
- **Kept**: Single `postcss.config.js` file for consistency

## Deployment Instructions

### Option 1: Quick Deployment (Recommended)
```bash
cd my-app
./deploy.sh
```

### Option 2: Manual Deployment

#### For Local Testing:
```bash
# Build and run locally
docker-compose -f dockerfrontend-compose.yml build --no-cache
docker-compose -f dockerfrontend-compose.yml up -d

# Test services
curl http://localhost:39882  # Frontend
curl http://localhost:33903/api/health  # Backend
```

#### For Flux Deployment:
```bash
# Build AMD64 images
docker-compose -f docker-compose.amd64.yml build --no-cache

# Push to registry
docker push kryptomerch/alpha-frontend:amd64
docker push kryptomerch/alpha-backend:amd64
```

## Service URLs
- **Frontend**: http://localhost:39882 (local) / https://alphabot_39882.app.runonflux.io (Flux)
- **Backend**: http://localhost:33903 (local) / https://alphabot_33903.app.runonflux.io (Flux)

## Troubleshooting

### Frontend Shows 503 Error
1. Check container logs: `docker logs alpha_frontend`
2. Verify build completed: Look for "dist" folder in build logs
3. Check nginx is serving files: `docker exec alpha_frontend ls -la /usr/share/nginx/html`

### Frontend Shows Backend Logs
This was the original issue - ensure you're using the updated Dockerfile and not mixing build contexts.

### Build Fails
1. Clear Docker cache: `docker system prune -a`
2. Check Node.js dependencies: `cd frontend && npm install`
3. Test local build: `cd frontend && npm run build`

### Flux Deployment Issues
1. Ensure using AMD64 images: `kryptomerch/alpha-frontend:amd64`
2. Verify platform compatibility in Flux console
3. Check resource allocation (CPU/RAM requirements)

## File Structure
```
my-app/
├── frontend/
│   ├── Dockerfile              # Standard multi-platform build
│   ├── Dockerfile.amd64        # AMD64-specific build for Flux
│   ├── package.json
│   ├── vite.config.js
│   ├── tailwind.config.js
│   ├── postcss.config.js
│   ├── nginx.conf
│   └── src/
├── backend/
│   └── Dockerfile
├── dockerfrontend-compose.yml  # Standard deployment
├── docker-compose.amd64.yml    # AMD64 deployment for Flux
└── deploy.sh                   # Automated deployment script
```

## Health Checks
Both services now include health checks:
- **Frontend**: `wget --spider http://localhost:80`
- **Backend**: `curl -f http://localhost:3005/api/health`

## Next Steps
1. Run the deployment script: `./deploy.sh`
2. Test locally to ensure everything works
3. Deploy AMD64 images to Flux
4. Monitor logs for any issues
5. Update DNS/domain settings if needed

## Support
If issues persist:
1. Check container logs: `docker logs <container_name>`
2. Verify build output: Look for successful "dist" folder creation
3. Test individual components: Frontend build, backend API, nginx configuration
