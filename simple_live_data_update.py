#!/usr/bin/env python3
"""
Simple script to update discover_tokens.json with live market data from KuCoin
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.kucoin_data import fetch_kucoin_spike_tokens
from backend.price_fetcher import get_price
from backend.kucoin_data import get_24h_volume

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def get_live_discover_tokens():
    """Get live token data for discover_tokens.json"""
    try:
        logger.info("🚀 Fetching live token data from KuCoin...")
        
        # Get spike tokens (tokens with high volume activity)
        spike_tokens = fetch_kucoin_spike_tokens(limit=10)
        
        if not spike_tokens:
            logger.warning("No spike tokens found, using fallback tokens")
            # Fallback to popular tokens
            fallback_symbols = ["BTC-USDT", "ETH-USDT", "BNB-USDT", "ADA-USDT", "SOL-USDT"]
            spike_tokens = [{"symbol": symbol} for symbol in fallback_symbols]
        
        discover_data = []
        
        for token in spike_tokens:
            symbol = token["symbol"]
            try:
                logger.info(f"📊 Processing {symbol}...")
                
                # Get live price
                price = get_price(symbol)
                
                # Get 24h volume
                volume = get_24h_volume(symbol)
                
                # Calculate a simple score based on volume
                score = min(volume / 1000000, 5.0) if volume > 0 else 1.0
                
                discover_data.append({
                    "symbol": symbol,
                    "price": price,
                    "score": round(score, 2),
                    "volume": volume,
                    "sentiment": 0,  # Neutral sentiment as fallback
                    "timestamp": datetime.now().isoformat(),
                    "source": "live_kucoin_data"
                })
                
                logger.info(f"✅ {symbol}: ${price}, Volume: ${volume:,.0f}")
                
            except Exception as e:
                logger.warning(f"⚠️ Failed to process {symbol}: {e}")
                continue
        
        if discover_data:
            # Save to file
            os.makedirs("backend/data", exist_ok=True)
            with open("backend/data/discover_tokens.json", "w") as f:
                json.dump(discover_data, f, indent=2)
            
            logger.info(f"💾 Saved {len(discover_data)} tokens to discover_tokens.json")
            return discover_data
        else:
            logger.error("❌ No valid token data collected")
            return []
            
    except Exception as e:
        logger.error(f"❌ Failed to get live discover tokens: {e}")
        return []

def main():
    """Main function"""
    try:
        result = asyncio.run(get_live_discover_tokens())
        
        if result:
            print(f"\n🎉 Successfully updated discover_tokens.json with {len(result)} live tokens!")
            print("📊 Tokens updated:")
            for token in result:
                print(f"  • {token['symbol']}: ${token['price']} (Volume: ${token['volume']:,.0f})")
            exit(0)
        else:
            print("\n❌ Failed to update discover_tokens.json")
            exit(1)
            
    except Exception as e:
        print(f"\n❌ Script failed: {e}")
        exit(1)

if __name__ == "__main__":
    main()
