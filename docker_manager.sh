#!/bin/bash

# Docker Manager for Alpha Predator Trading Bot
# This script manages Docker services and logs for frontend and backend

echo "=== Alpha Predator Docker Manager ==="
echo "Timestamp: $(date)"
echo "====================================="

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker daemon is not running!"
        echo ""
        echo "Please start Docker Desktop or run:"
        echo "  sudo systemctl start docker  (Linux)"
        echo "  open -a Docker  (macOS)"
        echo ""
        return 1
    fi
    return 0
}

# Function to start Docker Desktop on macOS
start_docker_macos() {
    echo "🚀 Starting Docker Desktop on macOS..."
    open -a Docker
    echo "⏳ Waiting for Docker to start..."
    
    # Wait for Docker to be ready
    local timeout=60
    local count=0
    while ! docker info >/dev/null 2>&1; do
        if [ $count -ge $timeout ]; then
            echo "❌ Timeout waiting for <PERSON><PERSON> to start"
            return 1
        fi
        sleep 2
        count=$((count + 2))
        echo -n "."
    done
    echo ""
    echo "✅ Docker is now running!"
    return 0
}

# Function to check container status
check_container() {
    local container_name=$1
    if docker ps -q -f name="$container_name" | grep -q .; then
        echo "✅ Container '$container_name' is running"
        return 0
    elif docker ps -a -q -f name="$container_name" | grep -q .; then
        echo "⚠️  Container '$container_name' exists but is stopped"
        return 1
    else
        echo "❌ Container '$container_name' not found"
        return 2
    fi
}

# Function to show logs
show_logs() {
    local container_name=$1
    local service_type=$2
    local lines=${3:-50}
    
    echo ""
    echo "==================== $service_type LOGS ===================="
    echo "Container: $container_name"
    echo "Last $lines lines:"
    echo "--------------------------------------------------------"
    
    if docker ps -q -f name="$container_name" | grep -q .; then
        docker logs --tail "$lines" --timestamps "$container_name"
    else
        echo "Cannot show logs - container not running"
        echo "Try starting the services first with: $0 start"
    fi
    
    echo "--------------------------------------------------------"
}

# Function to follow logs
follow_logs() {
    local container_name=$1
    local service_type=$2
    
    echo ""
    echo "Following $service_type logs in real-time (Ctrl+C to stop)..."
    echo "Container: $container_name"
    echo "--------------------------------------------------------"
    
    if docker ps -q -f name="$container_name" | grep -q .; then
        docker logs -f --timestamps "$container_name"
    else
        echo "Cannot follow logs - container not running"
        echo "Try starting the services first with: $0 start"
    fi
}

# Function to start services
start_services() {
    local compose_file=${1:-"docker-compose.prod.yml"}
    
    echo "🚀 Starting services with $compose_file..."
    
    if [ ! -f "$compose_file" ]; then
        echo "❌ Compose file '$compose_file' not found!"
        echo "Available compose files:"
        ls -la docker-compose*.yml dockerfrontend-compose.yml 2>/dev/null || echo "No compose files found"
        return 1
    fi
    
    docker-compose -f "$compose_file" up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Services started successfully!"
        echo ""
        echo "Checking container status..."
        sleep 3
        docker-compose -f "$compose_file" ps
    else
        echo "❌ Failed to start services"
        return 1
    fi
}

# Function to stop services
stop_services() {
    local compose_file=${1:-"docker-compose.prod.yml"}
    
    echo "🛑 Stopping services with $compose_file..."
    
    if [ ! -f "$compose_file" ]; then
        echo "❌ Compose file '$compose_file' not found!"
        return 1
    fi
    
    docker-compose -f "$compose_file" down
    
    if [ $? -eq 0 ]; then
        echo "✅ Services stopped successfully!"
    else
        echo "❌ Failed to stop services"
        return 1
    fi
}

# Function to restart services
restart_services() {
    local compose_file=${1:-"docker-compose.prod.yml"}
    
    echo "🔄 Restarting services with $compose_file..."
    stop_services "$compose_file"
    sleep 2
    start_services "$compose_file"
}

# Function to show service status
show_status() {
    echo ""
    echo "Docker Service Status:"
    echo "====================="
    
    # Check all possible containers
    BACKEND_CONTAINERS=("alpha-predator-backend" "alpha_backend_amd64" "alpha_backend")
    FRONTEND_CONTAINERS=("alpha-predator-frontend" "alpha_frontend_amd64" "alpha_frontend")
    
    echo ""
    echo "Backend Containers:"
    for container in "${BACKEND_CONTAINERS[@]}"; do
        check_container "$container"
    done
    
    echo ""
    echo "Frontend Containers:"
    for container in "${FRONTEND_CONTAINERS[@]}"; do
        check_container "$container"
    done
    
    echo ""
    echo "All Running Containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Function to show help
show_help() {
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start [compose-file]     Start services (default: docker-compose.prod.yml)"
    echo "  stop [compose-file]      Stop services"
    echo "  restart [compose-file]   Restart services"
    echo "  status                   Show container status"
    echo "  logs [service] [lines]   Show logs (service: backend|frontend|all, lines: number)"
    echo "  follow [service]         Follow logs in real-time (service: backend|frontend)"
    echo "  docker-start             Start Docker Desktop (macOS)"
    echo "  help                     Show this help message"
    echo ""
    echo "Compose Files:"
    echo "  docker-compose.prod.yml      Production setup"
    echo "  docker-compose.amd64.yml     AMD64 specific setup"
    echo "  dockerfrontend-compose.yml   Frontend focused setup"
    echo ""
    echo "Examples:"
    echo "  $0 start                           Start with production compose"
    echo "  $0 start docker-compose.amd64.yml Start with AMD64 compose"
    echo "  $0 logs backend 100               Show last 100 backend logs"
    echo "  $0 follow frontend                Follow frontend logs"
    echo "  $0 status                         Show all container status"
}

# Main script logic
case "$1" in
    start)
        if ! check_docker; then
            echo "Try running: $0 docker-start"
            exit 1
        fi
        start_services "$2"
        ;;
    stop)
        if ! check_docker; then
            exit 1
        fi
        stop_services "$2"
        ;;
    restart)
        if ! check_docker; then
            exit 1
        fi
        restart_services "$2"
        ;;
    status)
        if ! check_docker; then
            exit 1
        fi
        show_status
        ;;
    logs)
        if ! check_docker; then
            exit 1
        fi
        
        SERVICE=${2:-"all"}
        LINES=${3:-50}
        
        # Find active containers
        BACKEND_CONTAINERS=("alpha-predator-backend" "alpha_backend_amd64" "alpha_backend")
        FRONTEND_CONTAINERS=("alpha-predator-frontend" "alpha_frontend_amd64" "alpha_frontend")
        
        ACTIVE_BACKEND=""
        for container in "${BACKEND_CONTAINERS[@]}"; do
            if docker ps -q -f name="$container" | grep -q .; then
                ACTIVE_BACKEND="$container"
                break
            fi
        done
        
        ACTIVE_FRONTEND=""
        for container in "${FRONTEND_CONTAINERS[@]}"; do
            if docker ps -q -f name="$container" | grep -q .; then
                ACTIVE_FRONTEND="$container"
                break
            fi
        done
        
        case "$SERVICE" in
            backend)
                if [ -n "$ACTIVE_BACKEND" ]; then
                    show_logs "$ACTIVE_BACKEND" "BACKEND" "$LINES"
                else
                    echo "❌ No active backend container found"
                fi
                ;;
            frontend)
                if [ -n "$ACTIVE_FRONTEND" ]; then
                    show_logs "$ACTIVE_FRONTEND" "FRONTEND" "$LINES"
                else
                    echo "❌ No active frontend container found"
                fi
                ;;
            all|*)
                if [ -n "$ACTIVE_BACKEND" ]; then
                    show_logs "$ACTIVE_BACKEND" "BACKEND" "$LINES"
                fi
                if [ -n "$ACTIVE_FRONTEND" ]; then
                    show_logs "$ACTIVE_FRONTEND" "FRONTEND" "$LINES"
                fi
                if [ -z "$ACTIVE_BACKEND" ] && [ -z "$ACTIVE_FRONTEND" ]; then
                    echo "❌ No active containers found!"
                    echo "Start services first with: $0 start"
                fi
                ;;
        esac
        ;;
    follow)
        if ! check_docker; then
            exit 1
        fi
        
        SERVICE=${2:-"backend"}
        
        # Find active containers
        BACKEND_CONTAINERS=("alpha-predator-backend" "alpha_backend_amd64" "alpha_backend")
        FRONTEND_CONTAINERS=("alpha-predator-frontend" "alpha_frontend_amd64" "alpha_frontend")
        
        case "$SERVICE" in
            backend)
                for container in "${BACKEND_CONTAINERS[@]}"; do
                    if docker ps -q -f name="$container" | grep -q .; then
                        follow_logs "$container" "BACKEND"
                        exit 0
                    fi
                done
                echo "❌ No active backend container found"
                ;;
            frontend)
                for container in "${FRONTEND_CONTAINERS[@]}"; do
                    if docker ps -q -f name="$container" | grep -q .; then
                        follow_logs "$container" "FRONTEND"
                        exit 0
                    fi
                done
                echo "❌ No active frontend container found"
                ;;
            *)
                echo "❌ Invalid service. Use 'backend' or 'frontend'"
                ;;
        esac
        ;;
    docker-start)
        start_docker_macos
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_help
        exit 1
        ;;
esac

echo ""
echo "=== Docker Manager Complete ==="
