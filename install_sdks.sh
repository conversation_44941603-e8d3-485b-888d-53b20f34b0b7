#!/bin/bash

# 🚀 SDK INSTALLATION SCRIPT
# Install all official SDKs for Alpha Predator trading system

echo "🚀 INSTALLING OFFICIAL SDKs FOR ALPHA PREDATOR"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "backend/main.py" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_info "Starting SDK installation process..."

# 1. CRITICAL - TokenMetrics TypeScript SDK
echo ""
echo "1. 🔥 INSTALLING TOKENMETRICS TYPESCRIPT SDK"
echo "--------------------------------------------"
if command -v npm &> /dev/null; then
    print_info "Installing TokenMetrics SDK..."
    npm install tmai-api
    if [ $? -eq 0 ]; then
        print_status "TokenMetrics SDK installed successfully"
    else
        print_error "Failed to install TokenMetrics SDK"
    fi
else
    print_warning "npm not found - please install Node.js to use TokenMetrics SDK"
fi

# 2. CRITICAL - KuCoin Python SDK
echo ""
echo "2. 🔥 INSTALLING KUCOIN PYTHON SDK"
echo "-----------------------------------"
print_info "Installing KuCoin Python SDK..."
pip install kucoin-python
if [ $? -eq 0 ]; then
    print_status "KuCoin SDK installed successfully"
else
    print_error "Failed to install KuCoin SDK"
fi

# 3. HIGH - CoinGecko Python SDK
echo ""
echo "3. 🔥 INSTALLING COINGECKO PYTHON SDK"
echo "-------------------------------------"
print_info "Installing CoinGecko Python SDK..."
pip install pycoingecko
if [ $? -eq 0 ]; then
    print_status "CoinGecko SDK installed successfully"
else
    print_error "Failed to install CoinGecko SDK"
fi

# 4. HIGH - Anthropic Claude SDK
echo ""
echo "4. 🔥 INSTALLING ANTHROPIC CLAUDE SDK"
echo "-------------------------------------"
print_info "Installing Anthropic Python SDK..."
pip install anthropic
if [ $? -eq 0 ]; then
    print_status "Anthropic SDK installed successfully"
else
    print_error "Failed to install Anthropic SDK"
fi

# 5. HIGH - Google Gemini SDK
echo ""
echo "5. 🔥 INSTALLING GOOGLE GEMINI SDK"
echo "----------------------------------"
print_info "Installing Google Gemini SDK..."
pip install google-genai
if [ $? -eq 0 ]; then
    print_status "Google Gemini SDK installed successfully"
else
    print_error "Failed to install Google Gemini SDK"
fi

# 6. MEDIUM - Discord.py SDK
echo ""
echo "6. 🔥 INSTALLING DISCORD.PY SDK"
echo "-------------------------------"
print_info "Installing Discord.py SDK..."
pip install discord.py
if [ $? -eq 0 ]; then
    print_status "Discord.py SDK installed successfully"
else
    print_error "Failed to install Discord.py SDK"
fi

# 7. Check existing SDKs
echo ""
echo "7. ✅ CHECKING EXISTING SDKs"
echo "----------------------------"

# OpenAI (should already be installed)
if pip show openai &> /dev/null; then
    print_status "OpenAI SDK already installed"
else
    print_info "Installing OpenAI SDK..."
    pip install openai
fi

# Telegram Bot (should already be installed)
if pip show python-telegram-bot &> /dev/null; then
    print_status "Telegram Bot SDK already installed"
else
    print_info "Installing Telegram Bot SDK..."
    pip install python-telegram-bot
fi

# 8. Install additional dependencies
echo ""
echo "8. 📦 INSTALLING ADDITIONAL DEPENDENCIES"
echo "----------------------------------------"
print_info "Installing additional required packages..."

# For better async support
pip install aiohttp

# For better JSON handling
pip install ujson

# For better error handling
pip install tenacity

print_status "Additional dependencies installed"

# 9. Create SDK test script
echo ""
echo "9. 🧪 CREATING SDK TEST SCRIPT"
echo "------------------------------"

cat > test_sdks.py << 'EOF'
#!/usr/bin/env python3
"""
🧪 SDK TEST SCRIPT
Test all installed SDKs to ensure they're working
"""

import sys
import os
sys.path.append('backend')

def test_sdk_imports():
    """Test if all SDKs can be imported"""
    results = {}
    
    # Test KuCoin SDK
    try:
        from kucoin.client import Client
        results['kucoin'] = '✅ KuCoin SDK imported successfully'
    except ImportError as e:
        results['kucoin'] = f'❌ KuCoin SDK import failed: {e}'
    
    # Test CoinGecko SDK
    try:
        from pycoingecko import CoinGeckoAPI
        results['coingecko'] = '✅ CoinGecko SDK imported successfully'
    except ImportError as e:
        results['coingecko'] = f'❌ CoinGecko SDK import failed: {e}'
    
    # Test Anthropic SDK
    try:
        from anthropic import Anthropic
        results['anthropic'] = '✅ Anthropic SDK imported successfully'
    except ImportError as e:
        results['anthropic'] = f'❌ Anthropic SDK import failed: {e}'
    
    # Test Gemini SDK
    try:
        from google import genai
        results['gemini'] = '✅ Gemini SDK imported successfully'
    except ImportError as e:
        results['gemini'] = f'❌ Gemini SDK import failed: {e}'
    
    # Test OpenAI SDK
    try:
        from openai import OpenAI
        results['openai'] = '✅ OpenAI SDK imported successfully'
    except ImportError as e:
        results['openai'] = f'❌ OpenAI SDK import failed: {e}'
    
    # Test Telegram SDK
    try:
        from telegram import Bot
        results['telegram'] = '✅ Telegram SDK imported successfully'
    except ImportError as e:
        results['telegram'] = f'❌ Telegram SDK import failed: {e}'
    
    # Test Discord SDK
    try:
        import discord
        results['discord'] = '✅ Discord SDK imported successfully'
    except ImportError as e:
        results['discord'] = f'❌ Discord SDK import failed: {e}'
    
    return results

if __name__ == "__main__":
    print("🧪 TESTING SDK IMPORTS")
    print("=" * 40)
    
    results = test_sdk_imports()
    
    success_count = 0
    total_count = len(results)
    
    for sdk, result in results.items():
        print(f"{sdk.upper()}: {result}")
        if '✅' in result:
            success_count += 1
    
    print(f"\n📊 SUMMARY: {success_count}/{total_count} SDKs imported successfully")
    
    if success_count == total_count:
        print("🎉 All SDKs installed and working!")
    else:
        print("⚠️ Some SDKs failed to import - check installation")
EOF

chmod +x test_sdks.py
print_status "SDK test script created"

# 10. Run SDK test
echo ""
echo "10. 🧪 TESTING SDK INSTALLATIONS"
echo "--------------------------------"
python3 test_sdks.py

# 11. Create migration checklist
echo ""
echo "11. 📋 CREATING MIGRATION CHECKLIST"
echo "-----------------------------------"

cat > SDK_MIGRATION_CHECKLIST.md << 'EOF'
# 🚀 SDK MIGRATION CHECKLIST

## ✅ COMPLETED INSTALLATIONS

- [ ] TokenMetrics TypeScript SDK (`npm install tmai-api`)
- [ ] KuCoin Python SDK (`pip install kucoin-python`)
- [ ] CoinGecko Python SDK (`pip install pycoingecko`)
- [ ] Anthropic Claude SDK (`pip install anthropic`)
- [ ] Google Gemini SDK (`pip install google-genai`)
- [ ] Discord.py SDK (`pip install discord.py`)
- [ ] OpenAI SDK (already installed)
- [ ] Telegram Bot SDK (already installed)

## 🔄 MIGRATION TASKS

### 1. TokenMetrics Migration
- [ ] Replace `backend/tokenmetrics_client.py` with `backend/tokenmetrics_sdk_migration.js`
- [ ] Update all TokenMetrics API calls to use new SDK
- [ ] Test TokenMetrics integration

### 2. KuCoin Migration
- [ ] Replace `backend/kucoin_api.py` with `backend/kucoin_sdk_migration.py`
- [ ] Update all KuCoin API calls to use new SDK
- [ ] Test KuCoin trading functions

### 3. CoinGecko Migration
- [ ] Replace `backend/coingecko_data.py` with `backend/coingecko_sdk_migration.py`
- [ ] Update all CoinGecko API calls to use new SDK
- [ ] Test CoinGecko data fetching

### 4. AI Clients Migration
- [ ] Replace AI client files with `backend/ai_clients_sdk_migration.py`
- [ ] Update AI decision logic to use new SDKs
- [ ] Test all AI providers

### 5. Testing & Validation
- [ ] Run comprehensive tests
- [ ] Verify all endpoints work
- [ ] Check error handling
- [ ] Validate performance improvements

## 📊 EXPECTED BENEFITS

- **90% less custom code** - Official SDKs handle complexity
- **Better error handling** - Professional error management
- **Automatic updates** - SDKs update with API changes
- **Type safety** - Better IDE support and fewer bugs
- **Official support** - Get help from API providers
- **Performance** - Optimized by the API providers

## 🎯 NEXT STEPS

1. Run `python3 test_sdks.py` to verify installations
2. Start with KuCoin migration (most critical for trading)
3. Then TokenMetrics (most complex custom code)
4. Follow with CoinGecko and AI clients
5. Test thoroughly before deploying

EOF

print_status "Migration checklist created"

# Final summary
echo ""
echo "🎉 SDK INSTALLATION COMPLETE!"
echo "============================="
print_info "Next steps:"
echo "1. Review SDK_MIGRATION_CHECKLIST.md"
echo "2. Run 'python3 test_sdks.py' to verify installations"
echo "3. Start migrating custom clients to use official SDKs"
echo "4. Test thoroughly before deploying to production"
echo ""
print_status "All official SDKs are now installed and ready to use!"
