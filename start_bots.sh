#!/bin/bash
# AlphaPredatorBot Startup Script

echo "🚀 Starting AlphaPredatorBot..."

# Install dependencies
echo "📦 Installing dependencies..."
cd backend && pip install -r requirements.txt

# Start the main application
echo "🤖 Starting main application..."
python main.py &

# Start the monitoring script
echo "👁️ Starting 24/7 monitor..."
python ../bot_monitor.py &

echo "✅ AlphaPredatorBot is now running 24/7!"
echo "📊 Check logs: tail -f bot_monitor.log"
echo "🛑 To stop: pkill -f python"
