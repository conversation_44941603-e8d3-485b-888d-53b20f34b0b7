#!/usr/bin/env python3
"""
Test Real Trading Data Integration
Verifies that real KuCoin trades and TokenMetrics signals are working
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_kucoin_data():
    """Test real KuCoin data fetching"""
    logger.info("🧪 Testing KuCoin real data...")
    
    try:
        from backend.kucoin_transaction_tracker import get_kucoin_live_trades
        
        # Test KuCoin API
        kucoin_data = get_kucoin_live_trades(limit=10)
        
        if kucoin_data and kucoin_data.get("trades"):
            logger.info(f"✅ KuCoin API working: {len(kucoin_data['trades'])} trades")
            return True
        else:
            logger.warning("⚠️ KuCoin API returned no data")
            return False
            
    except Exception as e:
        logger.error(f"❌ KuCoin API test failed: {e}")
        return False

async def test_tokenmetrics_signals():
    """Test TokenMetrics AI signals"""
    logger.info("🧪 Testing TokenMetrics AI signals...")
    
    try:
        from backend.smart_tokenmetrics_client import get_optimized_trading_data
        
        # Test TokenMetrics API
        signals_data = await get_optimized_trading_data(["BTC", "ETH"])
        
        if signals_data and signals_data.get("total_data_points", 0) > 0:
            logger.info(f"✅ TokenMetrics API working: {signals_data['total_data_points']} data points")
            return True
        else:
            logger.warning("⚠️ TokenMetrics API returned no data")
            return False
            
    except Exception as e:
        logger.error(f"❌ TokenMetrics API test failed: {e}")
        return False

async def test_trade_logger():
    """Test trade logger with real data"""
    logger.info("🧪 Testing trade logger...")
    
    try:
        from backend.trade_logger import load_live_trades
        
        # Test trade loading
        trades_data = load_live_trades()
        
        if trades_data and trades_data.get("trades"):
            logger.info(f"✅ Trade logger working: {len(trades_data['trades'])} trades")
            
            # Check if data is real (not test data)
            real_trades = [
                t for t in trades_data["trades"] 
                if not (t.get("strategy", "").upper() in ["OPTIMIZATION_TEST", "AI_TEST"])
            ]
            
            if real_trades:
                logger.info(f"✅ Found {len(real_trades)} real trades")
                return True
            else:
                logger.warning("⚠️ Only test data found, no real trades")
                return False
        else:
            logger.warning("⚠️ No trades data found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Trade logger test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Real Data Integration Tests")
    logger.info("=" * 50)
    
    results = {
        "kucoin_data": await test_real_kucoin_data(),
        "tokenmetrics_signals": await test_tokenmetrics_signals(),
        "trade_logger": await test_trade_logger()
    }
    
    logger.info("\n📊 Test Results:")
    logger.info("-" * 30)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    overall_success = all(results.values())
    logger.info(f"\n🎯 Overall Status: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    asyncio.run(main())
