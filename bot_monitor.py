#!/usr/bin/env python3
"""
24/7 Bot Monitoring and Auto-Restart Script
"""

import asyncio
import logging
import time
import subprocess
import sys
import os
from datetime import datetime
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BotMonitor:
    def __init__(self):
        self.alpha_bot_running = False
        self.micro_bot_running = False
        self.last_alpha_heartbeat = time.time()
        self.last_micro_heartbeat = time.time()
        self.restart_count = 0
        
    async def check_bot_health(self):
        """Check if bots are healthy and restart if needed"""
        try:
            current_time = time.time()
            
            # Check Alpha bot (should run every 5 minutes)
            if current_time - self.last_alpha_heartbeat > 600:  # 10 minutes
                logger.warning("🚨 Alpha bot appears stuck, restarting...")
                await self.restart_alpha_bot()
                
            # Check Micro bot (should run every minute)  
            if current_time - self.last_micro_heartbeat > 300:  # 5 minutes
                logger.warning("🚨 Micro bot appears stuck, restarting...")
                await self.restart_micro_bot()
                
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            
    async def restart_alpha_bot(self):
        """Restart the alpha bot"""
        try:
            logger.info("🔄 Restarting Alpha bot...")
            # Import and restart
            from live_runner import start_alpha_bot, stop_alpha_bot
            await stop_alpha_bot()
            await asyncio.sleep(5)
            await start_alpha_bot()
            self.last_alpha_heartbeat = time.time()
            self.restart_count += 1
            await self.send_notification("🔄 Alpha bot restarted")
        except Exception as e:
            logger.error(f"❌ Failed to restart Alpha bot: {e}")
            
    async def restart_micro_bot(self):
        """Restart the micro bot"""
        try:
            logger.info("🔄 Restarting Micro bot...")
            from micro_bot import start_micro_bot, stop_micro_bot
            await stop_micro_bot()
            await asyncio.sleep(5)
            await start_micro_bot()
            self.last_micro_heartbeat = time.time()
            self.restart_count += 1
            await self.send_notification("🔄 Micro bot restarted")
        except Exception as e:
            logger.error(f"❌ Failed to restart Micro bot: {e}")
            
    async def send_notification(self, message: str):
        """Send notification via Telegram"""
        try:
            from telegram_utils import send_telegram_message
            full_message = f"""
🤖 **BOT MONITOR ALERT**
{message}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Restart Count: {self.restart_count}
"""
            await send_telegram_message(full_message)
        except Exception as e:
            logger.error(f"❌ Failed to send notification: {e}")
            
    async def run_monitor(self):
        """Main monitoring loop"""
        logger.info("🚀 Starting 24/7 bot monitor...")
        await self.send_notification("🚀 Bot monitor started - 24/7 operation enabled")
        
        while True:
            try:
                await self.check_bot_health()
                await asyncio.sleep(60)  # Check every minute
                
            except KeyboardInterrupt:
                logger.info("🛑 Monitor stopped by user")
                await self.send_notification("🛑 Bot monitor stopped")
                break
            except Exception as e:
                logger.error(f"❌ Monitor error: {e}")
                await asyncio.sleep(30)

if __name__ == "__main__":
    monitor = BotMonitor()
    asyncio.run(monitor.run_monitor())
