# Production Docker Compose for Alpha Predator Trading Bot

services:
  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: alpha-predator-backend
    restart: unless-stopped
    ports:
      - "3005:3005"
    environment:
      - ENV=production
      - LOG_LEVEL=INFO
    env_file:
      - ./backend/.env.production
    volumes:
      - backend_data:/app/data
      - backend_logs:/app/logs
      - ./backend/config:/app/config:ro
    networks:
      - alpha-predator-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Frontend Web Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: alpha-predator-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - alpha-predator-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Redis Cache (Optional - for rate limiting and caching)
  redis:
    image: redis:7-alpine
    container_name: alpha-predator-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis_data:/data
    networks:
      - alpha-predator-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: alpha-predator-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - alpha-predator-network
    profiles:
      - monitoring

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: alpha-predator-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - alpha-predator-network
    profiles:
      - monitoring

networks:
  alpha-predator-network:
    driver: bridge
    name: alpha-predator-network

volumes:
  backend_data:
    driver: local
    name: alpha-predator-backend-data
  backend_logs:
    driver: local
    name: alpha-predator-backend-logs
  redis_data:
    driver: local
    name: alpha-predator-redis-data
  prometheus_data:
    driver: local
    name: alpha-predator-prometheus-data
  grafana_data:
    driver: local
    name: alpha-predator-grafana-data
