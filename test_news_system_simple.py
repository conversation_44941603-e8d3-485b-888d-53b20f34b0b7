#!/usr/bin/env python3
"""
Simple News System Test with Mock Data
"""

import asyncio
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv('backend/.env')

async def test_sentiment_analysis_with_mock_news():
    """Test sentiment analysis with mock news headlines"""
    print("🧠 Testing Sentiment Analysis with Mock News...")
    
    # Mock news headlines for testing
    test_headlines = [
        "Bitcoin surges to new all-time high as institutional adoption accelerates",
        "Major cryptocurrency exchange suffers security breach, millions lost",
        "Ethereum upgrade successfully deployed, network efficiency improves",
        "Regulatory uncertainty continues to impact crypto market sentiment",
        "DeFi protocol launches innovative yield farming mechanism"
    ]
    
    try:
        try:
            from openai import OpenAI  # type: ignore
        except ImportError:
            print("❌ OpenAI library not installed")
            return False
        
        # Get OpenAI API key
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("❌ OpenAI API key not found")
            return False
        
        client = OpenAI(api_key=api_key)
        results = []
        
        for i, headline in enumerate(test_headlines, 1):
            print(f"\n📰 Analyzing headline {i}: {headline[:60]}...")
            
            prompt = f"""
            Analyze the sentiment of this crypto news headline and provide a JSON response:
            
            Headline: "{headline}"
            
            Respond with JSON format:
            {{
                "sentiment": "BULLISH|BEARISH|NEUTRAL",
                "confidence": 0.0-1.0,
                "reasoning": "brief explanation"
            }}
            """
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content.strip()
            
            try:
                sentiment_data = json.loads(result_text)
                sentiment = sentiment_data.get('sentiment', 'UNKNOWN')
                confidence = sentiment_data.get('confidence', 0)
                reasoning = sentiment_data.get('reasoning', 'No reasoning')
                
                print(f"   ✅ Sentiment: {sentiment}")
                print(f"   📊 Confidence: {confidence:.1%}")
                print(f"   💭 Reasoning: {reasoning}")
                
                results.append({
                    "headline": headline,
                    "sentiment": sentiment,
                    "confidence": confidence,
                    "reasoning": reasoning
                })
                
            except json.JSONDecodeError:
                print(f"   ⚠️ Raw result: {result_text}")
                results.append({
                    "headline": headline,
                    "sentiment": "NEUTRAL",
                    "confidence": 0.5,
                    "reasoning": result_text
                })
        
        return results
        
    except Exception as e:
        print(f"❌ Sentiment analysis failed: {e}")
        return False

async def test_telegram_news_notification(news_results):
    """Test Telegram news notification with analyzed results"""
    print("\n📱 Testing Telegram News Notifications...")
    
    try:
        from telegram import Bot
        
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        channel_id = os.getenv("TELEGRAM_CHANNEL_ID")
        
        if not token or not channel_id:
            print("❌ Telegram configuration missing")
            return False
        
        bot = Bot(token=token)
        
        # Send a summary of all analyzed news
        bullish_count = sum(1 for r in news_results if r['sentiment'] == 'BULLISH')
        bearish_count = sum(1 for r in news_results if r['sentiment'] == 'BEARISH')
        neutral_count = sum(1 for r in news_results if r['sentiment'] == 'NEUTRAL')
        
        avg_confidence = sum(r['confidence'] for r in news_results) / len(news_results)
        
        summary_message = f"""
📊 **NEWS SENTIMENT ANALYSIS SUMMARY**

**Analysis Time**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Headlines Analyzed**: {len(news_results)}
🚀 **Bullish**: {bullish_count}
📉 **Bearish**: {bearish_count}
📊 **Neutral**: {neutral_count}

**Average Confidence**: {avg_confidence:.1%}

**Market Sentiment**: {"BULLISH" if bullish_count > bearish_count else "BEARISH" if bearish_count > bullish_count else "NEUTRAL"}

#NewsAnalysis #CryptoSentiment #AI
"""
        
        await bot.send_message(
            chat_id=channel_id,
            text=summary_message,
            parse_mode="Markdown"
        )
        
        print("✅ News summary sent to Telegram!")
        
        # Send individual news alerts for high-confidence results
        for result in news_results:
            if result['confidence'] > 0.7:  # Only send high-confidence alerts
                sentiment = result['sentiment']
                emoji = "🚀" if sentiment == "BULLISH" else "📉" if sentiment == "BEARISH" else "📊"
                
                alert_message = f"""
{emoji} **HIGH-CONFIDENCE NEWS ALERT**

**Headline**: {result['headline'][:120]}...

**Sentiment**: {sentiment}
**Confidence**: {result['confidence']:.1%}
**Analysis**: {result['reasoning'][:100]}...

#HighConfidence #{sentiment.lower()}
"""
                
                await bot.send_message(
                    chat_id=channel_id,
                    text=alert_message,
                    parse_mode="Markdown"
                )
                
                print(f"✅ High-confidence {sentiment} alert sent!")
                await asyncio.sleep(2)  # Rate limiting
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram notification failed: {e}")
        return False

async def test_news_storage():
    """Test news data storage and retrieval"""
    print("\n💾 Testing News Storage System...")
    
    try:
        # Create a simple news storage test
        news_data = {
            "timestamp": datetime.now().isoformat(),
            "source": "test_system",
            "headlines": [
                {
                    "title": "Bitcoin reaches new milestone",
                    "sentiment": "BULLISH",
                    "confidence": 0.85,
                    "timestamp": datetime.now().isoformat()
                }
            ]
        }
        
        # Save to file
        os.makedirs("backend/data", exist_ok=True)
        with open("backend/data/news_test.json", "w") as f:
            json.dump(news_data, f, indent=2)
        
        print("✅ News data saved successfully")
        
        # Read back
        with open("backend/data/news_test.json", "r") as f:
            loaded_data = json.load(f)
        
        print(f"✅ News data loaded: {len(loaded_data['headlines'])} headlines")
        return True
        
    except Exception as e:
        print(f"❌ News storage test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Simple News System Test\n")
    
    # Test 1: Sentiment Analysis with Mock Data
    print("=" * 60)
    news_results = await test_sentiment_analysis_with_mock_news()
    
    if news_results:
        print(f"\n✅ Successfully analyzed {len(news_results)} headlines")
        
        # Test 2: Telegram Notifications
        print("\n" + "=" * 60)
        telegram_success = await test_telegram_news_notification(news_results)
        
        # Test 3: News Storage
        print("\n" + "=" * 60)
        storage_success = await test_news_storage()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 NEWS SYSTEM TEST SUMMARY:")
        print(f"✅ Sentiment Analysis: {'WORKING' if news_results else 'FAILED'}")
        print(f"✅ Telegram Integration: {'WORKING' if telegram_success else 'FAILED'}")
        print(f"✅ Data Storage: {'WORKING' if storage_success else 'FAILED'}")
        
        # Show sentiment breakdown
        if news_results:
            bullish = sum(1 for r in news_results if r['sentiment'] == 'BULLISH')
            bearish = sum(1 for r in news_results if r['sentiment'] == 'BEARISH')
            neutral = sum(1 for r in news_results if r['sentiment'] == 'NEUTRAL')
            avg_conf = sum(r['confidence'] for r in news_results) / len(news_results)
            
            print(f"\n📈 SENTIMENT BREAKDOWN:")
            print(f"   🚀 Bullish: {bullish}")
            print(f"   📉 Bearish: {bearish}")
            print(f"   📊 Neutral: {neutral}")
            print(f"   🎯 Avg Confidence: {avg_conf:.1%}")
        
        print("\n🎯 News system testing completed!")
        return True
    else:
        print("❌ News system test failed - no results from sentiment analysis")
        return False

if __name__ == "__main__":
    asyncio.run(main())
