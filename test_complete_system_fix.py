#!/usr/bin/env python3
"""
Complete System Fix Test
Tests all critical components that were failing in the backend logs
"""

import sys
import os
sys.path.append('./backend')

# Load environment variables
from dotenv import load_dotenv
load_dotenv('./backend/.env')

import asyncio
import logging
import json
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_ai_core():
    """Test AI core functionality"""
    print("🧠 TESTING AI CORE")
    print("=" * 50)
    
    try:
        from backend.ai_core import get_final_ai_decision
        
        # Test with proper parameters
        result = get_final_ai_decision(
            symbol="BTC-USDT",
            price=45000.0,
            volume_ratio=1.2,
            sentiment_score=0.6,
            chart_signal="BULLISH",
            traditional_signals={
                "ma_crossover": "BUY",
                "rsi": "NEUTRAL",
                "macd": "BUY"
            },
            tokenmetrics_signal="BUY"
        )
        
        if result and isinstance(result, dict):
            decision = result.get("decision", "UNKNOWN")
            confidence = result.get("confidence", 0)
            reason = result.get("reason", "No reason")
            
            print(f"✅ AI Core: {decision} (confidence: {confidence}%)")
            print(f"   Reason: {reason[:100]}...")
            return True
        else:
            print("❌ AI Core: Invalid response format")
            return False
            
    except Exception as e:
        print(f"❌ AI Core: Exception - {e}")
        return False

def test_tokenmetrics():
    """Test TokenMetrics integration"""
    print("\n📊 TESTING TOKENMETRICS")
    print("=" * 50)
    
    try:
        from backend.tokenmetrics_api import get_tokenmetrics_analysis
        
        result = get_tokenmetrics_analysis("BTC")
        
        if result:
            print(f"✅ TokenMetrics: Analysis received")
            print(f"   Signal: {result.get('signal', 'N/A')}")
            print(f"   Score: {result.get('score', 'N/A')}")
            return True
        else:
            print("⚠️ TokenMetrics: No analysis available (expected for some tokens)")
            return True  # This is acceptable
            
    except Exception as e:
        print(f"❌ TokenMetrics: Exception - {e}")
        return False

def test_news_sentiment():
    """Test news sentiment analysis"""
    print("\n📰 TESTING NEWS SENTIMENT")
    print("=" * 50)
    
    try:
        from backend.sentiment_engine import analyze_sentiment, get_combined_sentiment
        
        # Test basic sentiment analysis
        test_text = "Bitcoin shows strong bullish momentum with institutional adoption"
        sentiment_result = analyze_sentiment(test_text)
        
        print(f"✅ Sentiment Analysis: {sentiment_result}")
        
        # Test combined sentiment for a symbol
        combined_result = get_combined_sentiment("BTC-USDT")
        print(f"✅ Combined Sentiment: {combined_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ News Sentiment: Exception - {e}")
        return False

def test_chart_analysis():
    """Test chart analysis"""
    print("\n📈 TESTING CHART ANALYSIS")
    print("=" * 50)
    
    try:
        from backend.chart_analyzer import analyze_chart
        
        result = analyze_chart("BTC-USDT")
        
        if result:
            print(f"✅ Chart Analysis: {result}")
            return True
        else:
            print("⚠️ Chart Analysis: No data available (acceptable)")
            return True
            
    except Exception as e:
        print(f"❌ Chart Analysis: Exception - {e}")
        return False

def test_volume_ratio():
    """Test volume ratio calculation"""
    print("\n📊 TESTING VOLUME RATIO")
    print("=" * 50)
    
    try:
        from backend.volume_ratio import get_volume_ratio
        
        ratio = get_volume_ratio("BTC-USDT")
        
        if isinstance(ratio, (int, float)):
            print(f"✅ Volume Ratio: {ratio}x average")
            return True
        else:
            print(f"⚠️ Volume Ratio: {ratio} (fallback value)")
            return True
            
    except Exception as e:
        print(f"❌ Volume Ratio: Exception - {e}")
        return False

def test_multi_timeframe():
    """Test multi-timeframe analysis"""
    print("\n⏰ TESTING MULTI-TIMEFRAME ANALYSIS")
    print("=" * 50)
    
    try:
        from backend.multi_timeframe_analyzer import analyze_multi_timeframe
        
        result = analyze_multi_timeframe("BTC-USDT")
        
        if result and isinstance(result, dict):
            print(f"✅ Multi-timeframe: Analysis completed")
            print(f"   Timeframes: {list(result.keys())}")
            return True
        else:
            print("⚠️ Multi-timeframe: Limited data available")
            return True
            
    except Exception as e:
        print(f"❌ Multi-timeframe: Exception - {e}")
        return False

def test_prompt_builder():
    """Test prompt builder"""
    print("\n📝 TESTING PROMPT BUILDER")
    print("=" * 50)
    
    try:
        from backend.prompt_builder import build_trading_prompt
        
        prompt = build_trading_prompt(
            symbol="BTC-USDT",
            price=45000.0,
            price_change=2.5,
            volume_ratio=1.2,
            sentiment_score=0.6,
            chart_signal="BULLISH",
            traditional_signals={
                "ma_crossover": "BUY",
                "rsi": "NEUTRAL"
            },
            news_headlines=["Bitcoin adoption increases"],
            tokenmetrics_signal="BUY"
        )
        
        if prompt and len(prompt) > 100:
            print(f"✅ Prompt Builder: Generated {len(prompt)} character prompt")
            return True
        else:
            print("❌ Prompt Builder: Invalid prompt generated")
            return False
            
    except Exception as e:
        print(f"❌ Prompt Builder: Exception - {e}")
        return False

def test_api_clients():
    """Test AI API clients"""
    print("\n🤖 TESTING AI API CLIENTS")
    print("=" * 50)
    
    try:
        from backend.ai_clients.ai_request_manager import get_ai_decision
        
        test_prompt = """
        TOKEN: BTC-USDT
        CURRENT PRICE: $45,000 USDT
        CHART ANALYSIS: BULLISH
        SENTIMENT: Positive
        
        Provide trading decision.
        """
        
        result = get_ai_decision(test_prompt)
        
        if result and isinstance(result, dict):
            response = result.get("response", {})
            decision = response.get("decision", "UNKNOWN")
            confidence = response.get("confidence", 0)
            
            print(f"✅ AI Clients: {decision} (confidence: {confidence}%)")
            return True
        else:
            print("❌ AI Clients: Invalid response")
            return False
            
    except Exception as e:
        print(f"❌ AI Clients: Exception - {e}")
        return False

def main():
    """Run complete system test"""
    print("🚀 COMPLETE SYSTEM FIX TEST")
    print("=" * 80)
    
    tests = [
        ("AI Core", test_ai_core),
        ("TokenMetrics", test_tokenmetrics),
        ("News Sentiment", test_news_sentiment),
        ("Chart Analysis", test_chart_analysis),
        ("Volume Ratio", test_volume_ratio),
        ("Multi-timeframe", test_multi_timeframe),
        ("Prompt Builder", test_prompt_builder),
        ("AI Clients", test_api_clients)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 Running {test_name} test...")
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 FINAL TEST RESULTS")
    print("=" * 80)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed >= total * 0.75:  # 75% pass rate
        print("\n🎉 SYSTEM STATUS: GOOD - Most components working properly")
        return True
    elif passed >= total * 0.5:  # 50% pass rate
        print("\n⚠️ SYSTEM STATUS: PARTIAL - Some issues remain")
        return True
    else:
        print("\n❌ SYSTEM STATUS: CRITICAL - Major issues detected")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
