# Docker Logs and Management Guide for Alpha Predator Trading Bot

This guide provides comprehensive instructions for managing Docker containers and checking logs for both frontend and backend services.

## Quick Start

### 1. Start Docker Desktop (macOS)
```bash
./docker_manager.sh docker-start
```

### 2. Start Services
```bash
# Production setup
./docker_manager.sh start

# AMD64 specific setup
./docker_manager.sh start docker-compose.amd64.yml

# Frontend focused setup
./docker_manager.sh start dockerfrontend-compose.yml
```

### 3. Check Logs
```bash
# Check both frontend and backend logs
./docker_manager.sh logs

# Check specific service logs
./docker_manager.sh logs backend
./docker_manager.sh logs frontend

# Check more lines
./docker_manager.sh logs backend 100
```

## Available Scripts

### 1. `docker_manager.sh` - Complete Docker Management
Full-featured script for managing Docker services and logs.

**Commands:**
- `./docker_manager.sh start [compose-file]` - Start services
- `./docker_manager.sh stop [compose-file]` - Stop services  
- `./docker_manager.sh restart [compose-file]` - Restart services
- `./docker_manager.sh status` - Show container status
- `./docker_manager.sh logs [service] [lines]` - Show logs
- `./docker_manager.sh follow [service]` - Follow logs in real-time
- `./docker_manager.sh docker-start` - Start Docker Desktop (macOS)
- `./docker_manager.sh help` - Show help

### 2. `check_docker_logs.sh` - Simple Log Checker
Lightweight script focused on checking logs only.

**Usage:**
```bash
# Basic usage
./check_docker_logs.sh

# With options
./check_docker_logs.sh -l 100 -s backend
./check_docker_logs.sh -f -s frontend
./check_docker_logs.sh --help
```

## Container Names by Compose File

### docker-compose.prod.yml
- **Backend:** `alpha-predator-backend`
- **Frontend:** `alpha-predator-frontend`
- **Redis:** `alpha-predator-redis`
- **Prometheus:** `alpha-predator-prometheus`
- **Grafana:** `alpha-predator-grafana`

### docker-compose.amd64.yml
- **Backend:** `alpha_backend_amd64`
- **Frontend:** `alpha_frontend_amd64`

### dockerfrontend-compose.yml
- **Backend:** `alpha_backend`
- **Frontend:** `alpha_frontend`

## Direct Docker Commands

### Check Container Status
```bash
# List all containers
docker ps -a

# List running containers only
docker ps

# Check specific container
docker ps -f name=alpha-predator-backend
```

### View Logs
```bash
# Show last 50 lines with timestamps
docker logs --tail 50 --timestamps alpha-predator-backend

# Follow logs in real-time
docker logs -f --timestamps alpha-predator-backend

# Show logs since specific time
docker logs --since="2025-01-01T00:00:00" alpha-predator-backend

# Show logs between time range
docker logs --since="2025-01-01T00:00:00" --until="2025-01-01T12:00:00" alpha-predator-backend
```

### Container Management
```bash
# Start stopped container
docker start alpha-predator-backend

# Stop running container
docker stop alpha-predator-backend

# Restart container
docker restart alpha-predator-backend

# Remove container
docker rm alpha-predator-backend
```

## Docker Compose Commands

### Service Management
```bash
# Start all services
docker-compose -f docker-compose.prod.yml up -d

# Start specific service
docker-compose -f docker-compose.prod.yml up -d backend

# Stop all services
docker-compose -f docker-compose.prod.yml down

# Restart specific service
docker-compose -f docker-compose.prod.yml restart backend

# View service status
docker-compose -f docker-compose.prod.yml ps
```

### Logs with Docker Compose
```bash
# View logs for all services
docker-compose -f docker-compose.prod.yml logs

# View logs for specific service
docker-compose -f docker-compose.prod.yml logs backend

# Follow logs
docker-compose -f docker-compose.prod.yml logs -f backend

# Show last N lines
docker-compose -f docker-compose.prod.yml logs --tail=100 backend
```

## Troubleshooting

### Docker Daemon Not Running
**Error:** `Cannot connect to the Docker daemon`

**Solutions:**
1. **macOS:** Start Docker Desktop
   ```bash
   ./docker_manager.sh docker-start
   # or manually
   open -a Docker
   ```

2. **Linux:** Start Docker service
   ```bash
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

### Container Not Found
**Error:** `Container not found`

**Solutions:**
1. Check if services are running:
   ```bash
   ./docker_manager.sh status
   ```

2. Start services:
   ```bash
   ./docker_manager.sh start
   ```

3. Check available compose files:
   ```bash
   ls -la docker-compose*.yml
   ```

### Container Exists but Stopped
**Error:** `Container exists but is not running`

**Solutions:**
1. Start the container:
   ```bash
   docker start <container-name>
   ```

2. Or restart services:
   ```bash
   ./docker_manager.sh restart
   ```

### Port Already in Use
**Error:** `Port is already allocated`

**Solutions:**
1. Check what's using the port:
   ```bash
   lsof -i :3005  # for backend
   lsof -i :80    # for frontend
   ```

2. Stop conflicting services or change ports in compose file

### Memory/Resource Issues
**Error:** `Container killed due to memory`

**Solutions:**
1. Check resource limits in compose file
2. Increase Docker Desktop memory allocation
3. Monitor container resource usage:
   ```bash
   docker stats
   ```

## Log Analysis Tips

### Common Log Patterns to Look For

**Backend Logs:**
- API request/response logs
- Database connection status
- Trading signal processing
- Error messages and stack traces
- Performance metrics

**Frontend Logs:**
- HTTP request logs
- JavaScript errors
- Build/compilation messages
- Network connectivity issues

### Useful Log Filtering
```bash
# Filter for errors only
docker logs alpha-predator-backend 2>&1 | grep -i error

# Filter for specific time period
docker logs --since="1h" alpha-predator-backend

# Save logs to file
docker logs alpha-predator-backend > backend_logs.txt
```

## Monitoring and Alerts

### Health Checks
All containers include health checks that can be monitored:

```bash
# Check health status
docker inspect --format='{{.State.Health.Status}}' alpha-predator-backend

# View health check logs
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' alpha-predator-backend
```

### Log Rotation
To prevent log files from growing too large:

```bash
# Configure log rotation in compose file
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## Production Monitoring

### Using Prometheus and Grafana
If using the production compose file with monitoring:

1. **Prometheus:** http://localhost:9090
2. **Grafana:** http://localhost:3000 (admin/admin)

### Log Aggregation
For production environments, consider:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Fluentd
- Centralized logging solutions

## Examples

### Daily Log Check Routine
```bash
#!/bin/bash
# daily_log_check.sh

echo "=== Daily Log Check - $(date) ==="

# Check service status
./docker_manager.sh status

# Check for errors in last 24 hours
echo "Backend errors in last 24h:"
docker logs --since="24h" alpha-predator-backend 2>&1 | grep -i error | tail -10

echo "Frontend errors in last 24h:"
docker logs --since="24h" alpha-predator-frontend 2>&1 | grep -i error | tail -10

# Check resource usage
echo "Resource usage:"
docker stats --no-stream
```

### Emergency Restart
```bash
#!/bin/bash
# emergency_restart.sh

echo "Emergency restart initiated..."
./docker_manager.sh stop
sleep 5
./docker_manager.sh start
./docker_manager.sh status
```

## Best Practices

1. **Regular Monitoring:** Check logs daily for errors and performance issues
2. **Log Rotation:** Configure appropriate log rotation to prevent disk space issues
3. **Health Checks:** Monitor container health status regularly
4. **Resource Monitoring:** Keep an eye on CPU and memory usage
5. **Backup:** Regularly backup important data volumes
6. **Updates:** Keep Docker images updated for security patches

## Support

For additional help:
1. Check container health: `./docker_manager.sh status`
2. View recent logs: `./docker_manager.sh logs`
3. Follow real-time logs: `./docker_manager.sh follow backend`
4. Restart services: `./docker_manager.sh restart`

Remember to always check logs after making changes to identify any issues early.
