#!/usr/bin/env python3
"""
Final Bot Fix Script
Addresses authentication and endpoint issues
"""

import subprocess
import time
import requests
import sys
import os

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description} completed")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ {description} error: {e}")
        return False

def check_server_status():
    """Check if server is running and responsive"""
    try:
        response = requests.get("http://localhost:3005/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def restart_server():
    """Restart the server properly"""
    print("🔄 Restarting server...")
    
    # Kill existing processes
    run_command("pkill -f 'python3.*main.py'", "Stopping existing server")
    run_command("pkill -f 'uvicorn'", "Stopping uvicorn processes")
    
    time.sleep(3)
    
    # Start new server
    print("🚀 Starting new server...")
    subprocess.Popen(
        ["python3", "main.py"],
        cwd="backend",
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL
    )
    
    # Wait for server to start
    for i in range(10):
        time.sleep(2)
        if check_server_status():
            print("✅ Server started successfully")
            return True
        print(f"⏳ Waiting for server... ({i+1}/10)")
    
    print("❌ Server failed to start")
    return False

def test_authentication():
    """Test authentication with proper token"""
    print("🔐 Testing authentication...")
    
    # Add backend to path for imports
    sys.path.append('backend')
    
    try:
        from backend.auth import create_access_token
        from backend.config import ALLOWED_EMAILS
        
        print(f"📧 Allowed emails: {len(ALLOWED_EMAILS)} configured")
        
        # Create token for test user
        token = create_access_token(data={"sub": "<EMAIL>"})
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test a simple authenticated endpoint
        response = requests.get("http://localhost:3005/api/tokens?limit=1", 
                              headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ Authentication working properly")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code} - {response.text[:100]}")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test error: {e}")
        return False

def run_comprehensive_test():
    """Run the comprehensive test suite"""
    print("🧪 Running comprehensive tests...")
    
    try:
        result = subprocess.run(
            ["python3", "test_bot_comprehensive.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if "Success Rate:" in result.stdout:
            # Extract success rate
            lines = result.stdout.split('\n')
            for line in lines:
                if "Success Rate:" in line:
                    rate = line.split("Success Rate: ")[1].split("%")[0]
                    print(f"📊 Test Success Rate: {rate}%")
                    return float(rate) >= 80
        
        print("❌ Could not determine test results")
        return False
        
    except subprocess.TimeoutExpired:
        print("⏰ Comprehensive tests timed out")
        return False
    except Exception as e:
        print(f"❌ Test execution error: {e}")
        return False

def main():
    """Main fix routine"""
    print("🚀 Alpha Predator Bot Final Fix")
    print("=" * 50)
    
    fixes_applied = 0
    total_fixes = 4
    
    # 1. Check current server status
    if check_server_status():
        print("✅ Server is currently running")
    else:
        print("❌ Server is not running")
    
    # 2. Restart server to pick up auth changes
    if restart_server():
        fixes_applied += 1
        print("✅ Server restart successful")
    else:
        print("❌ Server restart failed")
    
    # 3. Test authentication
    if test_authentication():
        fixes_applied += 1
        print("✅ Authentication test passed")
    else:
        print("❌ Authentication still has issues")
    
    # 4. Run comprehensive tests
    if run_comprehensive_test():
        fixes_applied += 1
        print("✅ Comprehensive tests passed")
    else:
        print("❌ Comprehensive tests failed")
    
    # 5. Final status check
    if check_server_status():
        fixes_applied += 1
        print("✅ Final server status check passed")
    else:
        print("❌ Final server status check failed")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FIX SUMMARY")
    print("-" * 30)
    print(f"Fixes Applied: {fixes_applied}/{total_fixes}")
    success_rate = (fixes_applied / total_fixes) * 100
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("🎉 Bot is now functioning well!")
        print("\n🚀 Next steps:")
        print("1. Monitor endpoint performance")
        print("2. Check API key configurations")
        print("3. Test trading functionality")
        return True
    else:
        print("⚠️ Bot still has significant issues")
        print("\n🔧 Manual intervention needed:")
        print("1. Check server logs for errors")
        print("2. Verify API key configurations")
        print("3. Debug endpoint timeouts")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
