global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'alpha-predator-backend'
    static_configs:
      - targets: ['backend:3005']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'alpha-predator-frontend'
    static_configs:
      - targets: ['frontend:80']
    metrics_path: '/health'
    scrape_interval: 30s
