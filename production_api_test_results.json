[{"test_name": "Health - Root", "success": false, "details": "Exception: HTTPConnectionPool(host='localhost', port=33903): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1038b34d0>: Failed to establish a new connection: [Errno 61] Connection refused'))", "response_time": 0, "timestamp": "2025-07-17T00:40:46.461256"}, {"test_name": "Health - Basic Health", "success": false, "details": "Exception: HTTPConnectionPool(host='localhost', port=33903): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10389f9d0>: Failed to establish a new connection: [Errno 61] Connection refused'))", "response_time": 0, "timestamp": "2025-07-17T00:40:46.462060"}, {"test_name": "Health - API Health", "success": false, "details": "Exception: HTTPConnectionPool(host='localhost', port=33903): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10389f610>: Failed to establish a new connection: [Errno 61] Connection refused'))", "response_time": 0, "timestamp": "2025-07-17T00:40:46.462913"}, {"test_name": "Authentication", "success": false, "details": "Exception: HTTPConnectionPool(host='localhost', port=33903): Max retries exceeded with url: /api/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10387b6f0>: Failed to establish a new connection: [Errno 61] Connection refused'))", "response_time": 0, "timestamp": "2025-07-17T00:40:46.463535"}]