#!/bin/bash

# Docker Logs Checker for Alpha Predator Trading Bot
# This script checks logs for both frontend and backend services

echo "=== Alpha Predator Docker Logs Checker ==="
echo "Timestamp: $(date)"
echo "=========================================="

# Function to check if container exists and is running
check_container() {
    local container_name=$1
    if docker ps -q -f name="$container_name" | grep -q .; then
        echo "✅ Container '$container_name' is running"
        return 0
    elif docker ps -a -q -f name="$container_name" | grep -q .; then
        echo "⚠️  Container '$container_name' exists but is not running"
        return 1
    else
        echo "❌ Container '$container_name' not found"
        return 2
    fi
}

# Function to show logs for a container
show_logs() {
    local container_name=$1
    local service_type=$2
    local lines=${3:-50}
    
    echo ""
    echo "==================== $service_type LOGS ===================="
    echo "Container: $container_name"
    echo "Last $lines lines:"
    echo "--------------------------------------------------------"
    
    if check_container "$container_name"; then
        docker logs --tail "$lines" --timestamps "$container_name"
    else
        echo "Cannot show logs - container not running or not found"
    fi
    
    echo "--------------------------------------------------------"
}

# Function to follow logs in real-time
follow_logs() {
    local container_name=$1
    local service_type=$2
    
    echo ""
    echo "Following $service_type logs in real-time (Ctrl+C to stop)..."
    echo "Container: $container_name"
    echo "--------------------------------------------------------"
    
    if check_container "$container_name"; then
        docker logs -f --timestamps "$container_name"
    else
        echo "Cannot follow logs - container not running or not found"
    fi
}

# Check which containers are available
echo ""
echo "Checking available containers..."
echo "================================"

# Possible container names based on different compose files
BACKEND_CONTAINERS=("alpha-predator-backend" "alpha_backend_amd64" "alpha_backend")
FRONTEND_CONTAINERS=("alpha-predator-frontend" "alpha_frontend_amd64" "alpha_frontend")

# Find active backend container
ACTIVE_BACKEND=""
for container in "${BACKEND_CONTAINERS[@]}"; do
    if check_container "$container" >/dev/null 2>&1; then
        ACTIVE_BACKEND="$container"
        break
    fi
done

# Find active frontend container
ACTIVE_FRONTEND=""
for container in "${FRONTEND_CONTAINERS[@]}"; do
    if check_container "$container" >/dev/null 2>&1; then
        ACTIVE_FRONTEND="$container"
        break
    fi
done

# Show container status
echo ""
echo "Container Status:"
echo "=================="
if [ -n "$ACTIVE_BACKEND" ]; then
    check_container "$ACTIVE_BACKEND"
else
    echo "❌ No active backend container found"
fi

if [ -n "$ACTIVE_FRONTEND" ]; then
    check_container "$ACTIVE_FRONTEND"
else
    echo "❌ No active frontend container found"
fi

# Parse command line arguments
LINES=50
FOLLOW=false
SERVICE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -l|--lines)
            LINES="$2"
            shift 2
            ;;
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -h|--help)
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -f, --follow          Follow logs in real-time"
            echo "  -l, --lines NUMBER    Number of lines to show (default: 50)"
            echo "  -s, --service TYPE    Show logs for specific service (backend|frontend|all)"
            echo "  -h, --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    Show last 50 lines for both services"
            echo "  $0 -l 100             Show last 100 lines for both services"
            echo "  $0 -s backend         Show logs only for backend"
            echo "  $0 -f -s frontend     Follow frontend logs in real-time"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Show logs based on parameters
if [ "$FOLLOW" = true ]; then
    case "$SERVICE" in
        backend)
            if [ -n "$ACTIVE_BACKEND" ]; then
                follow_logs "$ACTIVE_BACKEND" "BACKEND"
            else
                echo "❌ No active backend container to follow"
            fi
            ;;
        frontend)
            if [ -n "$ACTIVE_FRONTEND" ]; then
                follow_logs "$ACTIVE_FRONTEND" "FRONTEND"
            else
                echo "❌ No active frontend container to follow"
            fi
            ;;
        *)
            echo "❌ Cannot follow multiple containers simultaneously"
            echo "Use -s backend or -s frontend with -f option"
            exit 1
            ;;
    esac
else
    case "$SERVICE" in
        backend)
            if [ -n "$ACTIVE_BACKEND" ]; then
                show_logs "$ACTIVE_BACKEND" "BACKEND" "$LINES"
            else
                echo "❌ No active backend container found"
            fi
            ;;
        frontend)
            if [ -n "$ACTIVE_FRONTEND" ]; then
                show_logs "$ACTIVE_FRONTEND" "FRONTEND" "$LINES"
            else
                echo "❌ No active frontend container found"
            fi
            ;;
        *)
            # Show both by default
            if [ -n "$ACTIVE_BACKEND" ]; then
                show_logs "$ACTIVE_BACKEND" "BACKEND" "$LINES"
            fi
            
            if [ -n "$ACTIVE_FRONTEND" ]; then
                show_logs "$ACTIVE_FRONTEND" "FRONTEND" "$LINES"
            fi
            
            if [ -z "$ACTIVE_BACKEND" ] && [ -z "$ACTIVE_FRONTEND" ]; then
                echo ""
                echo "❌ No active containers found!"
                echo "Make sure your Docker services are running with:"
                echo "  docker-compose -f docker-compose.prod.yml up -d"
                echo "  or"
                echo "  docker-compose -f docker-compose.amd64.yml up -d"
                echo "  or"
                echo "  docker-compose -f dockerfrontend-compose.yml up -d"
            fi
            ;;
    esac
fi

echo ""
echo "=== Docker Logs Check Complete ==="
