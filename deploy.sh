#!/bin/bash

# Deployment script for Alpha Predator Bot
# This script builds and deploys both frontend and backend services

set -e  # Exit on any error

echo "🚀 Starting Alpha Predator Bot Deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running ✓"

# Clean up existing containers and images
print_status "Cleaning up existing containers and images..."
docker-compose -f dockerfrontend-compose.yml down --remove-orphans 2>/dev/null || true
docker-compose -f docker-compose.amd64.yml down --remove-orphans 2>/dev/null || true

# Remove old images
docker rmi kryptomerch/alpha-frontend:latest 2>/dev/null || true
docker rmi kryptomerch/alpha-backend:latest 2>/dev/null || true
docker rmi kryptomerch/alpha-frontend:amd64 2>/dev/null || true
docker rmi kryptomerch/alpha-backend:amd64 2>/dev/null || true

print_success "Cleanup completed"

# Build and test locally first
print_status "Building containers locally for testing..."

# Test frontend build
print_status "Testing frontend build..."
cd frontend
if npm run build; then
    print_success "Frontend build test passed ✓"
else
    print_error "Frontend build test failed ✗"
    exit 1
fi
cd ..

# Build containers
print_status "Building Docker containers..."

if docker-compose -f dockerfrontend-compose.yml build --no-cache; then
    print_success "Docker build completed ✓"
else
    print_error "Docker build failed ✗"
    exit 1
fi

# Test containers locally
print_status "Testing containers locally..."
docker-compose -f dockerfrontend-compose.yml up -d

# Wait for services to start
sleep 10

# Test frontend
print_status "Testing frontend service..."
if curl -f http://localhost:39882 > /dev/null 2>&1; then
    print_success "Frontend service is responding ✓"
else
    print_warning "Frontend service test failed - checking logs..."
    docker logs alpha_frontend
fi

# Test backend
print_status "Testing backend service..."
if curl -f http://localhost:33903/api/health > /dev/null 2>&1; then
    print_success "Backend service is responding ✓"
else
    print_warning "Backend service test failed - checking logs..."
    docker logs alpha_backend
fi

# Stop test containers
docker-compose -f dockerfrontend-compose.yml down

print_success "Local testing completed"

# Build AMD64 versions for Flux deployment
print_status "Building AMD64 containers for Flux deployment..."

if docker-compose -f docker-compose.amd64.yml build --no-cache; then
    print_success "AMD64 build completed ✓"
else
    print_error "AMD64 build failed ✗"
    exit 1
fi

# Push to registry (optional)
read -p "Do you want to push images to Docker registry? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Pushing images to registry..."
    docker push kryptomerch/alpha-frontend:latest
    docker push kryptomerch/alpha-backend:latest
    docker push kryptomerch/alpha-frontend:amd64
    docker push kryptomerch/alpha-backend:amd64
    print_success "Images pushed to registry ✓"
fi

print_success "🎉 Deployment preparation completed!"
print_status "Next steps:"
echo "1. For local testing: docker-compose -f dockerfrontend-compose.yml up -d"
echo "2. For Flux deployment: Use the AMD64 images with docker-compose.amd64.yml"
echo "3. Frontend will be available at: http://localhost:39882"
echo "4. Backend will be available at: http://localhost:33903"
