#!/usr/bin/env python3
"""
Comprehensive Production API Testing Script
Tests news sentiment API and TokenMetrics API rigorously with real data
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any

# API Configuration
BASE_URL = "http://localhost:33903"
HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "AlphaPredator-Production-Test/1.0"
}

class ProductionAPITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = HEADERS
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str, response_time: float = 0):
        """Log test results"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details} ({response_time:.2f}s)")
        
    def test_health_endpoint(self):
        """Test basic health endpoint"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/health", headers=self.headers, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                self.log_test("Health Check", True, f"Status: {data.get('status')}, Version: {data.get('version')}", response_time)
                return True
            else:
                self.log_test("Health Check", False, f"HTTP {response.status_code}", response_time)
                return False
        except Exception as e:
            self.log_test("Health Check", False, f"Exception: {str(e)}", 0)
            return False

    def test_news_sentiment_api(self):
        """Test news sentiment API with real crypto news"""
        print("\n🔍 Testing News Sentiment API...")
        
        # Test cases with real crypto news scenarios
        test_cases = [
            {
                "name": "Bitcoin Positive News",
                "text": "Bitcoin reaches new all-time high as institutional adoption increases. Major companies are adding BTC to their treasury reserves.",
                "expected_sentiment": "positive"
            },
            {
                "name": "Ethereum Negative News", 
                "text": "Ethereum network faces congestion issues as gas fees spike to unprecedented levels. Users report failed transactions.",
                "expected_sentiment": "negative"
            },
            {
                "name": "Neutral Market Update",
                "text": "Cryptocurrency market shows mixed signals today with some altcoins gaining while others decline slightly.",
                "expected_sentiment": "neutral"
            },
            {
                "name": "Regulatory News",
                "text": "SEC announces new cryptocurrency regulations that could impact trading. Market participants await further clarification.",
                "expected_sentiment": "negative"
            },
            {
                "name": "DeFi Innovation",
                "text": "New DeFi protocol launches with innovative yield farming mechanisms. Early adopters report impressive returns.",
                "expected_sentiment": "positive"
            }
        ]
        
        success_count = 0
        for test_case in test_cases:
            try:
                start_time = time.time()
                
                # Test the news sentiment endpoint
                payload = {
                    "text": test_case["text"],
                    "source": "test_production"
                }
                
                response = requests.post(
                    f"{self.base_url}/api/news/sentiment",
                    json=payload,
                    headers=self.headers,
                    timeout=30
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    sentiment = data.get("sentiment", "unknown").lower()
                    confidence = data.get("confidence", 0)
                    
                    # Check if sentiment analysis is working
                    if sentiment in ["positive", "negative", "neutral"] and confidence > 0:
                        details = f"Sentiment: {sentiment} (confidence: {confidence:.2f})"
                        self.log_test(f"News Sentiment - {test_case['name']}", True, details, response_time)
                        success_count += 1
                    else:
                        self.log_test(f"News Sentiment - {test_case['name']}", False, f"Invalid response: {data}", response_time)
                else:
                    self.log_test(f"News Sentiment - {test_case['name']}", False, f"HTTP {response.status_code}: {response.text}", response_time)
                    
            except Exception as e:
                self.log_test(f"News Sentiment - {test_case['name']}", False, f"Exception: {str(e)}", 0)
        
        return success_count == len(test_cases)

    def test_tokenmetrics_api(self):
        """Test TokenMetrics API with real token data"""
        print("\n📊 Testing TokenMetrics API...")
        
        # Test popular tokens
        test_tokens = [
            {"symbol": "BTC", "name": "Bitcoin"},
            {"symbol": "ETH", "name": "Ethereum"}, 
            {"symbol": "ADA", "name": "Cardano"},
            {"symbol": "SOL", "name": "Solana"},
            {"symbol": "MATIC", "name": "Polygon"}
        ]
        
        success_count = 0
        for token in test_tokens:
            try:
                start_time = time.time()
                
                # Test TokenMetrics data endpoint
                response = requests.get(
                    f"{self.base_url}/api/tokenmetrics/{token['symbol']}",
                    headers=self.headers,
                    timeout=30
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Check if we got valid TokenMetrics data
                    if data and isinstance(data, dict):
                        # Look for key TokenMetrics fields
                        has_price = "price" in data or "current_price" in data
                        has_metrics = any(key in data for key in ["market_cap", "volume", "grade", "score"])
                        
                        if has_price or has_metrics:
                            details = f"Data received with {len(data)} fields"
                            self.log_test(f"TokenMetrics - {token['name']}", True, details, response_time)
                            success_count += 1
                        else:
                            self.log_test(f"TokenMetrics - {token['name']}", False, f"No valid metrics data: {list(data.keys())}", response_time)
                    else:
                        self.log_test(f"TokenMetrics - {token['name']}", False, f"Invalid response format: {type(data)}", response_time)
                else:
                    self.log_test(f"TokenMetrics - {token['name']}", False, f"HTTP {response.status_code}: {response.text}", response_time)
                    
            except Exception as e:
                self.log_test(f"TokenMetrics - {token['name']}", False, f"Exception: {str(e)}", 0)
        
        return success_count == len(test_tokens)

    def test_real_news_fetching(self):
        """Test fetching real news data from live sources"""
        print("\n📰 Testing Real News Data Fetching...")
        
        try:
            start_time = time.time()
            
            # Test news fetching endpoint
            response = requests.get(
                f"{self.base_url}/api/news/fetch",
                headers=self.headers,
                timeout=60  # Longer timeout for real news fetching
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                if isinstance(data, list) and len(data) > 0:
                    news_count = len(data)
                    # Check if news items have required fields
                    sample_news = data[0]
                    has_title = "title" in sample_news
                    has_content = "content" in sample_news or "description" in sample_news
                    has_sentiment = "sentiment" in sample_news
                    
                    details = f"Fetched {news_count} news items, fields: title={has_title}, content={has_content}, sentiment={has_sentiment}"
                    self.log_test("Real News Fetching", True, details, response_time)
                    return True
                else:
                    self.log_test("Real News Fetching", False, f"No news data returned: {data}", response_time)
                    return False
            else:
                self.log_test("Real News Fetching", False, f"HTTP {response.status_code}: {response.text}", response_time)
                return False
                
        except Exception as e:
            self.log_test("Real News Fetching", False, f"Exception: {str(e)}", 0)
            return False

    def test_api_performance(self):
        """Test API performance under load"""
        print("\n⚡ Testing API Performance...")
        
        # Test multiple concurrent requests
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def make_request(endpoint, test_id):
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers, timeout=10)
                response_time = time.time() - start_time
                results_queue.put({
                    "test_id": test_id,
                    "success": response.status_code == 200,
                    "response_time": response_time
                })
            except Exception as e:
                results_queue.put({
                    "test_id": test_id,
                    "success": False,
                    "response_time": 0,
                    "error": str(e)
                })
        
        # Create multiple threads to test concurrent requests
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request, args=("/api/health", f"concurrent_{i}"))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Collect results
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        success_count = sum(1 for r in results if r["success"])
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        
        details = f"{success_count}/{len(results)} requests successful, avg response time: {avg_response_time:.2f}s"
        self.log_test("API Performance", success_count == len(results), details, avg_response_time)
        
        return success_count == len(results)

    def test_error_handling(self):
        """Test API error handling"""
        print("\n🚨 Testing API Error Handling...")
        
        # Test invalid endpoints
        error_tests = [
            {"endpoint": "/api/invalid", "expected_status": 404},
            {"endpoint": "/api/tokenmetrics/INVALID", "expected_status": 404},
            {"endpoint": "/api/news/sentiment", "method": "GET", "expected_status": 405}  # Should be POST
        ]
        
        success_count = 0
        for test in error_tests:
            try:
                start_time = time.time()
                
                if test.get("method") == "GET":
                    response = requests.get(f"{self.base_url}{test['endpoint']}", headers=self.headers, timeout=10)
                else:
                    response = requests.get(f"{self.base_url}{test['endpoint']}", headers=self.headers, timeout=10)
                
                response_time = time.time() - start_time
                
                if response.status_code == test["expected_status"]:
                    self.log_test(f"Error Handling - {test['endpoint']}", True, f"Correctly returned {response.status_code}", response_time)
                    success_count += 1
                else:
                    self.log_test(f"Error Handling - {test['endpoint']}", False, f"Expected {test['expected_status']}, got {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_test(f"Error Handling - {test['endpoint']}", False, f"Exception: {str(e)}", 0)
        
        return success_count == len(error_tests)

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE PRODUCTION API TEST REPORT")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Average response times
        response_times = [r["response_time"] for r in self.test_results if r["response_time"] > 0]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            print(f"Average Response Time: {avg_response_time:.2f}s")
        
        print("\n" + "-"*80)
        print("DETAILED RESULTS:")
        print("-"*80)
        
        for result in self.test_results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{status} {result['test_name']}: {result['details']} ({result['response_time']:.2f}s)")
        
        # Save report to file
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests/total_tests)*100,
                "average_response_time": sum(response_times) / len(response_times) if response_times else 0
            },
            "detailed_results": self.test_results
        }
        
        with open("production_api_test_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: production_api_test_report.json")
        
        return passed_tests == total_tests

    def run_all_tests(self):
        """Run all production API tests"""
        print("🚀 Starting Comprehensive Production API Testing...")
        print(f"Target API: {self.base_url}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print("="*80)
        
        # Run all test suites
        test_suites = [
            ("Health Check", self.test_health_endpoint),
            ("News Sentiment API", self.test_news_sentiment_api),
            ("TokenMetrics API", self.test_tokenmetrics_api),
            ("Real News Fetching", self.test_real_news_fetching),
            ("API Performance", self.test_api_performance),
            ("Error Handling", self.test_error_handling)
        ]
        
        suite_results = []
        for suite_name, test_function in test_suites:
            print(f"\n🔄 Running {suite_name} tests...")
            try:
                result = test_function()
                suite_results.append((suite_name, result))
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"📋 {suite_name}: {status}")
            except Exception as e:
                print(f"❌ {suite_name}: EXCEPTION - {str(e)}")
                suite_results.append((suite_name, False))
        
        # Generate final report
        all_passed = self.generate_report()
        
        print("\n" + "="*80)
        print("🏁 FINAL RESULTS:")
        for suite_name, result in suite_results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {suite_name}: {status}")
        
        overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
        print(f"\n🎯 Overall Status: {overall_status}")
        print("="*80)
        
        return all_passed


def main():
    """Main function to run the production API tests"""
    if len(sys.argv) > 1:
        # Allow custom base URL
        global BASE_URL
        BASE_URL = sys.argv[1]
        print(f"Using custom API URL: {BASE_URL}")
    
    tester = ProductionAPITester()
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
