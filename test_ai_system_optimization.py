#!/usr/bin/env python3
"""
Test AI System Optimization
===========================

This test verifies that the AI system optimizations are working correctly:
1. Prompt builder enhancements
2. Optimized trading engine fixes
3. API call efficiency
4. Error handling improvements
"""

import asyncio
import sys
import os
import time
import logging

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_prompt_builder():
    """Test the enhanced prompt builder"""
    logger.info("🧪 Testing Prompt Builder...")
    
    try:
        from prompt_builder import build_trade_prompt
        
        # Test with basic parameters
        prompt = build_trade_prompt(
            token="BTC",
            price=43250.123456,
            price_change_24h=2.45,
            news_snippets=["Bitcoin ETF approval", "Institutional adoption", "Market rally"]
        )
        
        # Verify prompt structure
        assert "TOKEN: BTC" in prompt
        assert "CURRENT PRICE:" in prompt
        assert "PRICE CHANGE (24H):" in prompt
        assert "MARKET SENTIMENT SCORE:" in prompt
        assert "BUY / SELL / HOLD" in prompt
        
        logger.info("✅ Prompt Builder test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Prompt Builder test failed: {e}")
        return False

async def test_optimized_trading_engine():
    """Test the optimized trading engine"""
    logger.info("🧪 Testing Optimized Trading Engine...")
    
    try:
        from optimized_trading_engine import OptimizedTradingEngine, TradingDecision
        
        # Initialize engine
        engine = OptimizedTradingEngine()
        
        # Test circuit breaker
        assert engine.circuit_breaker.can_execute() == True
        
        # Test position size calculation
        position_size = engine._calculate_position_size("BTC-USDT", 0.8)
        assert 0 < position_size <= engine.risk_limits['max_position_size']
        
        # Test risk score calculation
        mock_market_data = [[0, 0, 43000, 0, 0] for _ in range(25)]  # Mock candlestick data
        risk_score = engine._calculate_risk_score("BTC-USDT", mock_market_data, 0.5)
        assert 0 <= risk_score <= 1.0
        
        logger.info("✅ Optimized Trading Engine test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Optimized Trading Engine test failed: {e}")
        return False

async def test_price_fetcher():
    """Test the price fetcher optimizations"""
    logger.info("🧪 Testing Price Fetcher...")
    
    try:
        from price_fetcher import get_prices_batch_coingecko, format_symbol
        
        # Test symbol formatting
        assert format_symbol("BTC") == "BTC-USDT"
        assert format_symbol("ETH-USDT") == "ETH-USDT"
        
        # Test batch price fetching (with timeout)
        start_time = time.time()
        prices = get_prices_batch_coingecko(["BTC", "ETH"])
        duration = time.time() - start_time
        
        # Should complete within reasonable time
        assert duration < 10.0  # 10 seconds max
        
        logger.info(f"✅ Price Fetcher test passed (took {duration:.2f}s)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Price Fetcher test failed: {e}")
        return False

async def test_ai_request_manager():
    """Test the AI request manager"""
    logger.info("🧪 Testing AI Request Manager...")
    
    try:
        from ai_clients.ai_request_manager import get_ai_decision, validate_response
        
        # Test response validation
        valid_response = {"decision": "BUY", "confidence": 0.8, "reason": "Test"}
        validated = validate_response(valid_response)
        assert validated == valid_response
        
        # Test invalid response handling
        invalid_response = "invalid string"
        validated = validate_response(invalid_response)
        assert validated == {}
        
        logger.info("✅ AI Request Manager test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ AI Request Manager test failed: {e}")
        return False

async def test_strategy_ai():
    """Test the strategy AI module"""
    logger.info("🧪 Testing Strategy AI...")
    
    try:
        from strategy_ai import decide_strategy
        
        # Mock data for testing
        mock_prices = [43000, 43100, 43200, 43150, 43250]
        mock_highs = [43050, 43150, 43250, 43200, 43300]
        mock_lows = [42950, 43050, 43150, 43100, 43200]
        mock_volumes = [1000, 1200, 1100, 1300, 1150]
        
        decision = decide_strategy(
            token="BTC",
            prices=mock_prices,
            high_prices=mock_highs,
            low_prices=mock_lows,
            volumes=mock_volumes,
            sentiment_score=0.6,
            news_keywords=["bullish", "rally"],
            events=[],
            target_symbol="BTC",
            current_price=43250
        )
        
        # Should return a valid decision
        assert decision in ["BUY_BREAKOUT", "SELL_BREAKOUT", "BUY_VOLUME_SPIKE", 
                          "SELL_VOLUME_SPIKE", "BUY_NEWS", "SELL_NEWS", 
                          "BUY_TREND", "SELL_TREND", "BUY_MEAN_REVERSION", 
                          "SELL_MEAN_REVERSION", "HOLD"]
        
        logger.info("✅ Strategy AI test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Strategy AI test failed: {e}")
        return False

async def main():
    """Run all optimization tests"""
    logger.info("🚀 Starting AI System Optimization Tests...")
    
    tests = [
        test_prompt_builder,
        test_optimized_trading_engine,
        test_price_fetcher,
        test_ai_request_manager,
        test_strategy_ai
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All AI system optimizations are working correctly!")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Check the logs above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
