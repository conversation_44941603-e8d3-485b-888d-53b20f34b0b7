#!/usr/bin/env python3
"""
Interactive Telegram Test with Yes/No Buttons
"""

import asyncio
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from telegram import Bo<PERSON>, InlineKeyboardButton, InlineKeyboardMarkup
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

async def send_interactive_test():
    """Send a test message with Yes/No buttons"""
    try:
        # Get bot token and channel ID
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        channel_id = os.getenv("TELEGRAM_CHANNEL_ID")
        
        if not token or not channel_id:
            print("❌ Missing Telegram configuration")
            return False
        
        # Create bot instance
        bot = Bot(token=token)
        
        # Create interactive message with buttons
        message = f"""
🤖 **INTERACTIVE TELEGRAM TEST**

Timestamp: `{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`

**Question**: Is the Telegram integration working perfectly?

Please click one of the buttons below to test the interactive functionality:
"""
        
        # Create inline keyboard with Yes/No buttons
        keyboard = [
            [
                InlineKeyboardButton("✅ YES - Working Perfect!", callback_data="test_yes"),
                InlineKeyboardButton("❌ NO - Has Issues", callback_data="test_no")
            ],
            [
                InlineKeyboardButton("🔄 Test Again", callback_data="test_again"),
                InlineKeyboardButton("📊 Show Status", callback_data="test_status")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Send message with buttons
        await bot.send_message(
            chat_id=channel_id,
            text=message,
            parse_mode="Markdown",
            reply_markup=reply_markup
        )
        
        print("✅ Interactive test message sent successfully!")
        print("📱 Check your Telegram channel and click the buttons to test")
        return True
        
    except Exception as e:
        print(f"❌ Failed to send interactive test: {e}")
        return False

async def send_trade_approval_test():
    """Send a trade approval test with buttons"""
    try:
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        channel_id = os.getenv("TELEGRAM_CHANNEL_ID")
        
        if not token or not channel_id:
            print("❌ Missing Telegram configuration")
            return False
        
        bot = Bot(token=token)
        
        message = """
🚀 **TRADE APPROVAL TEST**

**Token**: BTC-USDT
**Action**: BUY
**Quantity**: 0.001 BTC
**Price**: $45,000.00
**Strategy**: AI Signal
**Confidence**: 95%

**Reason**: Strong bullish sentiment detected with high volume spike

Do you approve this trade?
"""
        
        # Create trade approval buttons
        keyboard = [
            [
                InlineKeyboardButton("✅ APPROVE TRADE", callback_data="approve_BTC-USDT"),
                InlineKeyboardButton("❌ REJECT TRADE", callback_data="reject_BTC-USDT")
            ],
            [
                InlineKeyboardButton("⏸️ PAUSE BOT", callback_data="pause_bot"),
                InlineKeyboardButton("📊 VIEW ANALYTICS", callback_data="view_analytics")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await bot.send_message(
            chat_id=channel_id,
            text=message,
            parse_mode="Markdown",
            reply_markup=reply_markup
        )
        
        print("✅ Trade approval test sent successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to send trade approval test: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Interactive Telegram Tests\n")
    
    # Initialize results
    result1 = False
    result2 = False
    
    # Test 1: Basic interactive message
    print("📋 Test 1: Sending interactive Yes/No question...")
    result1 = await send_interactive_test()
    
    if result1:
        print("✅ Test 1 completed - Check Telegram and click buttons!")
        
        # Wait a bit before next test
        await asyncio.sleep(3)
        
        # Test 2: Trade approval simulation
        print("\n📋 Test 2: Sending trade approval simulation...")
        result2 = await send_trade_approval_test()
        
        if result2:
            print("✅ Test 2 completed - Check Telegram for trade approval!")
            print("\n🎯 Both tests sent successfully!")
            print("📱 Please check your Telegram channel and interact with the buttons")
            print("🔄 The buttons should respond when clicked")
        else:
            print("❌ Test 2 failed")
    else:
        print("❌ Test 1 failed")
    
    print("\n📊 Test Summary:")
    print(f"✅ Interactive Message: {'SENT' if result1 else 'FAILED'}")
    print(f"✅ Trade Approval: {'SENT' if result2 else 'FAILED'}")

if __name__ == "__main__":
    asyncio.run(main())
