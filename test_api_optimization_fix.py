#!/usr/bin/env python3
"""
Test script to verify API optimization fixes for CoinGecko rate limiting and token filtering
"""

import asyncio
import logging
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from api_optimization_manager import api_optimizer
from enhanced_arbitrage import enhanced_arbitrage_engine
from coingecko_enhanced import coingecko_enhanced

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_api_optimization():
    """Test the API optimization manager functionality"""
    
    print("🔧 Testing API Optimization Manager")
    print("=" * 50)
    
    # Test 1: Token filtering
    print("\n1. Testing token filtering...")
    test_symbols = ["BTC-USDT", "ETH-USDT", "S-USDT", "INVALID-USDT", "ADA-USDT"]
    
    print(f"Input symbols: {test_symbols}")
    
    # Check which tokens are supported
    for symbol in test_symbols:
        is_supported = api_optimizer.is_token_supported(symbol)
        coingecko_id = api_optimizer.get_coingecko_id(symbol)
        print(f"  {symbol}: supported={is_supported}, coingecko_id={coingecko_id}")
    
    # Filter supported tokens
    supported = api_optimizer.filter_supported_tokens(test_symbols)
    print(f"Filtered supported symbols: {supported}")
    
    # Test 2: Rate limiting
    print("\n2. Testing rate limiting...")
    for i in range(3):
        should_skip = api_optimizer.should_skip_api_call('coingecko', 'BTC-USDT')
        print(f"  Call {i+1}: should_skip={should_skip}")
        if not should_skip:
            api_optimizer.record_api_call('coingecko')
            delay = api_optimizer.get_optimal_delay('coingecko')
            print(f"    Recorded call, optimal delay: {delay}s")
    
    # Test 3: Batch sizes
    print("\n3. Testing batch sizes...")
    for api_name in ['coingecko', 'kucoin', 'tokenmetrics']:
        batch_size = api_optimizer.get_batch_size(api_name)
        print(f"  {api_name}: batch_size={batch_size}")
    
    print("\n✅ API Optimization Manager tests completed")

async def test_enhanced_arbitrage():
    """Test the enhanced arbitrage with API optimization"""
    
    print("\n🚀 Testing Enhanced Arbitrage with API Optimization")
    print("=" * 50)
    
    try:
        # Test with a small limit to avoid overwhelming APIs
        print("Finding enhanced arbitrage opportunities (limit=5)...")
        opportunities = await enhanced_arbitrage_engine.find_enhanced_arbitrage_opportunities(limit=5)
        
        print(f"Found {len(opportunities)} opportunities")
        
        for i, opp in enumerate(opportunities[:3], 1):  # Show top 3
            symbol = opp.get('symbol', 'Unknown')
            total_score = opp.get('total_score', 0)
            recommendation = opp.get('recommendation', {})
            action = recommendation.get('action', 'Unknown')
            
            print(f"  {i}. {symbol}: score={total_score:.3f}, action={action}")
        
        print("✅ Enhanced arbitrage test completed")
        
    except Exception as e:
        print(f"❌ Enhanced arbitrage test failed: {e}")
        logger.error(f"Enhanced arbitrage test error: {e}", exc_info=True)

async def test_coingecko_enhanced():
    """Test the enhanced CoinGecko integration"""
    
    print("\n📊 Testing Enhanced CoinGecko Integration")
    print("=" * 50)
    
    try:
        # Test with a known supported token
        test_symbol = "BTC-USDT"
        
        print(f"Testing comprehensive data for {test_symbol}...")
        data = coingecko_enhanced.get_comprehensive_token_data(test_symbol)
        
        if data:
            print(f"  ✅ Got comprehensive data for {test_symbol}")
            print(f"    Current price: ${data.get('current_price', 'N/A')}")
            print(f"    Market cap rank: {data.get('market_cap_rank', 'N/A')}")
            print(f"    24h change: {data.get('price_change_24h', 'N/A')}%")
        else:
            print(f"  ❌ No data returned for {test_symbol}")
        
        # Test batch processing with mixed tokens
        print("\nTesting batch processing...")
        test_symbols = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
        
        batch_results = coingecko_enhanced.batch_get_comprehensive_data(test_symbols)
        
        print(f"  Batch results for {len(test_symbols)} symbols:")
        for symbol, result in batch_results.items():
            has_data = bool(result and result.get('current_price'))
            print(f"    {symbol}: {'✅' if has_data else '❌'}")
        
        print("✅ Enhanced CoinGecko test completed")
        
    except Exception as e:
        print(f"❌ Enhanced CoinGecko test failed: {e}")
        logger.error(f"Enhanced CoinGecko test error: {e}", exc_info=True)

async def main():
    """Run all tests"""
    
    print("🧪 API Optimization Fix Verification")
    print("=" * 60)
    print("This script tests the fixes for CoinGecko rate limiting and token filtering issues.")
    print()
    
    try:
        # Test API optimization manager
        await test_api_optimization()
        
        # Test enhanced arbitrage
        await test_enhanced_arbitrage()
        
        # Test enhanced CoinGecko
        await test_coingecko_enhanced()
        
        print("\n🎉 All tests completed!")
        print("\nSummary of fixes:")
        print("✅ API Optimization Manager: Filters unsupported tokens, manages rate limits")
        print("✅ Enhanced Arbitrage: Uses optimized token filtering and batch processing")
        print("✅ Enhanced CoinGecko: Improved token ID mapping and error handling")
        print("\nThe system should now be more stable and efficient with external APIs.")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
