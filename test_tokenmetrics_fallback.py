#!/usr/bin/env python3
"""
TokenMetrics Fallback Test
Tests the API fallback manager with TokenMetrics as primary fallback
when CoinGecko is rate limited, leveraging advanced membership benefits.
"""

import asyncio
import logging
import sys
import os
import time

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from api_fallback_manager import api_fallback_manager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_tokenmetrics_fallback():
    """Test TokenMetrics as fallback when CoinGecko is rate limited"""
    
    print("🚀 Testing TokenMetrics Fallback System")
    print("=" * 60)
    print("Testing intelligent fallback to TokenMetrics advanced membership")
    print("when CoinGecko is rate limited or unavailable.")
    print()
    
    # Test tokens
    test_tokens = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
    
    print("1. Testing Token Data Fallback (TokenMetrics Priority)")
    print("-" * 50)
    
    for token in test_tokens:
        print(f"\n  Testing {token}:")
        
        # Test token data (TokenMetrics has priority)
        data = await api_fallback_manager.get_token_data_with_fallback(token, 'token_data')
        
        if data:
            source = data.get('source', 'unknown')
            price = data.get('current_price', 'N/A')
            print(f"    ✅ Data from {source}: Price=${price}")
        else:
            print(f"    ❌ No data available from any API")
    
    print("\n2. Testing Market Data Fallback (TokenMetrics Priority)")
    print("-" * 50)
    
    for token in test_tokens[:2]:  # Test fewer for market data
        print(f"\n  Testing market analysis for {token}:")
        
        # Test market data (TokenMetrics has priority)
        data = await api_fallback_manager.get_token_data_with_fallback(token, 'market_data')
        
        if data:
            source = data.get('source', 'unknown')
            signal = data.get('combined_signal', 'N/A')
            confidence = data.get('confidence', 'N/A')
            print(f"    ✅ Analysis from {source}: Signal={signal}, Confidence={confidence}")
        else:
            print(f"    ❌ No market data available")
    
    print("\n3. Testing Price Data Fallback (CoinGecko Priority)")
    print("-" * 50)
    
    for token in test_tokens[:2]:
        print(f"\n  Testing price data for {token}:")
        
        # Test price data (CoinGecko has priority for real-time prices)
        data = await api_fallback_manager.get_token_data_with_fallback(token, 'price_data')
        
        if data:
            source = data.get('source', 'unknown')
            price = data.get('price', data.get('current_price', 'N/A'))
            print(f"    ✅ Price from {source}: ${price}")
        else:
            print(f"    ❌ No price data available")

async def test_batch_fallback():
    """Test batch processing with fallback"""
    
    print("\n4. Testing Batch Processing with Fallback")
    print("-" * 50)
    
    test_symbols = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
    
    print(f"  Processing batch: {test_symbols}")
    
    start_time = time.time()
    results = await api_fallback_manager.batch_get_token_data_with_fallback(test_symbols, 'token_data')
    end_time = time.time()
    
    print(f"  Batch processing completed in {end_time - start_time:.2f}s")
    print(f"  Results: {len(results)}/{len(test_symbols)} tokens processed")
    
    for symbol, data in results.items():
        source = data.get('source', 'unknown')
        price = data.get('current_price', 'N/A')
        print(f"    {symbol}: ${price} from {source}")

async def test_enhanced_tokenmetrics_limits():
    """Test enhanced TokenMetrics rate limits"""
    
    print("\n5. Testing Enhanced TokenMetrics Rate Limits")
    print("-" * 50)
    
    # Get enhanced limits
    stats = api_fallback_manager.get_fallback_stats()
    enhanced_limits = stats.get('tokenmetrics_enhanced_limits', {})
    
    print(f"  Enhanced TokenMetrics limits:")
    print(f"    Calls per minute: {enhanced_limits.get('calls_per_minute', 'N/A')}")
    print(f"    Calls per hour: {enhanced_limits.get('calls_per_hour', 'N/A')}")
    print(f"    Delay between calls: {enhanced_limits.get('delay', 'N/A')}s")
    print(f"    Burst limit: {enhanced_limits.get('burst_limit', 'N/A')}")
    
    # Test rapid calls to TokenMetrics
    print(f"\n  Testing rapid TokenMetrics calls:")
    
    for i in range(5):
        start_time = time.time()
        data = await api_fallback_manager.get_token_data_with_fallback("BTC-USDT", 'token_data')
        end_time = time.time()
        
        if data:
            source = data.get('source', 'unknown')
            delay = end_time - start_time
            print(f"    Call {i+1}: ✅ {source} (took {delay:.2f}s)")
        else:
            print(f"    Call {i+1}: ❌ Failed")
        
        await asyncio.sleep(0.1)  # Small delay between calls

async def test_fallback_statistics():
    """Test fallback statistics tracking"""
    
    print("\n6. Testing Fallback Statistics")
    print("-" * 50)
    
    # Reset stats for clean test
    api_fallback_manager.reset_stats()
    
    # Make some test calls
    test_tokens = ["BTC-USDT", "ETH-USDT", "INVALID-TOKEN"]
    
    for token in test_tokens:
        await api_fallback_manager.get_token_data_with_fallback(token, 'token_data')
    
    # Get statistics
    stats = api_fallback_manager.get_fallback_stats()
    
    print(f"  Fallback Statistics:")
    print(f"    Total requests: {stats.get('total_requests', 0)}")
    print(f"    CoinGecko failures: {stats.get('coingecko_failures', 0)}")
    print(f"    TokenMetrics failures: {stats.get('tokenmetrics_failures', 0)}")
    print(f"    Total failures: {stats.get('total_failures', 0)}")
    print(f"    Success rate: {stats.get('success_rate', 0):.1f}%")

async def main():
    """Run all TokenMetrics fallback tests"""
    
    print("🧪 TokenMetrics Fallback Test Suite")
    print("=" * 70)
    print("Testing intelligent API fallback with TokenMetrics advanced membership")
    print("as primary fallback when CoinGecko is rate limited.")
    print()
    
    try:
        # Test TokenMetrics fallback
        await test_tokenmetrics_fallback()
        
        # Test batch processing
        await test_batch_fallback()
        
        # Test enhanced limits
        await test_enhanced_tokenmetrics_limits()
        
        # Test statistics
        await test_fallback_statistics()
        
        print("\n🎉 All TokenMetrics fallback tests completed!")
        print("\nKey Features Demonstrated:")
        print("✅ TokenMetrics as primary fallback for token data")
        print("✅ CoinGecko priority for real-time price data")
        print("✅ Enhanced rate limits for TokenMetrics advanced membership")
        print("✅ Intelligent API priority based on data type")
        print("✅ Batch processing with concurrent fallback")
        print("✅ Comprehensive fallback statistics tracking")
        print("\nThe system now leverages your TokenMetrics advanced membership by:")
        print("- Using TokenMetrics as primary source for comprehensive token data")
        print("- Applying enhanced rate limits (200 calls/min vs 60 standard)")
        print("- Falling back intelligently when APIs are rate limited")
        print("- Maintaining high availability through multiple API sources")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
