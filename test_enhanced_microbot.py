#!/usr/bin/env python3
"""
Enhanced MicroBot Test Script
============================
Test the enhanced MicroBot with multi-level profit taking
"""

import sys
import asyncio
import logging
from datetime import datetime

# Add backend to path
sys.path.append('./backend')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_microbot_configuration():
    """Test MicroBot configuration and logic"""
    try:
        from micro_bot import micro_bot_instance
        
        print("🧪 TESTING ENHANCED MICROBOT")
        print("=" * 60)
        
        # Display configuration
        micro_bot_instance.display_configuration()
        
        # Test profit levels
        print("\n📊 PROFIT LEVEL ANALYSIS:")
        print("-" * 40)
        
        # Simulate a $10 trade
        trade_amount = 10.0
        entry_price = 1.0
        quantity = trade_amount / entry_price
        
        print(f"Example Trade: ${trade_amount} at ${entry_price:.6f} = {quantity:.6f} tokens")
        print()
        
        cumulative_sold = 0
        total_profit = 0
        
        for i, level in enumerate(micro_bot_instance.micro_profit_levels, 1):
            profit_price = entry_price * (1 + level["percentage"])
            sell_quantity = quantity * level["sell_ratio"]
            sell_value = sell_quantity * profit_price
            profit = sell_quantity * (profit_price - entry_price)
            
            cumulative_sold += level["sell_ratio"]
            total_profit += profit
            
            print(f"Level {i} ({level['target_name']}):")
            print(f"  Trigger: {level['percentage']*100:.1f}% profit (${profit_price:.6f})")
            print(f"  Sell: {level['sell_ratio']*100:.0f}% = {sell_quantity:.6f} tokens")
            print(f"  Value: ${sell_value:.2f}")
            print(f"  Profit: ${profit:.2f}")
            print(f"  Cumulative sold: {cumulative_sold*100:.0f}%")
            print()
        
        remaining_quantity = quantity * (1 - cumulative_sold)
        print(f"Remaining position: {remaining_quantity:.6f} tokens ({(1-cumulative_sold)*100:.0f}%)")
        print(f"Total profit from levels: ${total_profit:.2f}")
        print()
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration test failed: {e}")
        return False

async def test_microbot_analysis():
    """Test MicroBot token analysis"""
    try:
        from micro_bot import analyze_token
        
        print("🔍 TESTING TOKEN ANALYSIS:")
        print("-" * 40)
        
        # Test with a common token
        test_symbol = "BTC-USDT"
        
        print(f"Analyzing {test_symbol}...")
        analysis = await analyze_token(test_symbol)
        
        if analysis:
            print(f"✅ Analysis successful:")
            print(f"  Decision: {analysis.get('decision', 'N/A')}")
            print(f"  Confidence: {analysis.get('confidence', 0)*100:.1f}%")
            print(f"  Price: ${analysis.get('price', 0):.6f}")
            print(f"  Combined Score: {analysis.get('combined_score', 0):.3f}")
        else:
            print("❌ Analysis failed")
            
        return analysis is not None
        
    except Exception as e:
        logger.error(f"Analysis test failed: {e}")
        return False

async def test_microbot_status():
    """Test MicroBot status reporting"""
    try:
        from micro_bot import get_micro_bot_status
        
        print("📊 TESTING STATUS REPORTING:")
        print("-" * 40)
        
        status = get_micro_bot_status()
        
        print(f"Running: {status.get('is_running', False)}")
        print(f"Trades Today: {status.get('trades_today', 0)}")
        print(f"P&L Today: ${status.get('pnl_today', 0):.2f}")
        print(f"Portfolio Size: {status.get('portfolio_size', 0)}")
        print(f"Balance: ${status.get('balance', 0):.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"Status test failed: {e}")
        return False

async def run_microbot_simulation():
    """Run a short simulation of MicroBot logic"""
    try:
        print("🎮 RUNNING MICROBOT SIMULATION:")
        print("-" * 40)
        
        from micro_bot import micro_bot_instance
        
        # Simulate position management
        test_position = {
            "buy_price": 1.0,
            "amount": 10.0,
            "original_amount": 10.0,
            "timestamp": datetime.now().isoformat(),
            "confidence": 0.75,
            "trailing_stop": 0.98,
            "highest_price": 1.0,
            "profit_levels_executed": []
        }
        
        # Test different price scenarios
        test_prices = [1.005, 1.01, 1.02, 1.03, 1.05]
        
        for price in test_prices:
            profit_pct = (price - 1.0) / 1.0
            print(f"\nPrice: ${price:.3f} ({profit_pct*100:.1f}% profit)")
            
            # Check which profit levels would trigger
            for i, level in enumerate(micro_bot_instance.micro_profit_levels):
                level_key = f"level_{i}"
                
                if (profit_pct >= level["percentage"] and 
                    level_key not in test_position["profit_levels_executed"]):
                    
                    sell_qty = test_position["original_amount"] * level["sell_ratio"]
                    profit = sell_qty * (price - 1.0)
                    
                    print(f"  🎯 {level['target_name']} TRIGGERED!")
                    print(f"     Sell {sell_qty:.2f} tokens for ${profit:.2f} profit")
                    
                    # Simulate execution
                    test_position["profit_levels_executed"].append(level_key)
                    test_position["amount"] -= sell_qty
        
        print(f"\nFinal position: {test_position['amount']:.2f} tokens")
        print(f"Levels executed: {len(test_position['profit_levels_executed'])}/4")
        
        return True
        
    except Exception as e:
        logger.error(f"Simulation failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🤖 ENHANCED MICROBOT TESTING SUITE")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Configuration Test", test_microbot_configuration),
        ("Token Analysis Test", test_microbot_analysis),
        ("Status Reporting Test", test_microbot_status),
        ("Logic Simulation Test", run_microbot_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"🧪 Running {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}\n")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ FAILED: {e}\n")
    
    # Summary
    print("📋 TEST SUMMARY:")
    print("=" * 40)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced MicroBot is ready!")
        print("\nTo start the Enhanced MicroBot:")
        print("python -c \"import asyncio; import sys; sys.path.append('./backend'); from micro_bot import start_micro_bot; asyncio.run(start_micro_bot())\"")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")

if __name__ == "__main__":
    asyncio.run(main())
