version: '3.9'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
        - VITE_API_URL=/api
    image: kryptomerch/alpha-frontend:latest
    container_name: alpha_frontend
    ports:
      - "37466:80"  # maps to Flux port 37466
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - alpha-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    image: kryptomerch/alpha-backend:latest
    container_name: alpha_backend
    ports:
      - "33903:3005"  # maps to Flux port 33903
    environment:
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - PORT=3005
      - HOST=0.0.0.0
      - CORS_ORIGINS=https://alphapredatorbot.xyz,https://www.alphapredatorbot.xyz,https://alphabot_39882.app.runonflux.io,http://alphabot_39882.app.runonflux.io
    volumes:
      - ./backend/data:/app/data
    restart: unless-stopped
    networks:
      - alpha-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  alpha-network:
    driver: bridge
