#!/usr/bin/env python3
"""
Test Multi-Timeframe Analysis System
Tests the enhanced AI system with multiple timeframes (1hour, 4hour, daily) covering 7 days
"""
import asyncio
import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from backend.multi_timeframe_analyzer import MultiTimeframeAnalyzer

async def test_multi_timeframe_system():
    print('🕐 Testing Multi-Timeframe Analysis System')
    print('=' * 70)
    
    analyzer = MultiTimeframeAnalyzer()
    
    # Test symbols
    test_symbols = ['BTC-USDT', 'ETH-USDT', 'SOL-USDT']
    
    for symbol in test_symbols:
        print(f'\n📊 Testing {symbol}')
        print('-' * 50)
        
        try:
            # Get comprehensive multi-timeframe analysis
            analysis = await analyzer.get_comprehensive_analysis(symbol)
            
            if analysis.get('analysis_available'):
                print(f'✅ Analysis successful for {symbol}')
                
                # Display timeframe coverage
                timeframes = analysis['data_coverage']['timeframes_analyzed']
                total_points = analysis['data_coverage']['total_data_points']
                print(f'📈 Timeframes analyzed: {", ".join(timeframes)}')
                print(f'📊 Total data points: {total_points}')
                print(f'🗓️ Coverage: {analysis["data_coverage"]["coverage_days"]} days')
                
                # Display trend alignment
                trend_alignment = analysis['trend_alignment']
                print(f'🎯 Trend Direction: {trend_alignment["trend_direction"]}')
                print(f'💪 Strength: {trend_alignment["strength"]}')
                print(f'🤝 Consensus: {trend_alignment["consensus"]}')
                print(f'📈 Bullish Timeframes: {trend_alignment["timeframes_bullish"]}')
                print(f'📉 Bearish Timeframes: {trend_alignment["timeframes_bearish"]}')
                
                # Display overall signal
                overall_signal = analysis['overall_signal']
                print(f'🚦 Overall Signal: {overall_signal["signal"]}')
                print(f'🎯 Confidence: {overall_signal["confidence"]:.3f}')
                print(f'📝 Reasoning: {overall_signal["reasoning"]}')
                
                # Display individual timeframe analysis
                print('\n📊 Individual Timeframe Analysis:')
                for timeframe, tf_analysis in analysis['timeframe_analysis'].items():
                    indicators = tf_analysis.get('indicators', {})
                    patterns = tf_analysis.get('patterns', {})
                    
                    print(f'  {timeframe.upper()}:')
                    print(f'    💰 Current Price: ${indicators.get("current_price", 0):,.2f}')
                    print(f'    📈 Price Change: {indicators.get("price_change", 0):+.2f}%')
                    print(f'    📊 RSI: {indicators.get("rsi", 0):.1f}')
                    print(f'    🔄 MACD Histogram: {indicators.get("macd_histogram", 0):+.4f}')
                    print(f'    🎯 Patterns Detected: {patterns.get("pattern_count", 0)}')
                    
                    if patterns.get('patterns_detected'):
                        for pattern in patterns['patterns_detected'][:2]:  # Show first 2 patterns
                            print(f'      • {pattern["pattern"]} ({pattern["strength"]})')
                
            else:
                print(f'❌ Analysis failed for {symbol}: {analysis.get("error", "Unknown error")}')
                
        except Exception as e:
            print(f'❌ Error testing {symbol}: {e}')
    
    print('\n🎯 Multi-Timeframe Analysis Summary')
    print('=' * 70)
    print('✅ Timeframes: 1-hour (168 points), 4-hour (42 points), Daily (7 points)')
    print('✅ Coverage: 7 days of comprehensive market data')
    print('✅ Analysis: Technical indicators across all timeframes')
    print('✅ Patterns: Support/resistance, momentum, volume spikes')
    print('✅ Alignment: Trend consensus across timeframes')
    print('✅ Signal: Weighted decision with confidence scoring')
    
    print('\n🚀 Enhanced Features:')
    print('• Multi-timeframe trend alignment analysis')
    print('• Pattern recognition across different time horizons')
    print('• Weighted signal generation (Daily 50%, 4H 30%, 1H 20%)')
    print('• Comprehensive technical indicator analysis')
    print('• Volume and momentum pattern detection')
    print('• Support/resistance level identification')

if __name__ == '__main__':
    asyncio.run(test_multi_timeframe_system())
