#!/usr/bin/env python3
"""
Test script for AI Model Optimization
Tests the new model selection system and cost optimization
"""

import sys
import os
sys.path.append('backend')

from backend.ai_clients.model_selector import (
    select_optimal_model, get_cost_estimate, optimize_prompt_for_model,
    TaskType, Urgency, Complexity
)

def test_model_selection():
    """Test different model selection scenarios"""
    print("🧪 Testing AI Model Selection System")
    print("=" * 60)
    
    test_scenarios = [
        # (task_type, urgency, complexity, expected_model)
        (TaskType.NEWS_SENTIMENT, Urgency.LOW, Complexity.SIMPLE, "gpt-4o-mini"),
        (TaskType.BREAKING_NEWS, Urgency.CRITICAL, Complexity.COMPLEX, "gpt-4o"),
        (TaskType.TRADE_DECISION, Urgency.HIGH, Complexity.SIMPLE, "gpt-4o-mini"),
        (TaskType.ARBITRAGE_ANALYSIS, Urgency.MEDIUM, Complexity.COMPLEX, "gpt-4o"),
        (TaskType.TECHNICAL_ANALYSIS, Urgency.MEDIUM, Complexity.SIMPLE, "gpt-4o-mini"),
        (TaskType.RISK_ASSESSMENT, Urgency.LOW, Complexity.MEDIUM, "gpt-4o-mini"),
    ]
    
    total_cost = 0.0
    
    for i, (task_type, urgency, complexity, expected_model) in enumerate(test_scenarios, 1):
        print(f"\n📋 Test {i}: {task_type.value}")
        print(f"   Urgency: {urgency.value}, Complexity: {complexity.value}")
        
        # Get model configuration
        config = select_optimal_model(task_type, urgency, complexity)
        
        # Verify expected model
        actual_model = config["model"]
        status = "✅ PASS" if actual_model == expected_model else "❌ FAIL"
        print(f"   Expected: {expected_model}, Got: {actual_model} {status}")
        
        # Calculate cost estimate
        cost_info = get_cost_estimate(config, input_tokens=1000)
        total_cost += cost_info["total_cost"]
        
        print(f"   Max Tokens: {config['max_tokens']}")
        print(f"   Temperature: {config['temperature']}")
        print(f"   Cost Tier: {config['cost_tier']}")
        print(f"   Estimated Cost: ${cost_info['total_cost']:.6f}")
        print(f"   Reasoning: {config['reasoning']}")
    
    print(f"\n💰 Total Estimated Cost for {len(test_scenarios)} calls: ${total_cost:.6f}")
    return total_cost

def test_prompt_optimization():
    """Test prompt optimization for different cost tiers"""
    print("\n🔧 Testing Prompt Optimization")
    print("=" * 60)
    
    original_prompt = """
    Please analyze the following cryptocurrency market data and provide a detailed 
    technical analysis with sentiment analysis. Based on the following information, 
    I would like you to determine if this is a good trading decision for Bitcoin.
    The cryptocurrency market is showing bullish signals today.
    """
    
    # Test fast tier optimization
    fast_config = {"cost_tier": "fast", "model": "gpt-4o-mini"}
    optimized_fast = optimize_prompt_for_model(original_prompt, fast_config)
    
    # Test premium tier (no optimization)
    premium_config = {"cost_tier": "premium", "model": "gpt-4o"}
    optimized_premium = optimize_prompt_for_model(original_prompt, premium_config)
    
    print(f"📝 Original prompt length: {len(original_prompt)} chars")
    print(f"⚡ Fast tier optimized: {len(optimized_fast)} chars")
    print(f"💎 Premium tier: {len(optimized_premium)} chars")
    
    print(f"\n📄 Original:\n{original_prompt}")
    print(f"\n⚡ Fast Optimized:\n{optimized_fast}")
    
    savings = len(original_prompt) - len(optimized_fast)
    savings_pct = (savings / len(original_prompt)) * 100
    print(f"\n💾 Token savings: {savings} chars ({savings_pct:.1f}%)")

def test_cost_comparison():
    """Compare costs between old and new model strategies"""
    print("\n💸 Cost Comparison: Old vs New Strategy")
    print("=" * 60)
    
    # Simulate 1000 daily trading decisions
    daily_calls = 1000
    
    # Old strategy: All calls use expensive models
    old_cost_per_call = 0.045  # Estimated $0.045 per GPT-4 call
    old_daily_cost = daily_calls * old_cost_per_call
    
    # New strategy: 90% cheap, 10% expensive
    cheap_calls = int(daily_calls * 0.9)
    expensive_calls = int(daily_calls * 0.1)
    
    cheap_cost_per_call = 0.0003  # gpt-4o-mini cost
    expensive_cost_per_call = 0.015  # gpt-4o cost
    
    new_daily_cost = (cheap_calls * cheap_cost_per_call) + (expensive_calls * expensive_cost_per_call)
    
    savings = old_daily_cost - new_daily_cost
    savings_pct = (savings / old_daily_cost) * 100
    
    print(f"📊 Daily Trading Decisions: {daily_calls}")
    print(f"💰 Old Strategy Cost: ${old_daily_cost:.2f}/day")
    print(f"✨ New Strategy Cost: ${new_daily_cost:.2f}/day")
    print(f"💵 Daily Savings: ${savings:.2f} ({savings_pct:.1f}%)")
    print(f"📅 Monthly Savings: ${savings * 30:.2f}")
    print(f"📆 Annual Savings: ${savings * 365:.2f}")

def test_real_world_scenarios():
    """Test with realistic trading scenarios"""
    print("\n🌍 Real-World Trading Scenarios")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "Regular Market Hours - News Sentiment",
            "task": TaskType.NEWS_SENTIMENT,
            "urgency": Urgency.LOW,
            "complexity": Complexity.SIMPLE,
            "frequency": 100  # per day
        },
        {
            "name": "Breaking News Alert",
            "task": TaskType.BREAKING_NEWS,
            "urgency": Urgency.CRITICAL,
            "complexity": Complexity.COMPLEX,
            "frequency": 5  # per day
        },
        {
            "name": "Standard Trade Decision",
            "task": TaskType.TRADE_DECISION,
            "urgency": Urgency.MEDIUM,
            "complexity": Complexity.SIMPLE,
            "frequency": 50  # per day
        },
        {
            "name": "Complex Arbitrage Analysis",
            "task": TaskType.ARBITRAGE_ANALYSIS,
            "urgency": Urgency.HIGH,
            "complexity": Complexity.COMPLEX,
            "frequency": 10  # per day
        }
    ]
    
    total_daily_cost = 0.0
    
    for scenario in scenarios:
        config = select_optimal_model(
            scenario["task"], 
            scenario["urgency"], 
            scenario["complexity"]
        )
        
        cost_info = get_cost_estimate(config, input_tokens=800)
        scenario_cost = cost_info["total_cost"] * scenario["frequency"]
        total_daily_cost += scenario_cost
        
        print(f"\n📈 {scenario['name']}")
        print(f"   Model: {config['model']}")
        print(f"   Frequency: {scenario['frequency']}/day")
        print(f"   Cost per call: ${cost_info['total_cost']:.6f}")
        print(f"   Daily cost: ${scenario_cost:.4f}")
    
    print(f"\n💰 Total Daily Cost (Real Scenarios): ${total_daily_cost:.4f}")
    print(f"📅 Monthly Cost: ${total_daily_cost * 30:.2f}")

def main():
    """Run all tests"""
    print("🚀 AI Model Optimization Test Suite")
    print("=" * 80)
    
    try:
        # Run all tests
        test_model_selection()
        test_prompt_optimization()
        test_cost_comparison()
        test_real_world_scenarios()
        
        print("\n✅ All tests completed successfully!")
        print("🎯 AI Model optimization is working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
