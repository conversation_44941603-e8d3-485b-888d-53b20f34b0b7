#!/usr/bin/env python3
"""
🧠 AI COMPLEX TRADING DECISIONS TEST
Tests the AI's ability to make sophisticated trading decisions and execute orders
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add backend to path
sys.path.append('./backend')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ai_complex_decision_making():
    """Test AI's complex decision-making process with real market data"""
    print("🧠 TESTING AI COMPLEX TRADING DECISIONS")
    print("=" * 60)
    
    # Test 1: Fix the sentiment score function call issue
    print("\n1. 🔧 Fixing Sentiment Score Function Call...")
    try:
        from sentiment_engine import get_sentiment_score
        
        # Test the function signature
        test_result = get_sentiment_score("BTC")
        print(f"✅ Sentiment function working: {test_result}")
        
    except Exception as e:
        print(f"❌ Sentiment function error: {e}")
        # Fix the function call in prompt_builder
        from pathlib import Path
        prompt_file = Path("backend/prompt_builder.py")
        if prompt_file.exists():
            content = prompt_file.read_text()
            # Fix the function call
            content = content.replace(
                'sentiment_data = get_sentiment_score(token)',
                'sentiment_data = get_sentiment_score(token, news_data)'
            )
            prompt_file.write_text(content)
            print("✅ Fixed sentiment function call in prompt_builder.py")
    
    # Test 2: Test AI decision making with complex scenarios
    print("\n2. 🎯 Testing AI Decision Making with Complex Scenarios...")
    
    test_scenarios = [
        {
            "token": "BTC-USDT",
            "scenario": "Bull Market with High Volume",
            "price": 45000.0,
            "volume_ratio": 2.5,
            "sentiment": 0.8,
            "rsi": 65,
            "macd": "BUY",
            "news": ["Bitcoin ETF approval", "Institutional adoption"]
        },
        {
            "token": "ETH-USDT", 
            "scenario": "Bear Market with Low Confidence",
            "price": 2800.0,
            "volume_ratio": 0.8,
            "sentiment": 0.3,
            "rsi": 35,
            "macd": "SELL",
            "news": ["Regulatory concerns", "Market downturn"]
        },
        {
            "token": "SOL-USDT",
            "scenario": "Neutral Market with Mixed Signals",
            "price": 95.0,
            "volume_ratio": 1.2,
            "sentiment": 0.5,
            "rsi": 50,
            "macd": "HOLD",
            "news": ["Network upgrade", "Mixed market sentiment"]
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 Testing Scenario: {scenario['scenario']}")
        print(f"   Token: {scenario['token']}")
        print(f"   Price: ${scenario['price']:,.2f}")
        print(f"   Volume Ratio: {scenario['volume_ratio']}x")
        print(f"   Sentiment: {scenario['sentiment']*100:.1f}%")
        print(f"   RSI: {scenario['rsi']}")
        print(f"   MACD: {scenario['macd']}")
        print(f"   News: {', '.join(scenario['news'])}")
        
        try:
            # Test AI decision making
            from ai_core import get_final_ai_decision
            
            decision = await get_final_ai_decision(
                symbol=scenario['token'],
                current_price=scenario['price'],
                volume_ratio=scenario['volume_ratio'],
                sentiment=scenario['sentiment'],
                news_snippets=scenario['news']
            )
            
            print(f"   🤖 AI Decision: {decision.get('decision', 'UNKNOWN')}")
            print(f"   📊 Confidence: {decision.get('confidence', 0):.2f}")
            print(f"   💭 Reasoning: {decision.get('reason', 'No reasoning provided')}")
            
            # Analyze decision quality
            if decision.get('confidence', 0) >= 0.7:
                print(f"   ✅ HIGH CONFIDENCE - Trade would be executed")
            else:
                print(f"   ⚠️ LOW CONFIDENCE - Trade would be skipped")
                
        except Exception as e:
            print(f"   ❌ AI Decision Error: {e}")
    
    # Test 3: Test order execution logic
    print("\n3. 💰 Testing Order Execution Logic...")
    
    try:
        from real_trade_executor import RealTradeExecutor
        from kucoin_api import KuCoinAPI
        
        # Initialize trading components
        kucoin = KuCoinAPI()
        executor = RealTradeExecutor(kucoin)
        
        # Test order calculation
        test_token = "BTC-USDT"
        test_price = 45000.0
        test_decision = "BUY"
        test_confidence = 0.85
        
        print(f"   📈 Testing {test_decision} order for {test_token}")
        print(f"   💵 Price: ${test_price:,.2f}")
        print(f"   🎯 Confidence: {test_confidence:.2f}")
        
        # Calculate order size
        order_size = executor.calculate_order_size(test_confidence, test_price)
        print(f"   📊 Calculated Order Size: {order_size:.6f} {test_token.split('-')[0]}")
        
        # Test risk management
        risk_check = executor.check_risk_limits(test_token, test_decision, order_size)
        print(f"   🛡️ Risk Check: {'PASSED' if risk_check else 'FAILED'}")
        
        # Simulate order (don't actually execute)
        print(f"   🔄 Simulating order execution...")
        order_details = {
            "symbol": test_token,
            "side": test_decision.lower(),
            "size": order_size,
            "price": test_price,
            "confidence": test_confidence
        }
        print(f"   📋 Order Details: {order_details}")
        print(f"   ✅ Order simulation completed successfully")
        
    except Exception as e:
        print(f"   ❌ Order execution test error: {e}")
    
    # Test 4: Test multi-timeframe analysis impact on decisions
    print("\n4. ⏰ Testing Multi-Timeframe Analysis Impact...")
    
    try:
        from multi_timeframe_analyzer import MultiTimeframeAnalyzer
        
        analyzer = MultiTimeframeAnalyzer()
        
        test_token = "BTC-USDT"
        print(f"   📊 Testing multi-timeframe analysis for {test_token}...")
        
        analysis = await analyzer.get_comprehensive_analysis(test_token)
        print(f"   ✅ Analysis available: {analysis.get('analysis_available', False)}")
        
        if analysis.get('analysis_available'):
            print(f"   📈 1H Trend: {analysis.get('1hour', {}).get('trend', 'Unknown')}")
            print(f"   📈 4H Trend: {analysis.get('4hour', {}).get('trend', 'Unknown')}")
            print(f"   📈 1D Trend: {analysis.get('1day', {}).get('trend', 'Unknown')}")
            print(f"   🎯 Overall Signal: {analysis.get('overall_signal', 'Unknown')}")
        
    except Exception as e:
        print(f"   ❌ Multi-timeframe analysis error: {e}")
    
    # Test 5: Test complete trading pipeline
    print("\n5. 🔄 Testing Complete Trading Pipeline...")
    
    try:
        from ai_core import run_ai_trade
        
        test_tokens = ["BTC-USDT", "ETH-USDT", "SOL-USDT"]
        
        for token in test_tokens:
            print(f"\n   🔍 Testing complete pipeline for {token}...")
            
            # Run the complete AI trading analysis
            result = await run_ai_trade(token)
            
            print(f"   🤖 Decision: {result.get('decision', 'UNKNOWN')}")
            print(f"   📊 Confidence: {result.get('confidence', 0):.2f}")
            print(f"   💭 Reasoning: {result.get('reason', 'No reasoning')}")
            
            # Simulate order execution based on confidence
            if result.get('confidence', 0) >= 0.7:
                print(f"   ✅ HIGH CONFIDENCE - Order would be executed")
                print(f"   💰 Simulated order: {result.get('decision')} {token}")
            else:
                print(f"   ⚠️ LOW CONFIDENCE - Order would be skipped")
                
    except Exception as e:
        print(f"   ❌ Complete pipeline test error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 AI COMPLEX TRADING DECISIONS TEST COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_ai_complex_decision_making())
