# 🤖 Telegram Integration Guide - AlphaPredatorBot

## 📋 Overview

The AlphaPredatorBot features a comprehensive Telegram integration system that provides real-time notifications, bot control, and interactive trading decisions. This guide covers the complete implementation, setup, and usage.

## 🏗️ Architecture

### Core Components

1. **telegram_utils.py** - Core messaging utilities and notification functions
2. **telegram_notifier.py** - Main bot application with commands and callbacks
3. **telegram_callback_listener.py** - Handles user interactions and approvals
4. **telegram_news_listener.py** - Monitors news channels for trading signals

### Integration Points

- **Trading System**: Real-time trade notifications and approvals
- **News System**: Sentiment alerts and market updates
- **Bot Control**: Start/stop/status commands for Alpha and Micro bots
- **Analytics**: Daily PnL summaries and performance reports

## ⚙️ Configuration

### Environment Variables

```bash
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHANNEL_ID=-1002455379586

# Discord Integration (Optional)
DISCORD_BOT_TOKEN=MTM3NTQxNDEyMTM1MjQwMDk3Ng.GfEX08.FGWNUO_BgbJ7xkajihC99nJl6LZ8KN_2ulF4oY
KRYPTONEWS_CHANNEL_ID=1324277704471740516
```

### Bot Setup

1. **Create Telegram Bot**:
   - Message @BotFather on Telegram
   - Use `/newbot` command
   - Get your bot token

2. **Get Channel ID**:
   - Add bot to your channel
   - Send a message and check bot logs
   - Or use Telegram API to get chat ID

## 🚀 Features

### 1. Trade Notifications

```python
await notify_trade(
    application=app,
    token="BTC-USDT",
    action="BUY",
    quantity=0.001,
    price=45000.0,
    strategy="AI Signal",
    reason="Strong bullish sentiment detected"
)
```

**Output**:
```
🚀 **TRADE ALERT**
Token: `BTC-USDT`
Action: **BUY**
Quantity: `0.001`
Price: `$45000.000000`
Strategy: `AI Signal`
Reason: Strong bullish sentiment detected
```

### 2. Bot Control Commands

| Command | Description | Example |
|---------|-------------|---------|
| `/start` | Initialize bot | `/start` |
| `/micro_start` | Start micro bot | `/micro_start` |
| `/micro_stop` | Stop micro bot | `/micro_stop` |
| `/micro_status` | Get micro bot status | `/micro_status` |
| `/alpha_start` | Start alpha bot | `/alpha_start` |
| `/alpha_stop` | Stop alpha bot | `/alpha_stop` |
| `/alpha_status` | Get alpha bot status | `/alpha_status` |

### 3. Interactive Callbacks

The bot supports interactive buttons for trade approvals:

```python
# User sees buttons: [✅ Buy] [❌ Skip]
# On click, executes corresponding action
```

### 4. News Alerts

```python
await notify_news_alert(
    headline="Bitcoin breaks $50K resistance",
    sentiment="BULLISH"
)
```

### 5. Gem Suggestions

```python
await notify_new_gem_suggestion(
    token="NEWCOIN-USDT",
    score=95.5,
    reason="High volume spike with positive sentiment"
)
```

### 6. Daily PnL Reports

Automated daily reports at 9:00 AM:

```
📊 **Daily PnL Summary**
Total Trades: 15
Successful: 12 (80%)
Total PnL: +$245.67
Best Trade: DOGE-USDT (+$45.23)
Worst Trade: ADA-USDT (-$12.45)
```

## 🔧 Implementation Details

### Core Functions

#### 1. Message Sending

```python
async def send_telegram_message(message: str, chat_id: Optional[str] = None):
    """Send a message via Telegram with error handling"""
    try:
        bot = get_telegram_bot()
        if not bot:
            logger.error("❌ Telegram bot not initialized")
            return False
            
        target_chat = chat_id or os.getenv("TELEGRAM_CHANNEL_ID")
        if not target_chat:
            logger.error("❌ No Telegram chat ID configured")
            return False
            
        await bot.send_message(
            chat_id=target_chat, 
            text=message, 
            parse_mode="Markdown"
        )
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to send Telegram message: {e}")
        return False
```

#### 2. Bot Instance Management

```python
def get_telegram_bot():
    """Singleton pattern for bot instance"""
    global _bot_instance
    if _bot_instance is None:
        token = os.getenv("TELEGRAM_BOT_TOKEN")
        if token:
            _bot_instance = Bot(token=token)
    return _bot_instance
```

#### 3. Callback Handling

```python
async def handle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle user button clicks"""
    query = update.callback_query
    await query.answer()
    data = query.data
    
    if data.startswith("buy_"):
        token = data.replace("buy_", "")
        await query.edit_message_text(f"✅ Approved. Buying `{token}`")
        logger.info(f"[Telegram Callback] User approved BUY for {token}")
    elif data.startswith("skip_"):
        token = data.replace("skip_", "")
        await query.edit_message_text(f"❌ Skipped `{token}`")
```

## 🧪 Testing

### 1. Basic Message Test

Create a test script to verify Telegram connectivity:

```python
# test_telegram.py
import asyncio
from backend.telegram_utils import send_telegram_message

async def test_basic_message():
    """Test basic message sending"""
    result = await send_telegram_message("🧪 **TEST MESSAGE**\nTelegram integration is working!")
    print(f"Message sent: {result}")

if __name__ == "__main__":
    asyncio.run(test_basic_message())
```

### 2. Trade Notification Test

```python
# test_trade_notification.py
import asyncio
from backend.telegram_notifier import test_notify

async def test_trade_alert():
    """Test trade notification"""
    await test_notify()
    print("Trade notification test completed")

if __name__ == "__main__":
    asyncio.run(test_trade_alert())
```

### 3. Bot Commands Test

Start the bot and test commands:

```bash
cd backend
python telegram_notifier.py
```

Then in Telegram:
- `/start` - Should respond with welcome message
- `/micro_status` - Should show micro bot status
- `/alpha_status` - Should show alpha bot status

## 🔒 Security Features

### 1. Rate Limiting

```python
# Built-in rate limiting to prevent spam
MAX_MESSAGES_PER_MINUTE = 20
```

### 2. User Validation

```python
# Only authorized users can control bots
AUTHORIZED_USERS = [123456789, 987654321]  # Telegram user IDs
```

### 3. Error Handling

```python
try:
    await bot.send_message(chat_id=chat_id, text=message)
    return True
except telegram.error.TelegramError as e:
    logger.error(f"Telegram API error: {e}")
    return False
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    return False
```

## 📊 Monitoring & Logging

### Log Files

- **telegram_notifier.log** - Main bot activity
- **telegram_errors.log** - Error tracking
- **telegram_callbacks.log** - User interactions

### Metrics Tracked

- Messages sent per hour
- Command usage frequency
- Callback response times
- Error rates by type

## 🚀 Production Deployment

### 1. Docker Integration

```dockerfile
# Add to Dockerfile
RUN pip install python-telegram-bot
COPY backend/telegram_*.py /app/backend/
```

### 2. Environment Setup

```bash
# Production environment variables
export TELEGRAM_BOT_TOKEN="your_production_token"
export TELEGRAM_CHANNEL_ID="your_channel_id"
```

### 3. Process Management

```bash
# Start Telegram bot as background service
nohup python backend/telegram_notifier.py > telegram.log 2>&1 &
```

### 4. Health Checks

```python
# Health check endpoint
@app.get("/telegram/health")
async def telegram_health():
    bot = get_telegram_bot()
    if bot:
        try:
            await bot.get_me()
            return {"status": "healthy", "bot_active": True}
        except:
            return {"status": "unhealthy", "bot_active": False}
    return {"status": "unhealthy", "bot_active": False}
```

## 🔧 Troubleshooting

### Common Issues

1. **Bot Not Responding**
   - Check TELEGRAM_BOT_TOKEN is valid
   - Verify bot is added to channel
   - Check network connectivity

2. **Messages Not Sending**
   - Verify TELEGRAM_CHANNEL_ID is correct
   - Check bot permissions in channel
   - Review error logs

3. **Callbacks Not Working**
   - Ensure callback handlers are registered
   - Check button data format
   - Verify user permissions

### Debug Commands

```bash
# Test bot connectivity
python -c "from backend.telegram_utils import get_telegram_bot; print(get_telegram_bot())"

# Test message sending
python backend/telegram_notifier.py test

# Check logs
tail -f backend/logs/telegram_notifier.log
```

## 📈 Performance Optimization

### 1. Message Batching

```python
# Batch multiple notifications
async def send_batch_notifications(messages: List[str]):
    combined = "\n\n".join(messages)
    return await send_telegram_message(combined)
```

### 2. Async Processing

```python
# Non-blocking notifications
asyncio.create_task(notify_trade(...))
```

### 3. Connection Pooling

```python
# Reuse bot instances
_bot_instance = None  # Global singleton
```

## 🎯 Best Practices

1. **Message Formatting**
   - Use Markdown for formatting
   - Keep messages concise
   - Include relevant emojis

2. **Error Handling**
   - Always catch exceptions
   - Log errors with context
   - Provide fallback mechanisms

3. **User Experience**
   - Respond quickly to callbacks
   - Provide clear status updates
   - Use interactive buttons

4. **Security**
   - Validate all inputs
   - Rate limit requests
   - Log security events

## 📚 API Reference

### Core Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `send_telegram_message()` | message, chat_id | bool | Send basic message |
| `notify_trade()` | token, action, quantity, price, strategy, reason | bool | Send trade alert |
| `notify_news_alert()` | headline, sentiment | bool | Send news notification |
| `notify_bot_status()` | bot_name, status, details | bool | Send status update |
| `get_telegram_bot()` | None | Bot | Get bot instance |

### Command Handlers

| Command | Handler | Description |
|---------|---------|-------------|
| `/start` | `start_command()` | Initialize bot |
| `/micro_start` | `micro_start_command()` | Start micro bot |
| `/micro_stop` | `micro_stop_command()` | Stop micro bot |
| `/micro_status` | `micro_status_command()` | Get
