#!/usr/bin/env python3
"""
Test script to verify batch processing logic with enhanced CoinGecko integration.
This tests that batch operations properly use the smart availability checking.
"""

import asyncio
import logging
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from coingecko_enhanced import coingecko_enhanced, batch_get_comprehensive_token_data, batch_check_binance_availability
from exchange_data import get_prices_from_all_exchanges

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_batch_binance_availability():
    """Test batch Binance availability checking"""
    print("\n" + "="*60)
    print("TESTING BATCH BINANCE AVAILABILITY CHECKING")
    print("="*60)
    
    test_symbols = [
        "BTC-USDT",    # Should be available on Binance
        "ETH-USDT",    # Should be available on Binance
        "NODE-USDT",   # Likely not available on Binance
        "MC-USDT",     # Likely not available on Binance
        "ZEND-USDT",   # Likely not available on Binance
        "ADA-USDT",    # Should be available on Binance
        "LINK-USDT"    # Should be available on Binance
    ]
    
    print(f"Testing batch availability check for: {test_symbols}")
    
    # Test batch availability checking
    availability_results = await batch_check_binance_availability(test_symbols)
    
    print("\nBatch Binance Availability Results:")
    for symbol, available in availability_results.items():
        status = "✅ Available" if available else "❌ Not Available"
        print(f"  {symbol}: {status}")
    
    return availability_results

async def test_batch_comprehensive_data():
    """Test batch comprehensive data fetching"""
    print("\n" + "="*60)
    print("TESTING BATCH COMPREHENSIVE DATA FETCHING")
    print("="*60)
    
    test_symbols = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
    
    print(f"Testing batch comprehensive data for: {test_symbols}")
    
    # Test batch comprehensive data
    comprehensive_results = await batch_get_comprehensive_token_data(test_symbols)
    
    print("\nBatch Comprehensive Data Results:")
    for symbol, data in comprehensive_results.items():
        if data:
            price = data.get('current_price', 'N/A')
            market_cap_rank = data.get('market_cap_rank', 'N/A')
            exchanges = list(data.get('exchanges', {}).keys())
            print(f"  {symbol}:")
            print(f"    Price: ${price}")
            print(f"    Market Cap Rank: {market_cap_rank}")
            print(f"    Available on exchanges: {exchanges[:5]}...")  # Show first 5
        else:
            print(f"  {symbol}: No data available")
    
    return comprehensive_results

async def test_smart_exchange_price_fetching():
    """Test that exchange price fetching uses smart availability checking"""
    print("\n" + "="*60)
    print("TESTING SMART EXCHANGE PRICE FETCHING")
    print("="*60)
    
    test_symbols = [
        "BTC-USDT",    # Should fetch from both KuCoin and Binance
        "NODE-USDT",   # Should only fetch from KuCoin
        "ETH-USDT"     # Should fetch from both KuCoin and Binance
    ]
    
    for symbol in test_symbols:
        print(f"\n--- Testing {symbol} ---")
        
        # Check availability first
        binance_available = coingecko_enhanced.is_token_available_on_binance(symbol)
        print(f"Binance availability for {symbol}: {binance_available}")
        
        # Fetch prices from all exchanges
        prices = await get_prices_from_all_exchanges(symbol)
        print(f"Prices fetched: {prices}")
        
        # Verify logic: if not available on Binance, should only have KuCoin price
        if not binance_available:
            if "Binance" in prices:
                print(f"⚠️  WARNING: Binance price fetched despite availability check!")
            else:
                print(f"✅ Correctly skipped Binance for {symbol}")
        else:
            print(f"✅ Token available on Binance, fetching allowed")

async def test_batch_processing_pipeline():
    """Test that batch processing respects smart availability checking"""
    print("\n" + "="*60)
    print("TESTING BATCH PROCESSING PIPELINE")
    print("="*60)
    
    # Import the optimized pipeline
    try:
        from optimized_data_pipeline import optimized_pipeline
        
        test_symbols = ["BTC-USDT", "NODE-USDT", "ETH-USDT"]
        
        print(f"Testing pipeline batch processing for: {test_symbols}")
        
        # Process tokens through the pipeline
        results = await optimized_pipeline.batch_process_tokens(test_symbols)
        
        print(f"\nPipeline processed {len(results)} tokens:")
        for result in results:
            print(f"  {result.symbol}:")
            print(f"    Success: {result.success}")
            print(f"    Processing time: {result.processing_time:.2f}s")
            if result.market_data:
                price = result.market_data.get('price', 'N/A')
                print(f"    Price: {price}")
            if result.error_message:
                print(f"    Error: {result.error_message}")
        
        return results
        
    except ImportError as e:
        print(f"Could not import optimized_data_pipeline: {e}")
        return []

def test_rate_limiting():
    """Test that rate limiting is working properly"""
    print("\n" + "="*60)
    print("TESTING RATE LIMITING")
    print("="*60)
    
    import time
    
    test_symbols = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
    
    print("Testing individual requests with rate limiting...")
    start_time = time.time()
    
    for symbol in test_symbols:
        request_start = time.time()
        data = coingecko_enhanced.get_comprehensive_token_data(symbol)
        request_end = time.time()
        
        print(f"  {symbol}: {request_end - request_start:.2f}s")
    
    total_time = time.time() - start_time
    print(f"Total time for {len(test_symbols)} requests: {total_time:.2f}s")
    print(f"Average time per request: {total_time/len(test_symbols):.2f}s")
    
    # Should be at least 1 second per request due to rate limiting
    expected_min_time = len(test_symbols) * 1.0
    if total_time >= expected_min_time:
        print(f"✅ Rate limiting working correctly (took {total_time:.2f}s, expected ≥{expected_min_time:.2f}s)")
    else:
        print(f"⚠️  Rate limiting may not be working (took {total_time:.2f}s, expected ≥{expected_min_time:.2f}s)")

async def main():
    """Run all batch processing tests"""
    print("🚀 Starting Batch CoinGecko Integration Tests")
    print("This will test that batch operations properly use smart availability checking")
    
    try:
        # Test 1: Batch Binance availability checking
        await test_batch_binance_availability()
        
        # Test 2: Batch comprehensive data fetching
        await test_batch_comprehensive_data()
        
        # Test 3: Smart exchange price fetching
        await test_smart_exchange_price_fetching()
        
        # Test 4: Rate limiting
        test_rate_limiting()
        
        # Test 5: Batch processing pipeline (if available)
        await test_batch_processing_pipeline()
        
        print("\n" + "="*60)
        print("✅ ALL BATCH PROCESSING TESTS COMPLETED")
        print("="*60)
        print("The enhanced CoinGecko integration with smart availability checking")
        print("is working correctly for both individual and batch operations.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
