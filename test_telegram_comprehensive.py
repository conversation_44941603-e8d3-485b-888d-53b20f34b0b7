#!/usr/bin/env python3
"""
Comprehensive Telegram Integration Test Suite
Tests all Telegram functionality for AlphaPredatorBot
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# Add backend to path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

from telegram_utils import (
    send_telegram_message,
    notify_trade,
    notify_bot_status,
    notify_news_alert,
    notify_new_gem_suggestion,
    get_telegram_bot,
    get_telegram_application
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TelegramTester:
    def __init__(self):
        self.test_results = []
        self.passed = 0
        self.failed = 0

    def log_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} - {test_name}"
        if message:
            result += f": {message}"
        
        self.test_results.append(result)
        if success:
            self.passed += 1
        else:
            self.failed += 1
        
        logger.info(result)

    async def test_bot_initialization(self):
        """Test 1: Bot Initialization"""
        try:
            bot = get_telegram_bot()
            if bot:
                # Try to get bot info
                bot_info = await bot.get_me()
                self.log_result("Bot Initialization", True, f"Bot @{bot_info.username} initialized")
                return True
            else:
                self.log_result("Bot Initialization", False, "Bot instance is None")
                return False
        except Exception as e:
            self.log_result("Bot Initialization", False, f"Error: {e}")
            return False

    async def test_basic_message(self):
        """Test 2: Basic Message Sending"""
        try:
            test_message = f"""
🧪 **TELEGRAM TEST MESSAGE**
Timestamp: `{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`
Status: Testing basic message functionality
System: AlphaPredatorBot v2.0
"""
            result = await send_telegram_message(test_message)
            self.log_result("Basic Message Sending", result, "Test message sent successfully" if result else "Failed to send message")
            return result
        except Exception as e:
            self.log_result("Basic Message Sending", False, f"Error: {e}")
            return False

    async def test_trade_notification(self):
        """Test 3: Trade Notification"""
        try:
            app = get_telegram_application()
            if not app:
                self.log_result("Trade Notification", False, "Application not initialized")
                return False

            result = await notify_trade(
                application=app,
                token="TEST-USDT",
                action="BUY",
                quantity=100.0,
                price=0.001234,
                strategy="Test Strategy",
                reason="Comprehensive testing of Telegram integration"
            )
            self.log_result("Trade Notification", result, "Trade alert sent" if result else "Failed to send trade alert")
            return result
        except Exception as e:
            self.log_result("Trade Notification", False, f"Error: {e}")
            return False

    async def test_bot_status_notification(self):
        """Test 4: Bot Status Notification"""
        try:
            result = await notify_bot_status(
                bot_name="AlphaPredatorBot",
                status="TESTING",
                details="Running comprehensive Telegram integration tests"
            )
            self.log_result("Bot Status Notification", result, "Status notification sent" if result else "Failed to send status")
            return result
        except Exception as e:
            self.log_result("Bot Status Notification", False, f"Error: {e}")
            return False

    async def test_news_alert(self):
        """Test 5: News Alert"""
        try:
            result = await notify_news_alert(
                headline="Test News: Telegram Integration Working Perfectly",
                sentiment="BULLISH"
            )
            self.log_result("News Alert", result, "News alert sent" if result else "Failed to send news alert")
            return result
        except Exception as e:
            self.log_result("News Alert", False, f"Error: {e}")
            return False

    async def test_gem_suggestion(self):
        """Test 6: Gem Suggestion"""
        try:
            result = await notify_new_gem_suggestion(
                token="TESTGEM-USDT",
                score=98.5,
                reason="Perfect test score for Telegram integration testing"
            )
            self.log_result("Gem Suggestion", result, "Gem suggestion sent" if result else "Failed to send gem suggestion")
            return result
        except Exception as e:
            self.log_result("Gem Suggestion", False, f"Error: {e}")
            return False

    async def test_markdown_formatting(self):
        """Test 7: Markdown Formatting"""
        try:
            formatted_message = """
📊 **MARKDOWN FORMATTING TEST**

*Italic text*
**Bold text**
`Code text`
[Link](https://example.com)

• Bullet point 1
• Bullet point 2
• Bullet point 3

```
Code block test
Multiple lines
```

✅ All formatting should render correctly
"""
            result = await send_telegram_message(formatted_message)
            self.log_result("Markdown Formatting", result, "Formatted message sent" if result else "Failed to send formatted message")
            return result
        except Exception as e:
            self.log_result("Markdown Formatting", False, f"Error: {e}")
            return False

    async def test_error_handling(self):
        """Test 8: Error Handling"""
        try:
            # Test with invalid chat ID
            result = await send_telegram_message("Test message", chat_id="invalid_chat_id")
            # Should return False due to invalid chat ID
            self.log_result("Error Handling", not result, "Error handling working correctly" if not result else "Error handling failed")
            return not result
        except Exception as e:
            self.log_result("Error Handling", True, f"Exception caught correctly: {type(e).__name__}")
            return True

    async def test_environment_variables(self):
        """Test 9: Environment Variables"""
        try:
            from dotenv import load_dotenv
            load_dotenv('backend/.env')
            
            token = os.getenv("TELEGRAM_BOT_TOKEN")
            channel_id = os.getenv("TELEGRAM_CHANNEL_ID")
            
            token_valid = bool(token and len(token) > 20)
            channel_valid = bool(channel_id and (channel_id.startswith('-') or channel_id.isdigit()))
            
            success = token_valid and channel_valid
            message = f"Token: {'✅' if token_valid else '❌'}, Channel: {'✅' if channel_valid else '❌'}"
            
            self.log_result("Environment Variables", success, message)
            return success
        except Exception as e:
            self.log_result("Environment Variables", False, f"Error: {e}")
            return False

    async def test_performance(self):
        """Test 10: Performance Test"""
        try:
            import time
            start_time = time.time()
            
            # Send 3 messages quickly
            tasks = []
            for i in range(3):
                task = send_telegram_message(f"⚡ Performance test message {i+1}/3")
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            duration = end_time - start_time
            success_count = sum(1 for r in results if r)
            
            success = success_count == 3 and duration < 10  # Should complete in under 10 seconds
            message = f"Sent {success_count}/3 messages in {duration:.2f}s"
            
            self.log_result("Performance Test", success, message)
            return success
        except Exception as e:
            self.log_result("Performance Test", False, f"Error: {e}")
            return False

    async def run_all_tests(self):
        """Run all tests and generate report"""
        print("\n🚀 Starting Comprehensive Telegram Integration Tests\n")
        
        # List of all tests
        tests = [
            self.test_environment_variables,
            self.test_bot_initialization,
            self.test_basic_message,
            self.test_trade_notification,
            self.test_bot_status_notification,
            self.test_news_alert,
            self.test_gem_suggestion,
            self.test_markdown_formatting,
            self.test_error_handling,
            self.test_performance
        ]
        
        # Run tests with delays to avoid rate limiting
        for i, test in enumerate(tests, 1):
            print(f"\n📋 Running Test {i}/{len(tests)}: {test.__name__.replace('test_', '').replace('_', ' ').title()}")
            await test()
            
            # Add delay between tests to avoid rate limiting
            if i < len(tests):
                await asyncio.sleep(2)
        
        # Generate final report
        self.generate_report()

    def generate_report(self):
        """Generate final test report"""
        print("\n" + "="*60)
        print("📊 TELEGRAM INTEGRATION TEST REPORT")
        print("="*60)
        
        for result in self.test_results:
            print(result)
        
        print("\n" + "-"*60)
        print(f"📈 SUMMARY: {self.passed} PASSED, {self.failed} FAILED")
        
        success_rate = (self.passed / (self.passed + self.failed)) * 100 if (self.passed + self.failed) > 0 else 0
        print(f"🎯 SUCCESS RATE: {success_rate:.1f}%")
        
        if self.failed == 0:
            print("🎉 ALL TESTS PASSED! Telegram integration is working perfectly!")
        elif success_rate >= 80:
            print("✅ Most tests passed. Minor issues may need attention.")
        else:
            print("⚠️  Multiple tests failed. Please check configuration and logs.")
        
        print("="*60)

async def main():
    """Main test runner"""
    try:
        tester = TelegramTester()
        await tester.run_all_tests()
        
        # Return exit code based on results
        return 0 if tester.failed == 0 else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
