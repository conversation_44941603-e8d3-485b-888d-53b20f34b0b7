{"timestamp": "2025-07-11T12:06:50.061253", "summary": {"total_tests": 16, "passed_tests": 3, "failed_tests": 13, "success_rate": 18.75, "average_response_time": 0.007645910978317261}, "detailed_results": [{"test_name": "Health Check", "success": true, "details": "Status: ok, Version: 1.1.0", "response_time": 0.0633091926574707, "timestamp": "2025-07-11T12:06:49.997742"}, {"test_name": "News Sentiment - Bitcoin Positive News", "success": false, "details": "HTTP 404: {\"detail\":\"Not Found\"}", "response_time": 0.005089998245239258, "timestamp": "2025-07-11T12:06:50.003813"}, {"test_name": "News Sentiment - Ethereum Negative News", "success": false, "details": "HTTP 404: {\"detail\":\"Not Found\"}", "response_time": 0.005463838577270508, "timestamp": "2025-07-11T12:06:50.009441"}, {"test_name": "News Sentiment - Neutral Market Update", "success": false, "details": "HTTP 404: {\"detail\":\"Not Found\"}", "response_time": 0.004072904586791992, "timestamp": "2025-07-11T12:06:50.013543"}, {"test_name": "News Sentiment - Regulatory News", "success": false, "details": "HTTP 404: {\"detail\":\"Not Found\"}", "response_time": 0.0057599544525146484, "timestamp": "2025-07-11T12:06:50.019388"}, {"test_name": "News Sentiment - DeFi Innovation", "success": false, "details": "HTTP 404: {\"detail\":\"Not Found\"}", "response_time": 0.005917787551879883, "timestamp": "2025-07-11T12:06:50.025368"}, {"test_name": "TokenMetrics - Bitcoin", "success": false, "details": "HTTP 401: {\"detail\":\"Not authenticated\"}", "response_time": 0.00820302963256836, "timestamp": "2025-07-11T12:06:50.033791"}, {"test_name": "TokenMetrics - Ethereum", "success": false, "details": "HTTP 401: {\"detail\":\"Not authenticated\"}", "response_time": 0.003292083740234375, "timestamp": "2025-07-11T12:06:50.037135"}, {"test_name": "TokenMetrics - Cardano", "success": false, "details": "HTTP 401: {\"detail\":\"Not authenticated\"}", "response_time": 0.0027341842651367188, "timestamp": "2025-07-11T12:06:50.039899"}, {"test_name": "TokenMetrics - Solana", "success": false, "details": "HTTP 401: {\"detail\":\"Not authenticated\"}", "response_time": 0.002811908721923828, "timestamp": "2025-07-11T12:06:50.042744"}, {"test_name": "TokenMetrics - Polygon", "success": false, "details": "HTTP 401: {\"detail\":\"Not authenticated\"}", "response_time": 0.0022668838500976562, "timestamp": "2025-07-11T12:06:50.045038"}, {"test_name": "Real News Fetching", "success": false, "details": "HTTP 405: {\"detail\":\"Method Not Allowed\"}", "response_time": 0.0028831958770751953, "timestamp": "2025-07-11T12:06:50.047989"}, {"test_name": "API Performance", "success": true, "details": "5/5 requests successful, avg response time: 0.00s", "response_time": 0.0042248725891113285, "timestamp": "2025-07-11T12:06:50.054349"}, {"test_name": "Error <PERSON> - /api/invalid", "success": true, "details": "Correctly returned 404", "response_time": 0.002151012420654297, "timestamp": "2025-07-11T12:06:50.056554"}, {"test_name": "Error Handling - /api/tokenmetrics/INVALID", "success": false, "details": "Expected 404, got 401", "response_time": 0.0021848678588867188, "timestamp": "2025-07-11T12:06:50.058762"}, {"test_name": "Error <PERSON>ling - /api/news/sentiment", "success": false, "details": "Expected 405, got 404", "response_time": 0.001968860626220703, "timestamp": "2025-07-11T12:06:50.060749"}]}