#!/usr/bin/env python3
"""
Bot Manager Test Script
======================

This script tests the enhanced bot manager functionality to ensure:
- Bot manager starts and stops correctly
- Individual bots can be started and stopped
- Health monitoring works
- Error recovery mechanisms function properly
- Status reporting is accurate

Usage:
    python test_bot_manager.py
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append('backend')

from backend.bot_manager import bot_manager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_bot_manager():
    """Test the bot manager functionality."""
    logger.info("🧪 Starting Bot Manager Tests...")
    
    try:
        # Test 1: Start Bot Manager
        logger.info("Test 1: Starting Bot Manager...")
        result = await bot_manager.start_manager()
        assert result["success"], f"Failed to start bot manager: {result['message']}"
        logger.info("✅ Bot Manager started successfully")
        
        # Test 2: Check Initial Status
        logger.info("Test 2: Checking initial status...")
        status = bot_manager.get_status()
        assert status["manager_running"], "Manager should be running"
        assert "alpha_bot" in status["bots"], "Alpha bot should be in status"
        assert "micro_bot" in status["bots"], "Micro bot should be in status"
        logger.info("✅ Initial status check passed")
        
        # Test 3: Start Alpha Bot
        logger.info("Test 3: Starting Alpha Predator Bot...")
        result = await bot_manager.start_bot("alpha_bot")
        if result["success"]:
            logger.info("✅ Alpha Predator Bot started successfully")
        else:
            logger.warning(f"⚠️ Alpha Predator Bot failed to start: {result['message']}")
        
        # Test 4: Start Micro Bot
        logger.info("Test 4: Starting Micro Bot...")
        result = await bot_manager.start_bot("micro_bot")
        if result["success"]:
            logger.info("✅ Micro Bot started successfully")
        else:
            logger.warning(f"⚠️ Micro Bot failed to start: {result['message']}")
        
        # Test 5: Check Running Status
        logger.info("Test 5: Checking running status...")
        await asyncio.sleep(2)  # Give bots time to start
        status = bot_manager.get_status()
        logger.info("📊 Current Status:")
        for bot_name, bot_status in status["bots"].items():
            logger.info(f"   {bot_status['name']}: {bot_status['state']}")
        
        # Test 6: Health Check
        logger.info("Test 6: Testing health checks...")
        alpha_healthy = await bot_manager.health_check("alpha_bot")
        micro_healthy = await bot_manager.health_check("micro_bot")
        logger.info(f"   Alpha Bot Health: {'✅ Healthy' if alpha_healthy else '❌ Unhealthy'}")
        logger.info(f"   Micro Bot Health: {'✅ Healthy' if micro_healthy else '❌ Unhealthy'}")
        
        # Test 7: Let bots run for a short time
        logger.info("Test 7: Letting bots run for 30 seconds...")
        await asyncio.sleep(30)
        
        # Test 8: Stop Individual Bots
        logger.info("Test 8: Stopping individual bots...")
        alpha_stop = await bot_manager.stop_bot("alpha_bot")
        micro_stop = await bot_manager.stop_bot("micro_bot")
        logger.info(f"   Alpha Bot Stop: {'✅ Success' if alpha_stop['success'] else '❌ Failed'}")
        logger.info(f"   Micro Bot Stop: {'✅ Success' if micro_stop['success'] else '❌ Failed'}")
        
        # Test 9: Final Status Check
        logger.info("Test 9: Final status check...")
        status = bot_manager.get_status()
        logger.info("📊 Final Status:")
        for bot_name, bot_status in status["bots"].items():
            logger.info(f"   {bot_status['name']}: {bot_status['state']} "
                      f"(Errors: {bot_status['error_count']}, "
                      f"Restarts: {bot_status['restart_count']})")
        
        # Test 10: Stop Bot Manager
        logger.info("Test 10: Stopping Bot Manager...")
        result = await bot_manager.stop_manager()
        assert result["success"], f"Failed to stop bot manager: {result['message']}"
        logger.info("✅ Bot Manager stopped successfully")
        
        logger.info("🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}", exc_info=True)
        return False
    finally:
        # Ensure cleanup
        try:
            await bot_manager.shutdown_all()
        except:
            pass

async def test_error_recovery():
    """Test error recovery mechanisms."""
    logger.info("🧪 Testing Error Recovery Mechanisms...")
    
    try:
        # Start manager
        await bot_manager.start_manager()
        
        # Test restart functionality
        logger.info("Testing bot restart functionality...")
        restart_result = await bot_manager.restart_bot("alpha_bot")
        logger.info(f"Restart result: {restart_result}")
        
        # Test circuit breaker recovery
        logger.info("Testing circuit breaker behavior...")
        # Simulate multiple errors by checking error count handling
        bot = bot_manager.bots["alpha_bot"]
        original_error_count = bot.error_count
        bot.error_count = 3  # Simulate some errors
        logger.info(f"Simulated error count: {bot.error_count}")
        
        # Reset for clean state
        bot.error_count = original_error_count
        
        logger.info("✅ Error recovery tests completed")
        
    except Exception as e:
        logger.error(f"❌ Error recovery test failed: {e}", exc_info=True)
    finally:
        await bot_manager.shutdown_all()

async def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("🤖 BOT MANAGER TEST SUITE")
    logger.info("=" * 60)
    logger.info(f"Started at: {datetime.now()}")
    
    # Ensure log directory exists
    os.makedirs('backend/logs', exist_ok=True)
    
    # Run basic functionality tests
    basic_tests_passed = await test_bot_manager()
    
    if basic_tests_passed:
        logger.info("\n" + "=" * 40)
        logger.info("🔄 RUNNING ERROR RECOVERY TESTS")
        logger.info("=" * 40)
        await test_error_recovery()
    
    logger.info("\n" + "=" * 60)
    if basic_tests_passed:
        logger.info("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("✅ Bot Manager is ready for production use")
    else:
        logger.error("❌ SOME TESTS FAILED!")
        logger.error("⚠️ Please check the logs and fix issues before production use")
    logger.info("=" * 60)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}", exc_info=True)
        sys.exit(1)
