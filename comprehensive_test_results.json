{"passed": 11, "failed": 14, "tests": [{"name": "Root Health", "status": "PASS", "endpoint": "/", "response_size": 50}, {"name": "Basic Health", "status": "PASS", "endpoint": "/health", "response_size": 21}, {"name": "API Health", "status": "PASS", "endpoint": "/api/health", "response_size": 57}, {"name": "Tokens List", "status": "FAIL", "endpoint": "/api/tokens?limit=5", "error": "No response received"}, {"name": "<PERSON>", "status": "FAIL", "endpoint": "/api/spike-tokens?limit=5", "error": "No response received"}, {"name": "Discover <PERSON>s", "status": "FAIL", "endpoint": "/api/discover?limit=5", "error": "No response received"}, {"name": "Portfolio Status", "status": "FAIL", "endpoint": "/api/portfolio", "error": "No response received"}, {"name": "Trading Summary", "status": "FAIL", "endpoint": "/api/summary", "error": "No response received"}, {"name": "Live News", "status": "FAIL", "endpoint": "/api/news/live?limit=10", "error": "No response received"}, {"name": "Sentiment Feed", "status": "FAIL", "endpoint": "/api/sentiment-feed", "error": "No response received"}, {"name": "AI Logic", "status": "FAIL", "endpoint": "/api/ai-logic", "error": "No response received"}, {"name": "Analytics", "status": "FAIL", "endpoint": "/api/analytics", "error": "No response received"}, {"name": "PnL Data", "status": "FAIL", "endpoint": "/api/pnl-data", "error": "No response received"}, {"name": "Summary Fast", "status": "PASS", "endpoint": "/api/dashboard/summary-fast", "response_size": 1956}, {"name": "Trades Fast", "status": "PASS", "endpoint": "/api/dashboard/trades-fast", "response_size": 1698}, {"name": "PnL Fast", "status": "PASS", "endpoint": "/api/dashboard/pnl-fast", "response_size": 2}, {"name": "All Dashboard Fast", "status": "PASS", "endpoint": "/api/dashboard/all-fast", "response_size": 14343}, {"name": "TokenMetrics Top Tokens", "status": "PASS", "endpoint": "/api/tokenmetrics/top-tokens", "response_size": 128}, {"name": "TokenMetrics Moonshots", "status": "FAIL", "endpoint": "/api/tokenmetrics/moonshots", "error": "No response received"}, {"name": "Alpha Bot Status", "status": "FAIL", "endpoint": "/api/alpha-bot/status", "error": "No response received"}, {"name": "Micro Bot Status", "status": "FAIL", "endpoint": "/api/micro-bot/status", "error": "No response received"}, {"name": "Arbitrage Opportunities", "status": "PASS", "endpoint": "/api/arbitrage/opportunities", "response_size": 210}, {"name": "Enhanced Arbitrage", "status": "FAIL", "endpoint": "/api/arbitrage/enhanced", "error": "No response received"}, {"name": "CoinGecko Usage", "status": "PASS", "endpoint": "/api/coingecko/usage", "response_size": 277}, {"name": "CoinGecko Enhanced <PERSON>", "status": "PASS", "endpoint": "/api/coingecko/enhanced-tokens", "response_size": 85}]}