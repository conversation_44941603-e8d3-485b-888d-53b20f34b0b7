# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# === SECURITY: Environment Variables & Secrets ===
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.staging
backend/.env
backend/.env.local
backend/.env.production
backend/.env.staging
frontend/.env
frontend/.env.local
frontend/.env.production
frontend/.env.staging

# Firebase service account keys
**/firebase-service-account.json
**/service-account*.json
**/*-service-account.json

# API Keys and credentials
**/credentials.json
**/secrets.json
**/*.pem
**/*.key
**/*.crt

# === NODE.JS ===
# dependencies
/node_modules
/.pnp
.pnp.js
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# testing
/coverage
**/coverage/

# production builds
/build
/dist
**/build/
**/dist/

# === PYTHON ===
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
backend/venv/
backend/env/

# === DEVELOPMENT TOOLS ===
.vscode/
.idea/
*.swp
*.swo
*~

# === SYSTEM FILES ===
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# === LOGS ===
*.log
logs/
**/logs/
backend/logs/
frontend/logs/

# === DATA FILES ===
*.csv
*.json.bak
*.jsonl
backend/data/*.json
backend/data/*.csv
backend/data/*.jsonl
!backend/data/.gitkeep

# === DOCKER ===
.dockerignore

# === DEPLOYMENT ===
flux/
.flux/

# === TEMPORARY FILES ===
*.tmp
*.temp
.cache/
.parcel-cache/

# === IDE ===
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# === OS ===
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# === BACKUP FILES ===
*.bak
*.backup
*.old
