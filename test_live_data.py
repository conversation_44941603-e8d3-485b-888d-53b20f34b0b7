#!/usr/bin/env python3
"""
Test script to verify live data fetching for 50 tokens
"""

import sys
import time
from datetime import datetime


def test_live_data_config():
    """Test live data configuration"""
    print("🔍 TESTING LIVE DATA CONFIGURATION")
    print("=" * 50)

    try:
        # Test live_runner configuration
        from live_runner import MAX_TOKENS, BATCH_SIZE, MAX_CONCURRENT_TRADES

        print(f"📊 MAX_TOKENS: {MAX_TOKENS}")
        print(f"🔄 BATCH_SIZE: {BATCH_SIZE}")
        print(f"⚡ MAX_CONCURRENT_TRADES: {MAX_CONCURRENT_TRADES}")

        # Test token_selector configuration
        from token_selector import (
            CACHE_TTL_TOKENS,
            CACHE_TTL_PRICES,
            API_RATE_LIMIT_DELAY,
        )

        print(f"⏰ CACHE_TTL_TOKENS: {CACHE_TTL_TOKENS}s (live data)")
        print(f"💰 CACHE_TTL_PRICES: {CACHE_TTL_PRICES}s (live data)")
        print(f"🚦 API_RATE_LIMIT_DELAY: {API_RATE_LIMIT_DELAY}s")

        # Test sentiment configuration
        from sentiment_engine import SENTIMENT_CACHE_TTL, MAX_WORKERS

        print(f"🧠 SENTIMENT_CACHE_TTL: {SENTIMENT_CACHE_TTL}s (live data)")
        print(f"👥 SENTIMENT_MAX_WORKERS: {MAX_WORKERS}")

        # Test TokenMetrics configuration
        from config import TOKENMETRICS_API_KEY

        if TOKENMETRICS_API_KEY:
            print(
                f"🎯 TOKENMETRICS_API_KEY: {'*' * 10}...{TOKENMETRICS_API_KEY[-4:]} (configured)"
            )
        else:
            print("⚠️ TOKENMETRICS_API_KEY: Not configured")

        print("\n✅ LIVE DATA CONFIGURATION VERIFIED!")
        print(f"🎯 System configured to monitor {MAX_TOKENS} tokens with live data")
        print("📊 Data sources: KuCoin + CoinGecko + TokenMetrics")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_token_generation():
    """Test token generation"""
    print("\n🔍 TESTING TOKEN GENERATION")
    print("=" * 50)

    try:
        from token_selector import generate_top_token_list

        print("🔴 Generating live token list...")
        start_time = time.time()

        tokens = generate_top_token_list(50)

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ Generated {len(tokens)} tokens in {duration:.2f}s")

        if tokens:
            print("\n📊 TOP 10 TOKENS:")
            for i, token in enumerate(tokens[:10]):
                symbol = token.get("symbol", "UNKNOWN")
                score = token.get("score", 0)
                volume = token.get("volume", 0)
                print(
                    f"  {i+1:2d}. {symbol:12s} | Score: {score:6.2f} | Volume: ${volume:,.0f}"
                )

            if len(tokens) > 10:
                print(f"  ... and {len(tokens) - 10} more tokens")

        return len(tokens) >= 50

    except Exception as e:
        print(f"❌ Token generation test failed: {e}")
        return False


if __name__ == "__main__":
    print(f"🚀 ALPHA PREDATOR LIVE DATA TEST")
    print(f"⏰ Test started at: {datetime.now()}")
    print()

    config_ok = test_live_data_config()
    token_ok = test_token_generation()

    # Run additional tests
    sentiment_ok = test_sentiment_analysis()
    ai_ok = test_ai_decision_engine()
    flow_ok = test_alpha_predator_flow()

    print("\n" + "=" * 50)
    if config_ok and token_ok and sentiment_ok and ai_ok and flow_ok:
        print("🎉 ALL TESTS PASSED - ALPHA PREDATOR SYSTEM READY!")
        print("🔴 System configured for real-time data fetching")
        print("📊 100 KuCoin tokens → 50 selected → AI analysis → Auto-trading")
        print("🧠 Sentiment analysis working with real data")
        print("🤖 AI decision engine using 200+ data points")
        print("⚡ Complete trading flow operational")
    else:
        print("❌ SOME TESTS FAILED - CHECK CONFIGURATION")
        print(
            f"Config: {'✅' if config_ok else '❌'} | Tokens: {'✅' if token_ok else '❌'} | Sentiment: {'✅' if sentiment_ok else '❌'} | AI: {'✅' if ai_ok else '❌'} | Flow: {'✅' if flow_ok else '❌'}"
        )

    print(f"⏰ Test completed at: {datetime.now()}")


def test_sentiment_analysis():
    """Test sentiment analysis functionality"""
    print("\n🧠 TESTING SENTIMENT ANALYSIS")
    print("=" * 50)

    try:
        from sentiment_engine import get_sentiment_score, get_combined_sentiment

        test_tokens = ["BTC", "ETH", "SOL", "DOGE", "SHIB"]

        for token in test_tokens:
            try:
                # Test basic sentiment
                sentiment_score = get_sentiment_score(token)

                # Test combined sentiment
                combined_result = get_combined_sentiment(token)
                combined_score = combined_result.get("combined_score", 0)
                confidence = combined_result.get("confidence", 0)

                print(f"  {token}:")
                print(f"    Basic Sentiment: {sentiment_score:.3f}")
                print(f"    Combined Score: {combined_score:.3f}")
                print(f"    Confidence: {confidence:.3f}")

                # Check if sentiment is not zero
                if sentiment_score != 0.0 and sentiment_score != 0.5:
                    print(f"    ✅ Non-zero sentiment detected")
                else:
                    print(f"    ⚠️ Default/zero sentiment")

            except Exception as e:
                print(f"  {token}: ❌ Error - {e}")

        return True

    except Exception as e:
        print(f"❌ Sentiment analysis test failed: {e}")
        return False


def test_ai_decision_engine():
    """Test AI decision engine with comprehensive data"""
    print("\n🤖 TESTING AI DECISION ENGINE")
    print("=" * 50)

    try:
        import asyncio
        from ai_core import get_ai_engine

        ai_engine = get_ai_engine()
        test_tokens = ["BTC-USDT", "ETH-USDT"]

        async def test_ai_decisions():
            for token in test_tokens:
                try:
                    print(f"\n🔍 Analyzing {token}...")
                    decision = await ai_engine.get_profitable_decision(token)

                    print(f"  Decision: {decision.get('decision', 'UNKNOWN')}")
                    print(f"  Confidence: {decision.get('confidence', 0):.3f}")
                    print(f"  Data Points Used: {decision.get('data_points_used', 0)}")
                    print(f"  Combined Score: {decision.get('combined_score', 0):.3f}")

                    # Check analysis breakdown
                    breakdown = decision.get("analysis_breakdown", {})
                    if breakdown:
                        print(f"  Analysis Breakdown:")
                        for component, value in breakdown.items():
                            print(f"    {component.capitalize()}: {value}")

                    if decision.get("data_points_used", 0) > 0:
                        print(f"    ✅ Using comprehensive data analysis")
                    else:
                        print(f"    ⚠️ Limited data analysis")

                except Exception as e:
                    print(f"  {token}: ❌ Error - {e}")

        # Run async test
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, test_ai_decisions())
                    future.result()
            else:
                asyncio.run(test_ai_decisions())
        except Exception as e:
            print(f"⚠️ Async test execution issue: {e}")

        return True

    except Exception as e:
        print(f"❌ AI decision engine test failed: {e}")
        return False


def test_alpha_predator_flow():
    """Test the complete Alpha Predator trading flow"""
    print("\n🎯 TESTING ALPHA PREDATOR COMPLETE FLOW")
    print("=" * 50)

    try:
        import asyncio
        from token_selector import alpha_predator_trading_flow

        async def test_complete_flow():
            print("🚀 Testing complete Alpha Predator trading flow...")
            print("📊 Flow: 100 KuCoin → 50 Selected → Sentiment → AI → Trading")

            # Test the complete flow (with shorter timeout for testing)
            try:
                flow_result = await alpha_predator_trading_flow()

                print(f"\n📈 Flow Results:")
                print(f"  Status: {flow_result.get('status', 'unknown')}")

                if flow_result.get("status") == "success":
                    print(
                        f"  ✅ KuCoin tokens fetched: {flow_result.get('kucoin_tokens_fetched', 0)}"
                    )
                    print(
                        f"  ✅ Tokens selected: {flow_result.get('tokens_selected', 0)}"
                    )
                    print(
                        f"  ✅ Tokens analyzed: {flow_result.get('tokens_analyzed', 0)}"
                    )
                    print(
                        f"  ✅ Trading decisions: {flow_result.get('trading_decisions', 0)}"
                    )
                    print(
                        f"  ✅ Trades executed: {flow_result.get('trades_executed', 0)}"
                    )

                    # Check execution results
                    execution_results = flow_result.get("execution_results", [])
                    if execution_results:
                        print(f"\n💰 Trade Execution Summary:")
                        successful = len(
                            [
                                r
                                for r in execution_results
                                if r.get("status") == "success"
                            ]
                        )
                        failed = len(
                            [
                                r
                                for r in execution_results
                                if r.get("status") == "failed"
                            ]
                        )
                        print(f"    Successful trades: {successful}")
                        print(f"    Failed trades: {failed}")

                        # Show sample trades
                        for i, result in enumerate(execution_results[:3]):
                            symbol = result.get("symbol", "UNKNOWN")
                            amount = result.get("amount", 0)
                            status = result.get("status", "unknown")
                            print(f"    {i+1}. {symbol}: ${amount} - {status}")

                    return True
                else:
                    print(
                        f"  ❌ Flow failed: {flow_result.get('error', 'Unknown error')}"
                    )
                    return False

            except Exception as e:
                print(f"  ❌ Flow execution error: {e}")
                return False

        # Run async test
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, test_complete_flow())
                    return future.result(timeout=120)  # 2 minute timeout
            else:
                return asyncio.run(test_complete_flow())
        except Exception as e:
            print(f"⚠️ Async flow test execution issue: {e}")
            return False

    except Exception as e:
        print(f"❌ Alpha Predator flow test failed: {e}")
        return False
