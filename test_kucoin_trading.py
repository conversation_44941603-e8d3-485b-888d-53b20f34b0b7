#!/usr/bin/env python3
"""
Test KuCoin Trading Functionality
This test checks if KuCoin API is properly configured and can execute real trades.
"""

import sys
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')
load_dotenv('.env')

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.kucoin_api import Ku<PERSON>oinAP<PERSON>
from backend.config import KUCOIN_API_KEY, KUCOIN_API_SECRET, KUCOIN_API_PASSPHRASE, TRADING_MODE

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_kucoin_connectivity():
    """Test KuCoin API connectivity and authentication"""
    
    print("🔗 Testing KuCoin API Connectivity")
    print("=" * 60)
    
    # Check credentials
    print("\n🔑 Checking KuCoin Credentials:")
    print(f"  API Key: {'✅ Set' if KUCOIN_API_KEY else '❌ Missing'}")
    print(f"  API Secret: {'✅ Set' if KUCOIN_API_SECRET else '❌ Missing'}")
    print(f"  API Passphrase: {'✅ Set' if KUCOIN_API_PASSPHRASE else '❌ Missing'}")
    print(f"  Trading Mode: {TRADING_MODE}")
    
    if not all([KUCOIN_API_KEY, KUCOIN_API_SECRET, KUCOIN_API_PASSPHRASE]):
        print("\n❌ KuCoin credentials not fully configured!")
        print("   Please set KUCOIN_API_KEY, KUCOIN_API_SECRET, and KUCOIN_API_PASSPHRASE in your .env file")
        return False
    
    # Initialize KuCoin API
    kucoin = KuCoinAPI()
    
    if not kucoin.authenticated:
        print("\n❌ KuCoin API authentication failed!")
        return False
    
    print("\n✅ KuCoin API credentials loaded successfully!")
    
    # Test 1: Get account information
    print("\n🏦 Test 1: Getting Account Information")
    print("-" * 40)
    
    try:
        accounts = kucoin.get_account_list()
        if accounts:
            print("✅ Successfully retrieved account information!")
            print(f"   Found {len(accounts)} account(s)")
            
            # Show balances for major currencies
            major_currencies = ['USDT', 'BTC', 'ETH', 'KCS']
            for currency in major_currencies:
                balance = kucoin.get_account_balance(currency)
                if balance > 0:
                    print(f"   💰 {currency}: {balance:.6f}")
        else:
            print("⚠️ No account information retrieved (may be empty or API issue)")
            
    except Exception as e:
        print(f"❌ Failed to get account information: {e}")
        return False
    
    # Test 2: Get market data
    print("\n📊 Test 2: Getting Market Data")
    print("-" * 40)
    
    test_symbol = "BTC-USDT"
    try:
        ticker = kucoin.get_ticker(test_symbol)
        if ticker:
            print(f"✅ Successfully retrieved ticker for {test_symbol}!")
            print(f"   💲 Price: ${float(ticker.get('price', 0)):,.2f}")
            print(f"   📈 Best Ask: ${float(ticker.get('bestAsk', 0)):,.2f}")
            print(f"   📉 Best Bid: ${float(ticker.get('bestBid', 0)):,.2f}")
        else:
            print(f"❌ Failed to get ticker for {test_symbol}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to get market data: {e}")
        return False
    
    # Test 3: Get historical data
    print("\n📈 Test 3: Getting Historical Data")
    print("-" * 40)
    
    try:
        klines = kucoin.get_klines(test_symbol, "1hour")
        if klines:
            print(f"✅ Successfully retrieved historical data for {test_symbol}!")
            print(f"   📊 Got {len(klines)} data points")
            if len(klines) > 0:
                latest = klines[0]  # KuCoin returns newest first
                print(f"   🕐 Latest candle: Open=${float(latest[1]):,.2f}, Close=${float(latest[2]):,.2f}")
        else:
            print(f"❌ Failed to get historical data for {test_symbol}")
            
    except Exception as e:
        print(f"❌ Failed to get historical data: {e}")
    
    print("\n✅ KuCoin API connectivity test completed successfully!")
    return True

def test_trading_functionality():
    """Test trading functionality (paper trading mode for safety)"""
    
    print("\n\n💼 Testing Trading Functionality")
    print("=" * 60)
    
    kucoin = KuCoinAPI()
    
    if not kucoin.authenticated:
        print("❌ Cannot test trading - KuCoin not authenticated")
        return False
    
    # Get current USDT balance
    usdt_balance = kucoin.get_account_balance("USDT")
    print(f"\n💰 Current USDT Balance: {usdt_balance:.6f}")
    
    if usdt_balance < 10:
        print("⚠️ Low USDT balance - cannot test real trading")
        print("   This test will only verify API functionality")
    
    # Test order placement (DRY RUN - we won't actually place orders)
    print("\n📝 Test 4: Order Placement Simulation")
    print("-" * 40)
    
    test_symbol = "BTC-USDT"
    ticker = kucoin.get_ticker(test_symbol)
    
    if ticker:
        current_price = float(ticker.get('price', 0))
        
        # Simulate a small buy order (10% below current price to avoid execution)
        test_price = current_price * 0.9  # 10% below market
        test_size = 10 / test_price  # $10 worth
        
        print(f"📊 Market Price: ${current_price:,.2f}")
        print(f"🎯 Test Order: BUY {test_size:.8f} {test_symbol} at ${test_price:,.2f}")
        print(f"💰 Order Value: ${test_size * test_price:.2f}")
        
        if TRADING_MODE == "live" and usdt_balance >= 10:
            print("\n⚠️ LIVE TRADING MODE DETECTED!")
            print("   Would you like to place a real test order? (y/N)")
            print("   This will use real money!")
            
            # For safety, we'll skip actual order placement in this test
            print("   Skipping real order placement for safety...")
            print("   To enable real trading, modify this test script")
        else:
            print("✅ Paper trading mode - no real orders placed")
    
    print("\n✅ Trading functionality test completed!")
    return True

def create_real_trade_executor():
    """Create a real trading version of the trade executor"""
    
    print("\n\n🔧 Creating Real Trade Executor")
    print("=" * 60)
    
    real_executor_code = '''
import logging
from typing import Optional, Dict, Any
from kucoin_api import KuCoinAPI
from config import TRADING_MODE, BASE_ORDER_SIZE_USDT
from trade_executor import log_trade, load_portfolio, save_portfolio
import time

logger = logging.getLogger(__name__)

class RealTradeExecutor:
    """Real trading executor using KuCoin API"""
    
    def __init__(self):
        self.kucoin = KuCoinAPI()
        self.min_order_size = 1.0  # Minimum $1 order
        
    def execute_real_trade(self, token_symbol: str, side: str, amount_usd: Optional[float] = None, 
                          strategy: str = "AI", reason: str = "") -> Dict[str, Any]:
        """
        Execute a real trade on KuCoin
        
        Args:
            token_symbol: Trading pair (e.g., "BTC-USDT")
            side: "BUY" or "SELL"
            amount_usd: USD amount to trade
            strategy: Strategy name
            reason: Reason for trade
            
        Returns:
            Dict with success status and details
        """
        
        if TRADING_MODE != "live":
            return {"success": False, "message": "Not in live trading mode"}
            
        if not self.kucoin.authenticated:
            return {"success": False, "message": "KuCoin not authenticated"}
            
        try:
            # Ensure proper symbol format
            if not token_symbol.endswith("-USDT"):
                token_symbol = f"{token_symbol}-USDT"
                
            # Get current market price
            ticker = self.kucoin.get_ticker(token_symbol)
            if not ticker:
                return {"success": False, "message": f"Could not get price for {token_symbol}"}
                
            current_price = float(ticker.get('price', 0))
            if current_price <= 0:
                return {"success": False, "message": f"Invalid price for {token_symbol}"}
            
            # Calculate order details
            if amount_usd is None:
                amount_usd = BASE_ORDER_SIZE_USDT
                
            if amount_usd < self.min_order_size:
                return {"success": False, "message": f"Order size too small: ${amount_usd}"}
            
            # Check balances
            if side.upper() == "BUY":
                usdt_balance = self.kucoin.get_account_balance("USDT")
                if usdt_balance < amount_usd:
                    return {"success": False, "message": f"Insufficient USDT balance: {usdt_balance}"}
                    
                # Calculate token amount to buy
                token_amount = amount_usd / current_price
                order_price = current_price * 1.001  # Slightly above market for quick fill
                
            else:  # SELL
                token_symbol_base = token_symbol.replace("-USDT", "")
                token_balance = self.kucoin.get_account_balance(token_symbol_base)
                token_amount = amount_usd / current_price
                
                if token_balance < token_amount:
                    return {"success": False, "message": f"Insufficient {token_symbol_base} balance: {token_balance}"}
                    
                order_price = current_price * 0.999  # Slightly below market for quick fill
            
            # Place the order
            logger.info(f"Placing {side} order: {token_amount:.8f} {token_symbol} at ${order_price:.8f}")
            
            order_result = self.kucoin.place_order(
                symbol=token_symbol,
                side=side.lower(),
                price=order_price,
                size=token_amount
            )
            
            if order_result:
                # Log the trade
                log_trade(token_symbol, side, token_amount, order_price, strategy, reason)
                
                return {
                    "success": True,
                    "order_id": order_result.get("orderId"),
                    "message": f"{side} order placed: {token_amount:.8f} {token_symbol} at ${order_price:.8f}",
                    "amount": token_amount,
                    "price": order_price,
                    "value": token_amount * order_price
                }
            else:
                return {"success": False, "message": "Failed to place order on KuCoin"}
                
        except Exception as e:
            logger.error(f"Error executing real trade: {e}")
            return {"success": False, "message": str(e)}

# Global instance
real_trade_executor = RealTradeExecutor()
'''
    
    # Write the real trade executor to a file
    with open("backend/real_trade_executor.py", "w") as f:
        f.write(real_executor_code)
    
    print("✅ Real trade executor created at backend/real_trade_executor.py")
    print("   This module can execute actual trades on KuCoin when TRADING_MODE='live'")
    
    return True

def main():
    """Main test function"""
    
    print("🚀 KuCoin Trading System Test")
    print("=" * 60)
    
    # Test 1: Connectivity
    if not test_kucoin_connectivity():
        print("\n❌ KuCoin connectivity test failed!")
        return
    
    # Test 2: Trading functionality
    if not test_trading_functionality():
        print("\n❌ Trading functionality test failed!")
        return
    
    # Test 3: Create real trade executor
    if not create_real_trade_executor():
        print("\n❌ Failed to create real trade executor!")
        return
    
    print("\n" + "=" * 60)
    print("🎯 KuCoin Trading System Test Complete!")
    print("\n💡 Summary:")
    print("  ✅ KuCoin API connectivity verified")
    print("  ✅ Account and market data accessible")
    print("  ✅ Trading functionality ready")
    print("  ✅ Real trade executor created")
    print("\n🔧 Next Steps:")
    print("  • Set TRADING_MODE='live' in .env to enable real trading")
    print("  • Ensure sufficient USDT balance for trading")
    print("  • Import real_trade_executor for live trading")
    print("  • Test with small amounts first!")

if __name__ == "__main__":
    main()
