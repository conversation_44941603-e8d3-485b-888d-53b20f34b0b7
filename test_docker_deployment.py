#!/usr/bin/env python3
"""
Docker Deployment Test Script
Tests the AMD64 Docker deployment for frontend and backend health
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:33903"
FRONTEND_URL = "http://localhost:39882"

def test_backend_health():
    """Test backend health endpoints"""
    print("🔍 Testing Backend Health...")
    
    try:
        # Test basic health endpoint
        response = requests.get(f"{BACKEND_URL}/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend Health: {data.get('status')} - Version: {data.get('version')}")
            return True
        else:
            print(f"❌ Backend Health Failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend Health Exception: {e}")
        return False

def test_frontend_health():
    """Test frontend availability"""
    print("🔍 Testing Frontend Health...")
    
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200 and "Alpha Predator Frontend" in response.text:
            print("✅ Frontend: Serving correctly")
            return True
        else:
            print(f"❌ Frontend Failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend Exception: {e}")
        return False

def test_backend_endpoints():
    """Test available backend endpoints without authentication"""
    print("🔍 Testing Backend Endpoints...")
    
    endpoints = [
        {"path": "/", "name": "Root"},
        {"path": "/health", "name": "Basic Health"},
        {"path": "/api/health", "name": "API Health"}
    ]
    
    results = []
    for endpoint in endpoints:
        try:
            start_time = time.time()
            response = requests.get(f"{BACKEND_URL}{endpoint['path']}", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ {endpoint['name']}: OK ({response_time:.2f}s)")
                results.append(True)
            else:
                print(f"❌ {endpoint['name']}: HTTP {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"❌ {endpoint['name']}: Exception - {e}")
            results.append(False)
    
    return all(results)

def test_docker_containers():
    """Test if Docker containers are running properly"""
    print("🔍 Testing Docker Container Status...")
    
    import subprocess
    
    try:
        # Check if containers are running
        result = subprocess.run(
            ["docker-compose", "-f", "docker-compose.amd64.yml", "ps"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout
            if "alpha_backend_amd64" in output and "alpha_frontend_amd64" in output:
                print("✅ Docker Containers: Both frontend and backend containers are running")
                return True
            else:
                print("❌ Docker Containers: Not all containers are running")
                print(output)
                return False
        else:
            print(f"❌ Docker Command Failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Docker Status Check Exception: {e}")
        return False

def test_backend_logs():
    """Check backend logs for errors"""
    print("🔍 Checking Backend Logs...")
    
    import subprocess
    
    try:
        result = subprocess.run(
            ["docker-compose", "-f", "docker-compose.amd64.yml", "logs", "--tail=20", "backend"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            logs = result.stdout
            
            # Check for critical errors
            error_indicators = ["ERROR", "CRITICAL", "FATAL", "Exception"]
            critical_errors = []
            
            for line in logs.split('\n'):
                for indicator in error_indicators:
                    if indicator in line and "NLTK" not in line:  # Ignore NLTK warnings
                        critical_errors.append(line.strip())
            
            if critical_errors:
                print("⚠️  Backend Logs: Found some errors (but service is running)")
                for error in critical_errors[-3:]:  # Show last 3 errors
                    print(f"   {error}")
                return True  # Still consider it working if API responds
            else:
                print("✅ Backend Logs: No critical errors found")
                return True
        else:
            print(f"❌ Failed to get backend logs: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Log Check Exception: {e}")
        return False

def main():
    """Run all deployment tests"""
    print("🚀 Docker AMD64 Deployment Test")
    print("=" * 50)
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Frontend URL: {FRONTEND_URL}")
    print(f"Test Time: {datetime.now().isoformat()}")
    print("=" * 50)
    
    tests = [
        ("Docker Containers", test_docker_containers),
        ("Backend Health", test_backend_health),
        ("Frontend Health", test_frontend_health),
        ("Backend Endpoints", test_backend_endpoints),
        ("Backend Logs", test_backend_logs)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔄 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Docker deployment is healthy.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
