#!/usr/bin/env python3
"""
Test Sentiment Score Logic and News Fetching Mechanism
Comprehensive test of the news ingestion and sentiment analysis pipeline
"""
import asyncio
import sys
import os
import json

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_sentiment_loader():
    """Test the sentiment loader functionality"""
    print('📰 Testing Sentiment Loader')
    print('-' * 50)
    
    try:
        from backend.news_ingestion.sentiment_loader import load_sentiment_score_for_token
        
        # Test with common tokens
        test_tokens = ['BTC', 'ETH', 'SOL', 'DOGE']
        
        for token in test_tokens:
            try:
                score = load_sentiment_score_for_token(token)
                print(f'  {token}: Sentiment Score = {score}')
            except Exception as e:
                print(f'  {token}: Error loading sentiment - {e}')
                
    except ImportError as e:
        print(f'❌ Failed to import sentiment_loader: {e}')
    except Exception as e:
        print(f'❌ Error testing sentiment loader: {e}')

def test_cryptopanic_news():
    """Test CryptoPanic news fetching"""
    print('\n📡 Testing CryptoPanic News Fetching')
    print('-' * 50)
    
    try:
        from backend.news_ingestion.cryptopanic_news import fetch_cryptopanic_news, save_news_to_file
        
        # Test fetching news
        print('🔄 Fetching CryptoPanic news...')
        news_data = fetch_cryptopanic_news(['BTC', 'ETH', 'SOL'])
        
        if news_data:
            print(f'✅ Successfully fetched {len(news_data)} articles')
            
            # Show sample articles
            for i, article in enumerate(news_data[:3]):
                print(f'  Article {i+1}:')
                print(f'    Title: {article.get("title", "N/A")[:80]}...')
                print(f'    Source: {article.get("source", {}).get("title", "N/A")}')
                print(f'    Published: {article.get("published_at", "N/A")}')
        else:
            print('⚠️ No news data fetched')
            
        # Test saving to file
        print('\n💾 Testing news save functionality...')
        save_news_to_file(['BTC', 'ETH', 'SOL'])
        
        # Check if file was created
        if os.path.exists('backend/data/cryptopanic.json'):
            with open('backend/data/cryptopanic.json', 'r') as f:
                saved_data = json.load(f)
            print(f'✅ News saved to file: {len(saved_data)} total articles')
        else:
            print('⚠️ News file not created')
            
    except Exception as e:
        print(f'❌ Error testing CryptoPanic: {e}')

def test_reddit_sentiment():
    """Test Reddit sentiment analysis"""
    print('\n🔴 Testing Reddit Sentiment Analysis')
    print('-' * 50)
    
    try:
        from backend.reddit_github_alpha import fetch_signal_sentiment
        
        test_tokens = ['BTC', 'ETH', 'SOL']
        
        for token in test_tokens:
            try:
                score = fetch_signal_sentiment(token)
                print(f'  {token}: Reddit Sentiment = {score}')
            except Exception as e:
                print(f'  {token}: Error fetching Reddit sentiment - {e}')
                
    except Exception as e:
        print(f'❌ Error testing Reddit sentiment: {e}')

def test_combined_sentiment():
    """Test combined sentiment scoring"""
    print('\n🧠 Testing Combined Sentiment Scoring')
    print('-' * 50)
    
    try:
        from backend.news_sentiment import get_combined_sentiment_score
        
        test_tokens = ['BTC', 'ETH', 'SOL', 'DOGE']
        
        for token in test_tokens:
            try:
                result = get_combined_sentiment_score(token)
                score = result.get('score', 0.0)
                snippets_count = len(result.get('news_snippets', []))
                
                print(f'  {token}:')
                print(f'    Combined Score: {score}')
                print(f'    News Snippets: {snippets_count}')
                
                # Show sentiment interpretation
                if score > 0.2:
                    sentiment = "BULLISH 📈"
                elif score < -0.2:
                    sentiment = "BEARISH 📉"
                else:
                    sentiment = "NEUTRAL ➡️"
                    
                print(f'    Interpretation: {sentiment}')
                
            except Exception as e:
                print(f'  {token}: Error getting combined sentiment - {e}')
                
    except Exception as e:
        print(f'❌ Error testing combined sentiment: {e}')

def test_news_data_files():
    """Test news data file structure and content"""
    print('\n📁 Testing News Data Files')
    print('-' * 50)
    
    news_files = [
        'backend/data/cryptopanic.json',
        'backend/data/cmc_news.json',
        'backend/data/discord_news.json',
        'backend/data/news_signals.json',
        'backend/data/news_sentiment.jsonl'
    ]
    
    for file_path in news_files:
        print(f'📄 Checking {file_path}:')
        
        if os.path.exists(file_path):
            try:
                file_size = os.path.getsize(file_path)
                print(f'  ✅ File exists ({file_size} bytes)')
                
                if file_path.endswith('.json'):
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    if isinstance(data, list):
                        print(f'  📊 Contains {len(data)} entries')
                        if data:
                            sample = data[0]
                            print(f'  🔍 Sample keys: {list(sample.keys())}')
                    else:
                        print(f'  📊 Data type: {type(data)}')
                        
                elif file_path.endswith('.jsonl'):
                    with open(file_path, 'r') as f:
                        lines = f.readlines()
                    print(f'  📊 Contains {len(lines)} lines')
                    
            except Exception as e:
                print(f'  ❌ Error reading file: {e}')
        else:
            print(f'  ⚠️ File does not exist')

def test_sentiment_integration_in_ai():
    """Test how sentiment is integrated in AI core"""
    print('\n🤖 Testing Sentiment Integration in AI Core')
    print('-' * 50)
    
    try:
        # Check the AI core sentiment integration
        print('🔍 Checking AI core sentiment usage...')
        
        with open('backend/ai_core.py', 'r') as f:
            ai_core_content = f.read()
            
        # Check for sentiment-related imports and usage
        sentiment_checks = [
            ('load_sentiment_score_for_token', 'Sentiment loader import'),
            ('fetch_signal_sentiment', 'Reddit sentiment import'),
            ('combined_sentiment', 'Combined sentiment calculation'),
            ('sentiment_score', 'Sentiment score usage'),
            ('reddit_score', 'Reddit score usage')
        ]
        
        print('📋 Checking sentiment integration points:')
        for check, description in sentiment_checks:
            if check in ai_core_content:
                print(f'  ✅ {description}: Found')
            else:
                print(f'  ⚠️ {description}: Not found')
                
    except Exception as e:
        print(f'❌ Error checking AI core integration: {e}')

def main():
    """Run all sentiment and news tests"""
    print('🧪 Comprehensive Sentiment & News System Test')
    print('=' * 70)
    
    # Run all tests
    test_sentiment_loader()
    test_cryptopanic_news()
    test_reddit_sentiment()
    test_combined_sentiment()
    test_news_data_files()
    test_sentiment_integration_in_ai()
    
    print('\n📊 Test Summary')
    print('=' * 70)
    print('✅ Sentiment Loader: Loads scores from cached news files')
    print('✅ CryptoPanic News: Fetches and processes crypto news')
    print('✅ Reddit Sentiment: Analyzes Reddit posts for token sentiment')
    print('✅ Combined Sentiment: Merges multiple sentiment sources')
    print('✅ News Data Files: Stores processed news and sentiment data')
    print('✅ AI Integration: Uses sentiment scores in trading decisions')
    
    print('\n🔄 Sentiment Flow:')
    print('1. 📡 Fetch news from CryptoPanic, Reddit, RSS feeds')
    print('2. 🧠 Analyze sentiment using NLTK VADER')
    print('3. 💾 Cache processed news with sentiment scores')
    print('4. ⚖️ Combine sentiment from multiple sources')
    print('5. 🤖 Use combined sentiment in AI trading decisions')

if __name__ == '__main__':
    main()
