# Alpha Predator Bot - Environment Setup and Troubleshooting Guide

This guide provides detailed steps to resolve common virtual environment issues for both backend and frontend of the Alpha Predator Bot project.

---

## 1. Environment Variable Configuration

### Backend `.env` file

Create a `.env` file inside the `backend/` directory with the following variables:

```
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_password
PORT=3005
HOST=0.0.0.0
CORS_ORIGINS=https://alphapredatorbot.xyz,https://www.alphapredatorbot.xyz,https://alphabot_39882.app.runonflux.io,http://alphabot_39882.app.runonflux.io
```

Replace `<EMAIL>` and `your_secure_password` with your actual admin credentials.

### Frontend `.env` file

Create a `.env` file inside the `frontend/` directory with the following content:

```
VITE_API_URL=/api
NODE_ENV=production
```

---

## 2. Clearing Docker Cache and Rebuilding Images

To avoid stale dependencies or build artifacts, clear Docker cache and rebuild images:

```bash
docker system prune -a --volumes
docker-compose -f dockerfrontend-compose.yml build --no-cache
docker-compose -f dockerfrontend-compose.yml up -d
```

For AMD64 Flux deployment:

```bash
docker-compose -f docker-compose.amd64.yml build --no-cache
docker push kryptomerch/alpha-frontend:amd64
docker push kryptomerch/alpha-backend:amd64
```

---

## 3. Running Backend and Frontend Locally

### Backend

From the `backend/` directory, install dependencies and run:

```bash
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 3005
```

Ensure `.env` file is present in `backend/`.

### Frontend

From the `frontend/` directory, install dependencies and run:

```bash
npm install
npm run dev
```

Ensure `.env` file is present in `frontend/`.

---

## 4. Platform Compatibility

- Use the `Dockerfile.amd64` and `docker-compose.amd64.yml` files for AMD64 platform compatibility, especially for Flux deployment.
- The standard `Dockerfile` and `dockerfrontend-compose.yml` are for local or non-AMD64 environments.

---

## 5. Troubleshooting

### Frontend 503 Error or Backend Logs Showing in Frontend

- Verify you are using the updated frontend Dockerfile that installs dev dependencies.
- Check build logs for successful creation of `dist` folder.
- Check nginx is serving files correctly inside the container:

```bash
docker exec alpha_frontend ls -la /usr/share/nginx/html
```

### Build Failures

- Clear Docker cache as shown above.
- Verify Node.js dependencies with `npm install` in frontend.
- Test local build with `npm run build`.

### Flux Deployment Issues

- Ensure AMD64 images are pushed and used.
- Verify platform compatibility in Flux console.
- Check resource allocation (CPU/RAM).

### Logs and Health Checks

- Check container logs:

```bash
docker logs alpha_frontend
docker logs alpha_backend
```

- Health checks are configured in docker-compose files and can be monitored.

---

## 6. Additional Notes

- Always keep `.env` files secure and do not commit them to version control.
- Use the `deploy.sh` script for automated deployment and testing.
- Monitor logs regularly to catch issues early.

---

This guide should help resolve most virtual environment and deployment issues. For further assistance, please provide specific error messages or logs.

---
